!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("highcharts/highcharts",[],e):"object"==typeof exports?exports.highcharts=e():(t.Highcharts&&t.Highcharts.error(16,!0),t.Highcharts=e())}(this,function(){return function(){"use strict";var t,e,i,o,r,n,s,a,h,l,c,d,p,u,f,g,v,m,y,x,b,k,w,M,S,T,C,A,P,O,L,E,I,D,B,N,z,R,W,X,H,j,F,Y,G,_,U,V,Z,q,K,$,J={298:function(){Array.prototype.includes||(Array.prototype.includes=function(t,e){return this.indexOf(t,e)>-1}),Array.prototype.find||(Array.prototype.find=function(t,e){for(var i=0;i<this.length;i++)if(t.call(e,this[i],i,this))return this[i]}),Object.entries||(Object.entries=function(t){for(var e=Object.keys(t),i=e.length,o=[],r=0;r<i;++r)o.push([e[r],t[e[r]]]);return o}),Object.values||(Object.values=function(t){for(var e=Object.keys(t),i=e.length,o=[],r=0;r<i;++r)o.push(t[e[r]]);return o});var t=window.Element.prototype;"function"!=typeof t.matches&&(t.matches=function(t){for(var e=this.ownerDocument.querySelectorAll(t),i=0;e[i]&&e[i]!==this;)++i;return!!e[i]}),"function"!=typeof t.closest&&(t.closest=function(t){for(var e=this;e&&1===e.nodeType;){if(null==e?void 0:e.matches(t))return e;e=e.parentNode||null}return null}),function(){"undefined"!=typeof window&&!window.CustomEvent&&window.document&&window.Event&&(t.prototype=window.Event.prototype,window.CustomEvent=t);function t(t,e){e=e||{bubbles:!1,cancelable:!1,detail:void 0};var i=window.document.createEvent("CustomEvent");return i.initCustomEvent(t,e.bubbles,e.cancelable,e.detail),i}}()}},Q={};function tt(t){var e=Q[t];if(void 0!==e)return e.exports;var i=Q[t]={exports:{}};return J[t](i,i.exports,tt),i.exports}tt.d=function(t,e){for(var i in e)tt.o(e,i)&&!tt.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},tt.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var te={};tt.d(te,{default:function(){return cS}}),tt(298),(t=I||(I={})).SVG_NS="http://www.w3.org/2000/svg",t.product="Highcharts",t.version="12.2.0",t.win="undefined"!=typeof window?window:{},t.doc=t.win.document,t.svg=!!(null===(i=null===(e=null===t.doc||void 0===t.doc?void 0:t.doc.createElementNS)||void 0===e?void 0:e.call(t.doc,t.SVG_NS,"svg"))||void 0===i?void 0:i.createSVGRect),t.pageLang=null===(r=null===(o=null===t.doc||void 0===t.doc?void 0:t.doc.documentElement)||void 0===o?void 0:o.closest("[lang]"))||void 0===r?void 0:r.lang,t.userAgent=(null===(n=t.win.navigator)||void 0===n?void 0:n.userAgent)||"",t.isChrome=t.win.chrome,t.isFirefox=-1!==t.userAgent.indexOf("Firefox"),t.isMS=/(edge|msie|trident)/i.test(t.userAgent)&&!t.win.opera,t.isSafari=!t.isChrome&&-1!==t.userAgent.indexOf("Safari"),t.isTouchDevice=/(Mobile|Android|Windows Phone)/.test(t.userAgent),t.isWebKit=-1!==t.userAgent.indexOf("AppleWebKit"),t.deg2rad=2*Math.PI/360,t.marginNames=["plotTop","marginRight","marginBottom","plotLeft"],t.noop=function(){},t.supportsPassiveEvents=function(){var e=!1;if(!t.isMS){var i=Object.defineProperty({},"passive",{get:function(){e=!0}});t.win.addEventListener&&t.win.removeEventListener&&(t.win.addEventListener("testPassive",t.noop,i),t.win.removeEventListener("testPassive",t.noop,i))}return e}(),t.charts=[],t.composed=[],t.dateFormats={},t.seriesTypes={},t.symbolSizes={},t.chartCount=0;var ti=I,to=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},tr=ti.charts,tn=ti.doc,ts=ti.win;function ta(t,e,i,o){var r=e?"Highcharts error":"Highcharts warning";32===t&&(t=""+r+": Deprecated member");var n=tf(t),s=n?""+r+" #"+t+": www.highcharts.com/errors/"+t+"/":t.toString();if(void 0!==o){var a="";n&&(s+="?"),tS(o,function(t,e){a+="\n - ".concat(e,": ").concat(t),n&&(s+=encodeURI(e)+"="+encodeURI(t))}),s+=a}tC(ti,"displayError",{chart:i,code:t,message:s,params:o},function(){if(e)throw Error(s);ts.console&&-1===ta.messages.indexOf(s)&&console.warn(s)}),ta.messages.push(s)}function th(t,e){return parseInt(t,e||10)}function tl(t){return"string"==typeof t}function tc(t){var e=Object.prototype.toString.call(t);return"[object Array]"===e||"[object Array Iterator]"===e}function td(t,e){return!!t&&"object"==typeof t&&(!e||!tc(t))}function tp(t){return td(t)&&"number"==typeof t.nodeType}function tu(t){var e=null==t?void 0:t.constructor;return!!(td(t,!0)&&!tp(t)&&(null==e?void 0:e.name)&&"Object"!==e.name)}function tf(t){return"number"==typeof t&&!isNaN(t)&&t<1/0&&t>-1/0}function tg(t){return null!=t}function tv(t,e,i){var o,r=tl(e)&&!tg(i),n=function(e,i){tg(e)?t.setAttribute(i,e):r?(o=t.getAttribute(i))||"class"!==i||(o=t.getAttribute(i+"Name")):t.removeAttribute(i)};return tl(e)?n(i,e):tS(e,n),o}function tm(t){return tc(t)?t:[t]}function ty(t,e){var i;for(i in t||(t={}),e)t[i]=e[i];return t}function tx(){for(var t=arguments,e=t.length,i=0;i<e;i++){var o=t[i];if(null!=o)return o}}function tb(t,e){ty(t.style,e)}function tk(t){return Math.pow(10,Math.floor(Math.log(t)/Math.LN10))}function tw(t,e){return t>1e14?t:parseFloat(t.toPrecision(e||14))}(ta||(ta={})).messages=[],Math.easeInOutSine=function(t){return -.5*(Math.cos(Math.PI*t)-1)};var tM=Array.prototype.find?function(t,e){return t.find(e)}:function(t,e){var i,o=t.length;for(i=0;i<o;i++)if(e(t[i],i))return t[i]};function tS(t,e,i){for(var o in t)Object.hasOwnProperty.call(t,o)&&e.call(i||t[o],t[o],o,t)}function tT(t,e,i){function o(e,i){var o=t.removeEventListener;o&&o.call(t,e,i,!1)}function r(i){var r,n;t.nodeName&&(e?(r={})[e]=!0:r=i,tS(r,function(t,e){if(i[e])for(n=i[e].length;n--;)o(e,i[e][n].fn)}))}var n="function"==typeof t&&t.prototype||t;if(Object.hasOwnProperty.call(n,"hcEvents")){var s=n.hcEvents;if(e){var a=s[e]||[];i?(s[e]=a.filter(function(t){return i!==t.fn}),o(e,i)):(r(s),s[e]=[])}else r(s),delete n.hcEvents}}function tC(t,e,i,o){if(i=i||{},(null==tn?void 0:tn.createEvent)&&(t.dispatchEvent||t.fireEvent&&t!==ti)){var r=tn.createEvent("Events");r.initEvent(e,!0,!0),i=ty(r,i),t.dispatchEvent?t.dispatchEvent(i):t.fireEvent(e,i)}else if(t.hcEvents){i.target||ty(i,{preventDefault:function(){i.defaultPrevented=!0},target:t,type:e});for(var n=[],s=t,a=!1;s.hcEvents;)Object.hasOwnProperty.call(s,"hcEvents")&&s.hcEvents[e]&&(n.length&&(a=!0),n.unshift.apply(n,s.hcEvents[e])),s=Object.getPrototypeOf(s);a&&n.sort(function(t,e){return t.order-e.order}),n.forEach(function(e){!1===e.fn.call(t,i)&&i.preventDefault()})}o&&!i.defaultPrevented&&o.call(t,i)}var tA=(s=Math.random().toString(36).substring(2,9)+"-",a=0,function(){return"highcharts-"+(D?"":s)+a++});ts.jQuery&&(ts.jQuery.fn.highcharts=function(){var t=[].slice.call(arguments);if(this[0])return t[0]?(new ti[tl(t[0])?t.shift():"Chart"](this[0],t[0],t[1]),this):tr[tv(this[0],"data-highcharts-chart")]});var tP={addEvent:function(t,e,i,o){void 0===o&&(o={});var r="function"==typeof t&&t.prototype||t;Object.hasOwnProperty.call(r,"hcEvents")||(r.hcEvents={});var n=r.hcEvents;ti.Point&&t instanceof ti.Point&&t.series&&t.series.chart&&(t.series.chart.runTrackerClick=!0);var s=t.addEventListener;s&&s.call(t,e,i,!!ti.supportsPassiveEvents&&{passive:void 0===o.passive?-1!==e.indexOf("touch"):o.passive,capture:!1}),n[e]||(n[e]=[]);var a={fn:i,order:"number"==typeof o.order?o.order:1/0};return n[e].push(a),n[e].sort(function(t,e){return t.order-e.order}),function(){tT(t,e,i)}},arrayMax:function(t){for(var e=t.length,i=t[0];e--;)t[e]>i&&(i=t[e]);return i},arrayMin:function(t){for(var e=t.length,i=t[0];e--;)t[e]<i&&(i=t[e]);return i},attr:tv,clamp:function(t,e,i){return t>e?t<i?t:i:e},clearTimeout:function(t){tg(t)&&clearTimeout(t)},correctFloat:tw,createElement:function(t,e,i,o,r){var n=tn.createElement(t);return e&&ty(n,e),r&&tb(n,{padding:"0",border:"none",margin:"0"}),i&&tb(n,i),o&&o.appendChild(n),n},crisp:function(t,e,i){void 0===e&&(e=0);var o=e%2/2,r=i?-1:1;return(Math.round(t*r-o)+o)*r},css:tb,defined:tg,destroyObjectProperties:function(t,e,i){tS(t,function(o,r){o!==e&&(null==o?void 0:o.destroy)&&o.destroy(),((null==o?void 0:o.destroy)||!i)&&delete t[r]})},diffObjects:function(t,e,i,o){var r={};return!function t(e,r,n,s){var a=i?r:e;tS(e,function(i,h){if(!s&&o&&o.indexOf(h)>-1&&r[h]){i=tm(i),n[h]=[];for(var l=0;l<Math.max(i.length,r[h].length);l++)r[h][l]&&(void 0===i[l]?n[h][l]=r[h][l]:(n[h][l]={},t(i[l],r[h][l],n[h][l],s+1)))}else td(i,!0)&&!i.nodeType?(n[h]=tc(i)?[]:{},t(i,r[h]||{},n[h],s+1),0===Object.keys(n[h]).length&&("colorAxis"!==h||0!==s)&&delete n[h]):(e[h]!==r[h]||h in e&&!(h in r))&&"__proto__"!==h&&"constructor"!==h&&(n[h]=a[h])})}(t,e,r,0),r},discardElement:function(t){var e;null===(e=null==t?void 0:t.parentElement)||void 0===e||e.removeChild(t)},erase:function(t,e){for(var i=t.length;i--;)if(t[i]===e){t.splice(i,1);break}},error:ta,extend:ty,extendClass:function(t,e){var i=function(){};return i.prototype=new t,ty(i.prototype,e),i},find:tM,fireEvent:tC,getAlignFactor:function(t){return void 0===t&&(t=""),({center:.5,right:1,middle:.5,bottom:1})[t]||0},getClosestDistance:function(t,e){var i,o,r,n,s=!e;return t.forEach(function(t){if(t.length>1)for(n=o=t.length-1;n>0;n--)(r=t[n]-t[n-1])<0&&!s?(null==e||e(),e=void 0):r&&(void 0===i||r<i)&&(i=r)}),i},getMagnitude:tk,getNestedProperty:function(t,e){for(var i=t.split(".");i.length&&tg(e);){var o=i.shift();if(void 0===o||"__proto__"===o)return;if("this"===o){var r=void 0;return td(e)&&(r=e["@this"]),null!=r?r:e}var n=e[o.replace(/[\\'"]/g,"")];if(!tg(n)||"function"==typeof n||"number"==typeof n.nodeType||n===ts)return;e=n}return e},getStyle:function t(e,i,o){if("width"===i){var r,n,s=Math.min(e.offsetWidth,e.scrollWidth),a=null===(r=e.getBoundingClientRect)||void 0===r?void 0:r.call(e).width;return a<s&&a>=s-1&&(s=Math.floor(a)),Math.max(0,s-(t(e,"padding-left",!0)||0)-(t(e,"padding-right",!0)||0))}if("height"===i)return Math.max(0,Math.min(e.offsetHeight,e.scrollHeight)-(t(e,"padding-top",!0)||0)-(t(e,"padding-bottom",!0)||0));var h=ts.getComputedStyle(e,void 0);return h&&(n=h.getPropertyValue(i),tx(o,"opacity"!==i)&&(n=th(n))),n},insertItem:function(t,e){var i,o=t.options.index,r=e.length;for(i=t.options.isInternal?r:0;i<r+1;i++)if(!e[i]||tf(o)&&o<tx(e[i].options.index,e[i]._i)||e[i].options.isInternal){e.splice(i,0,t);break}return i},isArray:tc,isClass:tu,isDOMElement:tp,isFunction:function(t){return"function"==typeof t},isNumber:tf,isObject:td,isString:tl,merge:function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];var o,r=to([t],e,!0),n={},s=function(t,e){return"object"!=typeof t&&(t={}),tS(e,function(i,o){"__proto__"!==o&&"constructor"!==o&&(!td(i,!0)||tu(i)||tp(i)?t[o]=e[o]:t[o]=s(t[o]||{},i))}),t};!0===t&&(n=r[1],r=Array.prototype.slice.call(r,2));var a=r.length;for(o=0;o<a;o++)n=s(n,r[o]);return n},normalizeTickInterval:function(t,e,i,o,r){var n,s=t;i=tx(i,tk(t));var a=t/i;for(!e&&(e=r?[1,1.2,1.5,2,2.5,3,4,5,6,8,10]:[1,2,2.5,5,10],!1===o&&(1===i?e=e.filter(function(t){return t%1==0}):i<=.1&&(e=[1/i]))),n=0;n<e.length&&(s=e[n],(!r||!(s*i>=t))&&(r||!(a<=(e[n]+(e[n+1]||e[n]))/2)));n++);return tw(s*i,-Math.round(Math.log(.001)/Math.LN10))},objectEach:tS,offset:function(t){var e=tn.documentElement,i=t.parentElement||t.parentNode?t.getBoundingClientRect():{top:0,left:0,width:0,height:0};return{top:i.top+(ts.pageYOffset||e.scrollTop)-(e.clientTop||0),left:i.left+(ts.pageXOffset||e.scrollLeft)-(e.clientLeft||0),width:i.width,height:i.height}},pad:function(t,e,i){return Array((e||2)+1-String(t).replace("-","").length).join(i||"0")+t},pick:tx,pInt:th,pushUnique:function(t,e){return 0>t.indexOf(e)&&!!t.push(e)},relativeLength:function(t,e,i){return/%$/.test(t)?e*parseFloat(t)/100+(i||0):parseFloat(t)},removeEvent:tT,replaceNested:function(t){for(var e,i,o=[],r=1;r<arguments.length;r++)o[r-1]=arguments[r];do{e=t;for(var n=0;n<o.length;n++)i=o[n],t=t.replace(i[0],i[1])}while(t!==e);return t},splat:tm,stableSort:function(t,e){var i,o,r=t.length;for(o=0;o<r;o++)t[o].safeI=o;for(t.sort(function(t,o){return 0===(i=e(t,o))?t.safeI-o.safeI:i}),o=0;o<r;o++)delete t[o].safeI},syncTimeout:function(t,e,i){return e>0?setTimeout(t,e,i):(t.call(0,i),-1)},timeUnits:{millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5,week:6048e5,month:24192e5,year:314496e5},ucfirst:function(t){return tl(t)?t.substring(0,1).toUpperCase()+t.substring(1):String(t)},uniqueKey:tA,useSerialIds:function(t){return D=tx(t,D)},wrap:function(t,e,i){var o=t[e];t[e]=function(){var t=arguments,e=this;return i.apply(this,[function(){return o.apply(e,arguments.length?arguments:t)}].concat([].slice.call(arguments)))}}},tO=ti.pageLang,tL=ti.win,tE=tP.defined,tI=tP.error,tD=tP.extend,tB=tP.isNumber,tN=tP.isObject,tz=tP.isString,tR=tP.merge,tW=tP.objectEach,tX=tP.pad,tH=tP.splat,tj=tP.timeUnits,tF=tP.ucfirst,tY=ti.isSafari&&tL.Intl&&!tL.Intl.DateTimeFormat.prototype.formatRange,tG=function(){function t(t,e){this.options={timezone:"UTC"},this.variableTimezone=!1,this.Date=tL.Date,this.update(t),this.lang=e}return t.prototype.update=function(t){var e=this;void 0===t&&(t={}),this.dTLCache={},this.options=t=tR(!0,this.options,t);var i=t.timezoneOffset,o=t.useUTC;this.Date=t.Date||tL.Date||Date;var r=t.timezone;tE(o)&&(r=o?"UTC":void 0),i&&i%60==0&&(r="Etc/GMT"+(i>0?"+":"")+i/60),this.variableTimezone="UTC"!==r&&(null==r?void 0:r.indexOf("Etc/GMT"))!==0,this.timezone=r,["months","shortMonths","weekdays","shortWeekdays"].forEach(function(t){var i=/months/i.test(t),o=/short/.test(t),r={timeZone:"UTC"};r[i?"month":"weekday"]=o?"short":"long",e[t]=(i?[0,1,2,3,4,5,6,7,8,9,10,11]:[3,4,5,6,7,8,9]).map(function(t){return e.dateFormat(r,(i?31:1)*24*36e5*t)})})},t.prototype.toParts=function(t){var e=this.dateTimeFormat({weekday:"narrow",day:"numeric",month:"numeric",year:"numeric",hour:"numeric",minute:"numeric",second:"numeric"},t,"es").split(/(?:, | |\/|:)/g),i=e[0],o=e[1],r=e[2];return[e[3],+r-1,o,e[4],e[5],e[6],Math.floor(Number(t)||0)%1e3,"DLMXJVS".indexOf(i)].map(Number)},t.prototype.dateTimeFormat=function(t,e,i){void 0===i&&(i=this.options.locale||tO);var o,r=JSON.stringify(t)+i;tz(t)&&(t=this.str2dtf(t));var n=this.dTLCache[r];if(!n){null!==(o=t.timeZone)&&void 0!==o||(t.timeZone=this.timezone);try{n=new Intl.DateTimeFormat(i,t)}catch(e){/Invalid time zone/i.test(e.message)?(tI(34),t.timeZone="UTC",n=new Intl.DateTimeFormat(i,t)):tI(e.message,!1)}}return this.dTLCache[r]=n,(null==n?void 0:n.format(e))||""},t.prototype.str2dtf=function(t,e){void 0===e&&(e={});var i={L:{fractionalSecondDigits:3},S:{second:"2-digit"},M:{minute:"numeric"},H:{hour:"2-digit"},k:{hour:"numeric"},E:{weekday:"narrow"},a:{weekday:"short"},A:{weekday:"long"},d:{day:"2-digit"},e:{day:"numeric"},b:{month:"short"},B:{month:"long"},m:{month:"2-digit"},o:{month:"numeric"},y:{year:"2-digit"},Y:{year:"numeric"}};return Object.keys(i).forEach(function(o){-1!==t.indexOf(o)&&tD(e,i[o])}),e},t.prototype.makeTime=function(t,e,i,o,r,n,s){void 0===i&&(i=1),void 0===o&&(o=0);var a=this.Date.UTC(t,e,i,o,r||0,n||0,s||0);if("UTC"!==this.timezone){var h=this.getTimezoneOffset(a);if(a+=h,-1!==[2,3,8,9,10,11].indexOf(e)&&(o<5||o>20)){var l=this.getTimezoneOffset(a);h!==l?a+=l-h:h-36e5!==this.getTimezoneOffset(a-36e5)||tY||(a-=36e5)}}return a},t.prototype.parse=function(t){if(!tz(t))return null!=t?t:void 0;var e=(t=t.replace(/\//g,"-").replace(/(GMT|UTC)/,"")).indexOf("Z")>-1||/([+-][0-9]{2}):?[0-9]{2}$/.test(t),i=/^[0-9]{4}-[0-9]{2}(-[0-9]{2}|)$/.test(t);e||i||(t+="Z");var o=Date.parse(t);if(tB(o))return o+(!e||i?this.getTimezoneOffset(o):0)},t.prototype.getTimezoneOffset=function(t){if("UTC"!==this.timezone){var e=this.dateTimeFormat({timeZoneName:"shortOffset"},t,"en").split(/(GMT|:)/).map(Number),i=(e[0],e[1],e[2]),o=(e[3],e[4]),r=-(36e5*(i+(void 0===o?0:o)/60));if(tB(r))return r}return 0},t.prototype.dateFormat=function(t,e,i){var o,r=this.lang;if(!tE(e)||isNaN(e))return(null==r?void 0:r.invalidDate)||"";if(tz(t=null!=t?t:"%Y-%m-%d %H:%M:%S"))for(var n=/%\[([a-zA-Z]+)\]/g,s=void 0;s=n.exec(t);)t=t.replace(s[0],this.dateTimeFormat(s[1],e,null==r?void 0:r.locale));if(tz(t)&&-1!==t.indexOf("%")){var a=this,h=this.toParts(e),l=h[0],c=h[1],d=h[2],p=h[3],u=h[4],f=h[5],g=h[6],v=h[7],m=(null==r?void 0:r.weekdays)||this.weekdays,y=(null==r?void 0:r.shortWeekdays)||this.shortWeekdays,x=(null==r?void 0:r.months)||this.months,b=(null==r?void 0:r.shortMonths)||this.shortMonths;tW(tD({a:y?y[v]:m[v].substr(0,3),A:m[v],d:tX(d),e:tX(d,2," "),w:v,v:null!==(o=null==r?void 0:r.weekFrom)&&void 0!==o?o:"",b:b[c],B:x[c],m:tX(c+1),o:c+1,y:l.toString().substr(2,2),Y:l,H:tX(p),k:p,I:tX(p%12||12),l:p%12||12,M:tX(u),p:p<12?"AM":"PM",P:p<12?"am":"pm",S:tX(f),L:tX(g,3)},ti.dateFormats),function(i,o){if(tz(t))for(;-1!==t.indexOf("%"+o);)t=t.replace("%"+o,"function"==typeof i?i.call(a,e):i)})}else if(tN(t)){var k=(this.getTimezoneOffset(e)||0)/36e5,w=this.timezone||"Etc/GMT"+(k>=0?"+":"")+k,M=t.prefix,S=t.suffix;t=(void 0===M?"":M)+this.dateTimeFormat(tD({timeZone:w},t),e)+(void 0===S?"":S)}return i?tF(t):t},t.prototype.resolveDTLFormat=function(t){return tN(t,!0)?tN(t,!0)&&void 0===t.main?{main:t}:t:{main:(t=tH(t))[0],from:t[1],to:t[2]}},t.prototype.getDateFormat=function(t,e,i,o){var r=this.dateFormat("%m-%d %H:%M:%S.%L",e),n="01-01 00:00:00.000",s={millisecond:15,second:12,minute:9,hour:6,day:3},a="millisecond",h=a;for(a in tj){if(t&&t===tj.week&&+this.dateFormat("%w",e)===i&&r.substr(6)===n.substr(6)){a="week";break}if(t&&tj[a]>t){a=h;break}if(s[a]&&r.substr(s[a])!==n.substr(s[a]))break;"week"!==a&&(h=a)}return this.resolveDTLFormat(o[a]).main},t}(),t_=(h=function(t,e){return(h=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}h(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),tU=tP.defined,tV=tP.extend,tZ=tP.timeUnits,tq=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return t_(e,t),e.prototype.getTimeTicks=function(t,e,i,o){var r,n=this,s=[],a={},h=t.count,l=void 0===h?1:h,c=t.unitRange,d=n.toParts(e),p=d[0],u=d[1],f=d[2],g=d[3],v=d[4],m=d[5],y=(e||0)%1e3;if(null!=o||(o=1),tU(e)){if(y=c>=tZ.second?0:l*Math.floor(y/l),c>=tZ.second&&(m=c>=tZ.minute?0:l*Math.floor(m/l)),c>=tZ.minute&&(v=c>=tZ.hour?0:l*Math.floor(v/l)),c>=tZ.hour&&(g=c>=tZ.day?0:l*Math.floor(g/l)),c>=tZ.day&&(f=c>=tZ.month?1:Math.max(1,l*Math.floor(f/l))),c>=tZ.month&&(u=c>=tZ.year?0:l*Math.floor(u/l)),c>=tZ.year&&(p-=p%l),c===tZ.week){l&&(e=n.makeTime(p,u,f,g,v,m,y));var x=this.dateTimeFormat({timeZone:this.timezone,weekday:"narrow"},e,"es"),b="DLMXJVS".indexOf(x);f+=-b+o+(b<o?-7:0)}e=n.makeTime(p,u,f,g,v,m,y),n.variableTimezone&&tU(i)&&(r=i-e>4*tZ.month||n.getTimezoneOffset(e)!==n.getTimezoneOffset(i));for(var k=e,w=1;k<i;)s.push(k),c===tZ.year?k=n.makeTime(p+w*l,0):c===tZ.month?k=n.makeTime(p,u+w*l):r&&(c===tZ.day||c===tZ.week)?k=n.makeTime(p,u,f+w*l*(c===tZ.day?1:7)):r&&c===tZ.hour&&l>1?k=n.makeTime(p,u,f,g+w*l):k+=c*l,w++;s.push(k),c<=tZ.hour&&s.length<1e4&&s.forEach(function(t){t%18e5==0&&"000000000"===n.dateFormat("%H%M%S%L",t)&&(a[t]="day")})}return s.info=tV(t,{higherRanks:a,totalRange:c*l}),s},e}(tG),tK=function(){return(tK=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},t$=ti.isTouchDevice,tJ=tP.fireEvent,tQ=tP.merge,t0={colors:["#2caffe","#544fc5","#00e272","#fe6a35","#6b8abc","#d568fb","#2ee0ca","#fa4b42","#feb56a","#91e8e1"],symbols:["circle","diamond","square","triangle","triangle-down"],lang:{weekFrom:"week from",chartTitle:"Chart title",locale:void 0,loading:"Loading...",months:void 0,seriesName:"Series {add index 1}",shortMonths:void 0,weekdays:void 0,numericSymbols:["k","M","G","T","P","E"],pieSliceName:"Slice",resetZoom:"Reset zoom",yAxisTitle:"Values",resetZoomTitle:"Reset zoom level 1:1"},global:{buttonTheme:{fill:"#f7f7f7",padding:8,r:2,stroke:"#cccccc","stroke-width":1,style:{color:"#333333",cursor:"pointer",fontSize:"0.8em",fontWeight:"normal"},states:{hover:{fill:"#e6e6e6"},select:{fill:"#e6e9ff",style:{color:"#000000",fontWeight:"bold"}},disabled:{style:{color:"#cccccc"}}}}},time:{Date:void 0,timezone:"UTC",timezoneOffset:0,useUTC:void 0},chart:{alignThresholds:!1,panning:{enabled:!1,type:"x"},styledMode:!1,borderRadius:0,colorCount:10,allowMutatingData:!0,ignoreHiddenSeries:!0,spacing:[10,10,15,10],resetZoomButton:{theme:{},position:{}},reflow:!0,type:"line",zooming:{singleTouch:!1,resetButton:{theme:{zIndex:6},position:{align:"right",x:-10,y:10}}},width:null,height:null,borderColor:"#334eff",backgroundColor:"#ffffff",plotBorderColor:"#cccccc"},title:{style:{color:"#333333",fontWeight:"bold"},text:"Chart title",margin:15,minScale:.67},subtitle:{style:{color:"#666666",fontSize:"0.8em"},text:""},caption:{margin:15,style:{color:"#666666",fontSize:"0.8em"},text:"",align:"left",verticalAlign:"bottom"},plotOptions:{},legend:{enabled:!0,align:"center",alignColumns:!0,className:"highcharts-no-tooltip",events:{},layout:"horizontal",itemMarginBottom:2,itemMarginTop:2,labelFormatter:function(){return this.name},borderColor:"#999999",borderRadius:0,navigation:{style:{fontSize:"0.8em"},activeColor:"#0022ff",inactiveColor:"#cccccc"},itemStyle:{color:"#333333",cursor:"pointer",fontSize:"0.8em",textDecoration:"none",textOverflow:"ellipsis"},itemHoverStyle:{color:"#000000"},itemHiddenStyle:{color:"#666666",textDecoration:"line-through"},shadow:!1,itemCheckboxStyle:{position:"absolute",width:"13px",height:"13px"},squareSymbol:!0,symbolPadding:5,verticalAlign:"bottom",x:0,y:0,title:{style:{fontSize:"0.8em",fontWeight:"bold"}}},loading:{labelStyle:{fontWeight:"bold",position:"relative",top:"45%"},style:{position:"absolute",backgroundColor:"#ffffff",opacity:.5,textAlign:"center"}},tooltip:{enabled:!0,animation:{duration:300,easing:function(t){return Math.sqrt(1-Math.pow(t-1,2))}},borderRadius:3,dateTimeLabelFormats:{millisecond:"%[AebHMSL]",second:"%[AebHMS]",minute:"%[AebHM]",hour:"%[AebHM]",day:"%[AebY]",week:"%v %[AebY]",month:"%[BY]",year:"%Y"},footerFormat:"",headerShape:"callout",hideDelay:500,padding:8,position:{x:0,y:3},shared:!1,snap:t$?25:10,headerFormat:'<span style="font-size: 0.8em">{ucfirst point.key}</span><br/>',pointFormat:'<span style="color:{point.color}">●</span> {series.name}: <b>{point.y}</b><br/>',backgroundColor:"#ffffff",borderWidth:void 0,stickOnContact:!1,style:{color:"#333333",cursor:"default",fontSize:"0.8em"},useHTML:!1},credits:{enabled:!0,href:"https://www.highcharts.com?credits",position:{align:"right",x:-10,verticalAlign:"bottom",y:-5},style:{cursor:"pointer",color:"#999999",fontSize:"0.6em"},text:"Highcharts.com"}},t1=new tq(t0.time,t0.lang),t2=ti.win,t3=tP.isNumber,t5=tP.isString,t6=tP.merge,t9=tP.pInt,t4=tP.defined,t8=function(t,e,i){return"color-mix(in srgb,".concat(t,",").concat(e," ").concat(100*i,"%)")},t7=function(t){return t5(t)&&!!t&&"none"!==t},et=function(){var t;function e(t){this.rgba=[NaN,NaN,NaN,NaN],this.input=t;var i,o,r,n,s=ti.Color;if(s&&s!==e)return new s(t);if("object"==typeof t&&void 0!==t.stops)this.stops=t.stops.map(function(t){return new e(t[1])});else if("string"==typeof t)for(this.input=t=e.names[t.toLowerCase()]||t,r=e.parsers.length;r--&&!o;)(i=(n=e.parsers[r]).regex.exec(t))&&(o=n.parse(i));o&&(this.rgba=o)}return e.parse=function(t){return t?new e(t):e.None},e.prototype.get=function(t){var e=this.input,i=this.rgba;if(this.output)return this.output;if("object"==typeof e&&void 0!==this.stops){var o=t6(e);return o.stops=[].slice.call(o.stops),this.stops.forEach(function(e,i){o.stops[i]=[o.stops[i][0],e.get(t)]}),o}return i&&t3(i[0])?"rgb"!==t&&(t||1!==i[3])?"a"===t?"".concat(i[3]):"rgba("+i.join(",")+")":"rgb("+i[0]+","+i[1]+","+i[2]+")":e},e.prototype.brighten=function(t){var i=this.rgba;if(this.stops)this.stops.forEach(function(e){e.brighten(t)});else if(t3(t)&&0!==t){if(t3(i[0]))for(var o=0;o<3;o++)i[o]+=t9(255*t),i[o]<0&&(i[o]=0),i[o]>255&&(i[o]=255);else e.useColorMix&&t7(this.input)&&(this.output=t8(this.input,t>0?"white":"black",Math.abs(t)))}return this},e.prototype.setOpacity=function(t){return this.rgba[3]=t,this},e.prototype.tweenTo=function(t,i){var o=this.rgba,r=t.rgba;if(!t3(o[0])||!t3(r[0]))return e.useColorMix&&t7(this.input)&&t7(t.input)&&i<.99?t8(this.input,t.input,i):t.input||"none";var n=1!==r[3]||1!==o[3],s=function(t,e){return t+(o[e]-t)*(1-i)},a=r.slice(0,3).map(s).map(Math.round);return n&&a.push(s(r[3],3)),(n?"rgba(":"rgb(")+a.join(",")+")"},e.names={white:"#ffffff",black:"#000000"},e.parsers=[{regex:/rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d?(?:\.\d+)?)\s*\)/,parse:function(t){return[t9(t[1]),t9(t[2]),t9(t[3]),parseFloat(t[4],10)]}},{regex:/rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)/,parse:function(t){return[t9(t[1]),t9(t[2]),t9(t[3]),1]}},{regex:/^#([a-f0-9])([a-f0-9])([a-f0-9])([a-f0-9])?$/i,parse:function(t){return[t9(t[1]+t[1],16),t9(t[2]+t[2],16),t9(t[3]+t[3],16),t4(t[4])?t9(t[4]+t[4],16)/255:1]}},{regex:/^#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})?$/i,parse:function(t){return[t9(t[1],16),t9(t[2],16),t9(t[3],16),t4(t[4])?t9(t[4],16)/255:1]}}],e.useColorMix=null===(t=t2.CSS)||void 0===t?void 0:t.supports("color","color-mix(in srgb,red,blue 9%)"),e.None=new e(""),e}(),ee=et.parse,ei=ti.win,eo=tP.isNumber,er=tP.objectEach,en=function(){function t(t,e,i){this.pos=NaN,this.options=e,this.elem=t,this.prop=i}return t.prototype.dSetter=function(){var t=this.paths,e=null==t?void 0:t[0],i=null==t?void 0:t[1],o=this.now||0,r=[];if(1!==o&&e&&i){if(e.length===i.length&&o<1)for(var n=0;n<i.length;n++){for(var s=e[n],a=i[n],h=[],l=0;l<a.length;l++){var c=s[l],d=a[l];eo(c)&&eo(d)&&("A"!==a[0]||4!==l&&5!==l)?h[l]=c+o*(d-c):h[l]=d}r.push(h)}else r=i}else r=this.toD||[];this.elem.attr("d",r,void 0,!0)},t.prototype.update=function(){var t=this.elem,e=this.prop,i=this.now,o=this.options.step;this[e+"Setter"]?this[e+"Setter"]():t.attr?t.element&&t.attr(e,i,null,!0):t.style[e]=i+this.unit,o&&o.call(t,i,this)},t.prototype.run=function(e,i,o){var r=this,n=r.options,s=function(t){return!s.stopped&&r.step(t)},a=ei.requestAnimationFrame||function(t){setTimeout(t,13)},h=function(){for(var e=0;e<t.timers.length;e++)t.timers[e]()||t.timers.splice(e--,1);t.timers.length&&a(h)};e!==i||this.elem["forceAnimate:"+this.prop]?(this.startTime=+new Date,this.start=e,this.end=i,this.unit=o,this.now=this.start,this.pos=0,s.elem=this.elem,s.prop=this.prop,s()&&1===t.timers.push(s)&&a(h)):(delete n.curAnim[this.prop],n.complete&&0===Object.keys(n.curAnim).length&&n.complete.call(this.elem))},t.prototype.step=function(t){var e,i,o=+new Date,r=this.options,n=this.elem,s=r.complete,a=r.duration,h=r.curAnim;return n.attr&&!n.element?e=!1:t||o>=a+this.startTime?(this.now=this.end,this.pos=1,this.update(),h[this.prop]=!0,i=!0,er(h,function(t){!0!==t&&(i=!1)}),i&&s&&s.call(n),e=!1):(this.pos=r.easing((o-this.startTime)/a),this.now=this.start+(this.end-this.start)*this.pos,this.update(),e=!0),e},t.prototype.initPath=function(t,e,i){var o,r,n,s,a=t.startX,h=t.endX,l=i.slice(),c=t.isArea,d=c?2:1,p=e&&i.length>e.length&&i.hasStackedCliffs,u=null==e?void 0:e.slice();if(!u||p)return[l,l];function f(t,e){for(;t.length<r;){var i=t[0],o=e[r-t.length];if(o&&"M"===i[0]&&("C"===o[0]?t[0]=["C",i[1],i[2],i[1],i[2],i[1],i[2]]:t[0]=["L",i[1],i[2]]),t.unshift(i),c){var n=t.pop();t.push(t[t.length-1],n)}}}function g(t){for(;t.length<r;){var e=t[Math.floor(t.length/d)-1].slice();if("C"===e[0]&&(e[1]=e[5],e[2]=e[6]),c){var i=t[Math.floor(t.length/d)].slice();t.splice(t.length/2,0,e,i)}else t.push(e)}}if(a&&h&&h.length){for(n=0;n<a.length;n++){if(a[n]===h[0]){o=n;break}if(a[0]===h[h.length-a.length+n]){o=n,s=!0;break}if(a[a.length-1]===h[h.length-a.length+n]){o=a.length-n;break}}void 0===o&&(u=[])}return u.length&&eo(o)&&(r=l.length+o*d,s?(f(u,l),g(l)):(f(l,u),g(u))),[u,l]},t.prototype.fillSetter=function(){t.prototype.strokeSetter.apply(this,arguments)},t.prototype.strokeSetter=function(){this.elem.attr(this.prop,ee(this.start).tweenTo(ee(this.end),this.pos),void 0,!0)},t.timers=[],t}(),es=tP.defined,ea=tP.getStyle,eh=tP.isArray,el=tP.isNumber,ec=tP.isObject,ed=tP.merge,ep=tP.objectEach,eu=tP.pick;function ef(t){return ec(t)?ed({duration:500,defer:0},t):{duration:500*!!t,defer:0}}function eg(t,e){for(var i=en.timers.length;i--;)en.timers[i].elem!==t||e&&e!==en.timers[i].prop||(en.timers[i].stopped=!0)}var ev=function(t,e,i){var o,r,n,s,a="";ec(i)||(s=arguments,i={duration:s[2],easing:s[3],complete:s[4]}),el(i.duration)||(i.duration=400),i.easing="function"==typeof i.easing?i.easing:Math[i.easing]||Math.easeInOutSine,i.curAnim=ed(e),ep(e,function(s,h){eg(t,h),n=new en(t,i,h),r=void 0,"d"===h&&eh(e.d)?(n.paths=n.initPath(t,t.pathArray,e.d),n.toD=e.d,o=0,r=1):t.attr?o=t.attr(h):(o=parseFloat(ea(t,h))||0,"opacity"!==h&&(a="px")),r||(r=s),"string"==typeof r&&r.match("px")&&(r=r.replace(/px/g,"")),n.run(o,r,a)})},em=function(t,e,i){var o=ef(e),r=i?[i]:t.series,n=0,s=0;return r.forEach(function(t){var i=ef(t.options.animation);n=ec(e)&&es(e.defer)?o.defer:Math.max(n,i.duration+i.defer),s=Math.min(o.duration,i.duration)}),t.renderer.forExport&&(n=0),{defer:Math.max(0,n-s),duration:Math.min(n,s)}},ey=function(t,e){e.renderer.globalAnimation=eu(t,e.options.chart.animation,!0)},ex=ti.SVG_NS,eb=ti.win,ek=tP.attr,ew=tP.createElement,eM=tP.css,eS=tP.error,eT=tP.isFunction,eC=tP.isString,eA=tP.objectEach,eP=tP.splat,eO=eb.trustedTypes,eL=eO&&eT(eO.createPolicy)&&eO.createPolicy("highcharts",{createHTML:function(t){return t}}),eE=eL?eL.createHTML(""):"",eI=function(){function t(t){this.nodes="string"==typeof t?this.parseMarkup(t):t}return t.filterUserAttributes=function(e){return eA(e,function(i,o){var r=!0;-1===t.allowedAttributes.indexOf(o)&&(r=!1),-1!==["background","dynsrc","href","lowsrc","src"].indexOf(o)&&(r=eC(i)&&t.allowedReferences.some(function(t){return 0===i.indexOf(t)})),r||(eS(33,!1,void 0,{"Invalid attribute in config":"".concat(o)}),delete e[o]),eC(i)&&e[o]&&(e[o]=i.replace(/</g,"&lt;"))}),e},t.parseStyle=function(t){return t.split(";").reduce(function(t,e){var i=e.split(":").map(function(t){return t.trim()}),o=i.shift();return o&&i.length&&(t[o.replace(/-([a-z])/g,function(t){return t[1].toUpperCase()})]=i.join(":")),t},{})},t.setElementHTML=function(e,i){e.innerHTML=t.emptyHTML,i&&new t(i).addToDOM(e)},t.prototype.addToDOM=function(e){return function e(i,o){var r;return eP(i).forEach(function(i){var n,s=i.tagName,a=i.textContent?ti.doc.createTextNode(i.textContent):void 0,h=t.bypassHTMLFiltering;if(s){if("#text"===s)n=a;else if(-1!==t.allowedTags.indexOf(s)||h){var l="svg"===s?ex:o.namespaceURI||ex,c=ti.doc.createElementNS(l,s),d=i.attributes||{};eA(i,function(t,e){"tagName"!==e&&"attributes"!==e&&"children"!==e&&"style"!==e&&"textContent"!==e&&(d[e]=t)}),ek(c,h?d:t.filterUserAttributes(d)),i.style&&eM(c,i.style),a&&c.appendChild(a),e(i.children||[],c),n=c}else eS(33,!1,void 0,{"Invalid tagName in config":s})}n&&o.appendChild(n),r=n}),r}(this.nodes,e)},t.prototype.parseMarkup=function(e){var i,o=[];e=e.trim().replace(/ style=(["'])/g," data-style=$1");try{i=new DOMParser().parseFromString(eL?eL.createHTML(e):e,"text/html")}catch(t){}if(!i){var r=ew("div");r.innerHTML=e,i={body:r}}var n=function(e,i){var o=e.nodeName.toLowerCase(),r={tagName:o};"#text"===o&&(r.textContent=e.textContent||"");var s=e.attributes;if(s){var a={};[].forEach.call(s,function(e){"data-style"===e.name?r.style=t.parseStyle(e.value):a[e.name]=e.value}),r.attributes=a}if(e.childNodes.length){var h=[];[].forEach.call(e.childNodes,function(t){n(t,h)}),h.length&&(r.children=h)}i.push(r)};return[].forEach.call(i.body.childNodes,function(t){return n(t,o)}),o},t.allowedAttributes=["alt","aria-controls","aria-describedby","aria-expanded","aria-haspopup","aria-hidden","aria-label","aria-labelledby","aria-live","aria-pressed","aria-readonly","aria-roledescription","aria-selected","class","clip-path","color","colspan","cx","cy","d","dx","dy","disabled","fill","filterUnits","flood-color","flood-opacity","height","href","id","in","in2","markerHeight","markerWidth","offset","opacity","operator","orient","padding","paddingLeft","paddingRight","patternUnits","r","radius","refX","refY","role","scope","slope","src","startOffset","stdDeviation","stroke","stroke-linecap","stroke-width","style","tableValues","result","rowspan","summary","target","tabindex","text-align","text-anchor","textAnchor","textLength","title","type","valign","width","x","x1","x2","xlink:href","y","y1","y2","zIndex"],t.allowedReferences=["https://","http://","mailto:","/","../","./","#"],t.allowedTags=["a","abbr","b","br","button","caption","circle","clipPath","code","dd","defs","div","dl","dt","em","feComponentTransfer","feComposite","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feMorphology","feOffset","feMerge","feMergeNode","filter","h1","h2","h3","h4","h5","h6","hr","i","img","li","linearGradient","marker","ol","p","path","pattern","pre","rect","small","span","stop","strong","style","sub","sup","svg","table","text","textPath","thead","title","tbody","tspan","td","th","tr","u","ul","#text"],t.emptyHTML=eE,t.bypassHTMLFiltering=!1,t}(),eD=ti.pageLang,eB=tP.extend,eN=tP.getNestedProperty,ez=tP.isArray,eR=tP.isNumber,eW=tP.isObject,eX=tP.isString,eH=tP.pick,ej={add:function(t,e){return t+e},divide:function(t,e){return 0!==e?t/e:""},eq:function(t,e){return t==e},each:function(t){var e=arguments[arguments.length-1];return!!ez(t)&&t.map(function(i,o){return eG(e.body,eB(eW(i)?i:{"@this":i},{"@index":o,"@first":0===o,"@last":o===t.length-1}))}).join("")},ge:function(t,e){return t>=e},gt:function(t,e){return t>e},if:function(t){return!!t},le:function(t,e){return t<=e},lt:function(t,e){return t<e},multiply:function(t,e){return t*e},ne:function(t,e){return t!=e},subtract:function(t,e){return t-e},ucfirst:tP.ucfirst,unless:function(t){return!t}},eF={},eY=function(t){return/^["'].+["']$/.test(t)};function eG(t,e,i){void 0===t&&(t="");for(var o,r,n,s,a=/\{([a-zA-Z\u00C0-\u017F\d:\.,;\-\/<>\[\]%_@+"'’= #\(\)]+)\}/g,h=/\(([a-zA-Z\u00C0-\u017F\d:\.,;\-\/<>\[\]%_@+"'= ]+)\)/g,l=[],c=/f$/,d=/\.(\d)/,p=(null===(o=null==i?void 0:i.options)||void 0===o?void 0:o.lang)||t0.lang,u=(null==i?void 0:i.time)||t1,f=(null==i?void 0:i.numberFormatter)||e_,g=function(t){var i;return void 0===t&&(t=""),"true"===t||"false"!==t&&((i=Number(t)).toString()===t?i:eY(t)?t.slice(1,-1):eN(t,e))},v=0;null!==(r=a.exec(t));){var m=r,y=h.exec(r[1]);y&&(r=y,s=!0),(null==n?void 0:n.isBlock)||(n={ctx:e,expression:r[1],find:r[0],isBlock:"#"===r[1].charAt(0),start:r.index,startInner:r.index+r[0].length,length:r[0].length});var x=(n.isBlock?m:r)[1].split(" ")[0].replace("#","");ej[x]&&(n.isBlock&&x===n.fn&&v++,n.fn||(n.fn=x));var b="else"===r[1];if(n.isBlock&&n.fn&&(r[1]==="/".concat(n.fn)||b)){if(v)!b&&v--;else{var k=n.startInner,w=t.substr(k,r.index-k);void 0===n.body?(n.body=w,n.startInner=r.index+r[0].length):n.elseBody=w,n.find+=w+r[0],b||(l.push(n),n=void 0)}}else n.isBlock||l.push(n);if(y&&!(null==n?void 0:n.isBlock))break}return l.forEach(function(o){var r,n,s=o.body,a=o.elseBody,l=o.expression,v=o.fn;if(v){var m=[o],y=[],x=l.length,b=0,k=void 0;for(n=0;n<=x;n++){var w=l.charAt(n);k||'"'!==w&&"'"!==w?k===w&&(k=""):k=w,k||" "!==w&&n!==x||(y.push(l.substr(b,n-b)),b=n+1)}for(n=ej[v].length;n--;)m.unshift(g(y[n+1]));r=ej[v].apply(e,m),o.isBlock&&"boolean"==typeof r&&(r=eG(r?s:a,e,i))}else{var M=eY(l)?[l]:l.split(":");if(r=g(M.shift()||""),M.length&&"number"==typeof r){var S=M.join(":");if(c.test(S)){var T=parseInt((S.match(d)||["","-1"])[1],10);null!==r&&(r=f(r,T,p.decimalPoint,S.indexOf(",")>-1?p.thousandsSep:""))}else r=u.dateFormat(S,r)}h.lastIndex=0,h.test(o.find)&&eX(r)&&(r='"'.concat(r,'"'))}t=t.replace(o.find,eH(r,""))}),s?eG(t,e,i):t}function e_(t,e,i,o){e*=1;var r,n,s,a,h=(t=+t||0).toString().split("e").map(Number),l=h[0],c=h[1],d=(null===(r=this===null||void 0===this?void 0:this.options)||void 0===r?void 0:r.lang)||t0.lang,p=(t.toString().split(".")[1]||"").split("e")[0].length,u=e,f={};null!=i||(i=d.decimalPoint),null!=o||(o=d.thousandsSep),-1===e?e=Math.min(p,20):eR(e)?e&&c<0&&((a=e+c)>=0?(l=+l.toExponential(a).split("e")[0],e=a):(l=Math.floor(l),t=e<20?+(l*Math.pow(10,c)).toFixed(e):0,c=0)):e=2,c&&(null!=e||(e=2),t=l),eR(e)&&e>=0&&(f.minimumFractionDigits=e,f.maximumFractionDigits=e),""===o&&(f.useGrouping=!1);var g=o||i,v=g?"en":(this===null||void 0===this?void 0:this.locale)||d.locale||eD,m=JSON.stringify(f)+v;return s=(null!==(n=eF[m])&&void 0!==n?n:eF[m]=new Intl.NumberFormat(v,f)).format(t),g&&(s=s.replace(/([,\.])/g,"_$1").replace(/_\,/g,null!=o?o:",").replace("_.",null!=i?i:".")),(e||0!=+s)&&(!(c<0)||u)||(s="0"),c&&0!=+s&&(s+="e"+(c<0?"":"+")+c),s}var eU={dateFormat:function(t,e,i){return t1.dateFormat(t,e,i)},format:eG,helpers:ej,numberFormat:e_};(l=B||(B={})).rendererTypes={},l.getRendererType=function(t){return void 0===t&&(t=c),l.rendererTypes[t]||l.rendererTypes[c]},l.registerRendererType=function(t,e,i){l.rendererTypes[t]=e,(!c||i)&&(c=t,ti.Renderer=e)};var eV=B,eZ=tP.clamp,eq=tP.pick,eK=tP.pushUnique,e$=tP.stableSort;(N||(N={})).distribute=function t(e,i,o){var r,n,s,a,h,l,c=e,d=c.reducedLen||i,p=function(t,e){return t.target-e.target},u=[],f=e.length,g=[],v=u.push,m=!0,y=0;for(r=f;r--;)y+=e[r].size;if(y>d){for(e$(e,function(t,e){return(e.rank||0)-(t.rank||0)}),s=(l=e[0].rank===e[e.length-1].rank)?f/2:-1,n=l?s:f-1;s&&y>d;)a=e[r=Math.floor(n)],eK(g,r)&&(y-=a.size),n+=s,l&&n>=e.length&&(s/=2,n=s);g.sort(function(t,e){return e-t}).forEach(function(t){return v.apply(u,e.splice(t,1))})}for(e$(e,p),e=e.map(function(t){return{size:t.size,targets:[t.target],align:eq(t.align,.5)}});m;){for(r=e.length;r--;)a=e[r],h=(Math.min.apply(0,a.targets)+Math.max.apply(0,a.targets))/2,a.pos=eZ(h-a.size*a.align,0,i-a.size);for(r=e.length,m=!1;r--;)r>0&&e[r-1].pos+e[r-1].size>e[r].pos&&(e[r-1].size+=e[r].size,e[r-1].targets=e[r-1].targets.concat(e[r].targets),e[r-1].align=.5,e[r-1].pos+e[r-1].size>i&&(e[r-1].pos=i-e[r-1].size),e.splice(r,1),m=!0)}return v.apply(c,u),r=0,e.some(function(e){var n=0;return(e.targets||[]).some(function(){return(c[r].pos=e.pos+n,void 0!==o&&Math.abs(c[r].pos-c[r].target)>o)?(c.slice(0,r+1).forEach(function(t){return delete t.pos}),c.reducedLen=(c.reducedLen||i)-.1*i,c.reducedLen>.1*i&&t(c,i,o),!0):(n+=c[r].size,r++,!1)})}),e$(c,p),c};var eJ=N,eQ=ti.deg2rad,e0=ti.doc,e1=ti.svg,e2=ti.SVG_NS,e3=ti.win,e5=ti.isFirefox,e6=tP.addEvent,e9=tP.attr,e4=tP.createElement,e8=tP.crisp,e7=tP.css,it=tP.defined,ie=tP.erase,ii=tP.extend,io=tP.fireEvent,ir=tP.getAlignFactor,is=tP.isArray,ia=tP.isFunction,ih=tP.isNumber,il=tP.isObject,ic=tP.isString,id=tP.merge,ip=tP.objectEach,iu=tP.pick,ig=tP.pInt,iv=tP.pushUnique,im=tP.replaceNested,iy=tP.syncTimeout,ix=tP.uniqueKey,ib=function(){function t(t,e){this.onEvents={},this.opacity=1,this.SVG_NS=e2,this.element="span"===e||"body"===e?e4(e):e0.createElementNS(this.SVG_NS,e),this.renderer=t,this.styles={},io(this,"afterInit")}return t.prototype._defaultGetter=function(t){var e=iu(this[t+"Value"],this[t],this.element?this.element.getAttribute(t):null,0);return/^-?[\d\.]+$/.test(e)&&(e=parseFloat(e)),e},t.prototype._defaultSetter=function(t,e,i){i.setAttribute(e,t)},t.prototype.add=function(t){var e,i=this.renderer,o=this.element;return t&&(this.parentGroup=t),void 0!==this.textStr&&"text"===this.element.nodeName&&i.buildText(this),this.added=!0,(!t||t.handleZ||this.zIndex)&&(e=this.zIndexSetter()),e||(t?t.element:i.box).appendChild(o),this.onAdd&&this.onAdd(),this},t.prototype.addClass=function(t,e){var i=e?"":this.attr("class")||"";return(t=(t||"").split(/ /g).reduce(function(t,e){return -1===i.indexOf(e)&&t.push(e),t},i?[i]:[]).join(" "))!==i&&this.attr("class",t),this},t.prototype.afterSetters=function(){this.doTransform&&(this.updateTransform(),this.doTransform=!1)},t.prototype.align=function(t,e,i,o){void 0===o&&(o=!0);var r=this.renderer,n=r.alignedObjects,s=!!t;t?(this.alignOptions=t,this.alignByTranslate=e,this.alignTo=i):(t=this.alignOptions||{},e=this.alignByTranslate,i=this.alignTo);var a=!i||ic(i)?i||"renderer":void 0;a&&(s&&iv(n,this),i=void 0);var h=iu(i,r[a],r),l=(h.x||0)+(t.x||0)+((h.width||0)-(t.width||0))*ir(t.align),c=(h.y||0)+(t.y||0)+((h.height||0)-(t.height||0))*ir(t.verticalAlign),d={"text-align":null==t?void 0:t.align};return d[e?"translateX":"x"]=Math.round(l),d[e?"translateY":"y"]=Math.round(c),o&&(this[this.placed?"animate":"attr"](d),this.placed=!0),this.alignAttr=d,this},t.prototype.alignSetter=function(t){var e={left:"start",center:"middle",right:"end"};e[t]&&(this.alignValue=t,this.element.setAttribute("text-anchor",e[t]))},t.prototype.animate=function(t,e,i){var o=this,r=ef(iu(e,this.renderer.globalAnimation,!0)),n=r.defer;return e0.hidden&&(r.duration=0),0!==r.duration?(i&&(r.complete=i),iy(function(){o.element&&ev(o,t,r)},n)):(this.attr(t,void 0,i||r.complete),ip(t,function(t,e){r.step&&r.step.call(this,t,{prop:e,pos:1,elem:this})},this)),this},t.prototype.applyTextOutline=function(t){var e=this.element;-1!==t.indexOf("contrast")&&(t=t.replace(/contrast/g,this.renderer.getContrast(e.style.fill)));var i=t.indexOf(" "),o=t.substring(i+1),r=t.substring(0,i);if(r&&"none"!==r&&ti.svg){this.fakeTS=!0,r=r.replace(/(^[\d\.]+)(.*?)$/g,function(t,e,i){return 2*Number(e)+i}),this.removeTextOutline();var n=e0.createElementNS(e2,"tspan");e9(n,{class:"highcharts-text-outline",fill:o,stroke:o,"stroke-width":r,"stroke-linejoin":"round"});var s=e.querySelector("textPath")||e;[].forEach.call(s.childNodes,function(t){var e=t.cloneNode(!0);e.removeAttribute&&["fill","stroke","stroke-width","stroke"].forEach(function(t){return e.removeAttribute(t)}),n.appendChild(e)});var a=0;[].forEach.call(s.querySelectorAll("text tspan"),function(t){a+=Number(t.getAttribute("dy"))});var h=e0.createElementNS(e2,"tspan");h.textContent="​",e9(h,{x:Number(e.getAttribute("x")),dy:-a}),n.appendChild(h),s.insertBefore(n,s.firstChild)}},t.prototype.attr=function(e,i,o,r){var n,s,a,h=this.element,l=t.symbolCustomAttribs,c=this;return"string"==typeof e&&void 0!==i&&(n=e,(e={})[n]=i),"string"==typeof e?c=(this[e+"Getter"]||this._defaultGetter).call(this,e,h):(ip(e,function(t,i){a=!1,r||eg(this,i),this.symbolName&&-1!==l.indexOf(i)&&(s||(this.symbolAttr(e),s=!0),a=!0),this.rotation&&("x"===i||"y"===i)&&(this.doTransform=!0),a||(this[i+"Setter"]||this._defaultSetter).call(this,t,i,h)},this),this.afterSetters()),o&&o.call(this),c},t.prototype.clip=function(t){if(t&&!t.clipPath){var e=ix()+"-",i=this.renderer.createElement("clipPath").attr({id:e}).add(this.renderer.defs);ii(t,{clipPath:i,id:e,count:0}),t.add(i)}return this.attr("clip-path",t?"url(".concat(this.renderer.url,"#").concat(t.id,")"):"none")},t.prototype.crisp=function(t,e){e=Math.round(e||t.strokeWidth||0);var i=t.x||this.x||0,o=t.y||this.y||0,r=(t.width||this.width||0)+i,n=(t.height||this.height||0)+o,s=e8(i,e),a=e8(o,e);return ii(t,{x:s,y:a,width:e8(r,e)-s,height:e8(n,e)-a}),it(t.strokeWidth)&&(t.strokeWidth=e),t},t.prototype.complexColor=function(t,e,i){var o,r,n,s,a,h,l,c,d,p,u,f=this.renderer,g=[];io(this.renderer,"complexColor",{args:arguments},function(){if(t.radialGradient?r="radialGradient":t.linearGradient&&(r="linearGradient"),r){if(n=t[r],a=f.gradients,h=t.stops,d=i.radialReference,is(n)&&(t[r]=n={x1:n[0],y1:n[1],x2:n[2],y2:n[3],gradientUnits:"userSpaceOnUse"}),"radialGradient"===r&&d&&!it(n.gradientUnits)&&(s=n,n=id(n,f.getRadialAttr(d,s),{gradientUnits:"userSpaceOnUse"})),ip(n,function(t,e){"id"!==e&&g.push(e,t)}),ip(h,function(t){g.push(t)}),a[g=g.join(",")])p=a[g].attr("id");else{n.id=p=ix();var v=a[g]=f.createElement(r).attr(n).add(f.defs);v.radAttr=s,v.stops=[],h.forEach(function(t){0===t[1].indexOf("rgba")?(l=(o=et.parse(t[1])).get("rgb"),c=o.get("a")):(l=t[1],c=1);var e=f.createElement("stop").attr({offset:t[0],"stop-color":l,"stop-opacity":c}).add(v);v.stops.push(e)})}u="url("+f.url+"#"+p+")",i.setAttribute(e,u),i.gradient=g,t.toString=function(){return u}}})},t.prototype.css=function(t){var e,i=this.styles,o={},r=this.element,n=!i;if(i&&ip(t,function(t,e){i&&i[e]!==t&&(o[e]=t,n=!0)}),n){i&&(t=ii(i,o)),null===t.width||"auto"===t.width?delete this.textWidth:"text"===r.nodeName.toLowerCase()&&t.width&&(e=this.textWidth=ig(t.width)),ii(this.styles,t),e&&!e1&&this.renderer.forExport&&delete t.width;var s=e5&&t.fontSize||null;s&&(ih(s)||/^\d+$/.test(s))&&(t.fontSize+="px");var a=id(t);r.namespaceURI===this.SVG_NS&&(["textOutline","textOverflow","whiteSpace","width"].forEach(function(t){return a&&delete a[t]}),a.color&&(a.fill=a.color,delete a.color)),e7(r,a)}return this.added&&("text"===this.element.nodeName&&this.renderer.buildText(this),t.textOutline&&this.applyTextOutline(t.textOutline)),this},t.prototype.dashstyleSetter=function(t){var e,i=this["stroke-width"];if("inherit"===i&&(i=1),t){var o=(t=t.toLowerCase()).replace("shortdashdotdot","3,1,1,1,1,1,").replace("shortdashdot","3,1,1,1").replace("shortdot","1,1,").replace("shortdash","3,1,").replace("longdash","8,3,").replace(/dot/g,"1,3,").replace("dash","4,3,").replace(/,$/,"").split(",");for(e=o.length;e--;)o[e]=""+ig(o[e])*iu(i,NaN);t=o.join(",").replace(/NaN/g,"none"),this.element.setAttribute("stroke-dasharray",t)}},t.prototype.destroy=function(){var t,e,i=this,o=i.element||{},r=i.renderer,n=o.ownerSVGElement,s="SPAN"===o.nodeName&&i.parentGroup||void 0;if(o.onclick=o.onmouseout=o.onmouseover=o.onmousemove=o.point=null,eg(i),i.clipPath&&n){var a=i.clipPath;[].forEach.call(n.querySelectorAll("[clip-path],[CLIP-PATH]"),function(t){t.getAttribute("clip-path").indexOf(a.element.id)>-1&&t.removeAttribute("clip-path")}),i.clipPath=a.destroy()}if(i.stops){for(e=0;e<i.stops.length;e++)i.stops[e].destroy();i.stops.length=0,i.stops=void 0}for(i.safeRemoveChild(o);(null==s?void 0:s.div)&&0===s.div.childNodes.length;)t=s.parentGroup,i.safeRemoveChild(s.div),delete s.div,s=t;i.alignOptions&&ie(r.alignedObjects,i),ip(i,function(t,e){var o,r,n;((null===(o=i[e])||void 0===o?void 0:o.parentGroup)===i||-1!==["connector","foreignObject"].indexOf(e))&&(null===(n=null===(r=i[e])||void 0===r?void 0:r.destroy)||void 0===n||n.call(r)),delete i[e]})},t.prototype.dSetter=function(t,e,i){is(t)&&("string"==typeof t[0]&&(t=this.renderer.pathToSegments(t)),this.pathArray=t,t=t.reduce(function(t,e,i){return(null==e?void 0:e.join)?(i?t+" ":"")+e.join(" "):(e||"").toString()},"")),/(NaN| {2}|^$)/.test(t)&&(t="M 0 0"),this[e]!==t&&(i.setAttribute(e,t),this[e]=t)},t.prototype.fillSetter=function(t,e,i){"string"==typeof t?i.setAttribute(e,t):t&&this.complexColor(t,e,i)},t.prototype.hrefSetter=function(t,e,i){i.setAttributeNS("http://www.w3.org/1999/xlink",e,t)},t.prototype.getBBox=function(e,i){var o,r,n,s,a=this.alignValue,h=this.element,l=this.renderer,c=this.styles,d=this.textStr,p=l.cache,u=l.cacheKeys,f=h.namespaceURI===this.SVG_NS,g=iu(i,this.rotation,0),v=l.styledMode?h&&t.prototype.getStyle.call(h,"font-size"):c.fontSize;if(it(d)&&(-1===(s=d.toString()).indexOf("<")&&(s=s.replace(/\d/g,"0")),s+=["",l.rootFontSize,v,g,this.textWidth,a,c.lineClamp,c.textOverflow,c.fontWeight].join(",")),s&&!e&&(o=p[s]),!o||o.polygon){if(f||l.forExport){try{n=this.fakeTS&&function(t){var e=h.querySelector(".highcharts-text-outline");e&&e7(e,{display:t})},ia(n)&&n("none"),o=h.getBBox?ii({},h.getBBox()):{width:h.offsetWidth,height:h.offsetHeight,x:0,y:0},ia(n)&&n("")}catch(t){}(!o||o.width<0)&&(o={x:0,y:0,width:0,height:0})}else o=this.htmlGetBBox();r=o.height,f&&(o.height=r=({"11px,17":14,"13px,20":16})[""+(v||"")+",".concat(Math.round(r))]||r),g&&(o=this.getRotatedBox(o,g));var m={bBox:o};io(this,"afterGetBBox",m),o=m.bBox}if(s&&(""===d||o.height>0)){for(;u.length>250;)delete p[u.shift()];p[s]||u.push(s),p[s]=o}return o},t.prototype.getRotatedBox=function(t,e){var i=t.x,o=t.y,r=t.width,n=t.height,s=this.alignValue,a=this.translateY,h=this.rotationOriginX,l=this.rotationOriginY,c=ir(s),d=Number(this.element.getAttribute("y")||0)-(a?0:o),p=e*eQ,u=(e-90)*eQ,f=Math.cos(p),g=Math.sin(p),v=r*f,m=r*g,y=Math.cos(u),x=Math.sin(u),b=[void 0===h?0:h,void 0===l?0:l].map(function(t){return[t-t*f,t*g]}),k=b[0],w=k[0],M=k[1],S=b[1],T=S[0],C=i+c*(r-v)+w+S[1]+d*y,A=C+v,P=A-n*y,O=P-v,L=o+d-c*m-M+T+d*x,E=L+m,I=E-n*x,D=I-m,B=Math.min(C,A,P,O),N=Math.min(L,E,I,D),z=Math.max(C,A,P,O)-B,R=Math.max(L,E,I,D)-N;return{x:B,y:N,width:z,height:R,polygon:[[C,L],[A,E],[P,I],[O,D]]}},t.prototype.getStyle=function(t){return e3.getComputedStyle(this.element||this,"").getPropertyValue(t)},t.prototype.hasClass=function(t){return -1!==(""+this.attr("class")).split(" ").indexOf(t)},t.prototype.hide=function(){return this.attr({visibility:"hidden"})},t.prototype.htmlGetBBox=function(){return{height:0,width:0,x:0,y:0}},t.prototype.on=function(t,e){var i=this.onEvents;return i[t]&&i[t](),i[t]=e6(this.element,t,e),this},t.prototype.opacitySetter=function(t,e,i){var o=Number(Number(t).toFixed(3));this.opacity=o,i.setAttribute(e,o)},t.prototype.reAlign=function(){var t;(null===(t=this.alignOptions)||void 0===t?void 0:t.width)&&"left"!==this.alignOptions.align&&(this.alignOptions.width=this.getBBox().width,this.placed=!1,this.align())},t.prototype.removeClass=function(t){return this.attr("class",(""+this.attr("class")).replace(ic(t)?new RegExp("(^| )".concat(t,"( |$)")):t," ").replace(/ +/g," ").trim())},t.prototype.removeTextOutline=function(){var t=this.element.querySelector("tspan.highcharts-text-outline");t&&this.safeRemoveChild(t)},t.prototype.safeRemoveChild=function(t){var e=t.parentNode;e&&e.removeChild(t)},t.prototype.setRadialReference=function(t){var e=this.element.gradient&&this.renderer.gradients[this.element.gradient]||void 0;return this.element.radialReference=t,(null==e?void 0:e.radAttr)&&e.animate(this.renderer.getRadialAttr(t,e.radAttr)),this},t.prototype.shadow=function(t){var e,i=this.renderer,o=id((null===(e=this.parentGroup)||void 0===e?void 0:e.rotation)===90?{offsetX:-1,offsetY:-1}:{},il(t)?t:{}),r=i.shadowDefinition(o);return this.attr({filter:t?"url(".concat(i.url,"#").concat(r,")"):"none"})},t.prototype.show=function(t){return void 0===t&&(t=!0),this.attr({visibility:t?"inherit":"visible"})},t.prototype["stroke-widthSetter"]=function(t,e,i){this[e]=t,i.setAttribute(e,t)},t.prototype.strokeWidth=function(){if(!this.renderer.styledMode)return this["stroke-width"]||0;var t,e=this.getStyle("stroke-width"),i=0;return/px$/.test(e)?i=ig(e):""!==e&&(e9(t=e0.createElementNS(e2,"rect"),{width:e,"stroke-width":0}),this.element.parentNode.appendChild(t),i=t.getBBox().width,t.parentNode.removeChild(t)),i},t.prototype.symbolAttr=function(e){var i=this;t.symbolCustomAttribs.forEach(function(t){i[t]=iu(e[t],i[t])}),i.attr({d:i.renderer.symbols[i.symbolName](i.x,i.y,i.width,i.height,i)})},t.prototype.textSetter=function(t){t!==this.textStr&&(delete this.textPxLength,this.textStr=t,this.added&&this.renderer.buildText(this),this.reAlign())},t.prototype.titleSetter=function(t){var e=this.element,i=e.getElementsByTagName("title")[0]||e0.createElementNS(this.SVG_NS,"title");e.insertBefore?e.insertBefore(i,e.firstChild):e.appendChild(i),i.textContent=im(iu(t,""),[/<[^>]*>/g,""]).replace(/&lt;/g,"<").replace(/&gt;/g,">")},t.prototype.toFront=function(){var t=this.element;return t.parentNode.appendChild(t),this},t.prototype.translate=function(t,e){return this.attr({translateX:t,translateY:e})},t.prototype.updateTransform=function(t){void 0===t&&(t="transform");var e,i,o,r,n=this.element,s=this.foreignObject,a=this.matrix,h=this.padding,l=this.rotation,c=void 0===l?0:l,d=this.rotationOriginX,p=this.rotationOriginY,u=this.scaleX,f=this.scaleY,g=this.text,v=this.translateX,m=this.translateY,y=["translate("+(void 0===v?0:v)+","+(void 0===m?0:m)+")"];it(a)&&y.push("matrix("+a.join(",")+")"),!c||(y.push("rotate("+c+" "+(null!==(i=null!==(e=null!=d?d:n.getAttribute("x"))&&void 0!==e?e:this.x)&&void 0!==i?i:0)+" "+(null!==(r=null!==(o=null!=p?p:n.getAttribute("y"))&&void 0!==o?o:this.y)&&void 0!==r?r:0)+")"),(null==g?void 0:g.element.tagName)!=="SPAN"||(null==g?void 0:g.foreignObject)||g.attr({rotation:c,rotationOriginX:(d||0)-h,rotationOriginY:(p||0)-h})),(it(u)||it(f))&&y.push("scale("+iu(u,1)+" "+iu(f,1)+")"),y.length&&!(g||this).textPath&&((null==s?void 0:s.element)||n).setAttribute(t,y.join(" "))},t.prototype.visibilitySetter=function(t,e,i){"inherit"===t?i.removeAttribute(e):this[e]!==t&&i.setAttribute(e,t),this[e]=t},t.prototype.xGetter=function(t){return"circle"===this.element.nodeName&&("x"===t?t="cx":"y"===t&&(t="cy")),this._defaultGetter(t)},t.prototype.zIndexSetter=function(t,e){var i,o,r,n,s,a=this.renderer,h=this.parentGroup,l=(h||a).element||a.box,c=this.element,d=l===a.box,p=!1,u=this.added;if(it(t)?(c.setAttribute("data-z-index",t),t*=1,this[e]===t&&(u=!1)):it(this[e])&&c.removeAttribute("data-z-index"),this[e]=t,u){for((t=this.zIndex)&&h&&(h.handleZ=!0),s=(i=l.childNodes).length-1;s>=0&&!p;s--)n=!it(r=(o=i[s]).getAttribute("data-z-index")),o!==c&&(t<0&&n&&!d&&!s?(l.insertBefore(c,i[s]),p=!0):(ig(r)<=t||n&&(!it(t)||t>=0))&&(l.insertBefore(c,i[s+1]),p=!0));p||(l.insertBefore(c,i[3*!!d]),p=!0)}return p},t.symbolCustomAttribs=["anchorX","anchorY","clockwise","end","height","innerR","r","start","width","x","y"],t}();ib.prototype.strokeSetter=ib.prototype.fillSetter,ib.prototype.yGetter=ib.prototype.xGetter,ib.prototype.matrixSetter=ib.prototype.rotationOriginXSetter=ib.prototype.rotationOriginYSetter=ib.prototype.rotationSetter=ib.prototype.scaleXSetter=ib.prototype.scaleYSetter=ib.prototype.translateXSetter=ib.prototype.translateYSetter=ib.prototype.verticalAlignSetter=function(t,e){this[e]=t,this.doTransform=!0};var ik=(d=function(t,e){return(d=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}d(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),iw=tP.defined,iM=tP.extend,iS=tP.getAlignFactor,iT=tP.isNumber,iC=tP.merge,iA=tP.pick,iP=tP.removeEvent,iO=function(t){function e(i,o,r,n,s,a,h,l,c,d){var p,u=t.call(this,i,"g")||this;return u.paddingLeftSetter=u.paddingSetter,u.paddingRightSetter=u.paddingSetter,u.doUpdate=!1,u.textStr=o,u.x=r,u.y=n,u.anchorX=a,u.anchorY=h,u.baseline=c,u.className=d,u.addClass("button"===d?"highcharts-no-tooltip":"highcharts-label"),d&&u.addClass("highcharts-"+d),u.text=i.text(void 0,0,0,l).attr({zIndex:1}),"string"==typeof s&&((p=/^url\((.*?)\)$/.test(s))||u.renderer.symbols[s])&&(u.symbolKey=s),u.bBox=e.emptyBBox,u.padding=3,u.baselineOffset=0,u.needsBox=i.styledMode||p,u.deferredAttr={},u.alignFactor=0,u}return ik(e,t),e.prototype.alignSetter=function(t){var e=iS(t);this.textAlign=t,e!==this.alignFactor&&(this.alignFactor=e,this.bBox&&iT(this.xSetting)&&this.attr({x:this.xSetting}))},e.prototype.anchorXSetter=function(t,e){this.anchorX=t,this.boxAttr(e,Math.round(t)-this.getCrispAdjust()-this.xSetting)},e.prototype.anchorYSetter=function(t,e){this.anchorY=t,this.boxAttr(e,t-this.ySetting)},e.prototype.boxAttr=function(t,e){this.box?this.box.attr(t,e):this.deferredAttr[t]=e},e.prototype.css=function(t){if(t){var i={};t=iC(t),e.textProps.forEach(function(e){void 0!==t[e]&&(i[e]=t[e],delete t[e])}),this.text.css(i),"fontSize"in i||"fontWeight"in i?this.updateTextPadding():("width"in i||"textOverflow"in i)&&this.updateBoxSize()}return ib.prototype.css.call(this,t)},e.prototype.destroy=function(){iP(this.element,"mouseenter"),iP(this.element,"mouseleave"),this.text&&this.text.destroy(),this.box&&(this.box=this.box.destroy()),ib.prototype.destroy.call(this)},e.prototype.fillSetter=function(t,e){t&&(this.needsBox=!0),this.fill=t,this.boxAttr(e,t)},e.prototype.getBBox=function(t,e){this.textStr&&0===this.bBox.width&&0===this.bBox.height&&this.updateBoxSize();var i=this.padding,o=this.height,r=this.translateX,n=this.translateY,s=this.width,a=iA(this.paddingLeft,i),h=null!=e?e:this.rotation||0,l={width:void 0===s?0:s,height:void 0===o?0:o,x:(void 0===r?0:r)+this.bBox.x-a,y:(void 0===n?0:n)+this.bBox.y-i+this.baselineOffset};return h&&(l=this.getRotatedBox(l,h)),l},e.prototype.getCrispAdjust=function(){return(this.renderer.styledMode&&this.box?this.box.strokeWidth():this["stroke-width"]?parseInt(this["stroke-width"],10):0)%2/2},e.prototype.heightSetter=function(t){this.heightSetting=t,this.doUpdate=!0},e.prototype.afterSetters=function(){t.prototype.afterSetters.call(this),this.doUpdate&&(this.updateBoxSize(),this.doUpdate=!1)},e.prototype.onAdd=function(){this.text.add(this),this.attr({text:iA(this.textStr,""),x:this.x||0,y:this.y||0}),this.box&&iw(this.anchorX)&&this.attr({anchorX:this.anchorX,anchorY:this.anchorY})},e.prototype.paddingSetter=function(t,e){iT(t)?t!==this[e]&&(this[e]=t,this.updateTextPadding()):this[e]=void 0},e.prototype.rSetter=function(t,e){this.boxAttr(e,t)},e.prototype.strokeSetter=function(t,e){this.stroke=t,this.boxAttr(e,t)},e.prototype["stroke-widthSetter"]=function(t,e){t&&(this.needsBox=!0),this["stroke-width"]=t,this.boxAttr(e,t)},e.prototype["text-alignSetter"]=function(t){this.textAlign=this["text-align"]=t,this.updateTextPadding()},e.prototype.textSetter=function(t){void 0!==t&&this.text.attr({text:t}),this.updateTextPadding(),this.reAlign()},e.prototype.updateBoxSize=function(){var t,i=this.text,o={},r=this.padding,n=this.bBox=(!iT(this.widthSetting)||!iT(this.heightSetting)||this.textAlign)&&iw(i.textStr)?i.getBBox(void 0,0):e.emptyBBox;this.width=this.getPaddedWidth(),this.height=(this.heightSetting||n.height||0)+2*r;var s=this.renderer.fontMetrics(i);if(this.baselineOffset=r+Math.min((this.text.firstLineMetrics||s).b,n.height||1/0),this.heightSetting&&(this.baselineOffset+=(this.heightSetting-s.h)/2),this.needsBox&&!i.textPath){if(!this.box){var a=this.box=this.symbolKey?this.renderer.symbol(this.symbolKey):this.renderer.rect();a.addClass(("button"===this.className?"":"highcharts-label-box")+(this.className?" highcharts-"+this.className+"-box":"")),a.add(this)}o.x=t=this.getCrispAdjust(),o.y=(this.baseline?-this.baselineOffset:0)+t,o.width=Math.round(this.width),o.height=Math.round(this.height),this.box.attr(iM(o,this.deferredAttr)),this.deferredAttr={}}},e.prototype.updateTextPadding=function(){var t,e,i=this.text,o=i.styles.textAlign||this.textAlign;if(!i.textPath){this.updateBoxSize();var r=this.baseline?0:this.baselineOffset,n=(null!==(t=this.paddingLeft)&&void 0!==t?t:this.padding)+iS(o)*(null!==(e=this.widthSetting)&&void 0!==e?e:this.bBox.width);(n!==i.x||r!==i.y)&&(i.attr({align:o,x:n}),void 0!==r&&i.attr("y",r)),i.x=n,i.y=r}},e.prototype.widthSetter=function(t){this.widthSetting=iT(t)?t:void 0,this.doUpdate=!0},e.prototype.getPaddedWidth=function(){var t=this.padding,e=iA(this.paddingLeft,t),i=iA(this.paddingRight,t);return(this.widthSetting||this.bBox.width||0)+e+i},e.prototype.xSetter=function(t){this.x=t,this.alignFactor&&(t-=this.alignFactor*this.getPaddedWidth(),this["forceAnimate:x"]=!0),this.xSetting=Math.round(t),this.attr("translateX",this.xSetting)},e.prototype.ySetter=function(t){this.ySetting=this.y=Math.round(t),this.attr("translateY",this.ySetting)},e.emptyBBox={width:0,height:0,x:0,y:0},e.textProps=["color","direction","fontFamily","fontSize","fontStyle","fontWeight","lineClamp","lineHeight","textAlign","textDecoration","textOutline","textOverflow","whiteSpace","width"],e}(ib),iL=tP.defined,iE=tP.isNumber,iI=tP.pick;function iD(t,e,i,o,r){var n=[];if(r){var s=r.start||0,a=r.end||0,h=iI(r.r,i),l=iI(r.r,o||i),c=2e-4/(r.borderRadius?1:Math.max(h,1)),d=Math.abs(a-s-2*Math.PI)<c;d&&(s=Math.PI/2,a=2.5*Math.PI-c);var p=r.innerR,u=iI(r.open,d),f=Math.cos(s),g=Math.sin(s),v=Math.cos(a),m=Math.sin(a),y=iI(r.longArc,a-s-Math.PI<c?0:1),x=["A",h,l,0,y,iI(r.clockwise,1),t+h*v,e+l*m];x.params={start:s,end:a,cx:t,cy:e},n.push(["M",t+h*f,e+l*g],x),iL(p)&&((x=["A",p,p,0,y,iL(r.clockwise)?1-r.clockwise:0,t+p*f,e+p*g]).params={start:a,end:s,cx:t,cy:e},n.push(u?["M",t+p*v,e+p*m]:["L",t+p*v,e+p*m],x)),u||n.push(["Z"])}return n}function iB(t,e,i,o,r){return(null==r?void 0:r.r)?iN(t,e,i,o,r):[["M",t,e],["L",t+i,e],["L",t+i,e+o],["L",t,e+o],["Z"]]}function iN(t,e,i,o,r){var n=(null==r?void 0:r.r)||0;return[["M",t+n,e],["L",t+i-n,e],["A",n,n,0,0,1,t+i,e+n],["L",t+i,e+o-n],["A",n,n,0,0,1,t+i-n,e+o],["L",t+n,e+o],["A",n,n,0,0,1,t,e+o-n],["L",t,e+n],["A",n,n,0,0,1,t+n,e],["Z"]]}var iz={arc:iD,callout:function(t,e,i,o,r){var n=Math.min((null==r?void 0:r.r)||0,i,o),s=n+6,a=null==r?void 0:r.anchorX,h=(null==r?void 0:r.anchorY)||0,l=iN(t,e,i,o,{r:n});if(!iE(a)||a<i&&a>0&&h<o&&h>0)return l;if(t+a>i-s){if(h>e+s&&h<e+o-s)l.splice(3,1,["L",t+i,h-6],["L",t+i+6,h],["L",t+i,h+6],["L",t+i,e+o-n]);else if(a<i){var c=h<e+s,d=c?e:e+o,p=c?2:5;l.splice(p,0,["L",a,h],["L",t+i-n,d])}else l.splice(3,1,["L",t+i,o/2],["L",a,h],["L",t+i,o/2],["L",t+i,e+o-n])}else if(t+a<s){if(h>e+s&&h<e+o-s)l.splice(7,1,["L",t,h+6],["L",t-6,h],["L",t,h-6],["L",t,e+n]);else if(a>0){var c=h<e+s,d=c?e:e+o,p=c?1:6;l.splice(p,0,["L",a,h],["L",t+n,d])}else l.splice(7,1,["L",t,o/2],["L",a,h],["L",t,o/2],["L",t,e+n])}else h>o&&a<i-s?l.splice(5,1,["L",a+6,e+o],["L",a,e+o+6],["L",a-6,e+o],["L",t+n,e+o]):h<0&&a>s&&l.splice(1,1,["L",a-6,e],["L",a,e-6],["L",a+6,e],["L",i-n,e]);return l},circle:function(t,e,i,o){return iD(t+i/2,e+o/2,i/2,o/2,{start:.5*Math.PI,end:2.5*Math.PI,open:!1})},diamond:function(t,e,i,o){return[["M",t+i/2,e],["L",t+i,e+o/2],["L",t+i/2,e+o],["L",t,e+o/2],["Z"]]},rect:iB,roundedRect:iN,square:iB,triangle:function(t,e,i,o){return[["M",t+i/2,e],["L",t+i,e+o],["L",t,e+o],["Z"]]},"triangle-down":function(t,e,i,o){return[["M",t,e],["L",t+i,e],["L",t+i/2,e+o],["Z"]]}},iR=ti.doc,iW=ti.SVG_NS,iX=ti.win,iH=tP.attr,ij=tP.extend,iF=tP.fireEvent,iY=tP.isString,iG=tP.objectEach,i_=tP.pick,iU=function(t,e){return t.substring(0,e)+"…"},iV=function(){function t(t){var e=t.styles;this.renderer=t.renderer,this.svgElement=t,this.width=t.textWidth,this.textLineHeight=null==e?void 0:e.lineHeight,this.textOutline=null==e?void 0:e.textOutline,this.ellipsis=(null==e?void 0:e.textOverflow)==="ellipsis",this.lineClamp=null==e?void 0:e.lineClamp,this.noWrap=(null==e?void 0:e.whiteSpace)==="nowrap"}return t.prototype.buildSVG=function(){var t=this.svgElement,e=t.element,i=t.renderer,o=i_(t.textStr,"").toString(),r=-1!==o.indexOf("<"),n=e.childNodes,s=!t.added&&i.box,a=[o,this.ellipsis,this.noWrap,this.textLineHeight,this.textOutline,t.getStyle("font-size"),t.styles.lineClamp,this.width].join(",");if(a!==t.textCache){t.textCache=a,delete t.actualWidth;for(var h=n.length;h--;)e.removeChild(n[h]);if(r||this.ellipsis||this.width||t.textPath||-1!==o.indexOf(" ")&&(!this.noWrap||/<br.*?>/g.test(o))){if(""!==o){s&&s.appendChild(e);var l=new eI(o);this.modifyTree(l.nodes),l.addToDOM(e),this.modifyDOM(),this.ellipsis&&-1!==(e.textContent||"").indexOf("…")&&t.attr("title",this.unescapeEntities(t.textStr||"",["&lt;","&gt;"])),s&&s.removeChild(e)}}else e.appendChild(iR.createTextNode(this.unescapeEntities(o)));iY(this.textOutline)&&t.applyTextOutline&&t.applyTextOutline(this.textOutline)}},t.prototype.modifyDOM=function(){var t,e=this,i=this.svgElement,o=iH(i.element,"x");for(i.firstLineMetrics=void 0;t=i.element.firstChild;)if(/^[\s\u200B]*$/.test(t.textContent||" "))i.element.removeChild(t);else break;[].forEach.call(i.element.querySelectorAll("tspan.highcharts-br"),function(t,r){t.nextSibling&&t.previousSibling&&(0===r&&1===t.previousSibling.nodeType&&(i.firstLineMetrics=i.renderer.fontMetrics(t.previousSibling)),iH(t,{dy:e.getLineHeight(t.nextSibling),x:o}))});var r=this.width||0;if(r){var n=function(t,n){var s,a=t.textContent||"",h=a.replace(/([^\^])-/g,"$1- ").split(" "),l=!e.noWrap&&(h.length>1||i.element.childNodes.length>1),c=e.getLineHeight(n),d=Math.max(0,r-.8*c),p=0,u=i.actualWidth;if(l){for(var f=[],g=[];n.firstChild&&n.firstChild!==t;)g.push(n.firstChild),n.removeChild(n.firstChild);for(;h.length;)if(h.length&&!e.noWrap&&p>0&&(f.push(t.textContent||""),t.textContent=h.join(" ").replace(/- /g,"-")),e.truncate(t,void 0,h,0===p&&u||0,r,d,function(t,e){return h.slice(0,e).join(" ").replace(/- /g,"-")}),u=i.actualWidth,p++,e.lineClamp&&p>=e.lineClamp){h.length&&(e.truncate(t,t.textContent||"",void 0,0,r,d,iU),t.textContent=(null===(s=t.textContent)||void 0===s?void 0:s.replace("…",""))+"…");break}g.forEach(function(e){n.insertBefore(e,t)}),f.forEach(function(e){n.insertBefore(iR.createTextNode(e),t);var i=iR.createElementNS(iW,"tspan");i.textContent="​",iH(i,{dy:c,x:o}),n.insertBefore(i,t)})}else e.ellipsis&&a&&e.truncate(t,a,void 0,0,r,d,iU)},s=function(t){[].slice.call(t.childNodes).forEach(function(e){e.nodeType===iX.Node.TEXT_NODE?n(e,t):(-1!==e.className.baseVal.indexOf("highcharts-br")&&(i.actualWidth=0),s(e))})};s(i.element)}},t.prototype.getLineHeight=function(t){var e=t.nodeType===iX.Node.TEXT_NODE?t.parentElement:t;return this.textLineHeight?parseInt(this.textLineHeight.toString(),10):this.renderer.fontMetrics(e||this.svgElement.element).h},t.prototype.modifyTree=function(t){var e=this,i=function(o,r){var n=o.attributes,s=void 0===n?{}:n,a=o.children,h=o.style,l=void 0===h?{}:h,c=o.tagName,d=e.renderer.styledMode;if("b"===c||"strong"===c?d?s.class="highcharts-strong":l.fontWeight="bold":("i"===c||"em"===c)&&(d?s.class="highcharts-emphasized":l.fontStyle="italic"),(null==l?void 0:l.color)&&(l.fill=l.color),"br"===c){s.class="highcharts-br",o.textContent="​";var p=t[r+1];(null==p?void 0:p.textContent)&&(p.textContent=p.textContent.replace(/^ +/gm,""))}else"a"===c&&a&&a.some(function(t){return"#text"===t.tagName})&&(o.children=[{children:a,tagName:"tspan"}]);"#text"!==c&&"a"!==c&&(o.tagName="tspan"),ij(o,{attributes:s,style:l}),a&&a.filter(function(t){return"#text"!==t.tagName}).forEach(i)};t.forEach(i),iF(this.svgElement,"afterModifyTree",{nodes:t})},t.prototype.truncate=function(t,e,i,o,r,n,s){var a,h,l=this.svgElement,c=l.rotation,d=[],p=i&&!o?1:0,u=(e||i||"").length,f=u;i||(r=n);var g=function(e,r){var n=r||e,s=t.parentNode;if(s&&void 0===d[n]&&s.getSubStringLength)try{d[n]=o+s.getSubStringLength(0,i?n+1:n)}catch(t){}return d[n]};if(l.rotation=0,o+(h=g(t.textContent.length))>r){for(;p<=u;)f=Math.ceil((p+u)/2),i&&(a=s(i,f)),h=g(f,a&&a.length-1),p===u?p=u+1:h>r?u=f-1:p=f;0===u?t.textContent="":e&&u===e.length-1||(t.textContent=a||s(e||i,f)),this.ellipsis&&h>r&&this.truncate(t,t.textContent||"",void 0,0,r,n,iU)}i&&i.splice(0,f),l.actualWidth=h,l.rotation=c},t.prototype.unescapeEntities=function(t,e){return iG(this.renderer.escapes,function(i,o){e&&-1!==e.indexOf(i)||(t=t.toString().replace(RegExp(i,"g"),o))}),t},t}(),iZ=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},iq=ti.charts,iK=ti.deg2rad,i$=ti.doc,iJ=ti.isFirefox,iQ=ti.isMS,i0=ti.isWebKit,i1=ti.noop,i2=ti.SVG_NS,i3=ti.symbolSizes,i5=ti.win,i6=tP.addEvent,i9=tP.attr,i4=tP.createElement,i8=tP.crisp,i7=tP.css,ot=tP.defined,oe=tP.destroyObjectProperties,oi=tP.extend,oo=tP.isArray,or=tP.isNumber,on=tP.isObject,os=tP.isString,oa=tP.merge,oh=tP.pick,ol=tP.pInt,oc=tP.replaceNested,od=tP.uniqueKey,op=function(){function t(t,e,i,o,r,n,s){this.x=0,this.y=0;var a,h,l=this.createElement("svg").attr({version:"1.1",class:"highcharts-root"}),c=l.element;s||l.css(this.getStyle(o||{})),t.appendChild(c),i9(t,"dir","ltr"),-1===t.innerHTML.indexOf("xmlns")&&i9(c,"xmlns",this.SVG_NS),this.box=c,this.boxWrapper=l,this.alignedObjects=[],this.url=this.getReferenceURL(),this.createElement("desc").add().element.appendChild(i$.createTextNode("Created with Highcharts 12.2.0")),this.defs=this.createElement("defs").add(),this.allowHTML=n,this.forExport=r,this.styledMode=s,this.gradients={},this.cache={},this.cacheKeys=[],this.imgCount=0,this.rootFontSize=l.getStyle("font-size"),this.setSize(e,i,!1),iJ&&t.getBoundingClientRect&&((a=function(){i7(t,{left:0,top:0}),h=t.getBoundingClientRect(),i7(t,{left:Math.ceil(h.left)-h.left+"px",top:Math.ceil(h.top)-h.top+"px"})})(),this.unSubPixelFix=i6(i5,"resize",a))}return t.prototype.definition=function(t){return new eI([t]).addToDOM(this.defs.element)},t.prototype.getReferenceURL=function(){if((iJ||i0)&&i$.getElementsByTagName("base").length){if(!ot(z)){var t=od(),e=new eI([{tagName:"svg",attributes:{width:8,height:8},children:[{tagName:"defs",children:[{tagName:"clipPath",attributes:{id:t},children:[{tagName:"rect",attributes:{width:4,height:4}}]}]},{tagName:"rect",attributes:{id:"hitme",width:8,height:8,"clip-path":"url(#".concat(t,")"),fill:"rgba(0,0,0,0.001)"}}]}]).addToDOM(i$.body);i7(e,{position:"fixed",top:0,left:0,zIndex:9e5});var i=i$.elementFromPoint(6,6);z=(null==i?void 0:i.id)==="hitme",i$.body.removeChild(e)}if(z)return oc(i5.location.href.split("#")[0],[/<[^>]*>/g,""],[/([\('\)])/g,"\\$1"],[/ /g,"%20"])}return""},t.prototype.getStyle=function(t){return this.style=oi({fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", sans-serif',fontSize:"1rem"},t),this.style},t.prototype.setStyle=function(t){this.boxWrapper.css(this.getStyle(t))},t.prototype.isHidden=function(){return!this.boxWrapper.getBBox().width},t.prototype.destroy=function(){var t=this.defs;return this.box=null,this.boxWrapper=this.boxWrapper.destroy(),oe(this.gradients||{}),this.gradients=null,this.defs=t.destroy(),this.unSubPixelFix&&this.unSubPixelFix(),this.alignedObjects=null,null},t.prototype.createElement=function(t){return new this.Element(this,t)},t.prototype.getRadialAttr=function(t,e){return{cx:t[0]-t[2]/2+(e.cx||0)*t[2],cy:t[1]-t[2]/2+(e.cy||0)*t[2],r:(e.r||0)*t[2]}},t.prototype.shadowDefinition=function(t){var e=iZ(["highcharts-drop-shadow-".concat(this.chartIndex)],Object.keys(t).map(function(e){return""+e+"-".concat(t[e])}),!0).join("-").toLowerCase().replace(/[^a-z\d\-]/g,""),i=oa({color:"#000000",offsetX:1,offsetY:1,opacity:.15,width:5},t);return this.defs.element.querySelector("#".concat(e))||this.definition({tagName:"filter",attributes:{id:e,filterUnits:i.filterUnits},children:this.getShadowFilterContent(i)}),e},t.prototype.getShadowFilterContent=function(t){return[{tagName:"feDropShadow",attributes:{dx:t.offsetX,dy:t.offsetY,"flood-color":t.color,"flood-opacity":Math.min(5*t.opacity,1),stdDeviation:t.width/2}}]},t.prototype.buildText=function(t){new iV(t).buildSVG()},t.prototype.getContrast=function(t){var e=et.parse(t).rgba,i=" clamp(0,calc(9e9*(0.5 - (0.2126*r + 0.7152*g + 0.0722*b))),1)";if(or(e[0])||!et.useColorMix){var o=e.map(function(t){var e=t/255;return e<=.04?e/12.92:Math.pow((e+.055)/1.055,2.4)}),r=.2126*o[0]+.7152*o[1]+.0722*o[2];return 1.05/(r+.05)>(r+.05)/.05?"#FFFFFF":"#000000"}return"color(from "+t+" srgb"+i+i+i+")"},t.prototype.button=function(t,e,i,o,r,n,s,a,h,l){void 0===r&&(r={});var c=this.label(t,e,i,h,void 0,void 0,l,void 0,"button"),d=this.styledMode,p=arguments,u=0;r=oa(t0.global.buttonTheme,r),d&&(delete r.fill,delete r.stroke,delete r["stroke-width"]);var f=r.states||{},g=r.style||{};delete r.states,delete r.style;var v=[eI.filterUserAttributes(r)],m=[g];return d||["hover","select","disabled"].forEach(function(t,e){v.push(oa(v[0],eI.filterUserAttributes(p[e+5]||f[t]||{}))),m.push(v[e+1].style),delete v[e+1].style}),i6(c.element,iQ?"mouseover":"mouseenter",function(){3!==u&&c.setState(1)}),i6(c.element,iQ?"mouseout":"mouseleave",function(){3!==u&&c.setState(u)}),c.setState=function(t){if(void 0===t&&(t=0),1!==t&&(c.state=u=t),c.removeClass(/highcharts-button-(normal|hover|pressed|disabled)/).addClass("highcharts-button-"+["normal","hover","pressed","disabled"][t]),!d){c.attr(v[t]);var e=m[t];on(e)&&c.css(e)}},c.attr(v[0]),!d&&(c.css(oi({cursor:"default"},g)),l&&c.text.css({pointerEvents:"none"})),c.on("touchstart",function(t){return t.stopPropagation()}).on("click",function(t){3!==u&&(null==o||o.call(c,t))})},t.prototype.crispLine=function(t,e){var i=t[0],o=t[1];return ot(i[1])&&i[1]===o[1]&&(i[1]=o[1]=i8(i[1],e)),ot(i[2])&&i[2]===o[2]&&(i[2]=o[2]=i8(i[2],e)),t},t.prototype.path=function(t){var e=this.styledMode?{}:{fill:"none"};return oo(t)?e.d=t:on(t)&&oi(e,t),this.createElement("path").attr(e)},t.prototype.circle=function(t,e,i){var o=on(t)?t:void 0===t?{}:{x:t,y:e,r:i},r=this.createElement("circle");return r.xSetter=r.ySetter=function(t,e,i){i.setAttribute("c"+e,t)},r.attr(o)},t.prototype.arc=function(t,e,i,o,r,n){on(t)?(e=(s=t).y,i=s.r,o=s.innerR,r=s.start,n=s.end,t=s.x):s={innerR:o,start:r,end:n};var s,a=this.symbol("arc",t,e,i,i,s);return a.r=i,a},t.prototype.rect=function(t,e,i,o,r,n){var s=on(t)?t:void 0===t?{}:{x:t,y:e,r:r,width:Math.max(i||0,0),height:Math.max(o||0,0)},a=this.createElement("rect");return this.styledMode||(void 0!==n&&(s["stroke-width"]=n,oi(s,a.crisp(s))),s.fill="none"),a.rSetter=function(t,e,i){a.r=t,i9(i,{rx:t,ry:t})},a.rGetter=function(){return a.r||0},a.attr(s)},t.prototype.roundedRect=function(t){return this.symbol("roundedRect").attr(t)},t.prototype.setSize=function(t,e,i){this.width=t,this.height=e,this.boxWrapper.animate({width:t,height:e},{step:function(){this.attr({viewBox:"0 0 "+this.attr("width")+" "+this.attr("height")})},duration:oh(i,!0)?void 0:0}),this.alignElements()},t.prototype.g=function(t){var e=this.createElement("g");return t?e.attr({class:"highcharts-"+t}):e},t.prototype.image=function(t,e,i,o,r,n){var s={preserveAspectRatio:"none"};or(e)&&(s.x=e),or(i)&&(s.y=i),or(o)&&(s.width=o),or(r)&&(s.height=r);var a=this.createElement("image").attr(s),h=function(e){a.attr({href:t}),n.call(a,e)};if(n){a.attr({href:"data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="});var l=new i5.Image;i6(l,"load",h),l.src=t,l.complete&&h({})}else a.attr({href:t});return a},t.prototype.symbol=function(t,e,i,o,r,n){var s,a,h,l,c,d,p=this,u=/^url\((.*?)\)$/,f=u.test(t),g=!f&&(this.symbols[t]?t:"circle"),v=g&&this.symbols[g];if(v)"number"==typeof e&&(l=v.call(this.symbols,e||0,i||0,o||0,r||0,n)),h=this.path(l),p.styledMode||h.attr("fill","none"),oi(h,{symbolName:g||void 0,x:e,y:i,width:o,height:r}),n&&oi(h,n);else if(f){c=t.match(u)[1];var m=h=this.image(c);m.imgwidth=oh(null==n?void 0:n.width,null===(s=i3[c])||void 0===s?void 0:s.width),m.imgheight=oh(null==n?void 0:n.height,null===(a=i3[c])||void 0===a?void 0:a.height),d=function(t){return t.attr({width:t.width,height:t.height})},["width","height"].forEach(function(t){m[""+t+"Setter"]=function(t,e){this[e]=t;var i=this.alignByTranslate,o=this.element,r=this.width,s=this.height,a=this.imgwidth,h=this.imgheight,l="width"===e?a:h,c=1;n&&"within"===n.backgroundSize&&r&&s&&a&&h?(c=Math.min(r/a,s/h),i9(o,{width:Math.round(a*c),height:Math.round(h*c)})):o&&l&&o.setAttribute(e,l),!i&&a&&h&&this.translate(((r||0)-a*c)/2,((s||0)-h*c)/2)}}),ot(e)&&m.attr({x:e,y:i}),m.isImg=!0,m.symbolUrl=t,ot(m.imgwidth)&&ot(m.imgheight)?d(m):(m.attr({width:0,height:0}),i4("img",{onload:function(){var t=iq[p.chartIndex];0===this.width&&(i7(this,{position:"absolute",top:"-999em"}),i$.body.appendChild(this)),i3[c]={width:this.width,height:this.height},m.imgwidth=this.width,m.imgheight=this.height,m.element&&d(m),this.parentNode&&this.parentNode.removeChild(this),p.imgCount--,p.imgCount||!t||t.hasLoaded||t.onload()},src:c}),this.imgCount++)}return h},t.prototype.clipRect=function(t,e,i,o){return this.rect(t,e,i,o,0)},t.prototype.text=function(t,e,i,o){var r={};if(o&&(this.allowHTML||!this.forExport))return this.html(t,e,i);r.x=Math.round(e||0),i&&(r.y=Math.round(i)),ot(t)&&(r.text=t);var n=this.createElement("text").attr(r);return o&&(!this.forExport||this.allowHTML)||(n.xSetter=function(t,e,i){for(var o=i.getElementsByTagName("tspan"),r=i.getAttribute(e),n=0,s=void 0;n<o.length;n++)(s=o[n]).getAttribute(e)===r&&s.setAttribute(e,t);i.setAttribute(e,t)}),n},t.prototype.fontMetrics=function(t){var e=ol(ib.prototype.getStyle.call(t,"font-size")||0),i=e<24?e+3:Math.round(1.2*e),o=Math.round(.8*i);return{h:i,b:o,f:e}},t.prototype.rotCorr=function(t,e,i){var o=t;return e&&i&&(o=Math.max(o*Math.cos(e*iK),4)),{x:-t/3*Math.sin(e*iK),y:o}},t.prototype.pathToSegments=function(t){for(var e=[],i=[],o={A:8,C:7,H:2,L:3,M:3,Q:5,S:5,T:3,V:2},r=0;r<t.length;r++)os(i[0])&&or(t[r])&&i.length===o[i[0].toUpperCase()]&&t.splice(r,0,i[0].replace("M","L").replace("m","l")),"string"==typeof t[r]&&(i.length&&e.push(i.slice(0)),i.length=0),i.push(t[r]);return e.push(i.slice(0)),e},t.prototype.label=function(t,e,i,o,r,n,s,a,h){return new iO(this,t,e,i,o,r,n,s,a,h)},t.prototype.alignElements=function(){this.alignedObjects.forEach(function(t){return t.align()})},t}();oi(op.prototype,{Element:ib,SVG_NS:i2,escapes:{"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"},symbols:iz,draw:i1}),eV.registerRendererType("svg",op,!0);var ou=(p=function(t,e){return(p=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}p(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),of=function(){return(of=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},og=ti.composed,ov=ti.isFirefox,om=tP.attr,oy=tP.css,ox=tP.createElement,ob=tP.defined,ok=tP.extend,ow=tP.getAlignFactor,oM=tP.isNumber,oS=tP.pInt,oT=tP.pushUnique;function oC(t,e,i){var o,r=(null===(o=this.div)||void 0===o?void 0:o.style)||i.style;ib.prototype[""+e+"Setter"].call(this,t,e,i),r&&(r[e]=t)}var oA=function(t,e){var i;if(!t.div){var o=om(t.element,"class"),r=t.css,n=ox("div",o?{className:o}:void 0,of(of({position:"absolute",left:""+(t.translateX||0)+"px",top:""+(t.translateY||0)+"px"},t.styles),{display:t.display,opacity:t.opacity,visibility:t.visibility}),(null===(i=t.parentGroup)||void 0===i?void 0:i.div)||e);t.classSetter=function(t,e,i){i.setAttribute("class",t),n.className=t},t.translateXSetter=t.translateYSetter=function(e,i){t[i]=e,n.style["translateX"===i?"left":"top"]=""+e+"px",t.doTransform=!0},t.opacitySetter=t.visibilitySetter=oC,t.css=function(e){return r.call(t,e),e.cursor&&(n.style.cursor=e.cursor),e.pointerEvents&&(n.style.pointerEvents=e.pointerEvents),t},t.on=function(){return ib.prototype.on.apply({element:n,onEvents:t.onEvents},arguments),t},t.div=n}return t.div},oP=function(t){function e(i,o){var r=t.call(this,i,o)||this;return e.useForeignObject?r.foreignObject=i.createElement("foreignObject").attr({zIndex:2}):r.css(of({position:"absolute"},i.styledMode?{}:{fontFamily:i.style.fontFamily,fontSize:i.style.fontSize})),r.element.style.whiteSpace="nowrap",r}return ou(e,t),e.compose=function(t){oT(og,this.compose)&&(t.prototype.html=function(t,i,o){return new e(this,"span").attr({text:t,x:Math.round(i),y:Math.round(o)})})},e.prototype.getSpanCorrection=function(t,e,i){this.xCorr=-t*i,this.yCorr=-e},e.prototype.css=function(t){var e,i=this.element,o="SPAN"===i.tagName&&t&&"width"in t,r=o&&t.width;return o&&(delete t.width,this.textWidth=oS(r)||void 0,e=!0),(null==t?void 0:t.textOverflow)==="ellipsis"&&(t.overflow="hidden",t.whiteSpace="nowrap"),(null==t?void 0:t.lineClamp)&&(t.display="-webkit-box",t.WebkitLineClamp=t.lineClamp,t.WebkitBoxOrient="vertical",t.overflow="hidden"),oM(Number(null==t?void 0:t.fontSize))&&(t.fontSize+="px"),ok(this.styles,t),oy(i,t),e&&this.updateTransform(),this},e.prototype.htmlGetBBox=function(){var t=this.element;return{x:t.offsetLeft,y:t.offsetTop,width:t.offsetWidth,height:t.offsetHeight}},e.prototype.updateTransform=function(){if(!this.added){this.alignOnAdd=!0;return}var e,i=this.element,o=this.foreignObject,r=this.oldTextWidth,n=this.renderer,s=this.rotation,a=this.rotationOriginX,h=this.rotationOriginY,l=this.scaleX,c=this.scaleY,d=this.styles,p=d.display,u=void 0===p?"inline-block":p,f=d.whiteSpace,g=this.textAlign,v=void 0===g?"left":g,m=this.textWidth,y=this.translateX,x=this.translateY,b=this.x,k=void 0===b?0:b,w=this.y,M=void 0===w?0:w;if(o||oy(i,{marginLeft:""+(void 0===y?0:y)+"px",marginTop:""+(void 0===x?0:x)+"px"}),"SPAN"===i.tagName){var S=[s,v,i.innerHTML,m,this.textAlign].join(","),T=-((null===(e=this.parentGroup)||void 0===e?void 0:e.padding)*1)||0,C=void 0;if(m!==r){var A=this.textPxLength?this.textPxLength:(oy(i,{width:"",whiteSpace:f||"nowrap"}),i.offsetWidth),P=m||0,O=""===i.style.textOverflow&&i.style.webkitLineClamp;(P>r||A>P||O)&&(/[\-\s\u00AD]/.test(i.textContent||i.innerText)||"ellipsis"===i.style.textOverflow)&&(oy(i,{width:(s||l||A>P||O)&&oM(m)?m+"px":"auto",display:u,whiteSpace:f||"normal"}),this.oldTextWidth=m)}o&&(oy(i,{display:"inline-block",verticalAlign:"top"}),o.attr({width:n.width,height:n.height})),S!==this.cTT&&(C=n.fontMetrics(i).b,ob(s)&&!o&&(s!==(this.oldRotation||0)||v!==this.oldAlign)&&oy(i,{transform:"rotate(".concat(s,"deg)"),transformOrigin:""+T+"% "+T+"px"}),this.getSpanCorrection(!ob(s)&&!this.textWidth&&this.textPxLength||i.offsetWidth,C,ow(v)));var L=this.xCorr,E=void 0===L?0:L,I=this.yCorr,D=void 0===I?0:I,B=(null!=a?a:k)-E-k-T,N=(null!=h?h:M)-D-M-T,z={left:""+(k+E)+"px",top:""+(M+D)+"px",textAlign:v,transformOrigin:""+B+"px "+N+"px"};(l||c)&&(z.transform="scale(".concat(null!=l?l:1,",").concat(null!=c?c:1,")")),o?(t.prototype.updateTransform.call(this),oM(k)&&oM(M)?(o.attr({x:k+E,y:M+D,width:i.offsetWidth+3,height:i.offsetHeight,"transform-origin":i.getAttribute("transform-origin")||"0 0"}),oy(i,{display:u,textAlign:v})):ov&&o.attr({width:0,height:0})):oy(i,z),this.cTT=S,this.oldRotation=s,this.oldAlign=v}},e.prototype.add=function(e){var i=this.foreignObject,o=this.renderer,r=o.box.parentNode,n=[];if(i)i.add(e),t.prototype.add.call(this,o.createElement("body").attr({xmlns:"http://www.w3.org/1999/xhtml"}).css({background:"transparent",margin:"0 3px 0 0"}).add(i));else{var s=void 0;if(this.parentGroup=e,e&&!(s=e.div)){for(var a=e;a;)n.push(a),a=a.parentGroup;for(var h=0,l=n.reverse();h<l.length;h++)s=oA(l[h],r)}(s||r).appendChild(this.element)}return this.added=!0,this.alignOnAdd&&this.updateTransform(),this},e.prototype.textSetter=function(t){t!==this.textStr&&(delete this.bBox,delete this.oldTextWidth,eI.setElementHTML(this.element,null!=t?t:""),this.textStr=t,this.doTransform=!0)},e.prototype.alignSetter=function(t){this.alignValue=this.textAlign=t,this.doTransform=!0},e.prototype.xSetter=function(t,e){this[e]=t,this.doTransform=!0},e}(ib),oO=oP.prototype;oO.visibilitySetter=oO.opacitySetter=oC,oO.ySetter=oO.rotationSetter=oO.rotationOriginXSetter=oO.rotationOriginYSetter=oO.xSetter,(u=R||(R={})).xAxis={alignTicks:!0,allowDecimals:void 0,panningEnabled:!0,zIndex:2,zoomEnabled:!0,dateTimeLabelFormats:{millisecond:{main:"%[HMSL]",range:!1},second:{main:"%[HMS]",range:!1},minute:{main:"%[HM]",range:!1},hour:{main:"%[HM]",range:!1},day:{main:"%[eb]"},week:{main:"%[eb]"},month:{main:"%[bY]"},year:{main:"%Y"}},endOnTick:!1,gridLineDashStyle:"Solid",gridZIndex:1,labels:{autoRotationLimit:80,distance:15,enabled:!0,indentation:10,overflow:"justify",reserveSpace:void 0,rotation:void 0,staggerLines:0,step:0,useHTML:!1,zIndex:7,style:{color:"#333333",cursor:"default",fontSize:"0.8em",textOverflow:"ellipsis"}},maxPadding:.01,minorGridLineDashStyle:"Solid",minorTickLength:2,minorTickPosition:"outside",minorTicksPerMajor:5,minPadding:.01,offset:void 0,reversed:void 0,reversedStacks:!1,showEmpty:!0,showFirstLabel:!0,showLastLabel:!0,startOfWeek:1,startOnTick:!1,tickLength:10,tickPixelInterval:100,tickmarkPlacement:"between",tickPosition:"outside",title:{align:"middle",useHTML:!1,x:0,y:0,style:{color:"#666666",fontSize:"0.8em"}},visible:!0,minorGridLineColor:"#f2f2f2",minorGridLineWidth:1,minorTickColor:"#999999",lineColor:"#333333",lineWidth:1,gridLineColor:"#e6e6e6",gridLineWidth:void 0,tickColor:"#333333"},u.yAxis={reversedStacks:!0,endOnTick:!0,maxPadding:.05,minPadding:.05,tickPixelInterval:72,showLastLabel:!0,labels:{x:void 0},startOnTick:!0,title:{},stackLabels:{animation:{},allowOverlap:!1,enabled:!1,crop:!0,overflow:"justify",formatter:function(){return(0,this.axis.chart.numberFormatter)(this.total||0,-1)},style:{color:"#000000",fontSize:"0.7em",fontWeight:"bold",textOutline:"1px contrast"}},gridLineWidth:1,lineWidth:0};var oL=R,oE=tP.addEvent,oI=tP.isFunction,oD=tP.objectEach,oB=tP.removeEvent;(W||(W={})).registerEventOptions=function(t,e){t.eventOptions=t.eventOptions||{},oD(e.events,function(e,i){t.eventOptions[i]!==e&&(t.eventOptions[i]&&(oB(t,i,t.eventOptions[i]),delete t.eventOptions[i]),oI(e)&&(t.eventOptions[i]=e,oE(t,i,e,{order:0})))})};var oN=W,oz=ti.deg2rad,oR=tP.clamp,oW=tP.correctFloat,oX=tP.defined,oH=tP.destroyObjectProperties,oj=tP.extend,oF=tP.fireEvent,oY=tP.getAlignFactor,oG=tP.isNumber,o_=tP.merge,oU=tP.objectEach,oV=tP.pick,oZ=function(){function t(t,e,i,o,r){this.isNew=!0,this.isNewLabel=!0,this.axis=t,this.pos=e,this.type=i||"",this.parameters=r||{},this.tickmarkOffset=this.parameters.tickmarkOffset,this.options=this.parameters.options,oF(this,"init"),i||o||this.addLabel()}return t.prototype.addLabel=function(){var t,e,i,o,r=this,n=r.axis,s=n.options,a=n.chart,h=n.categories,l=n.logarithmic,c=n.names,d=r.pos,p=oV(null===(t=r.options)||void 0===t?void 0:t.labels,s.labels),u=n.tickPositions,f=d===u[0],g=d===u[u.length-1],v=(!p.step||1===p.step)&&1===n.tickInterval,m=u.info,y=r.label,x=this.parameters.category||(h?oV(h[d],c[d],d):d);l&&oG(x)&&(x=oW(l.lin2log(x))),n.dateTime&&(m?e=(i=a.time.resolveDTLFormat(s.dateTimeLabelFormats[!s.grid&&m.higherRanks[d]||m.unitName])).main:oG(x)&&(e=n.dateTime.getXDateFormat(x,s.dateTimeLabelFormats||{}))),r.isFirst=f,r.isLast=g;var b={axis:n,chart:a,dateTimeLabelFormat:e,isFirst:f,isLast:g,pos:d,tick:r,tickPositionInfo:m,value:x};oF(this,"labelFormat",b);var k=function(t){return p.formatter?p.formatter.call(t,t):p.format?(t.text=n.defaultLabelFormatter.call(t),eU.format(p.format,t,a)):n.defaultLabelFormatter.call(t)},w=k.call(b,b),M=null==i?void 0:i.list;M?r.shortenLabel=function(){for(o=0;o<M.length;o++)if(oj(b,{dateTimeLabelFormat:M[o]}),y.attr({text:k.call(b,b)}),y.getBBox().width<n.getSlotWidth(r)-2*(p.padding||0))return;y.attr({text:""})}:r.shortenLabel=void 0,v&&n._addedPlotLB&&r.moveLabel(w,p),oX(y)||r.movedLabel?y&&y.textStr!==w&&!v&&(!y.textWidth||p.style.width||y.styles.width||y.css({width:null}),y.attr({text:w}),y.textPxLength=y.getBBox().width):(r.label=y=r.createLabel(w,p),r.rotation=0)},t.prototype.createLabel=function(t,e,i){var o=this.axis,r=o.chart,n=r.renderer,s=r.styledMode,a=e.style.whiteSpace,h=oX(t)&&e.enabled?n.text(t,null==i?void 0:i.x,null==i?void 0:i.y,e.useHTML).add(o.labelGroup):void 0;return h&&(s||h.css(o_(e.style)),h.textPxLength=h.getBBox().width,!s&&a&&h.css({whiteSpace:a})),h},t.prototype.destroy=function(){oH(this,this.axis)},t.prototype.getPosition=function(t,e,i,o){var r=this.axis,n=r.chart,s=o&&n.oldChartHeight||n.chartHeight,a={x:t?oW(r.translate(e+i,void 0,void 0,o)+r.transB):r.left+r.offset+(r.opposite?(o&&n.oldChartWidth||n.chartWidth)-r.right-r.left:0),y:t?s-r.bottom+r.offset-(r.opposite?r.height:0):oW(s-r.translate(e+i,void 0,void 0,o)-r.transB)};return a.y=oR(a.y,-1e9,1e9),oF(this,"afterGetPosition",{pos:a}),a},t.prototype.getLabelPosition=function(t,e,i,o,r,n,s,a){var h,l,c=this.axis,d=c.transA,p=c.isLinked&&c.linkedParent?c.linkedParent.reversed:c.reversed,u=c.staggerLines,f=c.tickRotCorr||{x:0,y:0},g=o||c.reserveSpaceDefault?0:-c.labelOffset*("center"===c.labelAlign?.5:1),v=r.distance,m={};return h=0===c.side?i.rotation?-v:-i.getBBox().height:2===c.side?f.y+v:Math.cos(i.rotation*oz)*(f.y-i.getBBox(!1,0).height/2),oX(r.y)&&(h=0===c.side&&c.horiz?r.y+h:r.y),t=t+oV(r.x,[0,1,0,-1][c.side]*v)+g+f.x-(n&&o?n*d*(p?-1:1):0),e=e+h-(n&&!o?n*d*(p?1:-1):0),u&&(l=s/(a||1)%u,c.opposite&&(l=u-l-1),e+=l*(c.labelOffset/u)),m.x=t,m.y=Math.round(e),oF(this,"afterGetLabelPosition",{pos:m,tickmarkOffset:n,index:s}),m},t.prototype.getLabelSize=function(){return this.label?this.label.getBBox()[this.axis.horiz?"height":"width"]:0},t.prototype.getMarkPath=function(t,e,i,o,r,n){return void 0===r&&(r=!1),n.crispLine([["M",t,e],["L",t+(r?0:-i),e+(r?i:0)]],o)},t.prototype.handleOverflow=function(t){var e,i,o=this.axis,r=o.options.labels,n=t.x,s=o.chart.chartWidth,a=o.chart.spacing,h=oV(o.labelLeft,Math.min(o.pos,a[3])),l=oV(o.labelRight,Math.max(o.isRadial?0:o.pos+o.len,s-a[1])),c=this.label,d=this.rotation,p=oY(o.labelAlign||c.attr("align")),u=c.getBBox().width,f=o.getSlotWidth(this),g=f,v=1;d||"justify"!==r.overflow?d<0&&n-p*u<h?i=Math.round(n/Math.cos(d*oz)-h):d>0&&n+p*u>l&&(i=Math.round((s-n)/Math.cos(d*oz))):(n-p*u<h?g=t.x+g*(1-p)-h:n+(1-p)*u>l&&(g=l-t.x+g*p,v=-1),(g=Math.min(f,g))<f&&"center"===o.labelAlign&&(t.x+=v*(f-g-p*(f-Math.min(u,g)))),(u>g||o.autoRotation&&(null===(e=null==c?void 0:c.styles)||void 0===e?void 0:e.width))&&(i=g)),i&&c&&(this.shortenLabel?this.shortenLabel():c.css(oj({},{width:Math.floor(i)+"px",lineClamp:+!o.isRadial})))},t.prototype.moveLabel=function(t,e){var i,o=this,r=o.label,n=o.axis,s=!1;r&&r.textStr===t?(o.movedLabel=r,s=!0,delete o.label):oU(n.ticks,function(e){s||e.isNew||e===o||!e.label||e.label.textStr!==t||(o.movedLabel=e.label,s=!0,e.labelPos=o.movedLabel.xy,delete e.label)}),!s&&(o.labelPos||r)&&(i=o.labelPos||r.xy,o.movedLabel=o.createLabel(t,e,i),o.movedLabel&&o.movedLabel.attr({opacity:0}))},t.prototype.render=function(t,e,i){var o,r=this.axis,n=r.horiz,s=this.pos,a=oV(this.tickmarkOffset,r.tickmarkOffset),h=this.getPosition(n,s,a,e),l=h.x,c=h.y,d=r.pos,p=d+r.len,u=n?l:c,f=oV(i,null===(o=this.label)||void 0===o?void 0:o.newOpacity,1);!r.chart.polar&&(oW(u)<d||u>p)&&(i=0),null!=i||(i=1),this.isActive=!0,this.renderGridLine(e,i),this.renderMark(h,i),this.renderLabel(h,e,f,t),this.isNew=!1,oF(this,"afterRender")},t.prototype.renderGridLine=function(t,e){var i,o=this.axis,r=o.options,n={},s=this.pos,a=this.type,h=oV(this.tickmarkOffset,o.tickmarkOffset),l=o.chart.renderer,c=this.gridLine,d=r.gridLineWidth,p=r.gridLineColor,u=r.gridLineDashStyle;"minor"===this.type&&(d=r.minorGridLineWidth,p=r.minorGridLineColor,u=r.minorGridLineDashStyle),c||(o.chart.styledMode||(n.stroke=p,n["stroke-width"]=d||0,n.dashstyle=u),a||(n.zIndex=1),t&&(e=0),this.gridLine=c=l.path().attr(n).addClass("highcharts-"+(a?a+"-":"")+"grid-line").add(o.gridGroup)),c&&(i=o.getPlotLinePath({value:s+h,lineWidth:c.strokeWidth(),force:"pass",old:t,acrossPanes:!1}))&&c[t||this.isNew?"attr":"animate"]({d:i,opacity:e})},t.prototype.renderMark=function(t,e){var i=this.axis,o=i.options,r=i.chart.renderer,n=this.type,s=i.tickSize(n?n+"Tick":"tick"),a=t.x,h=t.y,l=oV(o["minor"!==n?"tickWidth":"minorTickWidth"],!n&&i.isXAxis?1:0),c=o["minor"!==n?"tickColor":"minorTickColor"],d=this.mark,p=!d;s&&(i.opposite&&(s[0]=-s[0]),d||(this.mark=d=r.path().addClass("highcharts-"+(n?n+"-":"")+"tick").add(i.axisGroup),i.chart.styledMode||d.attr({stroke:c,"stroke-width":l})),d[p?"attr":"animate"]({d:this.getMarkPath(a,h,s[0],d.strokeWidth(),i.horiz,r),opacity:e}))},t.prototype.renderLabel=function(t,e,i,o){var r=this.axis,n=r.horiz,s=r.options,a=this.label,h=s.labels,l=h.step,c=oV(this.tickmarkOffset,r.tickmarkOffset),d=t.x,p=t.y,u=!0;a&&oG(d)&&(a.xy=t=this.getLabelPosition(d,p,a,n,h,c,o,l),(!this.isFirst||this.isLast||s.showFirstLabel)&&(!this.isLast||this.isFirst||s.showLastLabel)?!n||h.step||h.rotation||e||0===i||this.handleOverflow(t):u=!1,l&&o%l&&(u=!1),u&&oG(t.y)?(t.opacity=i,a[this.isNewLabel?"attr":"animate"](t).show(!0),this.isNewLabel=!1):(a.hide(),this.isNewLabel=!0))},t.prototype.replaceMovedLabel=function(){var t=this.label,e=this.axis;t&&!this.isNew&&(t.animate({opacity:0},void 0,t.destroy),delete this.label),e.isDirty=!0,this.label=this.movedLabel,delete this.movedLabel},t}(),oq=oL.xAxis,oK=oL.yAxis,o$=oN.registerEventOptions,oJ=ti.deg2rad,oQ=tP.arrayMax,o0=tP.arrayMin,o1=tP.clamp,o2=tP.correctFloat,o3=tP.defined,o5=tP.destroyObjectProperties,o6=tP.erase,o9=tP.error,o4=tP.extend,o8=tP.fireEvent,o7=tP.getClosestDistance,rt=tP.insertItem,re=tP.isArray,ri=tP.isNumber,ro=tP.isString,rr=tP.merge,rn=tP.normalizeTickInterval,rs=tP.objectEach,ra=tP.pick,rh=tP.relativeLength,rl=tP.removeEvent,rc=tP.splat,rd=tP.syncTimeout,rp=function(t,e){return rn(e,void 0,void 0,ra(t.options.allowDecimals,e<.5||void 0!==t.tickAmount),!!t.tickAmount)};o4(t0,{xAxis:oq,yAxis:rr(oq,oK)});var ru=function(){function t(t,e,i){this.init(t,e,i)}return t.prototype.init=function(t,e,i){void 0===i&&(i=this.coll);var o,r,n,s,a="xAxis"===i,h=this.isZAxis||(t.inverted?!a:a);this.chart=t,this.horiz=h,this.isXAxis=a,this.coll=i,o8(this,"init",{userOptions:e}),this.opposite=ra(e.opposite,this.opposite),this.side=ra(e.side,this.side,h?2*!this.opposite:this.opposite?1:3),this.setOptions(e);var l=this.options,c=l.labels;null!==(o=this.type)&&void 0!==o||(this.type=l.type||"linear"),null!==(r=this.uniqueNames)&&void 0!==r||(this.uniqueNames=null===(n=l.uniqueNames)||void 0===n||n),o8(this,"afterSetType"),this.userOptions=e,this.minPixelPadding=0,this.reversed=ra(l.reversed,this.reversed),this.visible=l.visible,this.zoomEnabled=l.zoomEnabled,this.hasNames="category"===this.type||!0===l.categories,this.categories=re(l.categories)&&l.categories||(this.hasNames?[]:void 0),this.names||(this.names=[],this.names.keys={}),this.plotLinesAndBandsGroups={},this.positiveValuesOnly=!!this.logarithmic,this.isLinked=o3(l.linkedTo),this.ticks={},this.labelEdge=[],this.minorTicks={},this.plotLinesAndBands=[],this.alternateBands={},null!==(s=this.len)&&void 0!==s||(this.len=0),this.minRange=this.userMinRange=l.minRange||l.maxZoom,this.range=l.range,this.offset=l.offset||0,this.max=void 0,this.min=void 0;var d=ra(l.crosshair,rc(t.options.tooltip.crosshairs)[+!a]);this.crosshair=!0===d?{}:d,-1===t.axes.indexOf(this)&&(a?t.axes.splice(t.xAxis.length,0,this):t.axes.push(this),rt(this,t[this.coll])),t.orderItems(this.coll),this.series=this.series||[],t.inverted&&!this.isZAxis&&a&&!o3(this.reversed)&&(this.reversed=!0),this.labelRotation=ri(c.rotation)?c.rotation:void 0,o$(this,l),o8(this,"afterInit")},t.prototype.setOptions=function(t){var e=this.horiz?{labels:{autoRotation:[-45],padding:3},margin:15}:{labels:{padding:1},title:{rotation:90*this.side}};this.options=rr(e,"yAxis"===this.coll?{title:{text:this.chart.options.lang.yAxisTitle}}:{},t0[this.coll],t),o8(this,"afterSetOptions",{userOptions:t})},t.prototype.defaultLabelFormatter=function(){var t,e,i=this.axis,o=this.chart.numberFormatter,r=ri(this.value)?this.value:NaN,n=i.chart.time,s=i.categories,a=this.dateTimeLabelFormat,h=t0.lang,l=h.numericSymbols,c=h.numericSymbolMagnitude||1e3,d=i.logarithmic?Math.abs(r):i.tickInterval,p=null==l?void 0:l.length;if(s)e="".concat(this.value);else if(a)e=n.dateFormat(a,r,!0);else if(p&&l&&d>=1e3)for(;p--&&void 0===e;)d>=(t=Math.pow(c,p+1))&&10*r%t==0&&null!==l[p]&&0!==r&&(e=o(r/t,-1)+l[p]);return void 0===e&&(e=Math.abs(r)>=1e4?o(r,-1):o(r,-1,void 0,"")),e},t.prototype.getSeriesExtremes=function(){var t,e=this;o8(this,"getSeriesExtremes",null,function(){e.hasVisibleSeries=!1,e.dataMin=e.dataMax=e.threshold=void 0,e.softThreshold=!e.isXAxis,e.series.forEach(function(i){if(i.reserveSpace()){var o=i.options,r=void 0,n=o.threshold,s=void 0,a=void 0;if(e.hasVisibleSeries=!0,e.positiveValuesOnly&&0>=(n||0)&&(n=void 0),e.isXAxis)(r=i.getColumn("x")).length&&(r=e.logarithmic?r.filter(function(t){return t>0}):r,s=(t=i.getXExtremes(r)).min,a=t.max,ri(s)||s instanceof Date||(r=r.filter(ri),s=(t=i.getXExtremes(r)).min,a=t.max),r.length&&(e.dataMin=Math.min(ra(e.dataMin,s),s),e.dataMax=Math.max(ra(e.dataMax,a),a)));else{var h=i.applyExtremes();ri(h.dataMin)&&(s=h.dataMin,e.dataMin=Math.min(ra(e.dataMin,s),s)),ri(h.dataMax)&&(a=h.dataMax,e.dataMax=Math.max(ra(e.dataMax,a),a)),o3(n)&&(e.threshold=n),(!o.softThreshold||e.positiveValuesOnly)&&(e.softThreshold=!1)}}})}),o8(this,"afterGetSeriesExtremes")},t.prototype.translate=function(t,e,i,o,r,n){var s,a=this.linkedParent||this,h=o&&a.old?a.old.min:a.min;if(!ri(h))return NaN;var l=a.minPixelPadding,c=(a.isOrdinal||(null===(s=a.brokenAxis)||void 0===s?void 0:s.hasBreaks)||a.logarithmic&&r)&&a.lin2val,d=1,p=0,u=o&&a.old?a.old.transA:a.transA,f=0;return u||(u=a.transA),i&&(d*=-1,p=a.len),a.reversed&&(d*=-1,p-=d*(a.sector||a.len)),e?(f=(t=t*d+p-l)/u+h,c&&(f=a.lin2val(f))):(c&&(t=a.val2lin(t)),f=d*(t-h)*u+p+d*l+(ri(n)?u*n:0),a.isRadial||(f=o2(f))),f},t.prototype.toPixels=function(t,e){var i,o;return this.translate(null!==(o=null===(i=this.chart)||void 0===i?void 0:i.time.parse(t))&&void 0!==o?o:NaN,!1,!this.horiz,void 0,!0)+(e?0:this.pos)},t.prototype.toValue=function(t,e){return this.translate(t-(e?0:this.pos),!0,!this.horiz,void 0,!0)},t.prototype.getPlotLinePath=function(t){var e,i,o,r,n,s=this,a=s.chart,h=s.left,l=s.top,c=t.old,d=t.value,p=t.lineWidth,u=c&&a.oldChartHeight||a.chartHeight,f=c&&a.oldChartWidth||a.chartWidth,g=s.transB,v=t.translatedValue,m=t.force;function y(t,e,i){return"pass"!==m&&(t<e||t>i)&&(m?t=o1(t,e,i):n=!0),t}var x={value:d,lineWidth:p,old:c,force:m,acrossPanes:t.acrossPanes,translatedValue:v};return o8(this,"getPlotLinePath",x,function(t){e=o=(v=o1(v=ra(v,s.translate(d,void 0,void 0,c)),-1e9,1e9))+g,i=r=u-v-g,ri(v)?s.horiz?(i=l,r=u-s.bottom+(s.options.isInternal?0:a.scrollablePixelsY||0),e=o=y(e,h,h+s.width)):(e=h,o=f-s.right+(a.scrollablePixelsX||0),i=r=y(i,l,l+s.height)):(n=!0,m=!1),t.path=n&&!m?void 0:a.renderer.crispLine([["M",e,i],["L",o,r]],p||1)}),x.path},t.prototype.getLinearTickPositions=function(t,e,i){var o,r,n,s=o2(Math.floor(e/t)*t),a=o2(Math.ceil(i/t)*t),h=[];if(o2(s+t)===s&&(n=20),this.single)return[e];for(o=s;o<=a&&(h.push(o),(o=o2(o+t,n))!==r);)r=o;return h},t.prototype.getMinorTickInterval=function(){var t=this.options,e=t.minorTicks,i=t.minorTickInterval;return!0===e?ra(i,"auto"):!1!==e?i:void 0},t.prototype.getMinorTickPositions=function(){var t,e,i=this.options,o=this.tickPositions,r=this.minorTickInterval,n=this.pointRangePadding||0,s=(this.min||0)-n,a=(this.max||0)+n,h=(null===(t=this.brokenAxis)||void 0===t?void 0:t.hasBreaks)?this.brokenAxis.unitLength:a-s,l=[];if(h&&h/r<this.len/3){var c=this.logarithmic;if(c)this.paddedTicks.forEach(function(t,e,i){e&&l.push.apply(l,c.getLogTickPositions(r,i[e-1],i[e],!0))});else if(this.dateTime&&"auto"===this.getMinorTickInterval())l=l.concat(this.getTimeTicks(this.dateTime.normalizeTimeTickInterval(r),s,a,i.startOfWeek));else for(e=s+(o[0]-s)%r;e<=a&&e!==l[0];e+=r)l.push(e)}return 0!==l.length&&this.trimTicks(l),l},t.prototype.adjustForMinRange=function(){var t,e,i,o,r,n,s,a=this.options,h=this.logarithmic,l=this.chart.time,c=this.max,d=this.min,p=this.minRange;this.isXAxis&&void 0===p&&!h&&(p=o3(a.min)||o3(a.max)||o3(a.floor)||o3(a.ceiling)?null:Math.min(5*(o7(this.series.map(function(t){var e=t.getColumn("x");return t.xIncrement?e.slice(0,2):e}))||0),this.dataMax-this.dataMin)),ri(c)&&ri(d)&&ri(p)&&c-d<p&&(r=this.dataMax-this.dataMin>=p,o=(p-c+d)/2,n=[d-o,null!==(t=l.parse(a.min))&&void 0!==t?t:d-o],r&&(n[2]=h?h.log2lin(this.dataMin):this.dataMin),s=[(d=oQ(n))+p,null!==(e=l.parse(a.max))&&void 0!==e?e:d+p],r&&(s[2]=h?h.log2lin(this.dataMax):this.dataMax),(c=o0(s))-d<p&&(n[0]=c-p,n[1]=null!==(i=l.parse(a.min))&&void 0!==i?i:c-p,d=oQ(n))),this.minRange=p,this.min=d,this.max=c},t.prototype.getClosest=function(){var t,e;if(this.categories)e=1;else{var i=[];this.series.forEach(function(t){var o=t.closestPointRange,r=t.getColumn("x");1===r.length?i.push(r[0]):t.sorted&&o3(o)&&t.reserveSpace()&&(e=o3(e)?Math.min(e,o):o)}),i.length&&(i.sort(function(t,e){return t-e}),t=o7([i]))}return t&&e?Math.min(t,e):t||e},t.prototype.nameToX=function(t){var e,i=re(this.options.categories),o=i?this.categories:this.names,r=t.options.x;return t.series.requireSorting=!1,o3(r)||(r=this.uniqueNames&&o?i?o.indexOf(t.name):ra(o.keys[t.name],-1):t.series.autoIncrement()),-1===r?!i&&o&&(e=o.length):ri(r)&&(e=r),void 0!==e?(this.names[e]=t.name,this.names.keys[t.name]=e):t.x&&(e=t.x),e},t.prototype.updateNames=function(){var t=this,e=this.names;e.length>0&&(Object.keys(e.keys).forEach(function(t){delete e.keys[t]}),e.length=0,this.minRange=this.userMinRange,(this.series||[]).forEach(function(e){e.xIncrement=null,(!e.points||e.isDirtyData)&&(t.max=Math.max(t.max||0,e.dataTable.rowCount-1),e.processData(),e.generatePoints());var i=e.getColumn("x").slice();e.data.forEach(function(e,o){var r=i[o];(null==e?void 0:e.options)&&void 0!==e.name&&void 0!==(r=t.nameToX(e))&&r!==e.x&&(i[o]=e.x=r)}),e.dataTable.setColumn("x",i)}))},t.prototype.setAxisTranslation=function(){var t,e,i,o=this,r=o.max-o.min,n=o.linkedParent,s=!!o.categories,a=o.isXAxis,h=o.axisPointRange||0,l=0,c=0,d=o.transA;(a||s||h)&&(e=o.getClosest(),n?(l=n.minPointOffset,c=n.pointRangePadding):o.series.forEach(function(t){var i=s?1:a?ra(t.options.pointRange,e,0):o.axisPointRange||0,r=t.options.pointPlacement;if(h=Math.max(h,i),!o.single||s){var n=t.is("xrange")?!a:a;l=Math.max(l,n&&ro(r)?0:i/2),c=Math.max(c,n&&"on"===r?0:i)}}),i=(null===(t=o.ordinal)||void 0===t?void 0:t.slope)&&e?o.ordinal.slope/e:1,o.minPointOffset=l*=i,o.pointRangePadding=c*=i,o.pointRange=Math.min(h,o.single&&s?1:r),a&&(o.closestPointRange=e)),o.translationSlope=o.transA=d=o.staticScale||o.len/(r+c||1),o.transB=o.horiz?o.left:o.bottom,o.minPixelPadding=d*l,o8(this,"afterSetAxisTranslation")},t.prototype.minFromRange=function(){var t=this.max,e=this.min;return ri(t)&&ri(e)&&t-e||void 0},t.prototype.setTickInterval=function(t){var e,i,o,r,n,s,a,h,l,c=this.categories,d=this.chart,p=this.dataMax,u=this.dataMin,f=this.dateTime,g=this.isXAxis,v=this.logarithmic,m=this.options,y=this.softThreshold,x=d.time,b=ri(this.threshold)?this.threshold:void 0,k=this.minRange||0,w=m.ceiling,M=m.floor,S=m.linkedTo,T=m.softMax,C=m.softMin,A=ri(S)&&(null===(e=d[this.coll])||void 0===e?void 0:e[S]),P=m.tickPixelInterval,O=m.maxPadding,L=m.minPadding,E=0,I=ri(m.tickInterval)&&m.tickInterval>=0?m.tickInterval:void 0;if(f||c||A||this.getTickAmount(),h=ra(this.userMin,x.parse(m.min)),l=ra(this.userMax,x.parse(m.max)),A?(this.linkedParent=A,n=A.getExtremes(),this.min=ra(n.min,n.dataMin),this.max=ra(n.max,n.dataMax),this.type!==A.type&&o9(11,!0,d)):(y&&o3(b)&&ri(p)&&ri(u)&&(u>=b?(s=b,L=0):p<=b&&(a=b,O=0)),this.min=ra(h,s,u),this.max=ra(l,a,p)),ri(this.max)&&ri(this.min)&&(v&&(this.positiveValuesOnly&&!t&&0>=Math.min(this.min,ra(u,this.min))&&o9(10,!0,d),this.min=o2(v.log2lin(this.min),16),this.max=o2(v.log2lin(this.max),16)),this.range&&ri(u)&&(this.userMin=this.min=h=Math.max(u,this.minFromRange()||0),this.userMax=l=this.max,this.range=void 0)),o8(this,"foundExtremes"),this.adjustForMinRange(),ri(this.min)&&ri(this.max)){if(!ri(this.userMin)&&ri(C)&&C<this.min&&(this.min=h=C),!ri(this.userMax)&&ri(T)&&T>this.max&&(this.max=l=T),c||this.axisPointRange||(null===(i=this.stacking)||void 0===i?void 0:i.usePercentage)||A||!(E=this.max-this.min)||(!o3(h)&&L&&(this.min-=E*L),o3(l)||!O||(this.max+=E*O)),!ri(this.userMin)&&ri(M)&&(this.min=Math.max(this.min,M)),!ri(this.userMax)&&ri(w)&&(this.max=Math.min(this.max,w)),y&&ri(u)&&ri(p)){var D=b||0;!o3(h)&&this.min<D&&u>=D?this.min=m.minRange?Math.min(D,this.max-k):D:!o3(l)&&this.max>D&&p<=D&&(this.max=m.minRange?Math.max(D,this.min+k):D)}!d.polar&&this.min>this.max&&(o3(m.min)?this.max=this.min:o3(m.max)&&(this.min=this.max)),E=this.max-this.min}if(this.min!==this.max&&ri(this.min)&&ri(this.max)?A&&!I&&P===A.options.tickPixelInterval?this.tickInterval=I=A.tickInterval:this.tickInterval=ra(I,this.tickAmount?E/Math.max(this.tickAmount-1,1):void 0,c?1:E*P/Math.max(this.len,P)):this.tickInterval=1,g&&!t){var B=this.min!==(null===(o=this.old)||void 0===o?void 0:o.min)||this.max!==(null===(r=this.old)||void 0===r?void 0:r.max);this.series.forEach(function(t){var e;t.forceCrop=null===(e=t.forceCropping)||void 0===e?void 0:e.call(t),t.processData(B)}),o8(this,"postProcessData",{hasExtremesChanged:B})}this.setAxisTranslation(),o8(this,"initialAxisTranslation"),this.pointRange&&!I&&(this.tickInterval=Math.max(this.pointRange,this.tickInterval));var N=ra(m.minTickInterval,f&&!this.series.some(function(t){return!t.sorted})?this.closestPointRange:0);!I&&N&&this.tickInterval<N&&(this.tickInterval=N),f||v||I||(this.tickInterval=rp(this,this.tickInterval)),this.tickAmount||(this.tickInterval=this.unsquish()),this.setTickPositions()},t.prototype.setTickPositions=function(){var t,e,i,o=this.options,r=o.tickPositions,n=o.tickPositioner,s=this.getMinorTickInterval(),a=!this.isPanning,h=a&&o.startOnTick,l=a&&o.endOnTick,c=[];if(this.tickmarkOffset=this.categories&&"between"===o.tickmarkPlacement&&1===this.tickInterval?.5:0,this.single=this.min===this.max&&o3(this.min)&&!this.tickAmount&&(this.min%1==0||!1!==o.allowDecimals),r)c=r.slice();else if(ri(this.min)&&ri(this.max)){if(!(null===(t=this.ordinal)||void 0===t?void 0:t.positions)&&(this.max-this.min)/this.tickInterval>Math.max(2*this.len,200))c=[this.min,this.max],o9(19,!1,this.chart);else if(this.dateTime)c=this.getTimeTicks(this.dateTime.normalizeTimeTickInterval(this.tickInterval,o.units),this.min,this.max,o.startOfWeek,null===(e=this.ordinal)||void 0===e?void 0:e.positions,this.closestPointRange,!0);else if(this.logarithmic)c=this.logarithmic.getLogTickPositions(this.tickInterval,this.min,this.max);else for(var d=this.tickInterval,p=d;p<=2*d;)if(c=this.getLinearTickPositions(this.tickInterval,this.min,this.max),this.tickAmount&&c.length>this.tickAmount)this.tickInterval=rp(this,p*=1.1);else break;c.length>this.len&&(c=[c[0],c[c.length-1]])[0]===c[1]&&(c.length=1),n&&(this.tickPositions=c,(i=n.apply(this,[this.min,this.max]))&&(c=i))}this.tickPositions=c,this.minorTickInterval="auto"===s&&this.tickInterval?this.tickInterval/o.minorTicksPerMajor:s,this.paddedTicks=c.slice(0),this.trimTicks(c,h,l),!this.isLinked&&ri(this.min)&&ri(this.max)&&(this.single&&c.length<2&&!this.categories&&!this.series.some(function(t){return t.is("heatmap")&&"between"===t.options.pointPlacement})&&(this.min-=.5,this.max+=.5),r||i||this.adjustTickAmount()),o8(this,"afterSetTickPositions")},t.prototype.trimTicks=function(t,e,i){var o=t[0],r=t[t.length-1],n=!this.isOrdinal&&this.minPointOffset||0;if(o8(this,"trimTicks"),!this.isLinked||!this.grid){if(e&&o!==-1/0)this.min=o;else for(;this.min-n>t[0];)t.shift();if(i)this.max=r;else for(;this.max+n<t[t.length-1];)t.pop();0===t.length&&o3(o)&&!this.options.tickPositions&&t.push((r+o)/2)}},t.prototype.alignToOthers=function(){var t,e=this,i=e.chart,o=[this],r=e.options,n=i.options.chart,s="yAxis"===this.coll&&n.alignThresholds,a=[];if(e.thresholdAlignment=void 0,(!1!==n.alignTicks&&r.alignTicks||s)&&!1!==r.startOnTick&&!1!==r.endOnTick&&!e.logarithmic){var h=function(t){var e=t.horiz,i=t.options;return[e?i.left:i.top,i.width,i.height,i.pane].join(",")},l=h(this);i[this.coll].forEach(function(i){var r=i.series;r.length&&r.some(function(t){return t.visible})&&i!==e&&h(i)===l&&(t=!0,o.push(i))})}if(t&&s){o.forEach(function(t){var i=t.getThresholdAlignment(e);ri(i)&&a.push(i)});var c=a.length>1?a.reduce(function(t,e){return t+e},0)/a.length:void 0;o.forEach(function(t){t.thresholdAlignment=c})}return t},t.prototype.getThresholdAlignment=function(t){if((!ri(this.dataMin)||this!==t&&this.series.some(function(t){return t.isDirty||t.isDirtyData}))&&this.getSeriesExtremes(),ri(this.threshold)){var e=o1((this.threshold-(this.dataMin||0))/((this.dataMax||0)-(this.dataMin||0)),0,1);return this.options.reversed&&(e=1-e),e}},t.prototype.getTickAmount=function(){var t=this.options,e=t.tickPixelInterval,i=t.tickAmount;o3(t.tickInterval)||i||!(this.len<e)||this.isRadial||this.logarithmic||!t.startOnTick||!t.endOnTick||(i=2),!i&&this.alignToOthers()&&(i=Math.ceil(this.len/e)+1),i<4&&(this.finalTickAmt=i,i=5),this.tickAmount=i},t.prototype.adjustTickAmount=function(){var t,e,i,o=this,r=o.finalTickAmt,n=o.max,s=o.min,a=o.options,h=o.tickPositions,l=o.tickAmount,c=o.thresholdAlignment,d=null==h?void 0:h.length,p=ra(o.threshold,o.softThreshold?0:null),u=o.tickInterval,f=function(){return h.push(o2(h[h.length-1]+u))},g=function(){return h.unshift(o2(h[0]-u))};if(ri(c)&&(i=c<.5?Math.ceil(c*(l-1)):Math.floor(c*(l-1)),a.reversed&&(i=l-1-i)),o.hasData()&&ri(s)&&ri(n)){var v=function(){o.transA*=(d-1)/(l-1),o.min=a.startOnTick?h[0]:Math.min(s,h[0]),o.max=a.endOnTick?h[h.length-1]:Math.max(n,h[h.length-1])};if(ri(i)&&ri(o.threshold)){for(;h[i]!==p||h.length!==l||h[0]>s||h[h.length-1]<n;){for(h.length=0,h.push(o.threshold);h.length<l;)void 0===h[i]||h[i]>o.threshold?g():f();if(u>8*o.tickInterval)break;u*=2}v()}else if(d<l){for(;h.length<l;)h.length%2||s===p?f():g();v()}if(o3(r)){for(e=t=h.length;e--;)(3===r&&e%2==1||r<=2&&e>0&&e<t-1)&&h.splice(e,1);o.finalTickAmt=void 0}}},t.prototype.setScale=function(){var t,e,i,o,r,n=this.coll,s=this.stacking,a=!1,h=!1;this.series.forEach(function(t){var e;a=a||t.isDirtyData||t.isDirty,h=h||(null===(e=t.xAxis)||void 0===e?void 0:e.isDirty)||!1}),this.setAxisSize();var l=this.len!==(null===(t=this.old)||void 0===t?void 0:t.len);l||a||h||this.isLinked||this.forceRedraw||this.userMin!==(null===(e=this.old)||void 0===e?void 0:e.userMin)||this.userMax!==(null===(i=this.old)||void 0===i?void 0:i.userMax)||this.alignToOthers()?(s&&"yAxis"===n&&s.buildStacks(),this.forceRedraw=!1,this.userMinRange||(this.minRange=void 0),this.getSeriesExtremes(),this.setTickInterval(),s&&"xAxis"===n&&s.buildStacks(),this.isDirty||(this.isDirty=l||this.min!==(null===(o=this.old)||void 0===o?void 0:o.min)||this.max!==(null===(r=this.old)||void 0===r?void 0:r.max))):s&&s.cleanStacks(),a&&delete this.allExtremes,o8(this,"afterSetScale")},t.prototype.setExtremes=function(t,e,i,o,r){var n=this;void 0===i&&(i=!0);var s=this.chart;this.series.forEach(function(t){delete t.kdTree}),o8(this,"setExtremes",r=o4(r,{min:t=s.time.parse(t),max:e=s.time.parse(e)}),function(t){n.userMin=t.min,n.userMax=t.max,n.eventArgs=t,i&&s.redraw(o)})},t.prototype.setAxisSize=function(){var t=this.chart,e=this.options,i=e.offsets||[0,0,0,0],o=this.horiz,r=this.width=Math.round(rh(ra(e.width,t.plotWidth-i[3]+i[1]),t.plotWidth)),n=this.height=Math.round(rh(ra(e.height,t.plotHeight-i[0]+i[2]),t.plotHeight)),s=this.top=Math.round(rh(ra(e.top,t.plotTop+i[0]),t.plotHeight,t.plotTop)),a=this.left=Math.round(rh(ra(e.left,t.plotLeft+i[3]),t.plotWidth,t.plotLeft));this.bottom=t.chartHeight-n-s,this.right=t.chartWidth-r-a,this.len=Math.max(o?r:n,0),this.pos=o?a:s},t.prototype.getExtremes=function(){var t=this.logarithmic;return{min:t?o2(t.lin2log(this.min)):this.min,max:t?o2(t.lin2log(this.max)):this.max,dataMin:this.dataMin,dataMax:this.dataMax,userMin:this.userMin,userMax:this.userMax}},t.prototype.getThreshold=function(t){var e=this.logarithmic,i=e?e.lin2log(this.min):this.min,o=e?e.lin2log(this.max):this.max;return null===t||t===-1/0?t=i:t===1/0?t=o:i>t?t=i:o<t&&(t=o),this.translate(t,0,1,0,1)},t.prototype.autoLabelAlign=function(t){var e=(ra(t,0)-90*this.side+720)%360,i={align:"center"};return o8(this,"autoLabelAlign",i,function(t){e>15&&e<165?t.align="right":e>195&&e<345&&(t.align="left")}),i.align},t.prototype.tickSize=function(t){var e,i=this.options,o=ra(i["tick"===t?"tickWidth":"minorTickWidth"],"tick"===t&&this.isXAxis&&!this.categories?1:0),r=i["tick"===t?"tickLength":"minorTickLength"];o&&r&&("inside"===i[t+"Position"]&&(r=-r),e=[r,o]);var n={tickSize:e};return o8(this,"afterTickSize",n),n.tickSize},t.prototype.labelMetrics=function(){var t=this.chart.renderer,e=this.ticks,i=e[Object.keys(e)[0]]||{};return this.chart.renderer.fontMetrics(i.label||i.movedLabel||t.box)},t.prototype.unsquish=function(){var t,e,i=this.options.labels,o=i.padding||0,r=this.horiz,n=this.tickInterval,s=this.len/((+!!this.categories+this.max-this.min)/n),a=i.rotation,h=o2(.8*this.labelMetrics().h),l=Math.max(this.max-this.min,0),c=function(t){var e=(t+2*o)/(s||1);return(e=e>1?Math.ceil(e):1)*n>l&&t!==1/0&&s!==1/0&&l&&(e=Math.ceil(l/n)),o2(e*n)},d=n,p=Number.MAX_VALUE;if(r){if(!i.staggerLines&&(ri(a)?e=[a]:s<i.autoRotationLimit&&(e=i.autoRotation)),e)for(var u=void 0,f=void 0,g=0,v=e;g<v.length;g++){var m=v[g];(m===a||m&&m>=-90&&m<=90)&&(f=(u=c(Math.abs(h/Math.sin(oJ*m))))+Math.abs(m/360))<p&&(p=f,t=m,d=u)}}else d=c(.75*h);return this.autoRotation=e,this.labelRotation=ra(t,ri(a)?a:0),i.step?n:d},t.prototype.getSlotWidth=function(t){var e=this.chart,i=this.horiz,o=this.options.labels,r=Math.max(this.tickPositions.length-+!this.categories,1),n=e.margin[3];if(t&&ri(t.slotWidth))return t.slotWidth;if(i&&o.step<2&&!this.isRadial)return o.rotation?0:(this.staggerLines||1)*this.len/r;if(!i){var s=o.style.width;if(void 0!==s)return parseInt(String(s),10);if(n)return n-e.spacing[3]}return .33*e.chartWidth},t.prototype.renderUnsquish=function(){var t,e=this.chart,i=e.renderer,o=this.tickPositions,r=this.ticks,n=this.options.labels,s=n.style,a=this.horiz,h=this.getSlotWidth(),l=Math.max(1,Math.round(h-(a?2*(n.padding||0):n.distance||0))),c={},d=this.labelMetrics(),p=s.lineClamp,u=null!=p?p:Math.floor(this.len/(o.length*d.h))||1,f=0;ro(n.rotation)||(c.rotation=n.rotation||0),o.forEach(function(t){var e,i=r[t];i.movedLabel&&i.replaceMovedLabel();var o=(null===(e=i.label)||void 0===e?void 0:e.textPxLength)||0;o>f&&(f=o)}),this.maxLabelLength=f,this.autoRotation?f>l&&f>d.h?c.rotation=this.labelRotation:this.labelRotation=0:h&&(t=l),c.rotation&&(t=f>.5*e.chartHeight?.33*e.chartHeight:f,p||(u=1)),this.labelAlign=n.align||this.autoLabelAlign(this.labelRotation),this.labelAlign&&(c.align=this.labelAlign),o.forEach(function(e){var i=r[e],o=null==i?void 0:i.label,n=s.width,a={};o&&(o.attr(c),i.shortenLabel?i.shortenLabel():t&&!n&&"nowrap"!==s.whiteSpace&&(t<(o.textPxLength||0)||"SPAN"===o.element.tagName)?o.css(o4(a,{width:""+t+"px",lineClamp:u})):!o.styles.width||a.width||n||o.css({width:"auto"}),i.rotation=c.rotation)},this),this.tickRotCorr=i.rotCorr(d.b,this.labelRotation||0,0!==this.side)},t.prototype.hasData=function(){return this.series.some(function(t){return t.hasData()})||this.options.showEmpty&&o3(this.min)&&o3(this.max)},t.prototype.addTitle=function(t){var e,i=this.chart.renderer,o=this.horiz,r=this.opposite,n=this.options.title,s=this.chart.styledMode;this.axisTitle||((e=n.textAlign)||(e=(o?{low:"left",middle:"center",high:"right"}:{low:r?"right":"left",middle:"center",high:r?"left":"right"})[n.align]),this.axisTitle=i.text(n.text||"",0,0,n.useHTML).attr({zIndex:7,rotation:n.rotation||0,align:e}).addClass("highcharts-axis-title"),s||this.axisTitle.css(rr(n.style)),this.axisTitle.add(this.axisGroup),this.axisTitle.isNew=!0),s||n.style.width||this.isRadial||this.axisTitle.css({width:this.len+"px"}),this.axisTitle[t?"show":"hide"](t)},t.prototype.generateTick=function(t){var e=this.ticks;e[t]?e[t].addLabel():e[t]=new oZ(this,t)},t.prototype.createGroups=function(){var t=this,e=this.axisParent,i=this.chart,o=this.coll,r=this.options,n=i.renderer,s=function(i,s,a){return n.g(i).attr({zIndex:a}).addClass("highcharts-".concat(o.toLowerCase()).concat(s," ")+(t.isRadial?"highcharts-radial-axis".concat(s," "):"")+(r.className||"")).add(e)};this.axisGroup||(this.gridGroup=s("grid","-grid",r.gridZIndex),this.axisGroup=s("axis","",r.zIndex),this.labelGroup=s("axis-labels","-labels",r.labels.zIndex))},t.prototype.getOffset=function(){var t,e,i,o,r=this,n=r.chart,s=r.horiz,a=r.options,h=r.side,l=r.ticks,c=r.tickPositions,d=r.coll,p=n.inverted&&!r.isZAxis?[1,0,3,2][h]:h,u=r.hasData(),f=a.title,g=a.labels,v=ri(a.crossing),m=n.axisOffset,y=n.clipOffset,x=[-1,1,1,-1][h],b=0,k=0,w=0;if(r.showAxis=t=u||a.showEmpty,r.staggerLines=r.horiz&&g.staggerLines||void 0,r.createGroups(),u||r.isLinked?(c.forEach(function(t){r.generateTick(t)}),r.renderUnsquish(),r.reserveSpaceDefault=0===h||2===h||({1:"left",3:"right"})[h]===r.labelAlign,ra(g.reserveSpace,!v&&null,"center"===r.labelAlign||null,r.reserveSpaceDefault)&&c.forEach(function(t){w=Math.max(l[t].getLabelSize(),w)}),r.staggerLines&&(w*=r.staggerLines),r.labelOffset=w*(r.opposite?-1:1)):rs(l,function(t,e){t.destroy(),delete l[e]}),(null==f?void 0:f.text)&&!1!==f.enabled&&(r.addTitle(t),t&&!v&&!1!==f.reserveSpace&&(r.titleOffset=b=r.axisTitle.getBBox()[s?"height":"width"],k=o3(e=f.offset)?0:ra(f.margin,s?5:10))),r.renderLine(),r.offset=x*ra(a.offset,m[h]?m[h]+(a.margin||0):0),r.tickRotCorr=r.tickRotCorr||{x:0,y:0},o=0===h?-r.labelMetrics().h:2===h?r.tickRotCorr.y:0,i=Math.abs(w)+k,w&&(i-=o,i+=x*(s?ra(g.y,r.tickRotCorr.y+x*g.distance):ra(g.x,x*g.distance))),r.axisTitleMargin=ra(e,i),r.getMaxLabelDimensions&&(r.maxLabelDimensions=r.getMaxLabelDimensions(l,c)),"colorAxis"!==d&&y){var M=this.tickSize("tick");m[h]=Math.max(m[h],(r.axisTitleMargin||0)+b+x*r.offset,i,(null==c?void 0:c.length)&&M?M[0]+x*r.offset:0);var S=!r.axisLine||a.offset?0:r.axisLine.strokeWidth()/2;y[p]=Math.max(y[p],S)}o8(this,"afterGetOffset")},t.prototype.getLinePath=function(t){var e=this.chart,i=this.opposite,o=this.offset,r=this.horiz,n=this.left+(i?this.width:0)+o,s=e.chartHeight-this.bottom-(i?this.height:0)+o;return i&&(t*=-1),e.renderer.crispLine([["M",r?this.left:n,r?s:this.top],["L",r?e.chartWidth-this.right:n,r?s:e.chartHeight-this.bottom]],t)},t.prototype.renderLine=function(){this.axisLine||(this.axisLine=this.chart.renderer.path().addClass("highcharts-axis-line").add(this.axisGroup),this.chart.styledMode||this.axisLine.attr({stroke:this.options.lineColor,"stroke-width":this.options.lineWidth,zIndex:7}))},t.prototype.getTitlePosition=function(t){var e=this.horiz,i=this.left,o=this.top,r=this.len,n=this.options.title,s=e?i:o,a=this.opposite,h=this.offset,l=n.x,c=n.y,d=this.chart.renderer.fontMetrics(t),p=t?Math.max(t.getBBox(!1,0).height-d.h-1,0):0,u={low:s+(e?0:r),middle:s+r/2,high:s+(e?r:0)}[n.align],f=(e?o+this.height:i)+(e?1:-1)*(a?-1:1)*(this.axisTitleMargin||0)+[-p,p,d.f,-p][this.side],g={x:e?u+l:f+(a?this.width:0)+h+l,y:e?f+c-(a?this.height:0)+h:u+c};return o8(this,"afterGetTitlePosition",{titlePosition:g}),g},t.prototype.renderMinorTick=function(t,e){var i=this.minorTicks;i[t]||(i[t]=new oZ(this,t,"minor")),e&&i[t].isNew&&i[t].render(null,!0),i[t].render(null,!1,1)},t.prototype.renderTick=function(t,e,i){var o,r=this.isLinked,n=this.ticks;(!r||t>=this.min&&t<=this.max||(null===(o=this.grid)||void 0===o?void 0:o.isColumn))&&(n[t]||(n[t]=new oZ(this,t)),i&&n[t].isNew&&n[t].render(e,!0,-1),n[t].render(e))},t.prototype.render=function(){var t,e,i=this,o=i.chart,r=i.logarithmic,n=o.renderer,s=i.options,a=i.isLinked,h=i.tickPositions,l=i.axisTitle,c=i.ticks,d=i.minorTicks,p=i.alternateBands,u=s.stackLabels,f=s.alternateGridColor,g=s.crossing,v=i.tickmarkOffset,m=i.axisLine,y=i.showAxis,x=ef(n.globalAnimation);if(i.labelEdge.length=0,i.overlap=!1,[c,d,p].forEach(function(t){rs(t,function(t){t.isActive=!1})}),ri(g)){var b=this.isXAxis?o.yAxis[0]:o.xAxis[0],k=[1,-1,-1,1][this.side];if(b){var w=b.toPixels(g,!0);i.horiz&&(w=b.len-w),i.offset=k*w}}if(i.hasData()||a){var M=i.chart.hasRendered&&i.old&&ri(i.old.min);i.minorTickInterval&&!i.categories&&i.getMinorTickPositions().forEach(function(t){i.renderMinorTick(t,M)}),h.length&&(h.forEach(function(t,e){i.renderTick(t,e,M)}),v&&(0===i.min||i.single)&&(c[-1]||(c[-1]=new oZ(i,-1,null,!0)),c[-1].render(-1))),f&&h.forEach(function(n,s){e=void 0!==h[s+1]?h[s+1]+v:i.max-v,s%2==0&&n<i.max&&e<=i.max+(o.polar?-v:v)&&(p[n]||(p[n]=new ti.PlotLineOrBand(i,{})),t=n+v,p[n].options={from:r?r.lin2log(t):t,to:r?r.lin2log(e):e,color:f,className:"highcharts-alternate-grid"},p[n].render(),p[n].isActive=!0)}),i._addedPlotLB||(i._addedPlotLB=!0,(s.plotLines||[]).concat(s.plotBands||[]).forEach(function(t){i.addPlotBandOrLine(t)}))}[c,d,p].forEach(function(t){var e=[],i=x.duration;rs(t,function(t,i){t.isActive||(t.render(i,!1,0),t.isActive=!1,e.push(i))}),rd(function(){for(var i=e.length;i--;)t[e[i]]&&!t[e[i]].isActive&&(t[e[i]].destroy(),delete t[e[i]])},t!==p&&o.hasRendered&&i?i:0)}),m&&(m[m.isPlaced?"animate":"attr"]({d:this.getLinePath(m.strokeWidth())}),m.isPlaced=!0,m[y?"show":"hide"](y)),l&&y&&(l[l.isNew?"attr":"animate"](i.getTitlePosition(l)),l.isNew=!1),(null==u?void 0:u.enabled)&&i.stacking&&i.stacking.renderStackTotals(),i.old={len:i.len,max:i.max,min:i.min,transA:i.transA,userMax:i.userMax,userMin:i.userMin},i.isDirty=!1,o8(this,"afterRender")},t.prototype.redraw=function(){this.visible&&(this.render(),this.plotLinesAndBands.forEach(function(t){t.render()})),this.series.forEach(function(t){t.isDirty=!0})},t.prototype.getKeepProps=function(){return this.keepProps||t.keepProps},t.prototype.destroy=function(t){var e=this,i=e.plotLinesAndBands,o=this.eventOptions;if(o8(this,"destroy",{keepEvents:t}),t||rl(e),[e.ticks,e.minorTicks,e.alternateBands].forEach(function(t){o5(t)}),i)for(var r=i.length;r--;)i[r].destroy();for(var n in["axisLine","axisTitle","axisGroup","gridGroup","labelGroup","cross","scrollbar"].forEach(function(t){e[t]&&(e[t]=e[t].destroy())}),e.plotLinesAndBandsGroups)e.plotLinesAndBandsGroups[n]=e.plotLinesAndBandsGroups[n].destroy();rs(e,function(t,i){-1===e.getKeepProps().indexOf(i)&&delete e[i]}),this.eventOptions=o},t.prototype.drawCrosshair=function(t,e){var i,o,r,n,s,a,h=this.crosshair,l=null===(i=null==h?void 0:h.snap)||void 0===i||i,c=this.chart,d=this.cross;if(o8(this,"drawCrosshair",{e:t,point:e}),t||(t=null===(o=this.cross)||void 0===o?void 0:o.e),h&&!1!==(o3(e)||!l)){if(l?o3(e)&&(n=ra("colorAxis"!==this.coll?e.crosshairPos:null,this.isXAxis?e.plotX:this.len-e.plotY)):n=t&&(this.horiz?t.chartX-this.pos:this.len-t.chartY+this.pos),o3(n)&&(a={value:e&&(this.isXAxis?e.x:ra(e.stackY,e.y)),translatedValue:n},c.polar&&o4(a,{isCrosshair:!0,chartX:null==t?void 0:t.chartX,chartY:null==t?void 0:t.chartY,point:e}),r=this.getPlotLinePath(a)||null),!o3(r)){this.hideCrosshair();return}s=this.categories&&!this.isRadial,d||(this.cross=d=c.renderer.path().addClass("highcharts-crosshair highcharts-crosshair-"+(s?"category ":"thin ")+(h.className||"")).attr({zIndex:ra(h.zIndex,2)}).add(),!c.styledMode&&(d.attr({stroke:h.color||(s?et.parse("#ccd3ff").setOpacity(.25).get():"#cccccc"),"stroke-width":ra(h.width,1)}).css({"pointer-events":"none"}),h.dashStyle&&d.attr({dashstyle:h.dashStyle}))),d.show().attr({d:r}),s&&!h.width&&d.attr({"stroke-width":this.transA}),this.cross.e=t}else this.hideCrosshair();o8(this,"afterDrawCrosshair",{e:t,point:e})},t.prototype.hideCrosshair=function(){this.cross&&this.cross.hide(),o8(this,"afterHideCrosshair")},t.prototype.update=function(t,e){var i=this.chart;t=rr(this.userOptions,t),this.destroy(!0),this.init(i,t),i.isDirtyBox=!0,ra(e,!0)&&i.redraw()},t.prototype.remove=function(t){for(var e=this.chart,i=this.coll,o=this.series,r=o.length;r--;)o[r]&&o[r].remove(!1);o6(e.axes,this),o6(e[i]||[],this),e.orderItems(i),this.destroy(),e.isDirtyBox=!0,ra(t,!0)&&e.redraw()},t.prototype.setTitle=function(t,e){this.update({title:t},e)},t.prototype.setCategories=function(t,e){this.update({categories:t},e)},t.keepProps=["coll","extKey","hcEvents","len","names","series","userMax","userMin"],t}(),rf=tP.addEvent,rg=tP.getMagnitude,rv=tP.normalizeTickInterval,rm=tP.timeUnits;!function(t){function e(){return this.chart.time.getTimeTicks.apply(this.chart.time,arguments)}function i(){if("datetime"!==this.type){this.dateTime=void 0;return}this.dateTime||(this.dateTime=new o(this))}t.compose=function(t){return t.keepProps.includes("dateTime")||(t.keepProps.push("dateTime"),t.prototype.getTimeTicks=e,rf(t,"afterSetType",i)),t};var o=function(){function t(t){this.axis=t}return t.prototype.normalizeTimeTickInterval=function(t,e){var i,o=e||[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2]],["week",[1,2]],["month",[1,2,3,4,6]],["year",null]],r=o[o.length-1],n=rm[r[0]],s=r[1];for(i=0;i<o.length&&(n=rm[(r=o[i])[0]],s=r[1],!o[i+1]||!(t<=(n*s[s.length-1]+rm[o[i+1][0]])/2));i++);n===rm.year&&t<5*n&&(s=[1,2,5]);var a=rv(t/n,s,"year"===r[0]?Math.max(rg(t/n),1):1);return{unitRange:n,count:a,unitName:r[0]}},t.prototype.getXDateFormat=function(t,e){var i=this.axis,o=i.chart.time;return i.closestPointRange?o.getDateFormat(i.closestPointRange,t,i.options.startOfWeek,e)||o.resolveDTLFormat(e.year).main:o.resolveDTLFormat(e.day).main},t}();t.Additions=o}(X||(X={}));var ry=X,rx=tP.addEvent,rb=tP.normalizeTickInterval,rk=tP.pick;!function(t){function e(){var t;"logarithmic"!==this.type?this.logarithmic=void 0:null!==(t=this.logarithmic)&&void 0!==t||(this.logarithmic=new o(this))}function i(){var t=this.logarithmic;t&&(this.lin2val=function(e){return t.lin2log(e)},this.val2lin=function(e){return t.log2lin(e)})}t.compose=function(t){return t.keepProps.includes("logarithmic")||(t.keepProps.push("logarithmic"),rx(t,"afterSetType",e),rx(t,"afterInit",i)),t};var o=function(){function t(t){this.axis=t}return t.prototype.getLogTickPositions=function(t,e,i,o){var r=this.axis,n=r.len,s=r.options,a=[];if(o||(this.minorAutoInterval=void 0),t>=.5)t=Math.round(t),a=r.getLinearTickPositions(t,e,i);else if(t>=.08){var h=Math.floor(e),l=void 0,c=void 0,d=void 0,p=void 0,u=void 0,f=void 0,g=void 0;for(l=t>.3?[1,2,4]:t>.15?[1,2,4,6,8]:[1,2,3,4,5,6,7,8,9],c=h;c<i+1&&!g;c++)for(d=0,p=l.length;d<p&&!g;d++)(u=this.log2lin(this.lin2log(c)*l[d]))>e&&(!o||f<=i)&&void 0!==f&&a.push(f),f>i&&(g=!0),f=u}else{var v=this.lin2log(e),m=this.lin2log(i),y=o?r.getMinorTickInterval():s.tickInterval,x=s.tickPixelInterval/(o?5:1),b=o?n/r.tickPositions.length:n;t=rb(t=rk("auto"===y?null:y,this.minorAutoInterval,(m-v)*x/(b||1))),a=r.getLinearTickPositions(t,v,m).map(this.log2lin),o||(this.minorAutoInterval=t/5)}return o||(r.tickInterval=t),a},t.prototype.lin2log=function(t){return Math.pow(10,t)},t.prototype.log2lin=function(t){return Math.log(t)/Math.LN10},t}();t.Additions=o}(H||(H={}));var rw=H,rM=tP.erase,rS=tP.extend,rT=tP.isNumber;!function(t){var e;function i(t){return this.addPlotBandOrLine(t,"plotBands")}function o(t,i){var o=this,r=this.userOptions,n=new e(this,t);if(this.visible&&(n=n.render()),n){if(this._addedPlotLB||(this._addedPlotLB=!0,(r.plotLines||[]).concat(r.plotBands||[]).forEach(function(t){o.addPlotBandOrLine(t)})),i){var s=r[i]||[];s.push(t),r[i]=s}this.plotLinesAndBands.push(n)}return n}function r(t){return this.addPlotBandOrLine(t,"plotLines")}function n(t,e,i){i=i||this.options;var o,r,n=this.getPlotLinePath({value:e,force:!0,acrossPanes:i.acrossPanes}),s=[],a=this.horiz,h=!rT(this.min)||!rT(this.max)||t<this.min&&e<this.min||t>this.max&&e>this.max,l=this.getPlotLinePath({value:t,force:!0,acrossPanes:i.acrossPanes}),c=1;if(l&&n)for(h&&(r=l.toString()===n.toString(),c=0),o=0;o<l.length;o+=2){var d=l[o],p=l[o+1],u=n[o],f=n[o+1];("M"===d[0]||"L"===d[0])&&("M"===p[0]||"L"===p[0])&&("M"===u[0]||"L"===u[0])&&("M"===f[0]||"L"===f[0])&&(a&&u[1]===d[1]?(u[1]+=c,f[1]+=c):a||u[2]!==d[2]||(u[2]+=c,f[2]+=c),s.push(["M",d[1],d[2]],["L",p[1],p[2]],["L",f[1],f[2]],["L",u[1],u[2]],["Z"])),s.isFlat=r}return s}function s(t){this.removePlotBandOrLine(t)}function a(t){var e=this.plotLinesAndBands,i=this.options,o=this.userOptions;if(e){for(var r=e.length;r--;)e[r].id===t&&e[r].destroy();[i.plotLines||[],o.plotLines||[],i.plotBands||[],o.plotBands||[]].forEach(function(e){var i;for(r=e.length;r--;)(null===(i=e[r])||void 0===i?void 0:i.id)===t&&rM(e,e[r])})}}function h(t){this.removePlotBandOrLine(t)}t.compose=function(t,l){var c=l.prototype;return c.addPlotBand||(e=t,rS(c,{addPlotBand:i,addPlotLine:r,addPlotBandOrLine:o,getPlotBandPath:n,removePlotBand:s,removePlotLine:h,removePlotBandOrLine:a})),l}}(j||(j={}));var rC=j,rA=function(){return(rA=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},rP=tP.addEvent,rO=tP.arrayMax,rL=tP.arrayMin,rE=tP.defined,rI=tP.destroyObjectProperties,rD=tP.erase,rB=tP.fireEvent,rN=tP.merge,rz=tP.objectEach,rR=tP.pick,rW=function(){function t(t,e){this.axis=t,this.options=e,this.id=e.id}return t.compose=function(e,i){return rP(e,"afterInit",function(){var t=this;this.labelCollectors.push(function(){for(var e,i=[],o=0,r=t.axes;o<r.length;o++)for(var n=r[o],s=0,a=n.plotLinesAndBands;s<a.length;s++){var h=a[s],l=h.label,c=h.options;!l||(null===(e=null==c?void 0:c.label)||void 0===e?void 0:e.allowOverlap)||i.push(l)}return i})}),rC.compose(t,i)},t.prototype.render=function(){var t=this;rB(this,"render");var e,i,o,r,n=this.axis,s=this.options,a=n.horiz,h=n.logarithmic,l=s.color,c=s.events,d=s.zIndex,p=void 0===d?0:d,u=n.chart,f=u.renderer,g=u.time,v={},m=g.parse(s.to),y=g.parse(s.from),x=g.parse(s.value),b=s.borderWidth,k=s.label,w=this.label,M=this.svgElem,S=[],T=rE(y)&&rE(m),C=rE(x),A=!M,P={class:"highcharts-plot-"+(T?"band ":"line ")+(s.className||"")},O=T?"bands":"lines";if(!n.chart.styledMode&&(C?(P.stroke=l||"#999999",P["stroke-width"]=rR(s.width,1),s.dashStyle&&(P.dashstyle=s.dashStyle)):T&&(P.fill=l||"#e6e9ff",b&&(P.stroke=s.borderColor,P["stroke-width"]=b))),v.zIndex=p,O+="-"+p,(r=n.plotLinesAndBandsGroups[O])||(n.plotLinesAndBandsGroups[O]=r=f.g("plot-"+O).attr(v).add()),M||(this.svgElem=M=f.path().attr(P).add(r)),rE(x))S=n.getPlotLinePath({value:null!==(e=null==h?void 0:h.log2lin(x))&&void 0!==e?e:x,lineWidth:M.strokeWidth(),acrossPanes:s.acrossPanes});else{if(!(rE(y)&&rE(m)))return;S=n.getPlotBandPath(null!==(i=null==h?void 0:h.log2lin(y))&&void 0!==i?i:y,null!==(o=null==h?void 0:h.log2lin(m))&&void 0!==o?o:m,s)}return!this.eventsAdded&&c&&(rz(c,function(e,i){null==M||M.on(i,function(e){c[i].apply(t,[e])})}),this.eventsAdded=!0),(A||!M.d)&&(null==S?void 0:S.length)?M.attr({d:S}):M&&(S?(M.show(),M.animate({d:S})):M.d&&(M.hide(),w&&(this.label=w=w.destroy()))),k&&(rE(k.text)||rE(k.formatter))&&(null==S?void 0:S.length)&&n.width>0&&n.height>0&&!S.isFlat?(k=rN(rA({align:a&&T?"center":void 0,x:a?!T&&4:10,verticalAlign:!a&&T?"middle":void 0,y:a?T?16:10:T?6:-4,rotation:a&&!T?90:0},T?{inside:!0}:{}),k),this.renderLabel(k,S,T,p)):w&&w.hide(),this},t.prototype.renderLabel=function(t,e,i,o){var r,n=this.axis,s=n.chart.renderer,a=t.inside,h=this.label;h||(this.label=h=s.text(this.getLabelText(t),0,0,t.useHTML).attr({align:t.textAlign||t.align,rotation:t.rotation,class:"highcharts-plot-"+(i?"band":"line")+"-label "+(t.className||""),zIndex:o}),n.chart.styledMode||h.css(rN({fontSize:"0.8em",textOverflow:i&&!a?"":"ellipsis"},t.style)),h.add());var l=e.xBounds||[e[0][1],e[1][1],i?e[2][1]:e[0][1]],c=e.yBounds||[e[0][2],e[1][2],i?e[2][2]:e[0][2]],d=rL(l),p=rL(c),u=rO(l)-d;h.align(t,!1,{x:d,y:p,width:u,height:rO(c)-p}),h.alignAttr.y-=s.fontMetrics(h).b,(!h.alignValue||"left"===h.alignValue||rE(a))&&h.css({width:((null===(r=t.style)||void 0===r?void 0:r.width)||(i&&a?u:90===h.rotation?n.height-(h.alignAttr.y-n.top):(t.clip?n.width:n.chart.chartWidth)-(h.alignAttr.x-n.left)))+"px"}),h.show(!0)},t.prototype.getLabelText=function(t){return rE(t.formatter)?t.formatter.call(this):t.text},t.prototype.destroy=function(){rD(this.axis.plotLinesAndBands,this),delete this.axis,rI(this)},t}(),rX=eU.format,rH=ti.composed,rj=ti.dateFormats,rF=ti.doc,rY=ti.isSafari,rG=eJ.distribute,r_=tP.addEvent,rU=tP.clamp,rV=tP.css,rZ=tP.discardElement,rq=tP.extend,rK=tP.fireEvent,r$=tP.getAlignFactor,rJ=tP.isArray,rQ=tP.isNumber,r0=tP.isObject,r1=tP.isString,r2=tP.merge,r3=tP.pick,r5=tP.pushUnique,r6=tP.splat,r9=tP.syncTimeout,r4=function(){function t(t,e,i){this.allowShared=!0,this.crosshairs=[],this.distance=0,this.isHidden=!0,this.isSticky=!1,this.options={},this.outside=!1,this.chart=t,this.init(t,e),this.pointer=i}return t.prototype.bodyFormatter=function(t){return t.map(function(t){var e=t.series.tooltipOptions,i=t.formatPrefix||"point";return(e[i+"Formatter"]||t.tooltipFormatter).call(t,e[i+"Format"]||"")})},t.prototype.cleanSplit=function(t){this.chart.series.forEach(function(e){var i=null==e?void 0:e.tt;i&&(!i.isActive||t?e.tt=i.destroy():i.isActive=!1)})},t.prototype.defaultFormatter=function(t){var e,i=this.points||r6(this);return(e=(e=[t.headerFooterFormatter(i[0])]).concat(t.bodyFormatter(i))).push(t.headerFooterFormatter(i[0],!0)),e},t.prototype.destroy=function(){this.label&&(this.label=this.label.destroy()),this.split&&(this.cleanSplit(!0),this.tt&&(this.tt=this.tt.destroy())),this.renderer&&(this.renderer=this.renderer.destroy(),rZ(this.container)),tP.clearTimeout(this.hideTimer)},t.prototype.getAnchor=function(t,e){var i,o,r=this.chart,n=this.pointer,s=r.inverted,a=r.plotTop,h=r.plotLeft;if((null===(i=(t=r6(t))[0].series)||void 0===i?void 0:i.yAxis)&&!t[0].series.yAxis.options.reversedStacks&&(t=t.slice().reverse()),this.followPointer&&e)void 0===e.chartX&&(e=n.normalize(e)),o=[e.chartX-h,e.chartY-a];else if(t[0].tooltipPos)o=t[0].tooltipPos;else{var l=0,c=0;t.forEach(function(t){var e=t.pos(!0);e&&(l+=e[0],c+=e[1])}),l/=t.length,c/=t.length,this.shared&&t.length>1&&e&&(s?l=e.chartX:c=e.chartY),o=[l-h,c-a]}return o.map(Math.round)},t.prototype.getClassName=function(t,e,i){var o=this.options,r=t.series,n=r.options;return[o.className,"highcharts-label",i&&"highcharts-tooltip-header",e?"highcharts-tooltip-box":"highcharts-tooltip",!i&&"highcharts-color-"+r3(t.colorIndex,r.colorIndex),null==n?void 0:n.className].filter(r1).join(" ")},t.prototype.getLabel=function(t){var e,i=void 0===t?{anchorX:0,anchorY:0}:t,o=i.anchorX,r=i.anchorY,n=this,s=this.chart.styledMode,a=this.options,h=this.split&&this.allowShared,l=this.container,c=this.chart.renderer;if(this.label){var d=!this.label.hasClass("highcharts-label");(!h&&d||h&&!d)&&this.destroy()}if(!this.label){if(this.outside){var p=this.chart,u=p.options.chart.style,f=eV.getRendererType();this.container=l=ti.doc.createElement("div"),l.className="highcharts-tooltip-container "+(p.renderTo.className.match(/(highcharts[a-zA-Z0-9-]+)\s?/gm)||""),rV(l,{position:"absolute",top:"1px",pointerEvents:"none",zIndex:Math.max(this.options.style.zIndex||0,((null==u?void 0:u.zIndex)||0)+3)}),this.renderer=c=new f(l,0,0,u,void 0,void 0,c.styledMode)}if(h?this.label=c.g("tooltip"):(this.label=c.label("",o,r,a.shape||"callout",void 0,void 0,a.useHTML,void 0,"tooltip").attr({padding:a.padding,r:a.borderRadius}),s||this.label.attr({fill:a.backgroundColor,"stroke-width":a.borderWidth||0}).css(a.style).css({pointerEvents:a.style.pointerEvents||(this.shouldStickOnContact()?"auto":"none")})),n.outside){var g=this.label;[g.xSetter,g.ySetter].forEach(function(t,e){g[e?"ySetter":"xSetter"]=function(i){t.call(g,n.distance),g[e?"y":"x"]=i,l&&(l.style[e?"top":"left"]=""+i+"px")}})}this.label.attr({zIndex:8}).shadow(null!==(e=a.shadow)&&void 0!==e?e:!a.fixed).add()}return l&&!l.parentElement&&ti.doc.body.appendChild(l),this.label},t.prototype.getPlayingField=function(){var t=rF.body,e=rF.documentElement,i=this.chart,o=this.distance,r=this.outside;return{width:r?Math.max(t.scrollWidth,e.scrollWidth,t.offsetWidth,e.offsetWidth,e.clientWidth)-2*o-2:i.chartWidth,height:r?Math.max(t.scrollHeight,e.scrollHeight,t.offsetHeight,e.offsetHeight,e.clientHeight):i.chartHeight}},t.prototype.getPosition=function(t,e,i){var o,r,n,s=this.distance,a=this.chart,h=this.outside,l=this.pointer,c=a.inverted,d=a.plotLeft,p=a.plotTop,u=a.polar,f=i.plotX,g=void 0===f?0:f,v=i.plotY,m=void 0===v?0:v,y={},x=c&&i.h||0,b=this.getPlayingField(),k=b.height,w=b.width,M=l.getChartPosition(),S=function(t){return t*M.scaleX},T=function(t){return t*M.scaleY},C=function(i){var o="x"===i;return[i,o?w:k,o?t:e].concat(h?[o?S(t):T(e),o?M.left-s+S(g+d):M.top-s+T(m+p),0,o?w:k]:[o?t:e,o?g+d:m+p,o?d:p,o?d+a.plotWidth:p+a.plotHeight])},A=C("y"),P=C("x"),O=!!i.negative;!u&&(null===(r=null===(o=a.hoverSeries)||void 0===o?void 0:o.yAxis)||void 0===r?void 0:r.reversed)&&(O=!O);var L=!this.followPointer&&r3(i.ttBelow,!u&&!c===O),E=function(t,e,i,o,r,n,a){var l=h?"y"===t?T(s):S(s):s,c=(i-o)/2,d=o<r-s,p=r+s+o<e,u=r-l-i+c,f=r+l-c;if(L&&p)y[t]=f;else if(!L&&d)y[t]=u;else if(d)y[t]=Math.min(a-o,u-x<0?u:u-x);else{if(!p)return y[t]=0,!1;y[t]=Math.max(n,f+x+i>e?f:f+x)}},I=function(t,e,i,o,r){if(r<s||r>e-s)return!1;r<i/2?y[t]=1:r>e-o/2?y[t]=e-o-2:y[t]=r-i/2},D=function(t){var e;A=(e=[P,A])[0],P=e[1],n=t},B=function(){!1!==E.apply(0,A)?!1!==I.apply(0,P)||n||(D(!0),B()):n?y.x=y.y=0:(D(!0),B())};return(c&&!u||this.len>1)&&D(),B(),y},t.prototype.getFixedPosition=function(t,e,i){var o,r=i.series,n=this.chart,s=this.options,a=this.split,h=s.position,l=h.relativeTo,c=s.shared||(null===(o=null==r?void 0:r.yAxis)||void 0===o?void 0:o.isRadial)&&("pane"===l||!l)?"plotBox":l,d="chart"===c?n.renderer:n[c]||n.getClipBox(r,!0);return{x:d.x+(d.width-t)*r$(h.align)+h.x,y:d.y+(d.height-e)*r$(h.verticalAlign)+(!a&&h.y||0)}},t.prototype.hide=function(t){var e=this;tP.clearTimeout(this.hideTimer),t=r3(t,this.options.hideDelay),this.isHidden||(this.hideTimer=r9(function(){var i=e.getLabel();e.getLabel().animate({opacity:0},{duration:t?150:t,complete:function(){i.hide(),e.container&&e.container.remove()}}),e.isHidden=!0},t))},t.prototype.init=function(t,e){this.chart=t,this.options=e,this.crosshairs=[],this.isHidden=!0,this.split=e.split&&!t.inverted&&!t.polar,this.shared=e.shared||this.split,this.outside=r3(e.outside,!!(t.scrollablePixelsX||t.scrollablePixelsY))},t.prototype.shouldStickOnContact=function(t){return!!(!this.followPointer&&this.options.stickOnContact&&(!t||this.pointer.inClass(t.target,"highcharts-tooltip")))},t.prototype.move=function(t,e,i,o){var r=this,n=this.followPointer,s=this.options,a=ef(!n&&!this.isHidden&&!s.fixed&&s.animation),h=n||(this.len||0)>1,l={x:t,y:e};h?l.anchorX=l.anchorY=NaN:(l.anchorX=i,l.anchorY=o),a.step=function(){return r.drawTracker()},this.getLabel().animate(l,a)},t.prototype.refresh=function(t,e){var i=this.chart,o=this.options,r=this.pointer,n=this.shared,s=r6(t),a=s[0],h=o.format,l=o.formatter||this.defaultFormatter,c=i.styledMode,d=this.allowShared;if(o.enabled&&a.series){tP.clearTimeout(this.hideTimer),this.allowShared=!(!rJ(t)&&t.series&&t.series.noSharedTooltip),d=d&&!this.allowShared,this.followPointer=!this.split&&a.series.tooltipOptions.followPointer;var p=this.getAnchor(t,e),u=p[0],f=p[1];n&&this.allowShared&&(r.applyInactiveState(s),s.forEach(function(t){return t.setState("hover")}),a.points=s),this.len=s.length;var g=r1(h)?rX(h,a,i):l.call(a,this);a.points=void 0;var v=a.series;if(this.distance=r3(v.tooltipOptions.distance,16),!1===g)this.hide();else{if(this.split&&this.allowShared)this.renderSplit(g,s);else{var m=u,y=f;if(e&&r.isDirectTouch&&(m=e.chartX-i.plotLeft,y=e.chartY-i.plotTop),i.polar||!1===v.options.clip||s.some(function(t){return r.isDirectTouch||t.series.shouldShowTooltip(m,y)})){var x=this.getLabel(d&&this.tt||{});(!o.style.width||c)&&x.css({width:(this.outside?this.getPlayingField():i.spacingBox).width+"px"}),x.attr({class:this.getClassName(a),text:g&&g.join?g.join(""):g}),this.outside&&x.attr({x:rU(x.x||0,0,this.getPlayingField().width-(x.width||0)-1)}),c||x.attr({stroke:o.borderColor||a.color||v.color||"#666666"}),this.updatePosition({plotX:u,plotY:f,negative:a.negative,ttBelow:a.ttBelow,series:v,h:p[2]||0})}else{this.hide();return}}this.isHidden&&this.label&&this.label.attr({opacity:1}).show(),this.isHidden=!1}rK(this,"refresh")}},t.prototype.renderSplit=function(t,e){var i,o,r=this,n=this,s=n.chart,a=n.chart,h=a.chartWidth,l=a.chartHeight,c=a.plotHeight,d=a.plotLeft,p=a.plotTop,u=a.scrollablePixelsY,f=a.scrollablePixelsX,g=a.styledMode,v=n.distance,m=n.options,y=n.options,x=y.fixed,b=y.position,k=y.positioner,w=n.pointer,M=(null===(i=s.scrollablePlotArea)||void 0===i?void 0:i.scrollingContainer)||{},S=M.scrollLeft,T=void 0===S?0:S,C=M.scrollTop,A=void 0===C?0:C,P=n.outside&&"number"!=typeof f?rF.documentElement.getBoundingClientRect():{left:T,right:T+h,top:A,bottom:A+l},O=n.getLabel(),L=this.renderer||s.renderer,E=!!(null===(o=s.xAxis[0])||void 0===o?void 0:o.opposite),I=w.getChartPosition(),D=I.left,B=I.top,N=k||x,z=p+A,R=0,W=c-(void 0===u?0:u),X=function(t,e,i,o,r){if(void 0===o&&(o=[0,0]),void 0===r&&(r=!0),i.isHeader)a=E?0:W,s=rU(o[0]-t/2,P.left,P.right-t-(n.outside?D:0));else if(x&&i){var s,a,h=n.getFixedPosition(t,e,i);s=h.x,a=h.y-z}else a=o[1]-z,s=rU(s=r?o[0]-t-v:o[0]+v,r?s:P.left,P.right);return{x:s,y:a}};r1(t)&&(t=[!1,t]);var H=t.slice(0,e.length+1).reduce(function(t,i,o){if(!1!==i&&""!==i){var r=e[o-1]||{isHeader:!0,plotX:e[0].plotX,plotY:c,series:{}},s=r.isHeader,a=s?n:r.series,h=a.tt=function(t,e,i){var o,r=t,s=e.isHeader,a=e.series,h=a.tooltipOptions||m;if(!r){var l={padding:h.padding,r:h.borderRadius};g||(l.fill=h.backgroundColor,l["stroke-width"]=null!==(o=h.borderWidth)&&void 0!==o?o:x&&!s?0:1),r=L.label("",0,0,h[s?"headerShape":"shape"]||(x&&!s?"rect":"callout"),void 0,void 0,h.useHTML).addClass(n.getClassName(e,!0,s)).attr(l).add(O)}return r.isActive=!0,r.attr({text:i}),g||r.css(h.style).attr({stroke:h.borderColor||e.color||a.color||"#333333"}),r}(a.tt,r,i.toString()),l=h.getBBox(),u=l.width+h.strokeWidth();s&&(R=l.height,W+=R,E&&(z-=R));var f=function(t){var e,i,o=t.isHeader,r=t.plotX,n=void 0===r?0:r,s=t.plotY,a=void 0===s?0:s,h=t.series;if(o)e=Math.max(d+n,d),i=p+c/2;else{var l=h.xAxis,u=h.yAxis;e=l.pos+rU(n,-v,l.len+v),h.shouldShowTooltip(0,u.pos-p+a,{ignoreX:!0})&&(i=u.pos+a)}return{anchorX:e=rU(e,P.left-v,P.right+v),anchorY:i}}(r),y=f.anchorX,b=f.anchorY;if("number"==typeof b){var w=l.height+1,M=(k||X).call(n,u,w,r,[y,b]);t.push({align:N?0:void 0,anchorX:y,anchorY:b,boxWidth:u,point:r,rank:r3(M.rank,+!!s),size:w,target:M.y,tt:h,x:M.x})}else h.isActive=!1}return t},[]);!N&&H.some(function(t){var e=(n.outside?D:0)+t.anchorX;return e<P.left&&e+t.boxWidth<P.right||e<D-P.left+t.boxWidth&&P.right-e>e})&&(H=H.map(function(t){var e=X.call(r,t.boxWidth,t.size,t.point,[t.anchorX,t.anchorY],!1),i=e.x;return rq(t,{target:e.y,x:i})})),n.cleanSplit(),rG(H,W);var j={left:D,right:D};H.forEach(function(t){var e=t.x,i=t.boxWidth,o=t.isHeader;!o&&(n.outside&&D+e<j.left&&(j.left=D+e),!o&&n.outside&&j.left+i>j.right&&(j.right=D+e))}),H.forEach(function(t){var e=t.x,i=t.anchorX,o=t.anchorY,r=t.pos,s=t.point.isHeader,a={visibility:void 0===r?"hidden":"inherit",x:e,y:(r||0)+z+(x&&b.y||0),anchorX:i,anchorY:o};if(n.outside&&e<i){var h=D-j.left;h>0&&(s||(a.x=e+h,a.anchorX=i+h),s&&(a.x=(j.right-j.left)/2,a.anchorX=i+h))}t.tt.attr(a)});var F=n.container,Y=n.outside,G=n.renderer;if(Y&&F&&G){var _=O.getBBox(),U=_.width,V=_.height,Z=_.x,q=_.y;G.setSize(U+Z,V+q,!1),F.style.left=j.left+"px",F.style.top=B+"px"}rY&&O.attr({opacity:1===O.opacity?.999:1})},t.prototype.drawTracker=function(){if(!this.shouldStickOnContact()){this.tracker&&(this.tracker=this.tracker.destroy());return}var t=this.chart,e=this.label,i=this.shared?t.hoverPoints:t.hoverPoint;if(e&&i){var o={x:0,y:0,width:0,height:0},r=this.getAnchor(i),n=e.getBBox();r[0]+=t.plotLeft-(e.translateX||0),r[1]+=t.plotTop-(e.translateY||0),o.x=Math.min(0,r[0]),o.y=Math.min(0,r[1]),o.width=r[0]<0?Math.max(Math.abs(r[0]),n.width-r[0]):Math.max(Math.abs(r[0]),n.width),o.height=r[1]<0?Math.max(Math.abs(r[1]),n.height-Math.abs(r[1])):Math.max(Math.abs(r[1]),n.height),this.tracker?this.tracker.attr(o):(this.tracker=e.renderer.rect(o).addClass("highcharts-tracker").add(e),t.styledMode||this.tracker.attr({fill:"rgba(0,0,0,0)"}))}},t.prototype.styledModeFormat=function(t){return t.replace('style="font-size: 0.8em"','class="highcharts-header"').replace(/style="color:{(point|series)\.color}"/g,'class="highcharts-color-{$1.colorIndex} {series.options.className} {point.options.className}"')},t.prototype.headerFooterFormatter=function(t,e){var i=t.series,o=i.tooltipOptions,r=i.xAxis,n=null==r?void 0:r.dateTime,s={isFooter:e,point:t},a=o.xDateFormat||"",h=o[e?"footerFormat":"headerFormat"];return rK(this,"headerFormatter",s,function(e){if(n&&!a&&rQ(t.key)&&(a=n.getXDateFormat(t.key,o.dateTimeLabelFormats)),n&&a){if(r0(a)){var r=a;rj[0]=function(t){return i.chart.time.dateFormat(r,t)},a="%0"}(t.tooltipDateKeys||["key"]).forEach(function(t){h=h.replace(RegExp("point\\."+t+"([ \\)}])"),"(point.".concat(t,":").concat(a,")$1"))})}i.chart.styledMode&&(h=this.styledModeFormat(h)),e.text=rX(h,t,this.chart)}),s.text||""},t.prototype.update=function(t){this.destroy(),this.init(this.chart,r2(!0,this.options,t))},t.prototype.updatePosition=function(t){var e,i,o=this.chart,r=this.container,n=this.distance,s=this.options,a=this.pointer,h=this.renderer,l=this.getLabel(),c=l.height,d=void 0===c?0:c,p=l.width,u=void 0===p?0:p,f=s.fixed,g=s.positioner,v=a.getChartPosition(),m=v.left,y=v.top,x=v.scaleX,b=v.scaleY,k=(g||f&&this.getFixedPosition||this.getPosition).call(this,u,d,t),w=ti.doc,M=(t.plotX||0)+o.plotLeft,S=(t.plotY||0)+o.plotTop;if(h&&r){if(g||f){var T=(null===(e=o.scrollablePlotArea)||void 0===e?void 0:e.scrollingContainer)||{},C=T.scrollLeft,A=T.scrollTop;k.x+=(void 0===C?0:C)+m-n,k.y+=(void 0===A?0:A)+y-n}i=(s.borderWidth||0)+2*n+2,h.setSize(rU(u+i,0,w.documentElement.clientWidth)-1,d+i,!1),(1!==x||1!==b)&&(rV(r,{transform:"scale(".concat(x,", ").concat(b,")")}),M*=x,S*=b),M+=m-k.x,S+=y-k.y}this.move(Math.round(k.x),Math.round(k.y||0),M,S)},t}();(f=r4||(r4={})).compose=function(t){r5(rH,"Core.Tooltip")&&r_(t,"afterInit",function(){var t=this.chart;t.options.tooltip&&(t.tooltip=new f(t,t.options.tooltip,this))})};var r8=r4,r7=eU.format,nt=tP.addEvent,ne=tP.crisp,ni=tP.erase,no=tP.extend,nr=tP.fireEvent,nn=tP.getNestedProperty,ns=tP.isArray,na=tP.isFunction,nh=tP.isNumber,nl=tP.isObject,nc=tP.merge,nd=tP.pick,np=tP.syncTimeout,nu=tP.removeEvent,nf=tP.uniqueKey,ng=function(){function t(t,e,i){var o,r;this.formatPrefix="point",this.visible=!0,this.point=this,this.series=t,this.applyOptions(e,i),null!==(o=this.id)&&void 0!==o||(this.id=nf()),this.resolveColor(),null!==(r=this.dataLabelOnNull)&&void 0!==r||(this.dataLabelOnNull=t.options.nullInteraction),t.chart.pointCount++,nr(this,"afterInit")}return t.prototype.animateBeforeDestroy=function(){var t=this,e={x:t.startXPos,opacity:0},i=t.getGraphicalProps();i.singular.forEach(function(i){t[i]=t[i].animate("dataLabel"===i?{x:t[i].startXPos,y:t[i].startYPos,opacity:0}:e)}),i.plural.forEach(function(e){t[e].forEach(function(e){e.element&&e.animate(no({x:t.startXPos},e.startYPos?{x:e.startXPos,y:e.startYPos}:{}))})})},t.prototype.applyOptions=function(e,i){var o=this.series,r=o.options.pointValKey||o.pointValKey;return no(this,e=t.prototype.optionsToObject.call(this,e)),this.options=this.options?no(this.options,e):e,e.group&&delete this.group,e.dataLabels&&delete this.dataLabels,r&&(this.y=t.prototype.getNestedProperty.call(this,r)),this.selected&&(this.state="select"),"name"in this&&void 0===i&&o.xAxis&&o.xAxis.hasNames&&(this.x=o.xAxis.nameToX(this)),void 0===this.x&&o?this.x=null!=i?i:o.autoIncrement():nh(e.x)&&o.options.relativeXValue?this.x=o.autoIncrement(e.x):"string"==typeof this.x&&(null!=i||(i=o.chart.time.parse(this.x)),nh(i)&&(this.x=i)),this.isNull=this.isValid&&!this.isValid(),this.formatPrefix=this.isNull?"null":"point",this},t.prototype.destroy=function(){if(!this.destroyed){var t=this,e=t.series,i=e.chart,o=e.options.dataSorting,r=i.hoverPoints,n=ef(t.series.chart.renderer.globalAnimation),s=function(){for(var e in(t.graphic||t.graphics||t.dataLabel||t.dataLabels)&&(nu(t),t.destroyElements()),t)delete t[e]};t.legendItem&&i.legend.destroyItem(t),r&&(t.setState(),ni(r,t),r.length||(i.hoverPoints=null)),t===i.hoverPoint&&t.onMouseOut(),(null==o?void 0:o.enabled)?(this.animateBeforeDestroy(),np(s,n.duration)):s(),i.pointCount--}this.destroyed=!0},t.prototype.destroyElements=function(t){var e=this,i=e.getGraphicalProps(t);i.singular.forEach(function(t){e[t]=e[t].destroy()}),i.plural.forEach(function(t){e[t].forEach(function(t){(null==t?void 0:t.element)&&t.destroy()}),delete e[t]})},t.prototype.firePointEvent=function(t,e,i){var o=this,r=this.series.options;o.manageEvent(t),"click"===t&&r.allowPointSelect&&(i=function(t){!o.destroyed&&o.select&&o.select(null,t.ctrlKey||t.metaKey||t.shiftKey)}),nr(o,t,e,i)},t.prototype.getClassName=function(){var t;return"highcharts-point"+(this.selected?" highcharts-point-select":"")+(this.negative?" highcharts-negative":"")+(this.isNull?" highcharts-null-point":"")+(void 0!==this.colorIndex?" highcharts-color-"+this.colorIndex:"")+(this.options.className?" "+this.options.className:"")+((null===(t=this.zone)||void 0===t?void 0:t.className)?" "+this.zone.className.replace("highcharts-negative",""):"")},t.prototype.getGraphicalProps=function(t){var e,i,o=this,r=[],n={singular:[],plural:[]};for((t=t||{graphic:1,dataLabel:1}).graphic&&r.push("graphic","connector"),t.dataLabel&&r.push("dataLabel","dataLabelPath","dataLabelUpper"),i=r.length;i--;)o[e=r[i]]&&n.singular.push(e);return["graphic","dataLabel"].forEach(function(e){var i=e+"s";t[e]&&o[i]&&n.plural.push(i)}),n},t.prototype.getNestedProperty=function(t){return t?0===t.indexOf("custom.")?nn(t,this.options):this[t]:void 0},t.prototype.getZone=function(){var t,e=this.series,i=e.zones,o=e.zoneAxis||"y",r=0;for(t=i[0];this[o]>=t.value;)t=i[++r];return this.nonZonedColor||(this.nonZonedColor=this.color),(null==t?void 0:t.color)&&!this.options.color?this.color=t.color:this.color=this.nonZonedColor,t},t.prototype.hasNewShapeType=function(){return(this.graphic&&(this.graphic.symbolName||this.graphic.element.nodeName))!==this.shapeType},t.prototype.isValid=function(){return(nh(this.x)||this.x instanceof Date)&&nh(this.y)},t.prototype.optionsToObject=function(e){var i,o,r=this.series,n=r.options.keys,s=n||r.pointArrayMap||["y"],a=s.length,h={},l=0,c=0;if(nh(e)||null===e)h[s[0]]=e;else if(ns(e))for(!n&&e.length>a&&("string"==(o=typeof e[0])?(null===(i=r.xAxis)||void 0===i?void 0:i.dateTime)?h.x=r.chart.time.parse(e[0]):h.name=e[0]:"number"===o&&(h.x=e[0]),l++);c<a;)n&&void 0===e[l]||(s[c].indexOf(".")>0?t.prototype.setNestedProperty(h,e[l],s[c]):h[s[c]]=e[l]),l++,c++;else"object"==typeof e&&(h=e,e.dataLabels&&(r.hasDataLabels=function(){return!0}),e.marker&&(r._hasPointMarkers=!0));return h},t.prototype.pos=function(t,e){if(void 0===e&&(e=this.plotY),!this.destroyed){var i=this.plotX,o=this.series,r=o.chart,n=o.xAxis,s=o.yAxis,a=0,h=0;if(nh(i)&&nh(e))return t&&(a=n?n.pos:r.plotLeft,h=s?s.pos:r.plotTop),r.inverted&&n&&s?[s.len-e+h,n.len-i+a]:[i+a,e+h]}},t.prototype.resolveColor=function(){var t,e,i,o=this.series,r=o.chart.options.chart,n=o.chart.styledMode,s=r.colorCount;delete this.nonZonedColor,o.options.colorByPoint?(n||(t=(e=o.options.colors||o.chart.options.colors)[o.colorCounter],s=e.length),i=o.colorCounter,o.colorCounter++,o.colorCounter===s&&(o.colorCounter=0)):(n||(t=o.color),i=o.colorIndex),this.colorIndex=nd(this.options.colorIndex,i),this.color=nd(this.options.color,t)},t.prototype.setNestedProperty=function(t,e,i){return i.split(".").reduce(function(t,i,o,r){var n=r.length-1===o;return t[i]=n?e:nl(t[i],!0)?t[i]:{},t[i]},t),t},t.prototype.shouldDraw=function(){return!this.isNull},t.prototype.tooltipFormatter=function(t){var e,i=this.series,o=i.chart,r=i.pointArrayMap,n=i.tooltipOptions,s=n.valueDecimals,a=void 0===s?"":s,h=n.valuePrefix,l=void 0===h?"":h,c=n.valueSuffix,d=void 0===c?"":c;return o.styledMode&&(t=(null===(e=o.tooltip)||void 0===e?void 0:e.styledModeFormat(t))||t),(void 0===r?["y"]:r).forEach(function(e){e="{point."+e,(l||d)&&(t=t.replace(RegExp(e+"}","g"),l+e+"}"+d)),t=t.replace(RegExp(e+"}","g"),e+":,."+a+"f}")}),r7(t,this,o)},t.prototype.update=function(t,e,i,o){var r,n=this,s=n.series,a=n.graphic,h=s.chart,l=s.options;function c(){n.applyOptions(t);var o=a&&n.hasMockGraphic,c=null===n.y?!o:o;a&&c&&(n.graphic=a.destroy(),delete n.hasMockGraphic),nl(t,!0)&&((null==a?void 0:a.element)&&t&&t.marker&&void 0!==t.marker.symbol&&(n.graphic=a.destroy()),(null==t?void 0:t.dataLabels)&&n.dataLabel&&(n.dataLabel=n.dataLabel.destroy())),r=n.index;for(var d={},p=0,u=s.dataColumnKeys();p<u.length;p++){var f=u[p];d[f]=n[f]}s.dataTable.setRow(d,r),l.data[r]=nl(l.data[r],!0)||nl(t,!0)?n.options:nd(t,l.data[r]),s.isDirty=s.isDirtyData=!0,!s.fixedBox&&s.hasCartesianSeries&&(h.isDirtyBox=!0),"point"===l.legendType&&(h.isDirtyLegend=!0),e&&h.redraw(i)}e=nd(e,!0),!1===o?c():n.firePointEvent("update",{options:t},c)},t.prototype.remove=function(t,e){this.series.removePoint(this.series.data.indexOf(this),t,e)},t.prototype.select=function(t,e){var i=this,o=i.series,r=o.chart;t=nd(t,!i.selected),this.selectedStaging=t,i.firePointEvent(t?"select":"unselect",{accumulate:e},function(){i.selected=i.options.selected=t,o.options.data[o.data.indexOf(i)]=i.options,i.setState(t&&"select"),e||r.getSelectedPoints().forEach(function(t){var e=t.series;t.selected&&t!==i&&(t.selected=t.options.selected=!1,e.options.data[e.data.indexOf(t)]=t.options,t.setState(r.hoverPoints&&e.options.inactiveOtherPoints?"inactive":""),t.firePointEvent("unselect"))})}),delete this.selectedStaging},t.prototype.onMouseOver=function(t){var e=this.series.chart,i=e.inverted,o=e.pointer;o&&(t=t?o.normalize(t):o.getChartCoordinatesFromPoint(this,i),o.runPointActions(t,this))},t.prototype.onMouseOut=function(){var t=this.series.chart;this.firePointEvent("mouseOut"),this.series.options.inactiveOtherPoints||(t.hoverPoints||[]).forEach(function(t){t.setState()}),t.hoverPoints=t.hoverPoint=null},t.prototype.manageEvent=function(t){var e,i,o,r,n,s,a,h=null===(e=nc(this.series.options.point,this.options).events)||void 0===e?void 0:e[t];!na(h)||(null===(i=this.hcEvents)||void 0===i?void 0:i[t])&&(null===(r=null===(o=this.hcEvents)||void 0===o?void 0:o[t])||void 0===r?void 0:r.map(function(t){return t.fn}).indexOf(h))!==-1?this.importedUserEvent&&!h&&(null===(s=this.hcEvents)||void 0===s?void 0:s[t])&&(null===(a=this.hcEvents)||void 0===a?void 0:a[t].userEvent)&&(nu(this,t),delete this.hcEvents[t],Object.keys(this.hcEvents)||delete this.importedUserEvent):(null===(n=this.importedUserEvent)||void 0===n||n.call(this),this.importedUserEvent=nt(this,t,h),this.hcEvents&&(this.hcEvents[t].userEvent=!0))},t.prototype.setState=function(t,e){var i,o,r,n,s,a,h=this.series,l=this.state,c=h.options.states[t||"normal"]||{},d=t0.plotOptions[h.type].marker&&h.options.marker,p=d&&!1===d.enabled,u=(null===(i=null==d?void 0:d.states)||void 0===i?void 0:i[t||"normal"])||{},f=!1===u.enabled,g=this.marker||{},v=h.chart,m=d&&h.markerAttribs,y=h.halo,x=h.stateMarkerGraphic;if(((t=t||"")!==this.state||e)&&(!this.selected||"select"===t)&&!1!==c.enabled&&(!t||!f&&(!p||!1!==u.enabled))&&(!t||!g.states||!g.states[t]||!1!==g.states[t].enabled)){if(this.state=t,m&&(r=h.markerAttribs(this,t)),this.graphic&&!this.hasMockGraphic){if(l&&this.graphic.removeClass("highcharts-point-"+l),t&&this.graphic.addClass("highcharts-point-"+t),!v.styledMode){n=h.pointAttribs(this,t),s=nd(v.options.chart.animation,c.animation);var b=n.opacity;h.options.inactiveOtherPoints&&nh(b)&&(this.dataLabels||[]).forEach(function(t){t&&!t.hasClass("highcharts-data-label-hidden")&&(t.animate({opacity:b},s),t.connector&&t.connector.animate({opacity:b},s))}),this.graphic.animate(n,s)}r&&this.graphic.animate(r,nd(v.options.chart.animation,u.animation,d.animation)),x&&x.hide()}else t&&u&&(a=g.symbol||h.symbol,x&&x.currentSymbol!==a&&(x=x.destroy()),r&&(x?x[e?"animate":"attr"]({x:r.x,y:r.y}):a&&(h.stateMarkerGraphic=x=v.renderer.symbol(a,r.x,r.y,r.width,r.height,nc(d,u)).add(h.markerGroup),x.currentSymbol=a)),!v.styledMode&&x&&"inactive"!==this.state&&x.attr(h.pointAttribs(this,t))),x&&(x[t&&this.isInside?"show":"hide"](),x.element.point=this,x.addClass(this.getClassName(),!0));var k=c.halo,w=this.graphic||x,M=(null==w?void 0:w.visibility)||"inherit";(null==k?void 0:k.size)&&w&&"hidden"!==M&&!this.isCluster?(y||(h.halo=y=v.renderer.path().add(w.parentGroup)),y.show()[e?"animate":"attr"]({d:this.haloPath(k.size)}),y.attr({class:"highcharts-halo highcharts-color-"+nd(this.colorIndex,h.colorIndex)+(this.className?" "+this.className:""),visibility:M,zIndex:-1}),y.point=this,v.styledMode||y.attr(no({fill:this.color||h.color,"fill-opacity":k.opacity},eI.filterUserAttributes(k.attributes||{})))):(null===(o=null==y?void 0:y.point)||void 0===o?void 0:o.haloPath)&&!y.point.destroyed&&y.animate({d:y.point.haloPath(0)},null,y.hide),nr(this,"afterSetState",{state:t})}},t.prototype.haloPath=function(t){var e=this.pos();return e?this.series.chart.renderer.symbols.circle(ne(e[0],1)-t,e[1]-t,2*t,2*t):[]},t}(),nv=function(){return(nv=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},nm=et.parse,ny=ti.charts,nx=ti.composed,nb=ti.isTouchDevice,nk=tP.addEvent,nw=tP.attr,nM=tP.css,nS=tP.extend,nT=tP.find,nC=tP.fireEvent,nA=tP.isNumber,nP=tP.isObject,nO=tP.objectEach,nL=tP.offset,nE=tP.pick,nI=tP.pushUnique,nD=tP.splat,nB=function(){function t(t,e){var i;this.hasDragged=0,this.pointerCaptureEventsToUnbind=[],this.eventsToUnbind=[],this.options=e,this.chart=t,this.runChartClick=!!(null===(i=e.chart.events)||void 0===i?void 0:i.click),this.pinchDown=[],this.setDOMEvents(),nC(this,"afterInit")}return t.prototype.applyInactiveState=function(t){var e=this;void 0===t&&(t=[]);var i=[];t.forEach(function(t){var o=t.series;i.push(o),o.linkedParent&&i.push(o.linkedParent),o.linkedSeries&&i.push.apply(i,o.linkedSeries),o.navigatorSeries&&i.push(o.navigatorSeries),o.boosted&&o.markerGroup&&i.push.apply(i,e.chart.series.filter(function(t){return t.markerGroup===o.markerGroup}))}),this.chart.series.forEach(function(t){-1===i.indexOf(t)?t.setState("inactive",!0):t.options.inactiveOtherPoints&&t.setAllPointsToState("inactive")})},t.prototype.destroy=function(){var e=this;this.eventsToUnbind.forEach(function(t){return t()}),this.eventsToUnbind=[],!ti.chartCount&&(t.unbindDocumentMouseUp.forEach(function(t){return t.unbind()}),t.unbindDocumentMouseUp.length=0,t.unbindDocumentTouchEnd&&(t.unbindDocumentTouchEnd=t.unbindDocumentTouchEnd())),clearInterval(e.tooltipTimeout),nO(e,function(t,i){e[i]=void 0})},t.prototype.getSelectionMarkerAttrs=function(t,e){var i=this,o={args:{chartX:t,chartY:e},attrs:{},shapeType:"rect"};return nC(this,"getSelectionMarkerAttrs",o,function(o){var r,n=i.chart,s=i.zoomHor,a=i.zoomVert,h=n.mouseDownX,l=void 0===h?0:h,c=n.mouseDownY,d=void 0===c?0:c,p=o.attrs;p.x=n.plotLeft,p.y=n.plotTop,p.width=s?1:n.plotWidth,p.height=a?1:n.plotHeight,s&&(p.width=Math.max(1,Math.abs(r=t-l)),p.x=(r>0?0:r)+l),a&&(p.height=Math.max(1,Math.abs(r=e-d)),p.y=(r>0?0:r)+d)}),o},t.prototype.drag=function(t){var e,i=this.chart,o=i.mouseDownX,r=void 0===o?0:o,n=i.mouseDownY,s=void 0===n?0:n,a=i.options.chart,h=a.panning,l=a.panKey,c=a.selectionMarkerFill,d=i.plotLeft,p=i.plotTop,u=i.plotWidth,f=i.plotHeight,g=nP(h)?h.enabled:h,v=l&&t[""+l+"Key"],m=t.chartX,y=t.chartY,x=this.selectionMarker;if((!x||!x.touch)&&(m<d?m=d:m>d+u&&(m=d+u),y<p?y=p:y>p+f&&(y=p+f),this.hasDragged=Math.sqrt(Math.pow(r-m,2)+Math.pow(s-y,2)),this.hasDragged>10)){e=i.isInsidePlot(r-d,s-p,{visiblePlotOnly:!0});var b=this.getSelectionMarkerAttrs(m,y),k=b.shapeType,w=b.attrs;(i.hasCartesianSeries||i.mapView)&&this.hasZoom&&e&&!v&&!x&&(this.selectionMarker=x=i.renderer[k](),x.attr({class:"highcharts-selection-marker",zIndex:7}).add(),i.styledMode||x.attr({fill:c||nm("#334eff").setOpacity(.25).get()})),x&&x.attr(w),e&&!x&&g&&i.pan(t,h)}},t.prototype.dragStart=function(t){var e=this.chart;e.mouseIsDown=t.type,e.cancelClick=!1,e.mouseDownX=t.chartX,e.mouseDownY=t.chartY},t.prototype.getSelectionBox=function(t){var e={args:{marker:t},result:t.getBBox()};return nC(this,"getSelectionBox",e),e.result},t.prototype.drop=function(t){for(var e,i=this,o=this.chart,r=this.selectionMarker,n=0,s=o.axes;n<s.length;n++){var a=s[n];a.isPanning&&(a.isPanning=!1,(a.options.startOnTick||a.options.endOnTick||a.series.some(function(t){return t.boosted}))&&(a.forceRedraw=!0,a.setExtremes(a.userMin,a.userMax,!1),e=!0))}if(e&&o.redraw(),r&&t){if(this.hasDragged){var h=this.getSelectionBox(r);o.transform({axes:o.axes.filter(function(t){return t.zoomEnabled&&("xAxis"===t.coll&&i.zoomX||"yAxis"===t.coll&&i.zoomY)}),selection:nv({originalEvent:t,xAxis:[],yAxis:[]},h),from:h})}nA(o.index)&&(this.selectionMarker=r.destroy())}o&&nA(o.index)&&(nM(o.container,{cursor:o._cursor}),o.cancelClick=this.hasDragged>10,o.mouseIsDown=!1,this.hasDragged=0,this.pinchDown=[])},t.prototype.findNearestKDPoint=function(t,e,i){var o;return t.forEach(function(t){var r,n,s,a,h,l,c,d=!(t.noSharedTooltip&&e)&&0>t.options.findNearestPointBy.indexOf("y"),p=t.searchPoint(i,d);nP(p,!0)&&p.series&&(!nP(o,!0)||(h=(r=o).distX-p.distX,l=r.dist-p.dist,c=(null===(n=p.series.group)||void 0===n?void 0:n.zIndex)-(null===(s=r.series.group)||void 0===s?void 0:s.zIndex),(0!==h&&e?h:0!==l?l:0!==c?c:r.series.index>p.series.index?-1:1)>0))&&(o=p)}),o},t.prototype.getChartCoordinatesFromPoint=function(t,e){var i,o,r=t.series,n=r.xAxis,s=r.yAxis,a=t.shapeArgs;if(n&&s){var h=null!==(o=null!==(i=t.clientX)&&void 0!==i?i:t.plotX)&&void 0!==o?o:0,l=t.plotY||0;return t.isNode&&a&&nA(a.x)&&nA(a.y)&&(h=a.x,l=a.y),e?{chartX:s.len+s.pos-l,chartY:n.len+n.pos-h}:{chartX:h+n.pos,chartY:l+s.pos}}if((null==a?void 0:a.x)&&a.y)return{chartX:a.x,chartY:a.y}},t.prototype.getChartPosition=function(){if(this.chartPosition)return this.chartPosition;var t=this.chart.container,e=nL(t);this.chartPosition={left:e.left,top:e.top,scaleX:1,scaleY:1};var i=t.offsetHeight,o=t.offsetWidth;return o>2&&i>2&&(this.chartPosition.scaleX=e.width/o,this.chartPosition.scaleY=e.height/i),this.chartPosition},t.prototype.getCoordinates=function(t){for(var e={xAxis:[],yAxis:[]},i=0,o=this.chart.axes;i<o.length;i++){var r=o[i];e[r.isXAxis?"xAxis":"yAxis"].push({axis:r,value:r.toValue(t[r.horiz?"chartX":"chartY"])})}return e},t.prototype.getHoverData=function(t,e,i,o,r,n){var s,a=[],h=function(t){return t.visible&&!(!r&&t.directTouch)&&nE(t.options.enableMouseTracking,!0)},l=e,c={chartX:n?n.chartX:void 0,chartY:n?n.chartY:void 0,shared:r};nC(this,"beforeGetHoverData",c),s=l&&!l.stickyTracking?[l]:i.filter(function(t){return t.stickyTracking&&(c.filter||h)(t)});var d=o&&t||!n?t:this.findNearestKDPoint(s,r,n);return l=null==d?void 0:d.series,d&&(r&&!l.noSharedTooltip?(s=i.filter(function(t){return c.filter?c.filter(t):h(t)&&!t.noSharedTooltip})).forEach(function(t){var e,i=null===(e=t.options)||void 0===e?void 0:e.nullInteraction,o=nT(t.points,function(t){return t.x===d.x&&(!t.isNull||!!i)});nP(o)&&(t.boosted&&t.boost&&(o=t.boost.getPoint(o)),a.push(o))}):a.push(d)),nC(this,"afterGetHoverData",c={hoverPoint:d}),{hoverPoint:c.hoverPoint,hoverSeries:l,hoverPoints:a}},t.prototype.getPointFromEvent=function(t){for(var e,i=t.target;i&&!e;)e=i.point,i=i.parentNode;return e},t.prototype.onTrackerMouseOut=function(t){var e=this.chart,i=t.relatedTarget,o=e.hoverSeries;this.isDirectTouch=!1,!o||!i||o.stickyTracking||this.inClass(i,"highcharts-tooltip")||this.inClass(i,"highcharts-series-"+o.index)&&this.inClass(i,"highcharts-tracker")||o.onMouseOut()},t.prototype.inClass=function(t,e){for(var i,o=t;o;){if(i=nw(o,"class")){if(-1!==i.indexOf(e))return!0;if(-1!==i.indexOf("highcharts-container"))return!1}o=o.parentElement}},t.prototype.normalize=function(t,e){var i=t.touches,o=i?i.length?i.item(0):nE(i.changedTouches,t.changedTouches)[0]:t;e||(e=this.getChartPosition());var r=o.pageX-e.left,n=o.pageY-e.top;return nS(t,{chartX:Math.round(r/=e.scaleX),chartY:Math.round(n/=e.scaleY)})},t.prototype.onContainerClick=function(t){var e=this.chart,i=e.hoverPoint,o=this.normalize(t),r=e.plotLeft,n=e.plotTop;!e.cancelClick&&(i&&this.inClass(o.target,"highcharts-tracker")?(nC(i.series,"click",nS(o,{point:i})),e.hoverPoint&&i.firePointEvent("click",o)):(nS(o,this.getCoordinates(o)),e.isInsidePlot(o.chartX-r,o.chartY-n,{visiblePlotOnly:!0})&&nC(e,"click",o)))},t.prototype.onContainerMouseDown=function(t){var e,i=(1&(t.buttons||t.button))==1;t=this.normalize(t),ti.isFirefox&&0!==t.button&&this.onContainerMouseMove(t),(void 0===t.button||i)&&(this.zoomOption(t),i&&(null===(e=t.preventDefault)||void 0===e||e.call(t)),this.dragStart(t))},t.prototype.onContainerMouseLeave=function(e){var i=(ny[nE(t.hoverChartIndex,-1)]||{}).pointer;e=this.normalize(e),this.onContainerMouseMove(e),i&&!this.inClass(e.relatedTarget,"highcharts-tooltip")&&(i.reset(),i.chartPosition=void 0)},t.prototype.onContainerMouseEnter=function(){delete this.chartPosition},t.prototype.onContainerMouseMove=function(t){var e=this.chart,i=e.tooltip,o=this.normalize(t);this.setHoverChartIndex(t),("mousedown"===e.mouseIsDown||this.touchSelect(o))&&this.drag(o),!e.openMenu&&(this.inClass(o.target,"highcharts-tracker")||e.isInsidePlot(o.chartX-e.plotLeft,o.chartY-e.plotTop,{visiblePlotOnly:!0}))&&!(null==i?void 0:i.shouldStickOnContact(o))&&(this.inClass(o.target,"highcharts-no-tooltip")?this.reset(!1,0):this.runPointActions(o))},t.prototype.onDocumentTouchEnd=function(t){this.onDocumentMouseUp(t)},t.prototype.onContainerTouchMove=function(t){this.touchSelect(t)?this.onContainerMouseMove(t):this.touch(t)},t.prototype.onContainerTouchStart=function(t){this.touchSelect(t)?this.onContainerMouseDown(t):(this.zoomOption(t),this.touch(t,!0))},t.prototype.onDocumentMouseMove=function(t){var e=this.chart,i=e.tooltip,o=this.chartPosition,r=this.normalize(t,o);!o||e.isInsidePlot(r.chartX-e.plotLeft,r.chartY-e.plotTop,{visiblePlotOnly:!0})||(null==i?void 0:i.shouldStickOnContact(r))||r.target!==e.container.ownerDocument&&this.inClass(r.target,"highcharts-tracker")||this.reset()},t.prototype.onDocumentMouseUp=function(e){var i,o;null===(o=null===(i=ny[nE(t.hoverChartIndex,-1)])||void 0===i?void 0:i.pointer)||void 0===o||o.drop(e)},t.prototype.pinch=function(t){var e=this,i=this,o=i.chart,r=i.hasZoom,n=i.lastTouches,s=[].map.call(t.touches||[],function(t){return i.normalize(t)}),a=s.length,h=1===a&&(i.inClass(t.target,"highcharts-tracker")&&o.runTrackerClick||i.runChartClick),l=o.tooltip,c=1===a&&nE(null==l?void 0:l.options.followTouchMove,!0);a>1?i.initiated=!0:c&&(i.initiated=!1),r&&i.initiated&&!h&&!1!==t.cancelable&&t.preventDefault(),"touchstart"===t.type?(i.pinchDown=s,i.res=!0,o.mouseDownX=t.chartX):c?this.runPointActions(i.normalize(t)):n&&(nC(o,"touchpan",{originalEvent:t,touches:s},function(){var i=function(t){var e=t[0],i=t[1]||e;return{x:e.chartX,y:e.chartY,width:i.chartX-e.chartX,height:i.chartY-e.chartY}};o.transform({axes:o.axes.filter(function(t){return t.zoomEnabled&&(e.zoomHor&&t.horiz||e.zoomVert&&!t.horiz)}),to:i(s),from:i(n),trigger:t.type})}),i.res&&(i.res=!1,this.reset(!1,0))),i.lastTouches=s},t.prototype.reset=function(t,e){var i=this.chart,o=i.hoverSeries,r=i.hoverPoint,n=i.hoverPoints,s=i.tooltip,a=(null==s?void 0:s.shared)?n:r;t&&a&&nD(a).forEach(function(e){e.series.isCartesian&&void 0===e.plotX&&(t=!1)}),t?s&&a&&nD(a).length&&(s.refresh(a),s.shared&&n?n.forEach(function(t){t.setState(t.state,!0),t.series.isCartesian&&(t.series.xAxis.crosshair&&t.series.xAxis.drawCrosshair(null,t),t.series.yAxis.crosshair&&t.series.yAxis.drawCrosshair(null,t))}):r&&(r.setState(r.state,!0),i.axes.forEach(function(t){t.crosshair&&r.series[t.coll]===t&&t.drawCrosshair(null,r)}))):(r&&r.onMouseOut(),n&&n.forEach(function(t){t.setState()}),o&&o.onMouseOut(),s&&s.hide(e),this.unDocMouseMove&&(this.unDocMouseMove=this.unDocMouseMove()),i.axes.forEach(function(t){t.hideCrosshair()}),i.hoverPoints=i.hoverPoint=void 0)},t.prototype.runPointActions=function(e,i,o){var r,n=this.chart,s=n.series,a=(null===(r=n.tooltip)||void 0===r?void 0:r.options.enabled)?n.tooltip:void 0,h=!!a&&a.shared,l=i||n.hoverPoint,c=(null==l?void 0:l.series)||n.hoverSeries,d=(!e||"touchmove"!==e.type)&&(!!i||(null==c?void 0:c.directTouch)&&this.isDirectTouch),p=this.getHoverData(l,c,s,d,h,e);l=p.hoverPoint,c=p.hoverSeries;var u=p.hoverPoints,f=(null==c?void 0:c.tooltipOptions.followPointer)&&!c.tooltipOptions.split,g=h&&c&&!c.noSharedTooltip;if(l&&(o||l!==n.hoverPoint||(null==a?void 0:a.isHidden))){if((n.hoverPoints||[]).forEach(function(t){-1===u.indexOf(t)&&t.setState()}),n.hoverSeries!==c&&c.onMouseOver(),this.applyInactiveState(u),(u||[]).forEach(function(t){t.setState("hover")}),n.hoverPoint&&n.hoverPoint.firePointEvent("mouseOut"),!l.series)return;n.hoverPoints=u,n.hoverPoint=l,l.firePointEvent("mouseOver",void 0,function(){a&&l&&a.refresh(g?u:l,e)})}else if(f&&a&&!a.isHidden){var v=a.getAnchor([{}],e);n.isInsidePlot(v[0],v[1],{visiblePlotOnly:!0})&&a.updatePosition({plotX:v[0],plotY:v[1]})}this.unDocMouseMove||(this.unDocMouseMove=nk(n.container.ownerDocument,"mousemove",function(e){var i,o,r;return null===(r=null===(o=ny[null!==(i=t.hoverChartIndex)&&void 0!==i?i:-1])||void 0===o?void 0:o.pointer)||void 0===r?void 0:r.onDocumentMouseMove(e)}),this.eventsToUnbind.push(this.unDocMouseMove)),n.axes.forEach(function(t){var i,o,r,s=null===(o=null===(i=t.crosshair)||void 0===i?void 0:i.snap)||void 0===o||o;!s||(r=n.hoverPoint)&&r.series[t.coll]===t||(r=nT(u,function(e){var i;return(null===(i=e.series)||void 0===i?void 0:i[t.coll])===t})),r||!s?t.drawCrosshair(e,r):t.hideCrosshair()})},t.prototype.setDOMEvents=function(){var e=this,i=this.chart.container,o=i.ownerDocument;i.onmousedown=this.onContainerMouseDown.bind(this),i.onmousemove=this.onContainerMouseMove.bind(this),i.onclick=this.onContainerClick.bind(this),this.eventsToUnbind.push(nk(i,"mouseenter",this.onContainerMouseEnter.bind(this)),nk(i,"mouseleave",this.onContainerMouseLeave.bind(this))),t.unbindDocumentMouseUp.some(function(t){return t.doc===o})||t.unbindDocumentMouseUp.push({doc:o,unbind:nk(o,"mouseup",this.onDocumentMouseUp.bind(this))});for(var r=this.chart.renderTo.parentElement;r&&"BODY"!==r.tagName;)this.eventsToUnbind.push(nk(r,"scroll",function(){delete e.chartPosition})),r=r.parentElement;this.eventsToUnbind.push(nk(i,"touchstart",this.onContainerTouchStart.bind(this),{passive:!1}),nk(i,"touchmove",this.onContainerTouchMove.bind(this),{passive:!1})),t.unbindDocumentTouchEnd||(t.unbindDocumentTouchEnd=nk(o,"touchend",this.onDocumentTouchEnd.bind(this),{passive:!1})),this.setPointerCapture(),nk(this.chart,"redraw",this.setPointerCapture.bind(this))},t.prototype.setPointerCapture=function(){if(nb){var t,e,i=this.pointerCaptureEventsToUnbind,o=this.chart,r=o.container,n=nE(null===(t=o.options.tooltip)||void 0===t?void 0:t.followTouchMove,!0)&&o.series.some(function(t){return t.options.findNearestPointBy.indexOf("y")>-1});!this.hasPointerCapture&&n?(i.push(nk(r,"pointerdown",function(t){var e,i;(null===(e=t.target)||void 0===e?void 0:e.hasPointerCapture(t.pointerId))&&(null===(i=t.target)||void 0===i||i.releasePointerCapture(t.pointerId))}),nk(r,"pointermove",function(t){var e,i;null===(i=null===(e=o.pointer)||void 0===e?void 0:e.getPointFromEvent(t))||void 0===i||i.onMouseOver(t)})),o.styledMode||nM(r,{"touch-action":"none"}),r.className+=" highcharts-no-touch-action",this.hasPointerCapture=!0):this.hasPointerCapture&&!n&&(i.forEach(function(t){return t()}),i.length=0,o.styledMode||nM(r,{"touch-action":nE(null===(e=o.options.chart.style)||void 0===e?void 0:e["touch-action"],"manipulation")}),r.className=r.className.replace(" highcharts-no-touch-action",""),this.hasPointerCapture=!1)}},t.prototype.setHoverChartIndex=function(e){var i,o=this.chart,r=ti.charts[nE(t.hoverChartIndex,-1)];if(r&&r!==o){var n={relatedTarget:o.container};!e||(null==e?void 0:e.relatedTarget)||Object.assign({},e,n),null===(i=r.pointer)||void 0===i||i.onContainerMouseLeave(e||n)}(null==r?void 0:r.mouseIsDown)||(t.hoverChartIndex=o.index)},t.prototype.touch=function(t,e){var i,o=this.chart,r=this.pinchDown,n=void 0===r?[]:r;this.setHoverChartIndex(),1===(t=this.normalize(t)).touches.length?o.isInsidePlot(t.chartX-o.plotLeft,t.chartY-o.plotTop,{visiblePlotOnly:!0})&&!o.openMenu?(e&&this.runPointActions(t),"touchmove"===t.type&&(i=!!n[0]&&Math.pow(n[0].chartX-t.chartX,2)+Math.pow(n[0].chartY-t.chartY,2)>=16),nE(i,!0)&&this.pinch(t)):e&&this.reset():2===t.touches.length&&this.pinch(t)},t.prototype.touchSelect=function(t){return!!(this.chart.zooming.singleTouch&&t.touches&&1===t.touches.length)},t.prototype.zoomOption=function(t){var e,i,o=this.chart,r=o.inverted,n=o.zooming.type||"";/touch/.test(t.type)&&(n=nE(o.zooming.pinchType,n)),this.zoomX=e=/x/.test(n),this.zoomY=i=/y/.test(n),this.zoomHor=e&&!r||i&&r,this.zoomVert=i&&!r||e&&r,this.hasZoom=e||i},t.unbindDocumentMouseUp=[],t}();(g=nB||(nB={})).compose=function(t){nI(nx,"Core.Pointer")&&nk(t,"beforeRender",function(){this.pointer=new g(this,this.options)})};var nN=nB,nz=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))};(v=F||(F={})).setLength=function(t,e,i){return Array.isArray(t)?(t.length=e,t):t[i?"subarray":"slice"](0,e)},v.splice=function(t,e,i,o,r){if(void 0===r&&(r=[]),Array.isArray(t))return Array.isArray(r)||(r=Array.from(r)),{removed:t.splice.apply(t,nz([e,i],r,!1)),array:t};var n=Object.getPrototypeOf(t).constructor,s=t[o?"subarray":"slice"](e,e+i),a=new n(t.length-i+r.length);return a.set(t.subarray(0,e),0),a.set(r,e),a.set(t.subarray(e+i),e+r.length),{removed:s,array:a}};var nR=F,nW=nR.setLength,nX=nR.splice,nH=tP.fireEvent,nj=tP.objectEach,nF=tP.uniqueKey,nY=function(){function t(t){void 0===t&&(t={});var e=this;this.autoId=!t.id,this.columns={},this.id=t.id||nF(),this.modified=this,this.rowCount=0,this.versionTag=nF();var i=0;nj(t.columns||{},function(t,o){e.columns[o]=t.slice(),i=Math.max(i,t.length)}),this.applyRowCount(i)}return t.prototype.applyRowCount=function(t){var e=this;this.rowCount=t,nj(this.columns,function(i,o){i.length!==t&&(e.columns[o]=nW(i,t))})},t.prototype.deleteRows=function(t,e){var i=this;if(void 0===e&&(e=1),e>0&&t<this.rowCount){var o=0;nj(this.columns,function(r,n){i.columns[n]=nX(r,t,e).array,o=r.length}),this.rowCount=o}nH(this,"afterDeleteRows",{rowIndex:t,rowCount:e}),this.versionTag=nF()},t.prototype.getColumn=function(t,e){return this.columns[t]},t.prototype.getColumns=function(t,e){var i=this;return(t||Object.keys(this.columns)).reduce(function(t,e){return t[e]=i.columns[e],t},{})},t.prototype.getRow=function(t,e){var i=this;return(e||Object.keys(this.columns)).map(function(e){var o;return null===(o=i.columns[e])||void 0===o?void 0:o[t]})},t.prototype.setColumn=function(t,e,i,o){var r;void 0===e&&(e=[]),void 0===i&&(i=0),this.setColumns(((r={})[t]=e,r),i,o)},t.prototype.setColumns=function(t,e,i){var o=this,r=this.rowCount;nj(t,function(t,e){o.columns[e]=t.slice(),r=t.length}),this.applyRowCount(r),(null==i?void 0:i.silent)||(nH(this,"afterSetColumns"),this.versionTag=nF())},t.prototype.setRow=function(t,e,i,o){void 0===e&&(e=this.rowCount);var r=this.columns,n=i?this.rowCount+1:e+1;nj(t,function(t,s){var a=r[s]||(null==o?void 0:o.addColumns)!==!1&&Array(n);a&&(i?a=nX(a,e,0,!0,[t]).array:a[e]=t,r[s]=a)}),n>this.rowCount&&this.applyRowCount(n),(null==o?void 0:o.silent)||(nH(this,"afterSetRows"),this.versionTag=nF())},t}(),nG=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},n_=tP.extend,nU=tP.merge,nV=tP.pick;!function(t){function e(t,e,i){var o,r,n,s=this.legendItem=this.legendItem||{},a=this.chart,h=this.options,l=t.baseline,c=void 0===l?0:l,d=t.symbolWidth,p=t.symbolHeight,u=this.symbol||"circle",f=p/2,g=a.renderer,v=s.group,m=c-Math.round(((null===(o=t.fontMetrics)||void 0===o?void 0:o.b)||p)*(i?.4:.3)),y={},x=h.marker,b=0;if(a.styledMode||(y["stroke-width"]=Math.min(h.lineWidth||0,24),h.dashStyle?y.dashstyle=h.dashStyle:"square"===h.linecap||(y["stroke-linecap"]="round")),s.line=g.path().addClass("highcharts-graph").attr(y).add(v),i&&(s.area=g.path().addClass("highcharts-area").add(v)),y["stroke-linecap"]&&(b=Math.min(s.line.strokeWidth(),d)/2),d){var k=[["M",b,m],["L",d-b,m]];s.line.attr({d:k}),null===(r=s.area)||void 0===r||r.attr({d:nG(nG([],k,!0),[["L",d-b,c],["L",b,c]],!1)})}if(x&&!1!==x.enabled&&d){var w=Math.min(nV(x.radius,f),f);0===u.indexOf("url")&&(x=nU(x,{width:p,height:p}),w=0),s.symbol=n=g.symbol(u,d/2-w,m-w,2*w,2*w,n_({context:"legend"},x)).addClass("highcharts-point").add(v),n.isMarker=!0}}t.areaMarker=function(t,i){e.call(this,t,i,!0)},t.lineMarker=e,t.rectangle=function(t,e){var i=e.legendItem||{},o=t.options,r=t.symbolHeight,n=o.squareSymbol,s=n?r:t.symbolWidth;i.symbol=this.chart.renderer.rect(n?(t.symbolWidth-r)/2:0,t.baseline-r+1,s,r,nV(t.options.symbolRadius,r/2)).addClass("highcharts-point").attr({zIndex:3}).add(i.group)}}(Y||(Y={}));var nZ=Y,nq={lineWidth:2,allowPointSelect:!1,crisp:!0,showCheckbox:!1,animation:{duration:1e3},enableMouseTracking:!0,events:{},marker:{enabledThreshold:2,lineColor:"#ffffff",lineWidth:0,radius:4,states:{normal:{animation:!0},hover:{animation:{duration:150},enabled:!0,radiusPlus:2,lineWidthPlus:1},select:{fillColor:"#cccccc",lineColor:"#000000",lineWidth:2}}},point:{events:{}},dataLabels:{animation:{},align:"center",borderWidth:0,defer:!0,formatter:function(){var t=this.series.chart.numberFormatter;return"number"!=typeof this.y?"":t(this.y,-1)},padding:5,style:{fontSize:"0.7em",fontWeight:"bold",color:"contrast",textOutline:"1px contrast"},verticalAlign:"bottom",x:0,y:0},cropThreshold:300,opacity:1,pointRange:0,softThreshold:!0,states:{normal:{animation:!0},hover:{animation:{duration:150},lineWidthPlus:1,marker:{},halo:{size:10,opacity:.25}},select:{animation:{duration:0}},inactive:{animation:{duration:150},opacity:.2}},stickyTracking:!0,turboThreshold:1e3,findNearestPointBy:"x"},nK=(m=function(t,e){return(m=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}m(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),n$=tP.extend,nJ=tP.extendClass,nQ=tP.merge;!function(t){function e(e,i){var o=t0.plotOptions||{},r=i.defaultOptions,n=i.prototype;return n.type=e,n.pointClass||(n.pointClass=ng),!t.seriesTypes[e]&&(r&&(o[e]=r),t.seriesTypes[e]=i,!0)}t.seriesTypes=ti.seriesTypes,t.registerSeriesType=e,t.seriesType=function(i,o,r,n,s){var a=t0.plotOptions||{};if(o=o||"",a[i]=nQ(a[o],r),delete t.seriesTypes[i],e(i,nJ(t.seriesTypes[o]||function(){},n)),t.seriesTypes[i].prototype.type=i,s){var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return nK(e,t),e}(ng);n$(h.prototype,s),t.seriesTypes[i].prototype.pointClass=h}return t.seriesTypes[i]}}(G||(G={}));var n0=G,n1=function(){return(n1=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},n2=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},n3=oN.registerEventOptions,n5=ti.svg,n6=ti.win,n9=n0.seriesTypes,n4=eU.format,n8=tP.arrayMax,n7=tP.arrayMin,st=tP.clamp,se=tP.correctFloat,si=tP.crisp,so=tP.defined,sr=tP.destroyObjectProperties,sn=tP.diffObjects,ss=tP.erase,sa=tP.error,sh=tP.extend,sl=tP.find,sc=tP.fireEvent,sd=tP.getClosestDistance,sp=tP.getNestedProperty,su=tP.insertItem,sf=tP.isArray,sg=tP.isNumber,sv=tP.isString,sm=tP.merge,sy=tP.objectEach,sx=tP.pick,sb=tP.removeEvent,sk=tP.syncTimeout,sw=function(){function t(){this.zoneAxis="y"}return t.prototype.init=function(t,e){sc(this,"init",{options:e}),null!==(i=this.dataTable)&&void 0!==i||(this.dataTable=new nY);var i,o,r,n,s,a=t.series;this.eventsToUnbind=[],this.chart=t,this.options=this.setOptions(e);var h=this.options,l=!1!==h.visible;this.linkedSeries=[],this.bindAxes(),sh(this,{name:h.name,state:"",visible:l,selected:!0===h.selected}),n3(this,h);var c=h.events;((null==c?void 0:c.click)||(null===(r=null===(o=h.point)||void 0===o?void 0:o.events)||void 0===r?void 0:r.click)||h.allowPointSelect)&&(t.runTrackerClick=!0),this.getColor(),this.getSymbol(),this.isCartesian&&(t.hasCartesianSeries=!0),a.length&&(s=a[a.length-1]),this._i=sx(null==s?void 0:s._i,-1)+1,this.opacity=this.options.opacity,t.orderItems("series",su(this,a)),(null===(n=h.dataSorting)||void 0===n?void 0:n.enabled)?this.setDataSortingOptions():this.points||this.data||this.setData(h.data,!1),sc(this,"afterInit")},t.prototype.is=function(t){return n9[t]&&this instanceof n9[t]},t.prototype.bindAxes=function(){var t,e=this,i=e.options,o=e.chart;sc(this,"bindAxes",null,function(){(e.axisTypes||[]).forEach(function(r){(o[r]||[]).forEach(function(o){t=o.options,(sx(i[r],0)===o.index||void 0!==i[r]&&i[r]===t.id)&&(su(e,o.series),e[r]=o,o.isDirty=!0)}),e[r]||e.optionalAxis===r||sa(18,!0,o)})}),sc(this,"afterBindAxes")},t.prototype.hasData=function(){return this.visible&&void 0!==this.dataMax&&void 0!==this.dataMin||this.visible&&this.dataTable.rowCount>0},t.prototype.hasMarkerChanged=function(t,e){var i=t.marker,o=e.marker||{};return i&&(o.enabled&&!i.enabled||o.symbol!==i.symbol||o.height!==i.height||o.width!==i.width)},t.prototype.autoIncrement=function(t){var e,i,o,r=this.options,n=this.options,s=n.pointIntervalUnit,a=n.relativeXValue,h=this.chart.time,l=null!==(i=null!==(e=this.xIncrement)&&void 0!==e?e:h.parse(r.pointStart))&&void 0!==i?i:0;if(this.pointInterval=o=sx(this.pointInterval,r.pointInterval,1),a&&sg(t)&&(o*=t),s){var c=h.toParts(l);"day"===s?c[2]+=o:"month"===s?c[1]+=o:"year"===s&&(c[0]+=o),o=h.makeTime.apply(h,c)-l}return a&&sg(t)?l+o:(this.xIncrement=l+o,l)},t.prototype.setDataSortingOptions=function(){var t=this.options;sh(this,{requireSorting:!1,sorted:!1,enabledDataSorting:!0,allowDG:!1}),so(t.pointRange)||(t.pointRange=1)},t.prototype.setOptions=function(t){var e,i,o,r=this.chart,n=r.options.plotOptions,s=r.userOptions||{},a=sm(t),h=r.styledMode,l={plotOptions:n,userOptions:a};sc(this,"setOptions",l);var c=l.plotOptions[this.type],d=s.plotOptions||{},p=d.series||{},u=t0.plotOptions[this.type]||{},f=d[this.type]||{};c.dataLabels=this.mergeArrays(u.dataLabels,c.dataLabels),this.userOptions=l.userOptions;var g=sm(c,n.series,f,a);this.tooltipOptions=sm(t0.tooltip,null===(e=t0.plotOptions.series)||void 0===e?void 0:e.tooltip,null==u?void 0:u.tooltip,r.userOptions.tooltip,null===(i=d.series)||void 0===i?void 0:i.tooltip,f.tooltip,a.tooltip),this.stickyTracking=sx(a.stickyTracking,f.stickyTracking,p.stickyTracking,!!this.tooltipOptions.shared&&!this.noSharedTooltip||g.stickyTracking),null===c.marker&&delete g.marker,this.zoneAxis=g.zoneAxis||"y";var v=this.zones=(g.zones||[]).map(function(t){return n1({},t)});return(g.negativeColor||g.negativeFillColor)&&!g.zones&&(o={value:g[this.zoneAxis+"Threshold"]||g.threshold||0,className:"highcharts-negative"},h||(o.color=g.negativeColor,o.fillColor=g.negativeFillColor),v.push(o)),v.length&&so(v[v.length-1].value)&&v.push(h?{}:{color:this.color,fillColor:this.fillColor}),sc(this,"afterSetOptions",{options:g}),g},t.prototype.getName=function(){var t;return null!==(t=this.options.name)&&void 0!==t?t:n4(this.chart.options.lang.seriesName,this,this.chart)},t.prototype.getCyclic=function(t,e,i){var o,r,n=this.chart,s=""+t+"Index",a=""+t+"Counter",h=(null==i?void 0:i.length)||n.options.chart.colorCount;!e&&(so(r=sx("color"===t?this.options.colorIndex:void 0,this[s]))?o=r:(n.series.length||(n[a]=0),o=n[a]%h,n[a]+=1),i&&(e=i[o])),void 0!==o&&(this[s]=o),this[t]=e},t.prototype.getColor=function(){this.chart.styledMode?this.getCyclic("color"):this.options.colorByPoint?this.color="#cccccc":this.getCyclic("color",this.options.color||t0.plotOptions[this.type].color,this.chart.options.colors)},t.prototype.getPointsCollection=function(){return(this.hasGroupedData?this.points:this.data)||[]},t.prototype.getSymbol=function(){var t=this.options.marker;this.getCyclic("symbol",t.symbol,this.chart.options.symbols)},t.prototype.getColumn=function(t,e){return(e?this.dataTable.modified:this.dataTable).getColumn(t,!0)||[]},t.prototype.findPointIndex=function(t,e){var i,o,r,n,s=t.id,a=t.x,h=this.points,l=this.options.dataSorting,c=this.cropStart||0;if(s){var d=this.chart.get(s);d instanceof ng&&(o=d)}else if(this.linkedParent||this.enabledDataSorting||this.options.relativeXValue){var p=function(e){return!e.touched&&e.index===t.index};if((null==l?void 0:l.matchByName)?p=function(e){return!e.touched&&e.name===t.name}:this.options.relativeXValue&&(p=function(e){return!e.touched&&e.options.x===t.x}),!(o=sl(h,p)))return}return o&&void 0!==(n=null==o?void 0:o.index)&&(r=!0),void 0===n&&sg(a)&&(n=this.getColumn("x").indexOf(a,e)),-1!==n&&void 0!==n&&this.cropped&&(n=n>=c?n-c:n),!r&&sg(n)&&(null===(i=h[n])||void 0===i?void 0:i.touched)&&(n=void 0),n},t.prototype.updateData=function(t,e){var i,o,r,n,s,a=this,h=this.options,l=this.requireSorting,c=h.dataSorting,d=this.points,p=[],u=t.length===d.length,f=!0;if(this.xIncrement=null,t.forEach(function(t,e){var i,r,n=so(t)&&a.pointClass.prototype.optionsToObject.call({series:a},t)||{},f=n.id,g=n.x;f||sg(g)?(-1===(r=a.findPointIndex(n,s))||void 0===r?p.push(t):d[r]&&t!==(null===(i=h.data)||void 0===i?void 0:i[r])?(d[r].update(t,!1,void 0,!1),d[r].touched=!0,l&&(s=r+1)):d[r]&&(d[r].touched=!0),(!u||e!==r||(null==c?void 0:c.enabled)||a.hasDerivedData)&&(o=!0)):p.push(t)},this),o)for(r=d.length;r--;)(n=d[r])&&!n.touched&&(null===(i=n.remove)||void 0===i||i.call(n,!1,e));else!u||(null==c?void 0:c.enabled)?f=!1:(t.forEach(function(t,e){t===d[e].y||d[e].destroyed||d[e].update(t,!1,void 0,!1)}),p.length=0);if(d.forEach(function(t){t&&(t.touched=!1)}),!f)return!1;p.forEach(function(t){a.addPoint(t,!1,void 0,void 0,!1)},this);var g=this.getColumn("x");return null===this.xIncrement&&g.length&&(this.xIncrement=n8(g),this.autoIncrement()),!0},t.prototype.dataColumnKeys=function(){return n2(["x"],this.pointArrayMap||["y"],!0)},t.prototype.setData=function(t,e,i,o){void 0===e&&(e=!0);var r,n,s,a,h,l,c,d=this.points,p=(null==d?void 0:d.length)||0,u=this.options,f=this.chart,g=u.dataSorting,v=this.xAxis,m=u.turboThreshold,y=this.dataTable,x=this.dataColumnKeys(),b=this.pointValKey||"y",k=(this.pointArrayMap||[]).length,w=u.keys,M=0,S=1;f.options.chart.allowMutatingData||(u.data&&delete this.options.data,this.userOptions.data&&delete this.userOptions.data,c=sm(!0,t));var T=(t=c||t||[]).length;if((null==g?void 0:g.enabled)&&(t=this.sortData(t)),f.options.chart.allowMutatingData&&!1!==o&&T&&p&&!this.cropped&&!this.hasGroupedData&&this.visible&&!this.boosted&&(l=this.updateData(t,i)),!l){this.xIncrement=null,this.colorCounter=0;var C=m&&T>m;if(C){var A=this.getFirstValidPoint(t),P=this.getFirstValidPoint(t,T-1,-1),O=function(t){return!!(sf(t)&&(w||sg(t[0])))};if(sg(A)&&sg(P)){for(var L=[],E=[],I=0,D=t;I<D.length;I++){var B=D[I];L.push(this.autoIncrement()),E.push(B)}y.setColumns(((r={x:L})[b]=E,r))}else if(O(A)&&O(P)){if(k){for(var N=+(A.length===k),z=Array(x.length).fill(0).map(function(){return[]}),R=0,W=t;R<W.length;R++){var X=W[R];N&&z[0].push(this.autoIncrement());for(var H=N;H<=k;H++)null===(s=z[H])||void 0===s||s.push(X[H-N])}y.setColumns(x.reduce(function(t,e,i){return t[e]=z[i],t},{}))}else{w&&(M=w.indexOf("x"),S=w.indexOf("y"),M=M>=0?M:0,S=S>=0?S:1),1===A.length&&(S=0);var j=[],E=[];if(M===S)for(var F=0,Y=t;F<Y.length;F++){var X=Y[F];j.push(this.autoIncrement()),E.push(X[S])}else for(var G=0,_=t;G<_.length;G++){var X=_[G];j.push(X[M]),E.push(X[S])}y.setColumns(((n={x:j})[b]=E,n))}}else C=!1}if(!C){var U=x.reduce(function(t,e){return t[e]=[],t},{});for(h=0;h<T;h++)for(var X=this.pointClass.prototype.applyOptions.apply({series:this},[t[h]]),V=0;V<x.length;V++){var Z=x[V];U[Z][h]=X[Z]}y.setColumns(U)}for(sv(this.getColumn("y")[0])&&sa(14,!0,f),this.data=[],this.options.data=this.userOptions.data=t,h=p;h--;)null===(a=d[h])||void 0===a||a.destroy();v&&(v.minRange=v.userMinRange),this.isDirty=f.isDirtyBox=!0,this.isDirtyData=!!d,i=!1}"point"===u.legendType&&(this.processData(),this.generatePoints()),e&&f.redraw(i)},t.prototype.sortData=function(t){var e=this,i=e.options.dataSorting.sortKey||"y",o=function(t,e){return so(e)&&t.pointClass.prototype.optionsToObject.call({series:t},e)||{}};return t.forEach(function(i,r){t[r]=o(e,i),t[r].index=r},this),t.concat().sort(function(t,e){var o=sp(i,t),r=sp(i,e);return r<o?-1:+(r>o)}).forEach(function(t,e){t.x=e},this),e.linkedSeries&&e.linkedSeries.forEach(function(e){var i,r=e.options,n=r.data;(null===(i=r.dataSorting)||void 0===i?void 0:i.enabled)||!n||(n.forEach(function(i,r){n[r]=o(e,i),t[r]&&(n[r].x=t[r].x,n[r].index=r)}),e.setData(n,!1))}),t},t.prototype.getProcessedData=function(t){var e,i,o,r,n,s=this,a=s.dataTable,h=s.isCartesian,l=s.options,c=s.xAxis,d=l.cropThreshold,p=t||s.getExtremesFromAll,u=null==c?void 0:c.logarithmic,f=a.rowCount,g=0,v=s.getColumn("x"),m=a,y=!1;return c&&(r=(o=c.getExtremes()).min,n=o.max,y=!!(c.categories&&!c.names.length),h&&s.sorted&&!p&&(!d||f>d||s.forceCrop)&&(v[f-1]<r||v[0]>n?m=new nY:s.getColumn(s.pointValKey||"y").length&&(v[0]<r||v[f-1]>n)&&(m=(e=this.cropData(a,r,n)).modified,g=e.start,i=!0))),v=m.getColumn("x")||[],{modified:m,cropped:i,cropStart:g,closestPointRange:sd([u?v.map(u.log2lin):v],function(){return s.requireSorting&&!y&&sa(15,!1,s.chart)})}},t.prototype.processData=function(t){var e=this.xAxis,i=this.dataTable;if(this.isCartesian&&!this.isDirty&&!e.isDirty&&!this.yAxis.isDirty&&!t)return!1;var o=this.getProcessedData();i.modified=o.modified,this.cropped=o.cropped,this.cropStart=o.cropStart,this.closestPointRange=this.basePointRange=o.closestPointRange,sc(this,"afterProcessData")},t.prototype.cropData=function(t,e,i){var o,r,n=t.getColumn("x",!0)||[],s=n.length,a={},h=0,l=s;for(o=0;o<s;o++)if(n[o]>=e){h=Math.max(0,o-1);break}for(r=o;r<s;r++)if(n[r]>i){l=r+1;break}for(var c=0,d=this.dataColumnKeys();c<d.length;c++){var p=d[c],u=t.getColumn(p,!0);u&&(a[p]=u.slice(h,l))}return{modified:new nY({columns:a}),start:h,end:l}},t.prototype.generatePoints=function(){var t,e,i,o,r,n,s,a,h,l,c=this.options,d=this.processedData||c.data,p=this.dataTable.modified,u=this.getColumn("x",!0),f=this.pointClass,g=p.rowCount,v=this.cropStart||0,m=this.hasGroupedData,y=c.keys,x=[],b=(null===(t=c.dataGrouping)||void 0===t?void 0:t.groupAll)?v:0,k=null===(e=this.xAxis)||void 0===e?void 0:e.categories,w=this.pointArrayMap||["y"],M=this.dataColumnKeys(),S=this.data;if(!S&&!m){var T=[];T.length=(null==d?void 0:d.length)||0,S=this.data=T}for(y&&m&&(this.options.keys=!1),h=0;h<g;h++)s=v+h,m?((a=new f(this,p.getRow(h,M)||[])).dataGroup=this.groupMap[b+h],(null===(i=a.dataGroup)||void 0===i?void 0:i.options)&&(a.options=a.dataGroup.options,sh(a,a.dataGroup.options),delete a.dataLabels)):(a=S[s],l=d?d[s]:p.getRow(h,w),a||void 0===l||(S[s]=a=new f(this,l,u[h]))),a&&(a.index=m?b+h:s,x[h]=a,a.category=null!==(o=null==k?void 0:k[a.x])&&void 0!==o?o:a.x,a.key=null!==(r=a.name)&&void 0!==r?r:a.category);if(this.options.keys=y,S&&(g!==(n=S.length)||m))for(h=0;h<n;h++)h!==v||m||(h+=g),S[h]&&(S[h].destroyElements(),S[h].plotX=void 0);this.data=S,this.points=x,sc(this,"afterGeneratePoints")},t.prototype.getXExtremes=function(t){return{min:n7(t),max:n8(t)}},t.prototype.getExtremes=function(t,e){var i,o,r,n,s=this.xAxis,a=this.yAxis,h=e||this.getExtremesFromAll||this.options.getExtremesFromAll,l=h&&this.cropped?this.dataTable:this.dataTable.modified,c=l.rowCount,d=t||this.stackedYData,p=d?[d]:(null===(i=this.keysAffectYAxis||this.pointArrayMap||["y"])||void 0===i?void 0:i.map(function(t){return l.getColumn(t,!0)||[]}))||[],u=this.getColumn("x",!0),f=[],g=this.requireSorting&&!this.is("column")?1:0,v=!!a&&a.positiveValuesOnly,m=h||this.cropped||!s,y=0,x=0;for(s&&(y=(o=s.getExtremes()).min,x=o.max),n=0;n<c;n++)if(r=u[n],m||(u[n+g]||r)>=y&&(u[n-g]||r)<=x)for(var b=0;b<p.length;b++){var k=p[b][n];sg(k)&&(k>0||!v)&&f.push(k)}var w={activeYData:f,dataMin:n7(f),dataMax:n8(f)};return sc(this,"afterGetExtremes",{dataExtremes:w}),w},t.prototype.applyExtremes=function(){var t=this.getExtremes();return this.dataMin=t.dataMin,this.dataMax=t.dataMax,t},t.prototype.getFirstValidPoint=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=1);for(var o=t.length,r=e;r>=0&&r<o;){if(so(t[r]))return t[r];r+=i}},t.prototype.translate=function(){this.generatePoints();var t,e,i,o,r,n=this.options,s=n.stacking,a=this.xAxis,h=this.enabledDataSorting,l=this.yAxis,c=this.points,d=c.length,p=this.pointPlacementToXValue(),u=!!p,f=n.threshold,g=n.startFromThreshold?f:0,v=(null==n?void 0:n.nullInteraction)&&l.len,m=Number.MAX_VALUE;function y(t){return st(t,-1e9,1e9)}for(e=0;e<d;e++){var x=c[e],b=x.x,k=void 0,w=void 0,M=x.y,S=x.low,T=s&&(null===(t=l.stacking)||void 0===t?void 0:t.stacks[(this.negStacks&&M<(g?0:f)?"-":"")+this.stackKey]);x.plotX=sg(i=a.translate(b,!1,!1,!1,!0,p))?se(y(i)):void 0,s&&this.visible&&T&&T[b]&&(r=this.getStackIndicator(r,b,this.index),!x.isNull&&r.key&&(w=(k=T[b]).points[r.key]),k&&sf(w)&&(S=w[0],M=w[1],S===g&&r.key===T[b].base&&(S=sx(sg(f)?f:l.min)),l.positiveValuesOnly&&so(S)&&S<=0&&(S=void 0),x.total=x.stackTotal=sx(k.total),x.percentage=so(x.y)&&k.total?x.y/k.total*100:void 0,x.stackY=M,this.irregularWidths||k.setOffset(this.pointXOffset||0,this.barW||0,void 0,void 0,void 0,this.xAxis))),x.yBottom=so(S)?y(l.translate(S,!1,!0,!1,!0)):void 0,this.dataModify&&(M=this.dataModify.modifyValue(M,e));var C=void 0;sg(M)&&void 0!==x.plotX?C=sg(C=l.translate(M,!1,!0,!1,!0))?y(C):void 0:!sg(M)&&v&&(C=v),x.plotY=C,x.isInside=this.isPointInside(x),x.clientX=u?se(a.translate(b,!1,!1,!1,!0,p)):i,x.negative=(x.y||0)<(f||0),x.isNull||!1===x.visible||(void 0!==o&&(m=Math.min(m,Math.abs(i-o))),o=i),x.zone=this.zones.length?x.getZone():void 0,!x.graphic&&this.group&&h&&(x.isNew=!0)}this.closestPointRangePx=m,sc(this,"afterTranslate")},t.prototype.getValidPoints=function(t,e,i){var o=this.chart;return(t||this.points||[]).filter(function(t){var r=t.plotX,n=t.plotY;return!!((i||!t.isNull&&sg(n))&&(!e||o.isInsidePlot(r,n,{inverted:o.inverted})))&&!1!==t.visible})},t.prototype.getSharedClipKey=function(){return this.sharedClipKey=(this.options.xAxis||0)+","+(this.options.yAxis||0),this.sharedClipKey},t.prototype.setClip=function(){var t=this.chart,e=this.group,i=this.markerGroup,o=t.sharedClips,r=t.renderer,n=t.getClipBox(this),s=this.getSharedClipKey(),a=o[s];a?a.animate(n):o[s]=a=r.clipRect(n),e&&e.clip(!1===this.options.clip?void 0:a),i&&i.clip()},t.prototype.animate=function(t){var e=this.chart,i=this.group,o=this.markerGroup,r=e.inverted,n=ef(this.options.animation),s=[this.getSharedClipKey(),n.duration,n.easing,n.defer].join(","),a=e.sharedClips[s],h=e.sharedClips[s+"m"];if(t&&i){var l=e.getClipBox(this);if(a)a.attr("height",l.height);else{l.width=0,r&&(l.x=e.plotHeight),a=e.renderer.clipRect(l),e.sharedClips[s]=a;var c={x:-99,y:-99,width:r?e.plotWidth+199:99,height:r?99:e.plotHeight+199};h=e.renderer.clipRect(c),e.sharedClips[s+"m"]=h}i.clip(a),null==o||o.clip(h)}else if(a&&!a.hasClass("highcharts-animating")){var d=e.getClipBox(this),p=n.step;((null==o?void 0:o.element.childNodes.length)||e.series.length>1)&&(n.step=function(t,e){p&&p.apply(e,arguments),"width"===e.prop&&(null==h?void 0:h.element)&&h.attr(r?"height":"width",t+99)}),a.addClass("highcharts-animating").animate(d,n)}},t.prototype.afterAnimate=function(){var t=this;this.setClip(),sy(this.chart.sharedClips,function(e,i,o){e&&!t.chart.container.querySelector('[clip-path="url(#'.concat(e.id,')"]'))&&(e.destroy(),delete o[i])}),this.finishedAnimating=!0,sc(this,"afterAnimate")},t.prototype.drawPoints=function(t){void 0===t&&(t=this.points);var e,i,o,r,n,s,a,h=this.chart,l=h.styledMode,c=this.colorAxis,d=this.options,p=d.marker,u=d.nullInteraction,f=this[this.specialGroup||"markerGroup"],g=this.xAxis,v=sx(p.enabled,!g||!!g.isRadial||null,this.closestPointRangePx>=p.enabledThreshold*p.radius);if(!1!==p.enabled||this._hasPointMarkers)for(e=0;e<t.length;e++){r=(o=(i=t[e]).graphic)?"animate":"attr",n=i.marker||{},s=!!i.marker;var m=i.isNull;if((v&&!so(n.enabled)||n.enabled)&&(!m||u)&&!1!==i.visible){var y=sx(n.symbol,this.symbol,"rect");a=this.markerAttribs(i,i.selected&&"select"),this.enabledDataSorting&&(i.startXPos=g.reversed?-(a.width||0):g.width);var x=!1!==i.isInside;if(!o&&x&&((a.width||0)>0||i.hasImage)&&(i.graphic=o=h.renderer.symbol(y,a.x,a.y,a.width,a.height,s?n:p).add(f),this.enabledDataSorting&&h.hasRendered&&(o.attr({x:i.startXPos}),r="animate")),o&&"animate"===r&&o[x?"show":"hide"](x).animate(a),o){var b=this.pointAttribs(i,l||!i.selected?void 0:"select");l?c&&o.css({fill:b.fill}):o[r](b)}o&&o.addClass(i.getClassName(),!0)}else o&&(i.graphic=o.destroy())}},t.prototype.markerAttribs=function(t,e){var i,o,r=this.options,n=r.marker,s=t.marker||{},a=s.symbol||n.symbol,h={},l=sx(s.radius,null==n?void 0:n.radius);e&&(i=n.states[e],l=sx(null==(o=s.states&&s.states[e])?void 0:o.radius,null==i?void 0:i.radius,l&&l+((null==i?void 0:i.radiusPlus)||0))),t.hasImage=a&&0===a.indexOf("url"),t.hasImage&&(l=0);var c=t.pos();return sg(l)&&c&&(r.crisp&&(c[0]=si(c[0],t.hasImage?0:"rect"===a?(null==n?void 0:n.lineWidth)||0:1)),h.x=c[0]-l,h.y=c[1]-l),l&&(h.width=h.height=2*l),h},t.prototype.pointAttribs=function(t,e){var i,o,r,n,s,a=this.options,h=a.marker,l=null==t?void 0:t.options,c=(null==l?void 0:l.marker)||{},d=null==l?void 0:l.color,p=null==t?void 0:t.color,u=null===(i=null==t?void 0:t.zone)||void 0===i?void 0:i.color,f=this.color,g=sx(c.lineWidth,h.lineWidth),v=(null==t?void 0:t.isNull)&&a.nullInteraction?0:1;return f=d||u||p||f,n=c.fillColor||h.fillColor||f,s=c.lineColor||h.lineColor||f,e=e||"normal",o=h.states[e]||{},g=sx((r=c.states&&c.states[e]||{}).lineWidth,o.lineWidth,g+sx(r.lineWidthPlus,o.lineWidthPlus,0)),n=r.fillColor||o.fillColor||n,s=r.lineColor||o.lineColor||s,{stroke:s,"stroke-width":g,fill:n,opacity:v=sx(r.opacity,o.opacity,v)}},t.prototype.destroy=function(t){var e,i,o,r,n=this,s=n.chart,a=/AppleWebKit\/533/.test(n6.navigator.userAgent),h=n.data||[];for(sc(n,"destroy",{keepEventsForUpdate:t}),this.removeEvents(t),(n.axisTypes||[]).forEach(function(t){(null==(r=n[t])?void 0:r.series)&&(ss(r.series,n),r.isDirty=r.forceRedraw=!0)}),n.legendItem&&n.chart.legend.destroyItem(n),o=h.length;o--;)null===(i=null===(e=h[o])||void 0===e?void 0:e.destroy)||void 0===i||i.call(e);for(var l=0,c=n.zones;l<c.length;l++)sr(c[l],void 0,!0);tP.clearTimeout(n.animationTimeout),sy(n,function(t,e){t instanceof ib&&!t.survive&&t[a&&"group"===e?"hide":"destroy"]()}),s.hoverSeries===n&&(s.hoverSeries=void 0),ss(s.series,n),s.orderItems("series"),sy(n,function(e,i){t&&"hcEvents"===i||delete n[i]})},t.prototype.applyZones=function(){var t=this.area,e=this.chart,i=this.graph,o=this.zones,r=this.points,n=this.xAxis,s=this.yAxis,a=this.zoneAxis,h=e.inverted,l=e.renderer,c=this[""+a+"Axis"],d=c||{},p=d.isXAxis,u=d.len,f=void 0===u?0:u,g=d.minPointOffset,v=void 0===g?0:g,m=((null==i?void 0:i.strokeWidth())||0)/2+1,y=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=0),h&&(i=f-i);var o=t.translated,r=void 0===o?0:o,n=t.lineClip,s=i-r;null==n||n.push(["L",e,Math.abs(s)<m?i-m*(s<=0?-1:1):r])};if(o.length&&(i||t)&&c&&sg(c.min)){var x=c.getExtremes().max+v,b=function(t){t.forEach(function(e,i){("M"===e[0]||"L"===e[0])&&(t[i]=[e[0],p?f-e[1]:e[1],p?e[2]:f-e[2]])})};if(o.forEach(function(t){t.lineClip=[],t.translated=st(c.toPixels(sx(t.value,x),!0)||0,0,f)}),i&&!this.showLine&&i.hide(),t&&t.hide(),"y"===a&&r.length<n.len)for(var k=0;k<r.length;k++){var w=r[k],M=w.plotX,S=w.plotY,T=w.zone,C=T&&o[o.indexOf(T)-1];T&&y(T,M,S),C&&y(C,M,S)}var A=[],P=c.toPixels(c.getExtremes().min-v,!0);o.forEach(function(e){var o,r,a=e.lineClip||[],c=Math.round(e.translated||0);n.reversed&&a.reverse();var d=e.clip,u=e.simpleClip,f=0,g=0,v=n.len,m=s.len;p?(f=c,v=P):(g=c,m=P);var y=[["M",f,g],["L",v,g],["L",v,m],["L",f,m],["Z"]],x=n2(n2(n2(n2([y[0]],a,!0),[y[1],y[2]],!1),A,!0),[y[3],y[4]],!1);A=a.reverse(),P=c,h&&(b(x),t&&b(y)),d?(d.animate({d:x}),null==u||u.animate({d:y})):(d=e.clip=l.path(x),t&&(u=e.simpleClip=l.path(y))),i&&(null===(o=e.graph)||void 0===o||o.clip(d)),t&&(null===(r=e.area)||void 0===r||r.clip(u))})}else this.visible&&(i&&i.show(),t&&t.show())},t.prototype.plotGroup=function(t,e,i,o,r){var n=this[t],s=!n,a={visibility:i,zIndex:o||.1};return so(this.opacity)&&!this.chart.styledMode&&"inactive"!==this.state&&(a.opacity=this.opacity),n||(this[t]=n=this.chart.renderer.g().add(r)),n.addClass("highcharts-"+e+" highcharts-series-"+this.index+" highcharts-"+this.type+"-series "+(so(this.colorIndex)?"highcharts-color-"+this.colorIndex+" ":"")+(this.options.className||"")+(n.hasClass("highcharts-tracker")?" highcharts-tracker":""),!0),n.attr(a)[s?"attr":"animate"](this.getPlotBox(e)),n},t.prototype.getPlotBox=function(t){var e=this.xAxis,i=this.yAxis,o=this.chart,r=o.inverted&&!o.polar&&e&&this.invertible&&"series"===t;return o.inverted&&(e=i,i=this.xAxis),{translateX:e?e.left:o.plotLeft,translateY:i?i.top:o.plotTop,rotation:90*!!r,rotationOriginX:r?(e.len-i.len)/2:0,rotationOriginY:r?(e.len+i.len)/2:0,scaleX:r?-1:1,scaleY:1}},t.prototype.removeEvents=function(t){var e=this.eventsToUnbind;t||sb(this),e.length&&(e.forEach(function(t){t()}),e.length=0)},t.prototype.render=function(){var t,e,i,o,r,n=this,s=n.chart,a=n.options,h=n.hasRendered,l=ef(a.animation),c=n.visible?"inherit":"hidden",d=a.zIndex,p=s.seriesGroup,u=n.finishedAnimating?0:l.duration;sc(this,"render"),n.plotGroup("group","series",c,d,p),n.markerGroup=n.plotGroup("markerGroup","markers",c,d,p),!1!==a.clip&&n.setClip(),u&&(null===(t=n.animate)||void 0===t||t.call(n,!0)),n.drawGraph&&(n.drawGraph(),n.applyZones()),n.visible&&n.drawPoints(),null===(e=n.drawDataLabels)||void 0===e||e.call(n),null===(i=n.redrawPoints)||void 0===i||i.call(n),a.enableMouseTracking&&(null===(o=n.drawTracker)||void 0===o||o.call(n)),u&&(null===(r=n.animate)||void 0===r||r.call(n)),h||(u&&l.defer&&(u+=l.defer),n.animationTimeout=sk(function(){n.afterAnimate()},u||0)),n.isDirty=!1,n.hasRendered=!0,sc(n,"afterRender")},t.prototype.redraw=function(){var t=this.isDirty||this.isDirtyData;this.translate(),this.render(),t&&delete this.kdTree},t.prototype.reserveSpace=function(){return this.visible||!this.chart.options.chart.ignoreHiddenSeries},t.prototype.searchPoint=function(t,e){var i=this.xAxis,o=this.yAxis,r=this.chart.inverted;return this.searchKDTree({clientX:r?i.len-t.chartY+i.pos:t.chartX-i.pos,plotY:r?o.len-t.chartX+o.pos:t.chartY-o.pos},e,t)},t.prototype.buildKDTree=function(t){this.buildingKdTree=!0;var e=this,i=e.options,o=i.findNearestPointBy.indexOf("y")>-1?2:1;delete e.kdTree,sk(function(){e.kdTree=function t(i,o,r){var n,s,a=null==i?void 0:i.length;if(a)return n=e.kdAxisArray[o%r],i.sort(function(t,e){return(t[n]||0)-(e[n]||0)}),{point:i[s=Math.floor(a/2)],left:t(i.slice(0,s),o+1,r),right:t(i.slice(s+1),o+1,r)}}(e.getValidPoints(void 0,!e.directTouch,null==i?void 0:i.nullInteraction),o,o),e.buildingKdTree=!1},i.kdNow||(null==t?void 0:t.type)==="touchstart"?0:1)},t.prototype.searchKDTree=function(t,e,i,o,r){var n=this,s=this.kdAxisArray,a=s[0],h=s[1],l=e?"distX":"dist",c=(n.options.findNearestPointBy||"").indexOf("y")>-1?2:1,d=!!n.isBubble,p=o||function(t,e,i){var o=t[i]||0,r=e[i]||0;return[o===r&&t.index>e.index||o<r?t:e,!1]},u=r||function(t,e){return t<e};if(this.kdTree||this.buildingKdTree||this.buildKDTree(i),this.kdTree)return function t(e,i,o,r){var s,c,f,g,v,m,y,x,b,k,w=i.point,M=n.kdAxisArray[o%r],S=w,T=!1;c=e[a],f=w[a],g=so(c)&&so(f)?c-f:null,v=e[h],m=w[h],y=so(v)&&so(m)?v-m:0,x=d&&(null===(s=w.marker)||void 0===s?void 0:s.radius)||0,w.dist=Math.sqrt((g&&g*g||0)+y*y)-x,w.distX=so(g)?Math.abs(g)-x:Number.MAX_VALUE;var C=(e[M]||0)-(w[M]||0)+(d&&(null===(k=w.marker)||void 0===k?void 0:k.radius)||0),A=C<0?"left":"right",P=C<0?"right":"left";return i[A]&&(S=(b=p(w,t(e,i[A],o+1,r),l))[0],T=b[1]),i[P]&&u(Math.sqrt(C*C),S[l],T)&&(S=p(S,t(e,i[P],o+1,r),l)[0]),S}(t,this.kdTree,c,c)},t.prototype.pointPlacementToXValue=function(){var t=this.options,e=this.xAxis,i=t.pointPlacement;return"between"===i&&(i=e.reversed?-.5:.5),sg(i)?i*(t.pointRange||e.pointRange):0},t.prototype.isPointInside=function(t){var e=this.chart,i=this.xAxis,o=this.yAxis,r=t.plotX,n=void 0===r?-1:r,s=t.plotY,a=void 0===s?-1:s;return a>=0&&a<=(o?o.len:e.plotHeight)&&n>=0&&n<=(i?i.len:e.plotWidth)},t.prototype.drawTracker=function(){var t,e=this,i=e.options,o=i.trackByArea,r=[].concat((o?e.areaPath:e.graphPath)||[]),n=e.chart,s=n.pointer,a=n.renderer,h=(null===(t=n.options.tooltip)||void 0===t?void 0:t.snap)||0,l=function(){i.enableMouseTracking&&n.hoverSeries!==e&&e.onMouseOver()},c="rgba(192,192,192,"+(n5?1e-4:.002)+")",d=e.tracker;d?d.attr({d:r}):e.graph&&(e.tracker=d=a.path(r).attr({visibility:e.visible?"inherit":"hidden",zIndex:2}).addClass(o?"highcharts-tracker-area":"highcharts-tracker-line").add(e.group),n.styledMode||d.attr({"stroke-linecap":"round","stroke-linejoin":"round",stroke:c,fill:o?c:"none","stroke-width":e.graph.strokeWidth()+(o?0:2*h)}),[e.tracker,e.markerGroup,e.dataLabelsGroup].forEach(function(t){t&&(t.addClass("highcharts-tracker").on("mouseover",l).on("mouseout",function(t){null==s||s.onTrackerMouseOut(t)}),i.cursor&&!n.styledMode&&t.css({cursor:i.cursor}),t.on("touchstart",l))})),sc(this,"afterDrawTracker")},t.prototype.addPoint=function(t,e,i,o,r){var n,s,a=this.options,h=this.chart,l=this.data,c=this.dataTable,d=this.xAxis,p=(null==d?void 0:d.hasNames)&&d.names,u=a.data,f=this.getColumn("x");e=sx(e,!0);var g={series:this};this.pointClass.prototype.applyOptions.apply(g,[t]);var v=g.x;if(s=f.length,this.requireSorting&&v<f[s-1])for(n=!0;s&&f[s-1]>v;)s--;c.setRow(g,s,!0,{addColumns:!1}),p&&g.name&&(p[v]=g.name),null==u||u.splice(s,0,t),(n||this.processedData)&&(this.data.splice(s,0,null),this.processData()),"point"===a.legendType&&this.generatePoints(),i&&(l[0]&&l[0].remove?l[0].remove(!1):([l,u].filter(so).forEach(function(t){t.shift()}),c.deleteRows(0))),!1!==r&&sc(this,"addPoint",{point:g}),this.isDirty=!0,this.isDirtyData=!0,e&&h.redraw(o)},t.prototype.removePoint=function(t,e,i){var o=this,r=o.chart,n=o.data,s=o.points,a=o.dataTable,h=n[t],l=function(){[(null==s?void 0:s.length)===n.length?s:void 0,n,o.options.data].filter(so).forEach(function(e){e.splice(t,1)}),a.deleteRows(t),null==h||h.destroy(),o.isDirty=!0,o.isDirtyData=!0,e&&r.redraw()};ey(i,r),e=sx(e,!0),h?h.firePointEvent("remove",null,l):l()},t.prototype.remove=function(t,e,i,o){var r=this,n=r.chart;function s(){r.destroy(o),n.isDirtyLegend=n.isDirtyBox=!0,n.linkSeries(o),sx(t,!0)&&n.redraw(e)}!1!==i?sc(r,"remove",null,s):s()},t.prototype.update=function(e,i){sc(this,"update",{options:e=sn(e,this.userOptions)});var o,r,n,s,a,h,l=this,c=l.chart,d=l.userOptions,p=l.initialType||l.type,u=c.options.plotOptions,f=n9[p].prototype,g=l.finishedAnimating&&{animation:!1},v={},m=t.keepProps.slice(),y=e.type||d.type||c.options.chart.type,x=!(this.hasDerivedData||y&&y!==this.type||void 0!==e.keys||void 0!==e.pointStart||void 0!==e.pointInterval||void 0!==e.relativeXValue||e.joinBy||e.mapData||["dataGrouping","pointStart","pointInterval","pointIntervalUnit","keys"].some(function(t){return l.hasOptionChanged(t)}));y=y||p,x?(m.push.apply(m,t.keepPropsForPoints),!1!==e.visible&&m.push("area","graph"),l.parallelArrays.forEach(function(t){m.push(t+"Data")}),e.data&&(e.dataSorting&&sh(l.options.dataSorting,e.dataSorting),this.setData(e.data,!1))):this.dataTable.modified=this.dataTable,e=sm(d,{index:void 0===d.index?l.index:d.index,pointStart:null!==(n=null!==(r=null===(o=null==u?void 0:u.series)||void 0===o?void 0:o.pointStart)&&void 0!==r?r:d.pointStart)&&void 0!==n?n:l.getColumn("x")[0]},!x&&{data:l.options.data},e,g),x&&e.data&&(e.data=l.options.data),(m=["group","markerGroup","dataLabelsGroup","transformGroup"].concat(m)).forEach(function(t){m[t]=l[t],delete l[t]});var b=!1;if(n9[y]){if(b=y!==l.type,l.remove(!1,!1,!1,!0),b){if(c.propFromSeries(),Object.setPrototypeOf)Object.setPrototypeOf(l,n9[y].prototype);else{var k=Object.hasOwnProperty.call(l,"hcEvents")&&l.hcEvents;for(h in f)l[h]=void 0;sh(l,n9[y].prototype),k?l.hcEvents=k:delete l.hcEvents}}}else sa(17,!0,c,{missingModuleFor:y});if(m.forEach(function(t){l[t]=m[t]}),l.init(c,e),x&&this.points){!1===(a=l.options).visible?(v.graphic=1,v.dataLabel=1):(this.hasMarkerChanged(a,d)&&(v.graphic=1),(null===(s=l.hasDataLabels)||void 0===s?void 0:s.call(l))||(v.dataLabel=1));for(var w=0,M=this.points;w<M.length;w++){var S=M[w];(null==S?void 0:S.series)&&(S.resolveColor(),Object.keys(v).length&&S.destroyElements(v),!1===a.showInLegend&&S.legendItem&&c.legend.destroyItem(S))}}l.initialType=p,c.linkSeries(),c.setSortedData(),b&&l.linkedSeries.length&&(l.isDirtyData=!0),sc(this,"afterUpdate"),sx(i,!0)&&c.redraw(!!x&&void 0)},t.prototype.setName=function(t){this.name=this.options.name=this.userOptions.name=t,this.chart.isDirtyLegend=!0},t.prototype.hasOptionChanged=function(t){var e,i,o=this.chart,r=this.options[t],n=o.options.plotOptions,s=this.userOptions[t],a=sx(null===(e=null==n?void 0:n[this.type])||void 0===e?void 0:e[t],null===(i=null==n?void 0:n.series)||void 0===i?void 0:i[t]);return s&&!so(a)?r!==s:r!==sx(a,r)},t.prototype.onMouseOver=function(){var t=this.chart,e=t.hoverSeries,i=t.pointer;null==i||i.setHoverChartIndex(),e&&e!==this&&e.onMouseOut(),this.options.events.mouseOver&&sc(this,"mouseOver"),this.setState("hover"),t.hoverSeries=this},t.prototype.onMouseOut=function(){var t=this.options,e=this.chart,i=e.tooltip,o=e.hoverPoint;e.hoverSeries=null,o&&o.onMouseOut(),this&&t.events.mouseOut&&sc(this,"mouseOut"),i&&!this.stickyTracking&&(!i.shared||this.noSharedTooltip)&&i.hide(),e.series.forEach(function(t){t.setState("",!0)})},t.prototype.setState=function(t,e){var i=this,o=i.options,r=i.graph,n=o.inactiveOtherPoints,s=o.states,a=sx(s[t||"normal"]&&s[t||"normal"].animation,i.chart.options.chart.animation),h=o.lineWidth,l=o.opacity;if(t=t||"",i.state!==t&&([i.group,i.markerGroup,i.dataLabelsGroup].forEach(function(e){e&&(i.state&&e.removeClass("highcharts-series-"+i.state),t&&e.addClass("highcharts-series-"+t))}),i.state=t,!i.chart.styledMode)){if(s[t]&&!1===s[t].enabled)return;if(t&&(h=s[t].lineWidth||h+(s[t].lineWidthPlus||0),l=sx(s[t].opacity,l)),r&&!r.dashstyle&&sg(h))for(var c=0,d=n2([r],this.zones.map(function(t){return t.graph}),!0);c<d.length;c++){var p=d[c];null==p||p.animate({"stroke-width":h},a)}n||[i.group,i.markerGroup,i.dataLabelsGroup,i.labelBySeries].forEach(function(t){t&&t.animate({opacity:l},a)})}e&&n&&i.points&&i.setAllPointsToState(t||void 0)},t.prototype.setAllPointsToState=function(t){this.points.forEach(function(e){e.setState&&e.setState(t)})},t.prototype.setVisible=function(t,e){var i,o=this,r=o.chart,n=r.options.chart.ignoreHiddenSeries,s=o.visible;o.visible=t=o.options.visible=o.userOptions.visible=void 0===t?!s:t;var a=t?"show":"hide";["group","dataLabelsGroup","markerGroup","tracker","tt"].forEach(function(t){var e;null===(e=o[t])||void 0===e||e[a]()}),(r.hoverSeries===o||(null===(i=r.hoverPoint)||void 0===i?void 0:i.series)===o)&&o.onMouseOut(),o.legendItem&&r.legend.colorizeItem(o,t),o.isDirty=!0,o.options.stacking&&r.series.forEach(function(t){t.options.stacking&&t.visible&&(t.isDirty=!0)}),o.linkedSeries.forEach(function(e){e.setVisible(t,!1)}),n&&(r.isDirtyBox=!0),sc(o,a),!1!==e&&r.redraw()},t.prototype.show=function(){this.setVisible(!0)},t.prototype.hide=function(){this.setVisible(!1)},t.prototype.select=function(t){this.selected=t=this.options.selected=void 0===t?!this.selected:t,this.checkbox&&(this.checkbox.checked=t),sc(this,t?"select":"unselect")},t.prototype.shouldShowTooltip=function(t,e,i){return void 0===i&&(i={}),i.series=this,i.visiblePlotOnly=!0,this.chart.isInsidePlot(t,e,i)},t.prototype.drawLegendSymbol=function(t,e){var i;null===(i=nZ[this.options.legendSymbol||"rectangle"])||void 0===i||i.call(this,t,e)},t.defaultOptions=nq,t.types=n0.seriesTypes,t.registerType=n0.registerSeriesType,t.keepProps=["colorIndex","eventOptions","navigatorSeries","symbolIndex","baseSeries"],t.keepPropsForPoints=["data","isDirtyData","isDirtyCanvas","points","dataTable","processedData","xIncrement","cropped","_hasPointMarkers","hasDataLabels","nodes","layout","level","mapMap","mapData","minY","maxY","minX","maxX","transformGroups"],t}();sh(sw.prototype,{axisTypes:["xAxis","yAxis"],coll:"series",colorCounter:0,directTouch:!1,invertible:!0,isCartesian:!0,kdAxisArray:["clientX","plotY"],parallelArrays:["x","y"],pointClass:ng,requireSorting:!0,sorted:!0}),n0.series=sw;var sM=oN.registerEventOptions,sS=ti.composed,sT=ti.marginNames,sC=eJ.distribute,sA=eU.format,sP=tP.addEvent,sO=tP.createElement,sL=tP.css,sE=tP.defined,sI=tP.discardElement,sD=tP.find,sB=tP.fireEvent,sN=tP.isNumber,sz=tP.merge,sR=tP.pick,sW=tP.pushUnique,sX=tP.relativeLength,sH=tP.stableSort,sj=tP.syncTimeout,sF=function(){function t(t,e){var i=this;this.allItems=[],this.initialItemY=0,this.itemHeight=0,this.itemMarginBottom=0,this.itemMarginTop=0,this.itemX=0,this.itemY=0,this.lastItemY=0,this.lastLineHeight=0,this.legendHeight=0,this.legendWidth=0,this.maxItemWidth=0,this.maxLegendWidth=0,this.offsetWidth=0,this.padding=0,this.pages=[],this.symbolHeight=0,this.symbolWidth=0,this.titleHeight=0,this.totalItemWidth=0,this.widthOption=0,this.chart=t,this.setOptions(e),e.enabled&&(this.render(),sM(this,e),sP(this.chart,"endResize",function(){this.legend.positionCheckboxes()})),sP(this.chart,"render",function(){i.options.enabled&&i.proximate&&(i.proximatePositions(),i.positionItems())})}return t.prototype.setOptions=function(t){var e=sR(t.padding,8);this.options=t,this.chart.styledMode||(this.itemStyle=t.itemStyle,this.itemHiddenStyle=sz(this.itemStyle,t.itemHiddenStyle)),this.itemMarginTop=t.itemMarginTop,this.itemMarginBottom=t.itemMarginBottom,this.padding=e,this.initialItemY=e-5,this.symbolWidth=sR(t.symbolWidth,16),this.pages=[],this.proximate="proximate"===t.layout&&!this.chart.inverted,this.baseline=void 0},t.prototype.update=function(t,e){var i=this.chart;this.setOptions(sz(!0,this.options,t)),"events"in this.options&&sM(this,this.options),this.destroy(),i.isDirtyLegend=i.isDirtyBox=!0,sR(e,!0)&&i.redraw(),sB(this,"afterUpdate",{redraw:e})},t.prototype.colorizeItem=function(t,e){var i,o=t.color,r=t.legendItem||{},n=r.area,s=r.group,a=r.label,h=r.line,l=r.symbol;if((t instanceof sw||t instanceof ng)&&(t.color=(null===(i=t.options)||void 0===i?void 0:i.legendSymbolColor)||o),null==s||s[e?"removeClass":"addClass"]("highcharts-legend-item-hidden"),!this.chart.styledMode){var c=this.itemHiddenStyle,d=void 0===c?{}:c,p=d.color,u=t.options,f=u.fillColor,g=u.fillOpacity,v=u.lineColor,m=u.marker,y=function(t){return!e&&(t.fill&&(t.fill=p),t.stroke&&(t.stroke=p)),t};null==a||a.css(sz(e?this.itemStyle:d)),null==h||h.attr(y({stroke:v||t.color})),l&&l.attr(y(m&&l.isMarker?t.pointAttribs():{fill:t.color})),null==n||n.attr(y({fill:f||t.color,"fill-opacity":f?1:null!=g?g:.75}))}t.color=o,sB(this,"afterColorizeItem",{item:t,visible:e})},t.prototype.positionItems=function(){this.allItems.forEach(this.positionItem,this),this.chart.isResizing||this.positionCheckboxes()},t.prototype.positionItem=function(t){var e=this,i=t.legendItem||{},o=i.group,r=i.x,n=void 0===r?0:r,s=i.y,a=void 0===s?0:s,h=this.options,l=h.symbolPadding,c=!h.rtl,d=t.checkbox;if(null==o?void 0:o.element){var p={translateX:c?n:this.legendWidth-n-2*l-4,translateY:a};o[sE(o.translateY)?"animate":"attr"](p,void 0,function(){sB(e,"afterPositionItem",{item:t})})}d&&(d.x=n,d.y=a)},t.prototype.destroyItem=function(t){for(var e=t.checkbox,i=t.legendItem||{},o=0,r=["group","label","line","symbol"];o<r.length;o++){var n=r[o];i[n]&&(i[n]=i[n].destroy())}e&&sI(e),t.legendItem=void 0},t.prototype.destroy=function(){for(var t=0,e=this.getAllItems();t<e.length;t++){var i=e[t];this.destroyItem(i)}for(var o=0,r=["clipRect","up","down","pager","nav","box","title","group"];o<r.length;o++){var n=r[o];this[n]&&(this[n]=this[n].destroy())}this.display=null},t.prototype.positionCheckboxes=function(){var t,e,i=null===(t=this.group)||void 0===t?void 0:t.alignAttr,o=this.clipHeight||this.legendHeight,r=this.titleHeight;i&&(e=i.translateY,this.allItems.forEach(function(t){var n,s=t.checkbox;s&&(n=e+r+s.y+(this.scrollOffset||0)+3,sL(s,{left:i.translateX+t.checkboxOffset+s.x-20+"px",top:n+"px",display:this.proximate||n>e-6&&n<e+o-6?"":"none"}))},this))},t.prototype.renderTitle=function(){var t,e=this.options,i=this.padding,o=e.title,r=0;o.text&&(this.title||(this.title=this.chart.renderer.label(o.text,i-3,i-4,void 0,void 0,void 0,e.useHTML,void 0,"legend-title").attr({zIndex:1}),this.chart.styledMode||this.title.css(o.style),this.title.add(this.group)),o.width||this.title.css({width:this.maxLegendWidth+"px"}),r=(t=this.title.getBBox()).height,this.offsetWidth=t.width,this.contentGroup.attr({translateY:r})),this.titleHeight=r},t.prototype.setText=function(t){var e=this.options;t.legendItem.label.attr({text:e.labelFormat?sA(e.labelFormat,t,this.chart):e.labelFormatter.call(t)})},t.prototype.renderItem=function(t){var e,i=t.legendItem=t.legendItem||{},o=this.chart,r=o.renderer,n=this.options,s="horizontal"===n.layout,a=this.symbolWidth,h=n.symbolPadding||0,l=this.itemStyle,c=this.itemHiddenStyle,d=s?sR(n.itemDistance,20):0,p=!n.rtl,u=!t.series,f=!u&&t.series.drawLegendSymbol?t.series:t,g=f.options,v=!!this.createCheckboxForItem&&g&&g.showCheckbox,m=n.useHTML,y=t.options.className,x=i.label,b=a+h+d+20*!!v;!x&&(i.group=r.g("legend-item").addClass("highcharts-"+f.type+"-series highcharts-color-"+t.colorIndex+(y?" "+y:"")+(u?" highcharts-series-"+t.index:"")).attr({zIndex:1}).add(this.scrollGroup),i.label=x=r.text("",p?a+h:-h,this.baseline||0,m),o.styledMode||x.css(sz(t.visible?l:c)),x.attr({align:p?"left":"right",zIndex:2}).add(i.group),!this.baseline&&(this.fontMetrics=r.fontMetrics(x),this.baseline=this.fontMetrics.f+3+this.itemMarginTop,x.attr("y",this.baseline),this.symbolHeight=sR(n.symbolHeight,this.fontMetrics.f),n.squareSymbol&&(this.symbolWidth=sR(n.symbolWidth,Math.max(this.symbolHeight,16)),b=this.symbolWidth+h+d+20*!!v,p&&x.attr("x",this.symbolWidth+h))),f.drawLegendSymbol(this,t),this.setItemEvents&&this.setItemEvents(t,x,m)),v&&!t.checkbox&&this.createCheckboxForItem&&this.createCheckboxForItem(t),this.colorizeItem(t,t.visible),(o.styledMode||!l.width)&&x.css({width:(n.itemWidth||this.widthOption||o.spacingBox.width)-b+"px"}),this.setText(t);var k=x.getBBox(),w=(null===(e=this.fontMetrics)||void 0===e?void 0:e.h)||0;t.itemWidth=t.checkboxOffset=n.itemWidth||i.labelWidth||k.width+b,this.maxItemWidth=Math.max(this.maxItemWidth,t.itemWidth),this.totalItemWidth+=t.itemWidth,this.itemHeight=t.itemHeight=Math.round(i.labelHeight||(k.height>1.5*w?k.height:w))},t.prototype.layoutItem=function(t){var e=this.options,i=this.padding,o="horizontal"===e.layout,r=t.itemHeight,n=this.itemMarginBottom,s=this.itemMarginTop,a=o?sR(e.itemDistance,20):0,h=this.maxLegendWidth,l=e.alignColumns&&this.totalItemWidth>h?this.maxItemWidth:t.itemWidth,c=t.legendItem||{};o&&this.itemX-i+l>h&&(this.itemX=i,this.lastLineHeight&&(this.itemY+=s+this.lastLineHeight+n),this.lastLineHeight=0),this.lastItemY=s+this.itemY+n,this.lastLineHeight=Math.max(r,this.lastLineHeight),c.x=this.itemX,c.y=this.itemY,o?this.itemX+=l:(this.itemY+=s+r+n,this.lastLineHeight=r),this.offsetWidth=this.widthOption||Math.max((o?this.itemX-i-(t.checkbox?0:a):l)+i,this.offsetWidth)},t.prototype.getAllItems=function(){var t=[];return this.chart.series.forEach(function(e){var i,o=null==e?void 0:e.options;e&&sR(o.showInLegend,!sE(o.linkedTo)&&void 0,!0)&&(t=t.concat((null===(i=e.legendItem)||void 0===i?void 0:i.labels)||("point"===o.legendType?e.data:e)))}),sB(this,"afterGetAllItems",{allItems:t}),t},t.prototype.getAlignment=function(){var t=this.options;return this.proximate?t.align.charAt(0)+"tv":t.floating?"":t.align.charAt(0)+t.verticalAlign.charAt(0)+t.layout.charAt(0)},t.prototype.adjustMargins=function(t,e){var i=this.chart,o=this.options,r=this.getAlignment();r&&[/(lth|ct|rth)/,/(rtv|rm|rbv)/,/(rbh|cb|lbh)/,/(lbv|lm|ltv)/].forEach(function(n,s){n.test(r)&&!sE(t[s])&&(i[sT[s]]=Math.max(i[sT[s]],i.legend[(s+1)%2?"legendHeight":"legendWidth"]+[1,-1,-1,1][s]*o[s%2?"x":"y"]+sR(o.margin,12)+e[s]+(i.titleOffset[s]||0)))})},t.prototype.proximatePositions=function(){var t,e=this.chart,i=[],o="left"===this.options.align;this.allItems.forEach(function(t){var r,n,s,a,h=o;t.yAxis&&(t.xAxis.options.reversed&&(h=!h),t.points&&(r=sD(h?t.points:t.points.slice(0).reverse(),function(t){return sN(t.plotY)})),n=this.itemMarginTop+t.legendItem.label.getBBox().height+this.itemMarginBottom,a=t.yAxis.top-e.plotTop,s=t.visible?(r?r.plotY:t.yAxis.height)+(a-.3*n):a+t.yAxis.height,i.push({target:s,size:n,item:t}))},this);for(var r=0,n=sC(i,e.plotHeight);r<n.length;r++){var s=n[r];t=s.item.legendItem||{},sN(s.pos)&&(t.y=e.plotTop-e.spacing[0]+s.pos)}},t.prototype.render=function(){var t,e,i,o,r=this.chart,n=r.renderer,s=this.options,a=this.padding,h=this.getAllItems(),l=this.group,c=this.box;this.itemX=a,this.itemY=this.initialItemY,this.offsetWidth=0,this.lastItemY=0,this.widthOption=sX(s.width,r.spacingBox.width-a),o=r.spacingBox.width-2*a-s.x,["rm","lm"].indexOf(this.getAlignment().substring(0,2))>-1&&(o/=2),this.maxLegendWidth=this.widthOption||o,l||(this.group=l=n.g("legend").addClass(s.className||"").attr({zIndex:7}).add(),this.contentGroup=n.g().attr({zIndex:1}).add(l),this.scrollGroup=n.g().add(this.contentGroup)),this.renderTitle(),sH(h,function(t,e){var i,o;return((null===(i=t.options)||void 0===i?void 0:i.legendIndex)||0)-((null===(o=e.options)||void 0===o?void 0:o.legendIndex)||0)}),s.reversed&&h.reverse(),this.allItems=h,this.display=t=!!h.length,this.lastLineHeight=0,this.maxItemWidth=0,this.totalItemWidth=0,this.itemHeight=0,h.forEach(this.renderItem,this),h.forEach(this.layoutItem,this),e=(this.widthOption||this.offsetWidth)+a,i=this.lastItemY+this.lastLineHeight+this.titleHeight,i=this.handleOverflow(i)+a,c||(this.box=c=n.rect().addClass("highcharts-legend-box").attr({r:s.borderRadius}).add(l)),r.styledMode||c.attr({stroke:s.borderColor,"stroke-width":s.borderWidth||0,fill:s.backgroundColor||"none"}).shadow(s.shadow),e>0&&i>0&&c[c.placed?"animate":"attr"](c.crisp.call({},{x:0,y:0,width:e,height:i},c.strokeWidth())),l[t?"show":"hide"](),r.styledMode&&"none"===l.getStyle("display")&&(e=i=0),this.legendWidth=e,this.legendHeight=i,t&&this.align(),this.proximate||this.positionItems(),sB(this,"afterRender")},t.prototype.align=function(t){void 0===t&&(t=this.chart.spacingBox);var e=this.chart,i=this.options,o=t.y;/(lth|ct|rth)/.test(this.getAlignment())&&e.titleOffset[0]>0?o+=e.titleOffset[0]:/(lbh|cb|rbh)/.test(this.getAlignment())&&e.titleOffset[2]>0&&(o-=e.titleOffset[2]),o!==t.y&&(t=sz(t,{y:o})),e.hasRendered||(this.group.placed=!1),this.group.align(sz(i,{width:this.legendWidth,height:this.legendHeight,verticalAlign:this.proximate?"top":i.verticalAlign}),!0,t)},t.prototype.handleOverflow=function(t){var e,i,o,r,n=this,s=this.chart,a=s.renderer,h=this.options,l=h.y,c="top"===h.verticalAlign,d=this.padding,p=h.maxHeight,u=h.navigation,f=sR(u.animation,!0),g=u.arrowSize||12,v=this.pages,m=this.allItems,y=function(t){"number"==typeof t?w.attr({height:t}):w&&(n.clipRect=w.destroy(),n.contentGroup.clip()),n.contentGroup.div&&(n.contentGroup.div.style.clip=t?"rect("+d+"px,9999px,"+(d+t)+"px,0)":"auto")},x=function(t){return n[t]=a.circle(0,0,1.3*g).translate(g/2,g/2).add(k),s.styledMode||n[t].attr("fill","rgba(0,0,0,0.0001)"),n[t]},b=s.spacingBox.height+(c?-l:l)-d,k=this.nav,w=this.clipRect;return"horizontal"!==h.layout||"middle"===h.verticalAlign||h.floating||(b/=2),p&&(b=Math.min(b,p)),v.length=0,t&&b>0&&t>b&&!1!==u.enabled?(this.clipHeight=e=Math.max(b-20-this.titleHeight-d,0),this.currentPage=sR(this.currentPage,1),this.fullHeight=t,m.forEach(function(t,n){var s=(o=t.legendItem||{}).y||0,a=Math.round(o.label.getBBox().height),h=v.length;(!h||s-v[h-1]>e&&(i||s)!==v[h-1])&&(v.push(i||s),h++),o.pageIx=h-1,i&&r&&(r.pageIx=h-1),n===m.length-1&&s+a-v[h-1]>e&&s>v[h-1]&&(v.push(s),o.pageIx=h),s!==i&&(i=s),r=o}),w||(w=n.clipRect=a.clipRect(0,d-2,9999,0),n.contentGroup.clip(w)),y(e),k||(this.nav=k=a.g().attr({zIndex:1}).add(this.group),this.up=a.symbol("triangle",0,0,g,g).add(k),x("upTracker").on("click",function(){n.scroll(-1,f)}),this.pager=a.text("",15,10).addClass("highcharts-legend-navigation"),!s.styledMode&&u.style&&this.pager.css(u.style),this.pager.add(k),this.down=a.symbol("triangle-down",0,0,g,g).add(k),x("downTracker").on("click",function(){n.scroll(1,f)})),n.scroll(0),t=b):k&&(y(),this.nav=k.destroy(),this.scrollGroup.attr({translateY:1}),this.clipHeight=0),t},t.prototype.scroll=function(t,e){var i=this,o=this.chart,r=this.pages,n=r.length,s=this.clipHeight,a=this.options.navigation,h=this.pager,l=this.padding,c=this.currentPage+t;c>n&&(c=n),c>0&&(void 0!==e&&ey(e,o),this.nav.attr({translateX:l,translateY:s+this.padding+7+this.titleHeight,visibility:"inherit"}),[this.up,this.upTracker].forEach(function(t){t.attr({class:1===c?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"})}),h.attr({text:c+"/"+n}),[this.down,this.downTracker].forEach(function(t){t.attr({x:18+this.pager.getBBox().width,class:c===n?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"})},this),o.styledMode||(this.up.attr({fill:1===c?a.inactiveColor:a.activeColor}),this.upTracker.css({cursor:1===c?"default":"pointer"}),this.down.attr({fill:c===n?a.inactiveColor:a.activeColor}),this.downTracker.css({cursor:c===n?"default":"pointer"})),this.scrollOffset=-r[c-1]+this.initialItemY,this.scrollGroup.animate({translateY:this.scrollOffset}),this.currentPage=c,this.positionCheckboxes(),sj(function(){sB(i,"afterScroll",{currentPage:c})},ef(sR(e,o.renderer.globalAnimation,!0)).duration))},t.prototype.setItemEvents=function(t,e,i){for(var o=this,r=t.legendItem||{},n=o.chart.renderer.boxWrapper,s=t instanceof ng,a=t instanceof sw,h="highcharts-legend-"+(s?"point":"series")+"-active",l=o.chart.styledMode,c=i?[e,r.symbol]:[r.group],d=function(e){o.allItems.forEach(function(i){t!==i&&[i].concat(i.linkedSeries||[]).forEach(function(t){t.setState(e,!s)})})},p=0;p<c.length;p++){var u=c[p];u&&u.on("mouseover",function(){t.visible&&d("inactive"),t.setState("hover"),t.visible&&n.addClass(h),l||e.css(o.options.itemHoverStyle)}).on("mouseout",function(){o.chart.styledMode||e.css(sz(t.visible?o.itemStyle:o.itemHiddenStyle)),d(""),n.removeClass(h),t.setState()}).on("click",function(e){var i=function(){t.setVisible&&t.setVisible(),d(t.visible?"inactive":"")};n.removeClass(h),sB(o,"itemClick",{browserEvent:e,legendItem:t},i),s?t.firePointEvent("legendItemClick",{browserEvent:e}):a&&sB(t,"legendItemClick",{browserEvent:e})})}},t.prototype.createCheckboxForItem=function(t){t.checkbox=sO("input",{type:"checkbox",className:"highcharts-legend-checkbox",checked:t.selected,defaultChecked:t.selected},this.options.itemCheckboxStyle,this.chart.container),sP(t.checkbox,"click",function(e){var i=e.target;sB(t.series||t,"checkboxClick",{checked:i.checked,item:t},function(){t.select()})})},t}();(y=sF||(sF={})).compose=function(t){sW(sS,"Core.Legend")&&sP(t,"beforeMargins",function(){this.legend=new y(this,this.options.legend)})};var sY=sF,sG=function(){return(sG=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},s_=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},sU=eU.numberFormat,sV=oN.registerEventOptions,sZ=ti.charts,sq=ti.doc,sK=ti.marginNames,s$=ti.svg,sJ=ti.win,sQ=n0.seriesTypes,s0=tP.addEvent,s1=tP.attr,s2=tP.createElement,s3=tP.css,s5=tP.defined,s6=tP.diffObjects,s9=tP.discardElement,s4=tP.erase,s8=tP.error,s7=tP.extend,at=tP.find,ae=tP.fireEvent,ai=tP.getAlignFactor,ao=tP.getStyle,ar=tP.isArray,an=tP.isNumber,as=tP.isObject,aa=tP.isString,ah=tP.merge,al=tP.objectEach,ac=tP.pick,ad=tP.pInt,ap=tP.relativeLength,au=tP.removeEvent,af=tP.splat,ag=tP.syncTimeout,av=tP.uniqueKey,am=function(){function t(t,e,i){this.sharedClips={};var o=s_([],arguments,!0);(aa(t)||t.nodeName)&&(this.renderTo=o.shift()),this.init(o[0],o[1])}return t.chart=function(e,i,o){return new t(e,i,o)},t.prototype.setZoomOptions=function(){var t=this.options.chart,e=t.zooming;this.zooming=sG(sG({},e),{type:ac(t.zoomType,e.type),key:ac(t.zoomKey,e.key),pinchType:ac(t.pinchType,e.pinchType),singleTouch:ac(t.zoomBySingleTouch,e.singleTouch,!1),resetButton:ah(e.resetButton,t.resetZoomButton)})},t.prototype.init=function(t,e){ae(this,"init",{args:arguments},function(){var i,o,r=ah(t0,t),n=r.chart,s=this.renderTo||n.renderTo;this.userOptions=s7({},t),(this.renderTo=aa(s)?sq.getElementById(s):s)||s8(13,!0,this),this.margin=[],this.spacing=[],this.labelCollectors=[],this.callback=e,this.isResizing=0,this.options=r,this.axes=[],this.series=[],this.locale=null!==(i=r.lang.locale)&&void 0!==i?i:null===(o=this.renderTo.closest("[lang]"))||void 0===o?void 0:o.lang,this.time=new tq(s7(r.time||{},{locale:this.locale}),r.lang),r.time=this.time.options,this.numberFormatter=(n.numberFormatter||sU).bind(this),this.styledMode=n.styledMode,this.hasCartesianSeries=n.showAxes,this.index=sZ.length,sZ.push(this),ti.chartCount++,sV(this,n),this.xAxis=[],this.yAxis=[],this.pointCount=this.colorCounter=this.symbolCounter=0,this.setZoomOptions(),ae(this,"afterInit"),this.firstRender()})},t.prototype.initSeries=function(t){var e=this.options.chart,i=t.type||e.type,o=sQ[i];o||s8(17,!0,this,{missingModuleFor:i});var r=new o;return"function"==typeof r.init&&r.init(this,t),r},t.prototype.setSortedData=function(){this.getSeriesOrderByLinks().forEach(function(t){t.points||t.data||!t.enabledDataSorting||t.setData(t.options.data,!1)})},t.prototype.getSeriesOrderByLinks=function(){return this.series.concat().sort(function(t,e){return t.linkedSeries.length||e.linkedSeries.length?e.linkedSeries.length-t.linkedSeries.length:0})},t.prototype.orderItems=function(t,e){void 0===e&&(e=0);var i=this[t],o=this.options[t]=af(this.options[t]).slice(),r=this.userOptions[t]=this.userOptions[t]?af(this.userOptions[t]).slice():[];if(this.hasRendered&&(o.splice(e),r.splice(e)),i)for(var n=e,s=i.length;n<s;++n){var a=i[n];a&&(a.index=n,a instanceof sw&&(a.name=a.getName()),a.options.isInternal||(o[n]=a.options,r[n]=a.userOptions))}},t.prototype.getClipBox=function(t,e){var i,o,r,n,s,a=this.inverted,h=t||{},l=h.xAxis,c=h.yAxis,d=ah(this.clipBox),p=d.x,u=d.y,f=d.width,g=d.height;return t&&(l&&l.len!==this.plotSizeX&&(f=l.len),c&&c.len!==this.plotSizeY&&(g=c.len),a&&!t.invertible&&(f=(i=[g,f])[0],g=i[1])),e&&(p+=null!==(r=null===(o=a?c:l)||void 0===o?void 0:o.pos)&&void 0!==r?r:this.plotLeft,u+=null!==(s=null===(n=a?l:c)||void 0===n?void 0:n.pos)&&void 0!==s?s:this.plotTop),{x:p,y:u,width:f,height:g}},t.prototype.isInsidePlot=function(t,e,i){void 0===i&&(i={});var o,r=this.inverted,n=this.plotBox,s=this.plotLeft,a=this.plotTop,h=this.scrollablePlotBox,l=i.visiblePlotOnly&&(null===(o=this.scrollablePlotArea)||void 0===o?void 0:o.scrollingContainer)||{},c=l.scrollLeft,d=void 0===c?0:c,p=l.scrollTop,u=void 0===p?0:p,f=i.series,g=i.visiblePlotOnly&&h||n,v=i.inverted?e:t,m=i.inverted?t:e,y={x:v,y:m,isInsidePlot:!0,options:i};if(!i.ignoreX){var x=f&&(r&&!this.polar?f.yAxis:f.xAxis)||{pos:s,len:1/0},b=i.paneCoordinates?x.pos+v:s+v;b>=Math.max(d+s,x.pos)&&b<=Math.min(d+s+g.width,x.pos+x.len)||(y.isInsidePlot=!1)}if(!i.ignoreY&&y.isInsidePlot){var k=!r&&i.axis&&!i.axis.isXAxis&&i.axis||f&&(r?f.xAxis:f.yAxis)||{pos:a,len:1/0},w=i.paneCoordinates?k.pos+m:a+m;w>=Math.max(u+a,k.pos)&&w<=Math.min(u+a+g.height,k.pos+k.len)||(y.isInsidePlot=!1)}return ae(this,"afterIsInsidePlot",y),y.isInsidePlot},t.prototype.redraw=function(t){ae(this,"beforeRedraw");var e,i,o,r,n=this.hasCartesianSeries?this.axes:this.colorAxis||[],s=this.series,a=this.pointer,h=this.legend,l=this.userOptions.legend,c=this.renderer,d=c.isHidden(),p=[],u=this.isDirtyBox,f=this.isDirtyLegend;for(c.rootFontSize=c.boxWrapper.getStyle("font-size"),this.setResponsive&&this.setResponsive(!1),ey(!!this.hasRendered&&t,this),d&&this.temporaryDisplay(),this.layOutTitles(!1),o=s.length;o--;)if(((r=s[o]).options.stacking||r.options.centerInCategory)&&(i=!0,r.isDirty)){e=!0;break}if(e)for(o=s.length;o--;)(r=s[o]).options.stacking&&(r.isDirty=!0);s.forEach(function(t){t.isDirty&&("point"===t.options.legendType?("function"==typeof t.updateTotals&&t.updateTotals(),f=!0):l&&(l.labelFormatter||l.labelFormat)&&(f=!0)),t.isDirtyData&&ae(t,"updatedData")}),f&&h&&h.options.enabled&&(h.render(),this.isDirtyLegend=!1),i&&this.getStacks(),n.forEach(function(t){t.updateNames(),t.setScale()}),this.getMargins(),n.forEach(function(t){t.isDirty&&(u=!0)}),n.forEach(function(t){var e=t.min+","+t.max;t.extKey!==e&&(t.extKey=e,p.push(function(){ae(t,"afterSetExtremes",s7(t.eventArgs,t.getExtremes())),delete t.eventArgs})),(u||i)&&t.redraw()}),u&&this.drawChartBox(),ae(this,"predraw"),s.forEach(function(t){(u||t.isDirty)&&t.visible&&t.redraw(),t.isDirtyData=!1}),a&&a.reset(!0),c.draw(),ae(this,"redraw"),ae(this,"render"),d&&this.temporaryDisplay(!0),p.forEach(function(t){t.call()})},t.prototype.get=function(t){var e=this.series;function i(e){return e.id===t||e.options&&e.options.id===t}for(var o=at(this.axes,i)||at(this.series,i),r=0;!o&&r<e.length;r++)o=at(e[r].points||[],i);return o},t.prototype.createAxes=function(){var t=this.userOptions;ae(this,"createAxes");for(var e=0,i=["xAxis","yAxis"];e<i.length;e++)for(var o=i[e],r=t[o]=af(t[o]||{}),n=0;n<r.length;n++)new ru(this,r[n],o);ae(this,"afterCreateAxes")},t.prototype.getSelectedPoints=function(){return this.series.reduce(function(t,e){return e.getPointsCollection().forEach(function(e){ac(e.selectedStaging,e.selected)&&t.push(e)}),t},[])},t.prototype.getSelectedSeries=function(){return this.series.filter(function(t){return t.selected})},t.prototype.setTitle=function(t,e,i){this.applyDescription("title",t),this.applyDescription("subtitle",e),this.applyDescription("caption",void 0),this.layOutTitles(i)},t.prototype.applyDescription=function(t,e){var i,o=this,r=this.options[t]=ah(this.options[t],e),n=this[t];n&&e&&(this[t]=n=n.destroy()),r&&!n&&((n=this.renderer.text(r.text,0,0,r.useHTML).attr({align:r.align,class:"highcharts-"+t,zIndex:r.zIndex||4}).css({textOverflow:"ellipsis",whiteSpace:"nowrap"}).add()).update=function(e,i){o.applyDescription(t,e),o.layOutTitles(i)},this.styledMode||n.css(s7("title"===t?{fontSize:this.options.isStock?"1em":"1.2em"}:{},r.style)),n.textPxLength=n.getBBox().width,n.css({whiteSpace:null===(i=r.style)||void 0===i?void 0:i.whiteSpace}),this[t]=n)},t.prototype.layOutTitles=function(t){var e,i,o,r,n=this;void 0===t&&(t=!0);var s=[0,0,0],a=this.options,h=this.renderer,l=this.spacingBox;["title","subtitle","caption"].forEach(function(t){var e,i=n[t],o=n.options[t],r=ah(l),a=(null==i?void 0:i.textPxLength)||0;if(i&&o){ae(n,"layOutTitle",{alignTo:r,key:t,textPxLength:a});var c=h.fontMetrics(i),d=c.b,p=c.h,u=o.verticalAlign||"top",f="top"===u,g=f&&o.minScale||1,v="title"===t?f?-3:0:f?s[0]+2:0,m=Math.min(r.width/a,1),y=Math.max(g,m),x=ah({y:"bottom"===u?d:v+d},{align:"title"===t?m<g?"left":"center":null===(e=n.title)||void 0===e?void 0:e.alignValue},o),b=(o.width||(m>g?n.chartWidth:r.width)/y)+"px";i.alignValue!==x.align&&(i.placed=!1);var k=Math.round(i.css({width:b}).getBBox(o.useHTML).height);if(x.height=k,i.align(x,!1,r).attr({align:x.align,scaleX:y,scaleY:y,"transform-origin":""+(r.x+a*y*ai(x.align))+" ".concat(p)}),!o.floating){var w=k*(k<1.2*p?1:y);"top"===u?s[0]=Math.ceil(s[0]+w):"bottom"===u&&(s[2]=Math.ceil(s[2]+w))}}},this),s[0]&&"top"===((null===(e=a.title)||void 0===e?void 0:e.verticalAlign)||"top")&&(s[0]+=(null===(i=a.title)||void 0===i?void 0:i.margin)||0),s[2]&&(null===(o=a.caption)||void 0===o?void 0:o.verticalAlign)==="bottom"&&(s[2]+=(null===(r=a.caption)||void 0===r?void 0:r.margin)||0);var c=!this.titleOffset||this.titleOffset.join(",")!==s.join(",");this.titleOffset=s,ae(this,"afterLayOutTitles"),!this.isDirtyBox&&c&&(this.isDirtyBox=this.isDirtyLegend=c,this.hasRendered&&t&&this.isDirtyBox&&this.redraw())},t.prototype.getContainerBox=function(){var t=this,e=[].map.call(this.renderTo.children,function(e){if(e!==t.container){var i=e.style.display;return e.style.display="none",[e,i]}}),i={width:ao(this.renderTo,"width",!0)||0,height:ao(this.renderTo,"height",!0)||0};return e.filter(Boolean).forEach(function(t){var e=t[0],i=t[1];e.style.display=i}),i},t.prototype.getChartSize=function(){var t,e=this.options.chart,i=e.width,o=e.height,r=this.getContainerBox(),n=r.height<=1||!(null===(t=this.renderTo.parentElement)||void 0===t?void 0:t.style.height)&&"100%"===this.renderTo.style.height;this.chartWidth=Math.max(0,i||r.width||600),this.chartHeight=Math.max(0,ap(o,this.chartWidth)||(n?400:r.height)),this.containerBox=r},t.prototype.temporaryDisplay=function(t){var e,i=this.renderTo;if(t)for(;null==i?void 0:i.style;)i.hcOrigStyle&&(s3(i,i.hcOrigStyle),delete i.hcOrigStyle),i.hcOrigDetached&&(sq.body.removeChild(i),i.hcOrigDetached=!1),i=i.parentNode;else for(;(null==i?void 0:i.style)&&(sq.body.contains(i)||i.parentNode||(i.hcOrigDetached=!0,sq.body.appendChild(i)),("none"===ao(i,"display",!1)||i.hcOricDetached)&&(i.hcOrigStyle={display:i.style.display,height:i.style.height,overflow:i.style.overflow},e={display:"block",overflow:"hidden"},i!==this.renderTo&&(e.height=0),s3(i,e),i.offsetWidth||i.style.setProperty("display","block","important")),(i=i.parentNode)!==sq.body););},t.prototype.setClassName=function(t){this.container.className="highcharts-container "+(t||"")},t.prototype.getContainer=function(){var t,e,i,o=this.options,r=o.chart,n="data-highcharts-chart",s=av(),a=this.renderTo,h=ad(s1(a,n));an(h)&&sZ[h]&&sZ[h].hasRendered&&sZ[h].destroy(),s1(a,n,this.index),a.innerHTML=eI.emptyHTML,r.skipClone||a.offsetWidth||this.temporaryDisplay(),this.getChartSize();var l=this.chartHeight,c=this.chartWidth;s3(a,{overflow:"hidden"}),this.styledMode||(i=s7({position:"relative",overflow:"hidden",width:c+"px",height:l+"px",textAlign:"left",lineHeight:"normal",zIndex:0,"-webkit-tap-highlight-color":"rgba(0,0,0,0)",userSelect:"none","touch-action":"manipulation",outline:"none",padding:"0px"},r.style||{}));var d=s2("div",{id:s},i,a);this.container=d,this.getChartSize(),c===this.chartWidth||(c=this.chartWidth,this.styledMode||s3(d,{width:ac(null===(t=r.style)||void 0===t?void 0:t.width,c+"px")})),this.containerBox=this.getContainerBox(),this._cursor=d.style.cursor;var p=r.renderer||!s$?eV.getRendererType(r.renderer):op;if(this.renderer=new p(d,c,l,void 0,r.forExport,null===(e=o.exporting)||void 0===e?void 0:e.allowHTML,this.styledMode),ey(void 0,this),this.setClassName(r.className),this.styledMode)for(var u in o.defs)this.renderer.definition(o.defs[u]);else this.renderer.setStyle(r.style);this.renderer.chartIndex=this.index,ae(this,"afterGetContainer")},t.prototype.getMargins=function(t){var e,i=this.spacing,o=this.margin,r=this.titleOffset;this.resetMargins(),r[0]&&!s5(o[0])&&(this.plotTop=Math.max(this.plotTop,r[0]+i[0])),r[2]&&!s5(o[2])&&(this.marginBottom=Math.max(this.marginBottom,r[2]+i[2])),(null===(e=this.legend)||void 0===e?void 0:e.display)&&this.legend.adjustMargins(o,i),ae(this,"getMargins"),t||this.getAxisMargins()},t.prototype.getAxisMargins=function(){var t=this,e=t.axisOffset=[0,0,0,0],i=t.colorAxis,o=t.margin,r=function(t){t.forEach(function(t){t.visible&&t.getOffset()})};t.hasCartesianSeries?r(t.axes):(null==i?void 0:i.length)&&r(i),sK.forEach(function(i,r){s5(o[r])||(t[i]+=e[r])}),t.setChartSize()},t.prototype.getOptions=function(){return s6(this.userOptions,t0)},t.prototype.reflow=function(t){var e,i=this,o=i.containerBox,r=i.getContainerBox();null===(e=i.pointer)||void 0===e||delete e.chartPosition,!i.isPrinting&&!i.isResizing&&o&&r.width&&((r.width!==o.width||r.height!==o.height)&&(tP.clearTimeout(i.reflowTimeout),i.reflowTimeout=ag(function(){i.container&&i.setSize(void 0,void 0,!1)},100*!!t)),i.containerBox=r)},t.prototype.setReflow=function(){var t=this,e=function(e){var i;(null===(i=t.options)||void 0===i?void 0:i.chart.reflow)&&t.hasLoaded&&t.reflow(e)};if("function"==typeof ResizeObserver)new ResizeObserver(e).observe(t.renderTo);else{var i=s0(sJ,"resize",e);s0(this,"destroy",i)}},t.prototype.setSize=function(t,e,i){var o=this,r=o.renderer;o.isResizing+=1,ey(i,o);var n=r.globalAnimation;o.oldChartHeight=o.chartHeight,o.oldChartWidth=o.chartWidth,void 0!==t&&(o.options.chart.width=t),void 0!==e&&(o.options.chart.height=e),o.getChartSize();var s=o.chartWidth,a=o.chartHeight,h=o.scrollablePixelsX,l=o.scrollablePixelsY;(o.isDirtyBox||s!==o.oldChartWidth||a!==o.oldChartHeight)&&(o.styledMode||(n?ev:s3)(o.container,{width:""+(s+(void 0===h?0:h))+"px",height:""+(a+(void 0===l?0:l))+"px"},n),o.setChartSize(!0),r.setSize(s,a,n),o.axes.forEach(function(t){t.isDirty=!0,t.setScale()}),o.isDirtyLegend=!0,o.isDirtyBox=!0,o.layOutTitles(),o.getMargins(),o.redraw(n),o.oldChartHeight=void 0,ae(o,"resize"),setTimeout(function(){o&&ae(o,"endResize")},ef(n).duration)),o.isResizing-=1},t.prototype.setChartSize=function(t){var e,i,o,r,n,s,a=this.chartHeight,h=this.chartWidth,l=this.inverted,c=this.spacing,d=this.renderer,p=this.clipOffset,u=Math[l?"floor":"round"];this.plotLeft=o=Math.round(this.plotLeft),this.plotTop=r=Math.round(this.plotTop),this.plotWidth=n=Math.max(0,Math.round(h-o-(null!==(e=this.marginRight)&&void 0!==e?e:0))),this.plotHeight=s=Math.max(0,Math.round(a-r-(null!==(i=this.marginBottom)&&void 0!==i?i:0))),this.plotSizeX=l?s:n,this.plotSizeY=l?n:s,this.spacingBox=d.spacingBox={x:c[3],y:c[0],width:h-c[3]-c[1],height:a-c[0]-c[2]},this.plotBox=d.plotBox={x:o,y:r,width:n,height:s},p&&(this.clipBox={x:u(p[3]),y:u(p[0]),width:u(this.plotSizeX-p[1]-p[3]),height:u(this.plotSizeY-p[0]-p[2])}),t||(this.axes.forEach(function(t){t.setAxisSize(),t.setAxisTranslation()}),d.alignElements()),ae(this,"afterSetChartSize",{skipAxes:t})},t.prototype.resetMargins=function(){ae(this,"resetMargins");var t=this,e=t.options.chart,i=e.plotBorderWidth||0,o=Math.round(i)/2;["margin","spacing"].forEach(function(i){var o=e[i],r=as(o)?o:[o,o,o,o];["Top","Right","Bottom","Left"].forEach(function(o,n){t[i][n]=ac(e[i+o],r[n])})}),sK.forEach(function(e,i){t[e]=ac(t.margin[i],t.spacing[i])}),t.axisOffset=[0,0,0,0],t.clipOffset=[o,o,o,o],t.plotBorderWidth=i},t.prototype.drawChartBox=function(){var t,e,i,o=this.options.chart,r=this.renderer,n=this.chartWidth,s=this.chartHeight,a=this.styledMode,h=this.plotBGImage,l=o.backgroundColor,c=o.plotBackgroundColor,d=o.plotBackgroundImage,p=this.plotLeft,u=this.plotTop,f=this.plotWidth,g=this.plotHeight,v=this.plotBox,m=this.clipRect,y=this.clipBox,x=this.chartBackground,b=this.plotBackground,k=this.plotBorder,w="animate";x||(this.chartBackground=x=r.rect().addClass("highcharts-background").add(),w="attr"),a?t=e=x.strokeWidth():(e=(t=o.borderWidth||0)+8*!!o.shadow,i={fill:l||"none"},(t||x["stroke-width"])&&(i.stroke=o.borderColor,i["stroke-width"]=t),x.attr(i).shadow(o.shadow)),x[w]({x:e/2,y:e/2,width:n-e-t%2,height:s-e-t%2,r:o.borderRadius}),w="animate",b||(w="attr",this.plotBackground=b=r.rect().addClass("highcharts-plot-background").add()),b[w](v),!a&&(b.attr({fill:c||"none"}).shadow(o.plotShadow),d&&(h?(d!==h.attr("href")&&h.attr("href",d),h.animate(v)):this.plotBGImage=r.image(d,p,u,f,g).add())),m?m.animate({width:y.width,height:y.height}):this.clipRect=r.clipRect(y),w="animate",k||(w="attr",this.plotBorder=k=r.rect().addClass("highcharts-plot-border").attr({zIndex:1}).add()),a||k.attr({stroke:o.plotBorderColor,"stroke-width":o.plotBorderWidth||0,fill:"none"}),k[w](k.crisp(v,-k.strokeWidth())),this.isDirtyBox=!1,ae(this,"afterDrawChartBox")},t.prototype.propFromSeries=function(){var t,e,i,o=this,r=o.options.chart,n=o.options.series;["inverted","angular","polar"].forEach(function(s){for(e=sQ[r.type],i=r[s]||e&&e.prototype[s],t=null==n?void 0:n.length;!i&&t--;)(e=sQ[n[t].type])&&e.prototype[s]&&(i=!0);o[s]=i})},t.prototype.linkSeries=function(t){var e=this,i=e.series;i.forEach(function(t){t.linkedSeries.length=0}),i.forEach(function(t){var i=t.options.linkedTo;if(aa(i)){var o=void 0;(o=":previous"===i?e.series[t.index-1]:e.get(i))&&o.linkedParent!==t&&(o.linkedSeries.push(t),t.linkedParent=o,o.enabledDataSorting&&t.setDataSortingOptions(),t.visible=ac(t.options.visible,o.options.visible,t.visible))}}),ae(this,"afterLinkSeries",{isUpdating:t})},t.prototype.renderSeries=function(){this.series.forEach(function(t){t.translate(),t.render()})},t.prototype.render=function(){var t,e,i=this.axes,o=this.colorAxis,r=this.renderer,n=this.options.chart.axisLayoutRuns||2,s=function(t){t.forEach(function(t){t.visible&&t.render()})},a=0,h=!0,l=0;this.setTitle(),ae(this,"beforeMargins"),null===(t=this.getStacks)||void 0===t||t.call(this),this.getMargins(!0),this.setChartSize();for(var c=0;c<i.length;c++){var d=i[c],p=d.options,u=p.labels;if(this.hasCartesianSeries&&d.horiz&&d.visible&&u.enabled&&d.series.length&&"colorAxis"!==d.coll&&!this.polar){a=p.tickLength,d.createGroups();var f=new oZ(d,0,"",!0),g=f.createLabel("x",u);if(f.destroy(),g&&ac(u.reserveSpace,!an(p.crossing))&&(a=g.getBBox().height+u.distance+Math.max(p.offset||0,0)),a){null==g||g.destroy();break}}}for(this.plotHeight=Math.max(this.plotHeight-a,0);(h||e||n>1)&&l<n;){for(var v=this.plotWidth,m=this.plotHeight,y=0;y<i.length;y++){var d=i[y];0===l?d.setScale():(d.horiz&&h||!d.horiz&&e)&&d.setTickInterval(!0)}0===l?this.getAxisMargins():this.getMargins(),h=v/this.plotWidth>(l?1:1.1),e=m/this.plotHeight>(l?1:1.05),l++}this.drawChartBox(),this.hasCartesianSeries?s(i):(null==o?void 0:o.length)&&s(o),this.seriesGroup||(this.seriesGroup=r.g("series-group").attr({zIndex:3}).shadow(this.options.chart.seriesGroupShadow).add()),this.renderSeries(),this.addCredits(),this.setResponsive&&this.setResponsive(),this.hasRendered=!0},t.prototype.addCredits=function(t){var e=this,i=ah(!0,this.options.credits,t);i.enabled&&!this.credits&&(this.credits=this.renderer.text(i.text+(this.mapCredits||""),0,0).addClass("highcharts-credits").on("click",function(){i.href&&(sJ.location.href=i.href)}).attr({align:i.position.align,zIndex:8}),e.styledMode||this.credits.css(i.style),this.credits.add().align(i.position),this.credits.update=function(t){e.credits=e.credits.destroy(),e.addCredits(t)})},t.prototype.destroy=function(){var t,e,i,o=this,r=o.axes,n=o.series,s=o.container,a=null==s?void 0:s.parentNode;for(ae(o,"destroy"),o.renderer.forExport?s4(sZ,o):sZ[o.index]=void 0,ti.chartCount--,o.renderTo.removeAttribute("data-highcharts-chart"),au(o),i=r.length;i--;)r[i]=r[i].destroy();for(null===(e=null===(t=this.scroller)||void 0===t?void 0:t.destroy)||void 0===e||e.call(t),i=n.length;i--;)n[i]=n[i].destroy();["title","subtitle","chartBackground","plotBackground","plotBGImage","plotBorder","seriesGroup","clipRect","credits","pointer","rangeSelector","legend","resetZoomButton","tooltip","renderer"].forEach(function(t){var e,i;o[t]=null===(i=null===(e=o[t])||void 0===e?void 0:e.destroy)||void 0===i?void 0:i.call(e)}),s&&(s.innerHTML=eI.emptyHTML,au(s),a&&s9(s)),al(o,function(t,e){delete o[e]})},t.prototype.firstRender=function(){var t,e=this,i=e.options;e.getContainer(),e.resetMargins(),e.setChartSize(),e.propFromSeries(),e.createAxes();var o=ar(i.series)?i.series:[];i.series=[],o.forEach(function(t){e.initSeries(t)}),e.linkSeries(),e.setSortedData(),ae(e,"beforeRender"),e.render(),null===(t=e.pointer)||void 0===t||t.getChartPosition(),e.renderer.imgCount||e.hasLoaded||e.onload(),e.temporaryDisplay(!0)},t.prototype.onload=function(){this.callbacks.concat([this.callback]).forEach(function(t){t&&void 0!==this.index&&t.apply(this,[this])},this),ae(this,"load"),ae(this,"render"),s5(this.index)&&this.setReflow(),this.warnIfA11yModuleNotLoaded(),this.hasLoaded=!0},t.prototype.warnIfA11yModuleNotLoaded=function(){var t=this.options,e=this.title;!t||this.accessibility||(this.renderer.boxWrapper.attr({role:"img","aria-label":((null==e?void 0:e.element.textContent)||"").replace(/</g,"&lt;")}),t.accessibility&&!1===t.accessibility.enabled||s8('Highcharts warning: Consider including the "accessibility.js" module to make your chart more usable for people with disabilities. Set the "accessibility.enabled" option to false to remove this warning. See https://www.highcharts.com/docs/accessibility/accessibility-module.',!1,this))},t.prototype.addSeries=function(t,e,i){var o,r=this;return t&&(e=ac(e,!0),ae(r,"addSeries",{options:t},function(){o=r.initSeries(t),r.isDirtyLegend=!0,r.linkSeries(),o.enabledDataSorting&&o.setData(t.data,!1),ae(r,"afterAddSeries",{series:o}),e&&r.redraw(i)})),o},t.prototype.addAxis=function(t,e,i,o){return this.createAxis(e?"xAxis":"yAxis",{axis:t,redraw:i,animation:o})},t.prototype.addColorAxis=function(t,e,i){return this.createAxis("colorAxis",{axis:t,redraw:e,animation:i})},t.prototype.createAxis=function(t,e){var i=new ru(this,e.axis,t);return ac(e.redraw,!0)&&this.redraw(e.animation),i},t.prototype.showLoading=function(t){var e=this,i=e.options,o=i.loading,r=function(){n&&s3(n,{left:e.plotLeft+"px",top:e.plotTop+"px",width:e.plotWidth+"px",height:e.plotHeight+"px"})},n=e.loadingDiv,s=e.loadingSpan;n||(e.loadingDiv=n=s2("div",{className:"highcharts-loading highcharts-loading-hidden"},null,e.container)),s||(e.loadingSpan=s=s2("span",{className:"highcharts-loading-inner"},null,n),s0(e,"redraw",r)),n.className="highcharts-loading",eI.setElementHTML(s,ac(t,i.lang.loading,"")),e.styledMode||(s3(n,s7(o.style,{zIndex:10})),s3(s,o.labelStyle),e.loadingShown||(s3(n,{opacity:0,display:""}),ev(n,{opacity:o.style.opacity||.5},{duration:o.showDuration||0}))),e.loadingShown=!0,r()},t.prototype.hideLoading=function(){var t=this.options,e=this.loadingDiv;e&&(e.className="highcharts-loading highcharts-loading-hidden",this.styledMode||ev(e,{opacity:0},{duration:t.loading.hideDuration||100,complete:function(){s3(e,{display:"none"})}})),this.loadingShown=!1},t.prototype.update=function(t,e,i,o){var r,n,s,a=this,h={credits:"addCredits",title:"setTitle",subtitle:"setSubtitle",caption:"setCaption"},l=t.isResponsiveOptions,c=[];ae(a,"update",{options:t}),l||a.setResponsive(!1,!0),t=s6(t,a.options),a.userOptions=ah(a.userOptions,t);var d=t.chart;d&&(ah(!0,a.options.chart,d),this.setZoomOptions(),"className"in d&&a.setClassName(d.className),("inverted"in d||"polar"in d||"type"in d)&&(a.propFromSeries(),r=!0),"alignTicks"in d&&(r=!0),"events"in d&&sV(this,d),al(d,function(t,e){-1!==a.propsRequireUpdateSeries.indexOf("chart."+e)&&(n=!0),-1!==a.propsRequireDirtyBox.indexOf(e)&&(a.isDirtyBox=!0),-1===a.propsRequireReflow.indexOf(e)||(a.isDirtyBox=!0,l||(s=!0))}),!a.styledMode&&d.style&&a.renderer.setStyle(a.options.chart.style||{})),!a.styledMode&&t.colors&&(this.options.colors=t.colors),al(t,function(e,i){a[i]&&"function"==typeof a[i].update?a[i].update(e,!1):"function"==typeof a[h[i]]?a[h[i]](e):"colors"!==i&&-1===a.collectionsWithUpdate.indexOf(i)&&ah(!0,a.options[i],t[i]),"chart"!==i&&-1!==a.propsRequireUpdateSeries.indexOf(i)&&(n=!0)}),this.collectionsWithUpdate.forEach(function(e){t[e]&&(af(t[e]).forEach(function(t,o){var r,n=s5(t.id);n&&(r=a.get(t.id)),!r&&a[e]&&(r=a[e][ac(t.index,o)])&&(n&&s5(r.options.id)||r.options.isInternal)&&(r=void 0),r&&r.coll===e&&(r.update(t,!1),i&&(r.touched=!0)),!r&&i&&a.collectionsWithInit[e]&&(a.collectionsWithInit[e][0].apply(a,[t].concat(a.collectionsWithInit[e][1]||[]).concat([!1])).touched=!0)}),i&&a[e].forEach(function(t){t.touched||t.options.isInternal?delete t.touched:c.push(t)}))}),c.forEach(function(t){t.chart&&t.remove&&t.remove(!1)}),r&&a.axes.forEach(function(t){t.update({},!1)}),n&&a.getSeriesOrderByLinks().forEach(function(t){t.chart&&t.update({},!1)},this);var p=null==d?void 0:d.width,u=d&&(aa(d.height)?ap(d.height,p||a.chartWidth):d.height);s||an(p)&&p!==a.chartWidth||an(u)&&u!==a.chartHeight?a.setSize(p,u,o):ac(e,!0)&&a.redraw(o),ae(a,"afterUpdate",{options:t,redraw:e,animation:o})},t.prototype.setSubtitle=function(t,e){this.applyDescription("subtitle",t),this.layOutTitles(e)},t.prototype.setCaption=function(t,e){this.applyDescription("caption",t),this.layOutTitles(e)},t.prototype.showResetZoom=function(){var t=this,e=t0.lang,i=t.zooming.resetButton,o=i.theme,r="chart"===i.relativeTo||"spacingBox"===i.relativeTo?null:"plotBox";function n(){t.zoomOut()}ae(this,"beforeShowResetZoom",null,function(){t.resetZoomButton=t.renderer.button(e.resetZoom,null,null,n,o).attr({align:i.position.align,title:e.resetZoomTitle}).addClass("highcharts-reset-zoom").add().align(i.position,!1,r)}),ae(this,"afterShowResetZoom")},t.prototype.zoomOut=function(){var t=this;ae(this,"selection",{resetSelection:!0},function(){return t.transform({reset:!0,trigger:"zoom"})})},t.prototype.pan=function(t,e){var i=this,o="object"==typeof e?e:{enabled:e,type:"x"},r=o.type,n=r&&i[({x:"xAxis",xy:"axes",y:"yAxis"})[r]].filter(function(t){return t.options.panningEnabled&&!t.options.isInternal}),s=i.options.chart;(null==s?void 0:s.panning)&&(s.panning=o),ae(this,"pan",{originalEvent:t},function(){i.transform({axes:n,event:t,to:{x:t.chartX-(i.mouseDownX||0),y:t.chartY-(i.mouseDownY||0)},trigger:"pan"}),s3(i.container,{cursor:"move"})})},t.prototype.transform=function(t){var e,i,o,r,n,s,a=this,h=t.axes,l=void 0===h?this.axes:h,c=t.event,d=t.from,p=void 0===d?{}:d,u=t.reset,f=t.selection,g=t.to,v=void 0===g?{}:g,m=t.trigger,y=this.inverted,x=this.time,b=!1;null===(i=this.hoverPoints)||void 0===i||i.forEach(function(t){return t.setState()});for(var k=0;k<l.length;k++){var w=l[k],M=w.horiz,S=w.len,T=w.minPointOffset,C=void 0===T?0:T,A=w.options,P=w.reversed,O=M?"width":"height",L=M?"x":"y",E=ac(v[O],w.len),I=ac(p[O],w.len),D=10>Math.abs(E)?1:E/I,B=(p[L]||0)+I/2-w.pos,N=B-((null!==(o=v[L])&&void 0!==o?o:w.pos)+E/2-w.pos)/D,z=P&&!y||!P&&y?-1:1;if(u||!(B<0)&&!(B>w.len)){var R=w.toValue(N,!0)+(f||w.isOrdinal?0:C*z),W=w.toValue(N+S/D,!0)-(f||w.isOrdinal?0:C*z||0),X=w.allExtremes;if(R>W&&(R=(e=[W,R])[0],W=e[1]),1===D&&!u&&"yAxis"===w.coll&&!X){for(var H=0,j=w.series;H<j.length;H++){var F=j[H],Y=F.getExtremes(F.getProcessedData(!0).modified.getColumn("y")||[],!0);null!=X||(X={dataMin:Number.MAX_VALUE,dataMax:-Number.MAX_VALUE}),an(Y.dataMin)&&an(Y.dataMax)&&(X.dataMin=Math.min(Y.dataMin,X.dataMin),X.dataMax=Math.max(Y.dataMax,X.dataMax))}w.allExtremes=X}var G=s7(w.getExtremes(),X||{}),_=G.dataMin,U=G.dataMax,V=G.min,Z=G.max,q=x.parse(A.min),K=x.parse(A.max),$=null!=_?_:q,J=null!=U?U:K,Q=W-R,tt=w.categories?0:Math.min(Q,J-$),te=$-tt*(s5(q)?0:A.minPadding),ti=J+tt*(s5(K)?0:A.maxPadding),to=w.allowZoomOutside||1===D||"zoom"!==m&&D>1,tr=Math.min(null!=q?q:te,te,to?V:te),tn=Math.max(null!=K?K:ti,ti,to?Z:ti);(!w.isOrdinal||w.options.overscroll||1!==D||u)&&(R<tr&&(R=tr,D>=1&&(W=R+Q)),W>tn&&(W=tn,D>=1&&(R=W-Q)),(u||w.series.length&&(R!==V||W!==Z)&&R>=tr&&W<=tn)&&(f?f[w.coll].push({axis:w,min:R,max:W}):(w.isPanning="zoom"!==m,w.isPanning&&(s=!0),w.setExtremes(u?void 0:R,u?void 0:W,!1,!1,{move:N,trigger:m,scale:D}),!u&&(R>tr||W<tn)&&"mousewheel"!==m&&(n=!0)),b=!0),c&&(this[M?"mouseDownX":"mouseDownY"]=c[M?"chartX":"chartY"]))}}return b&&(f?ae(this,"selection",f,function(){delete t.selection,t.trigger="zoom",a.transform(t)}):(!n||s||this.resetZoomButton?!n&&this.resetZoomButton&&(this.resetZoomButton=this.resetZoomButton.destroy()):this.showResetZoom(),this.redraw("zoom"===m&&(null!==(r=this.options.chart.animation)&&void 0!==r?r:this.pointCount<100)))),b},t}();s7(am.prototype,{callbacks:[],collectionsWithInit:{xAxis:[am.prototype.addAxis,[!0]],yAxis:[am.prototype.addAxis,[!1]],series:[am.prototype.addSeries]},collectionsWithUpdate:["xAxis","yAxis","series"],propsRequireDirtyBox:["backgroundColor","borderColor","borderWidth","borderRadius","plotBackgroundColor","plotBackgroundImage","plotBorderColor","plotBorderWidth","plotShadow","shadow"],propsRequireReflow:["margin","marginTop","marginRight","marginBottom","marginLeft","spacing","spacingTop","spacingRight","spacingBottom","spacingLeft"],propsRequireUpdateSeries:["chart.inverted","chart.polar","chart.ignoreHiddenSeries","chart.type","colors","plotOptions","time","tooltip"]});var ay=ti.composed,ax=tP.addEvent,ab=tP.createElement,ak=tP.css,aw=tP.defined,aM=tP.erase,aS=tP.merge,aT=tP.pushUnique;function aC(){var t=this.scrollablePlotArea;(this.scrollablePixelsX||this.scrollablePixelsY)&&!t&&(this.scrollablePlotArea=t=new aP(this)),null==t||t.applyFixed()}function aA(){this.chart.scrollablePlotArea&&(this.chart.scrollablePlotArea.isDirty=!0)}var aP=function(){function t(t){var e,i,o,r=t.options.chart,n=eV.getRendererType(),s=r.scrollablePlotArea||{},a=this.moveFixedElements.bind(this),h={WebkitOverflowScrolling:"touch",overflowX:"hidden",overflowY:"hidden"};t.scrollablePixelsX&&(h.overflowX="auto"),t.scrollablePixelsY&&(h.overflowY="auto"),this.chart=t;var l=this.parentDiv=ab("div",{className:"highcharts-scrolling-parent"},{position:"relative"},t.renderTo),c=this.scrollingContainer=ab("div",{className:"highcharts-scrolling"},h,l),d=this.innerContainer=ab("div",{className:"highcharts-inner-container"},void 0,c),p=this.fixedDiv=ab("div",{className:"highcharts-fixed"},{position:"absolute",overflow:"hidden",pointerEvents:"none",zIndex:((null===(e=r.style)||void 0===e?void 0:e.zIndex)||0)+2,top:0},void 0,!0),u=this.fixedRenderer=new n(p,t.chartWidth,t.chartHeight,r.style);this.mask=u.path().attr({fill:r.backgroundColor||"#fff","fill-opacity":null!==(i=s.opacity)&&void 0!==i?i:.85,zIndex:-1}).addClass("highcharts-scrollable-mask").add(),c.parentNode.insertBefore(p,c),ak(t.renderTo,{overflow:"visible"}),ax(t,"afterShowResetZoom",a),ax(t,"afterApplyDrilldown",a),ax(t,"afterLayOutTitles",a),ax(c,"scroll",function(){var e=t.pointer,i=t.hoverPoint;e&&(delete e.chartPosition,i&&(o=i),e.runPointActions(void 0,o,!0))}),d.appendChild(t.container)}return t.compose=function(t,e,i){var o=this;aT(ay,this.compose)&&(ax(t,"afterInit",aA),ax(e,"afterSetChartSize",function(t){return o.afterSetSize(t.target,t)}),ax(e,"render",aC),ax(i,"show",aA))},t.afterSetSize=function(t,e){var i,o,r,n=t.options.chart.scrollablePlotArea||{},s=n.minWidth,a=n.minHeight,h=t.clipBox,l=t.plotBox,c=t.inverted;if(!t.renderer.forExport&&(s?(t.scrollablePixelsX=i=Math.max(0,s-t.chartWidth),i&&(t.scrollablePlotBox=aS(t.plotBox),l.width=t.plotWidth+=i,h[c?"height":"width"]+=i,r=!0)):a&&(t.scrollablePixelsY=o=Math.max(0,a-t.chartHeight),aw(o)&&(t.scrollablePlotBox=aS(t.plotBox),l.height=t.plotHeight+=o,h[c?"width":"height"]+=o,r=!1)),aw(r)&&!e.skipAxes))for(var d=0,p=t.axes;d<p.length;d++){var u=p[d];(u.horiz===r||t.hasParallelCoordinates&&"yAxis"===u.coll)&&(u.setAxisSize(),u.setAxisTranslation())}},t.prototype.applyFixed=function(){var t,e=this.chart,i=this.fixedRenderer,o=this.isDirty,r=this.scrollingContainer,n=e.axisOffset,s=e.chartWidth,a=e.chartHeight,h=e.container,l=e.plotHeight,c=e.plotLeft,d=e.plotTop,p=e.plotWidth,u=e.scrollablePixelsX,f=void 0===u?0:u,g=e.scrollablePixelsY,v=void 0===g?0:g,m=e.options.chart.scrollablePlotArea||{},y=m.scrollPositionX,x=m.scrollPositionY,b=s+f,k=a+v;i.setSize(s,a),(null==o||o)&&(this.isDirty=!1,this.moveFixedElements()),eg(e.container),ak(h,{width:""+b+"px",height:""+k+"px"}),e.renderer.boxWrapper.attr({width:b,height:k,viewBox:[0,0,b,k].join(" ")}),null===(t=e.chartBackground)||void 0===t||t.attr({width:b,height:k}),ak(r,{width:""+s+"px",height:""+a+"px"}),aw(o)||(r.scrollLeft=f*(void 0===y?0:y),r.scrollTop=v*(void 0===x?0:x));var w=d-n[0]-1,M=c-n[3]-1,S=d+l+n[2]+1,T=c+p+n[1]+1,C=c+p-f,A=d+l-v,P=[["M",0,0]];f?P=[["M",0,w],["L",c-1,w],["L",c-1,S],["L",0,S],["Z"],["M",C,w],["L",s,w],["L",s,S],["L",C,S],["Z"]]:v&&(P=[["M",M,0],["L",M,d-1],["L",T,d-1],["L",T,0],["Z"],["M",M,A],["L",M,a],["L",T,a],["L",T,A],["Z"]]),"adjustHeight"!==e.redrawTrigger&&this.mask.attr({d:P})},t.prototype.moveFixedElements=function(){var e,i=this.chart,o=i.container,r=i.inverted,n=i.scrollablePixelsX,s=i.scrollablePixelsY,a=this.fixedRenderer,h=t.fixedSelectors;if(n&&!r?e=".highcharts-yaxis":n&&r?e=".highcharts-xaxis":s&&!r?e=".highcharts-xaxis":s&&r&&(e=".highcharts-yaxis"),e&&!(this.chart.hasParallelCoordinates&&".highcharts-yaxis"===e))for(var l=0,c=[""+e+":not(.highcharts-radial-axis)",""+e+"-labels:not(.highcharts-radial-axis-labels)"];l<c.length;l++){var d=c[l];aT(h,d)}else for(var p=0,u=[".highcharts-xaxis",".highcharts-yaxis"];p<u.length;p++)for(var f=u[p],g=0,v=[""+f+":not(.highcharts-radial-axis)",""+f+"-labels:not(.highcharts-radial-axis-labels)"];g<v.length;g++){var d=v[g];aM(h,d)}for(var m=0;m<h.length;m++){var d=h[m];[].forEach.call(o.querySelectorAll(d),function(t){(t.namespaceURI===a.SVG_NS?a.box:a.box.parentNode).appendChild(t),t.style.pointerEvents="auto"})}},t.fixedSelectors=[".highcharts-breadcrumbs-group",".highcharts-contextbutton",".highcharts-caption",".highcharts-credits",".highcharts-drillup-button",".highcharts-legend",".highcharts-legend-checkbox",".highcharts-navigator-series",".highcharts-navigator-xaxis",".highcharts-navigator-yaxis",".highcharts-navigator",".highcharts-range-selector-group",".highcharts-reset-zoom",".highcharts-scrollbar",".highcharts-subtitle",".highcharts-title"],t}(),aO=eU.format,aL=n0.series,aE=tP.destroyObjectProperties,aI=tP.fireEvent,aD=tP.getAlignFactor,aB=tP.isNumber,aN=tP.pick,az=function(){function t(t,e,i,o,r){var n=t.chart.inverted,s=t.reversed;this.axis=t;var a=this.isNegative=!!i!=!!s;this.options=e=e||{},this.x=o,this.total=null,this.cumulative=null,this.points={},this.hasValidPoints=!1,this.stack=r,this.leftCliff=0,this.rightCliff=0,this.alignOptions={align:e.align||(n?a?"left":"right":"center"),verticalAlign:e.verticalAlign||(n?"middle":a?"bottom":"top"),y:e.y,x:e.x},this.textAlign=e.textAlign||(n?a?"right":"left":"center")}return t.prototype.destroy=function(){aE(this,this.axis)},t.prototype.render=function(t){var e=this.axis.chart,i=this.options,o=i.format,r=o?aO(o,this,e):i.formatter.call(this);if(this.label)this.label.attr({text:r,visibility:"hidden"});else{this.label=e.renderer.label(r,null,void 0,i.shape,void 0,void 0,i.useHTML,!1,"stack-labels");var n={r:i.borderRadius||0,text:r,padding:aN(i.padding,5),visibility:"hidden"};e.styledMode||(n.fill=i.backgroundColor,n.stroke=i.borderColor,n["stroke-width"]=i.borderWidth,this.label.css(i.style||{})),this.label.attr(n),this.label.added||this.label.add(t)}this.label.labelrank=e.plotSizeY,aI(this,"afterRender")},t.prototype.setOffset=function(t,e,i,o,r,n){var s=this.alignOptions,a=this.axis,h=this.label,l=this.options,c=this.textAlign,d=a.chart,p=this.getStackBox({xOffset:t,width:e,boxBottom:i,boxTop:o,defaultX:r,xAxis:n}),u=s.verticalAlign;if(h&&p){var f=h.getBBox(void 0,0),g=h.padding,v="justify"===aN(l.overflow,"justify"),m=void 0;s.x=l.x||0,s.y=l.y||0;var y=this.adjustStackPosition({labelBox:f,verticalAlign:u,textAlign:c}),x=y.x,b=y.y;p.x-=x,p.y-=b,h.align(s,!1,p),(m=d.isInsidePlot(h.alignAttr.x+s.x+x,h.alignAttr.y+s.y+b))||(v=!1),v&&aL.prototype.justifyDataLabel.call(a,h,s,h.alignAttr,f,p),h.attr({x:h.alignAttr.x,y:h.alignAttr.y,rotation:l.rotation,rotationOriginX:f.width*aD(l.textAlign||"center"),rotationOriginY:f.height/2}),aN(!v&&l.crop,!0)&&(m=aB(h.x)&&aB(h.y)&&d.isInsidePlot(h.x-g+(h.width||0),h.y)&&d.isInsidePlot(h.x+g,h.y)),h[m?"show":"hide"]()}aI(this,"afterSetOffset",{xOffset:t,width:e})},t.prototype.adjustStackPosition=function(t){var e=t.labelBox,i=t.verticalAlign,o=t.textAlign;return{x:e.width/2+e.width/2*(2*aD(o)-1),y:e.height/2*2*(1-aD(i))}},t.prototype.getStackBox=function(t){var e=this.axis,i=e.chart,o=t.boxTop,r=t.defaultX,n=t.xOffset,s=t.width,a=t.boxBottom,h=e.stacking.usePercentage?100:aN(o,this.total,0),l=e.toPixels(h),c=t.xAxis||i.xAxis[0],d=aN(r,c.translate(this.x))+n,p=Math.abs(l-e.toPixels(a||aB(e.min)&&e.logarithmic&&e.logarithmic.lin2log(e.min)||0)),u=i.inverted,f=this.isNegative;return u?{x:(f?l:l-p)-i.plotLeft,y:c.height-d-s+c.top-i.plotTop,width:p,height:s}:{x:d+c.transB-i.plotLeft,y:(f?l-p:l)-i.plotTop,width:s,height:p}},t}(),aR=n0.series.prototype,aW=tP.addEvent,aX=tP.correctFloat,aH=tP.defined,aj=tP.destroyObjectProperties,aF=tP.fireEvent,aY=tP.isNumber,aG=tP.objectEach,a_=tP.pick;function aU(){var t=this.inverted;this.axes.forEach(function(t){var e;(null===(e=t.stacking)||void 0===e?void 0:e.stacks)&&t.hasVisibleSeries&&(t.stacking.oldStacks=t.stacking.stacks)}),this.series.forEach(function(e){var i,o=(null===(i=e.xAxis)||void 0===i?void 0:i.options)||{};e.options.stacking&&e.reserveSpace()&&(e.stackKey=[e.type,a_(e.options.stack,""),t?o.top:o.left,t?o.height:o.width].join(","))})}function aV(){var t,e=this.stacking;if(e){var i=e.stacks;aG(i,function(t,e){aj(t),delete i[e]}),null===(t=e.stackTotalGroup)||void 0===t||t.destroy()}}function aZ(){this.stacking||(this.stacking=new a0(this))}function aq(t,e,i,o){return!aH(t)||t.x!==e||o&&t.stackKey!==o?t={x:e,index:0,key:o,stackKey:o}:t.index++,t.key=[i,e,t.index].join(","),t}function aK(){var t,e=this,i=e.yAxis,o=e.stackKey||"",r=i.stacking.stacks,n=e.getColumn("x",!0),s=e.options.stacking,a=e[s+"Stacker"];a&&[o,"-"+o].forEach(function(i){for(var o,s,h,l,c=n.length;c--;)s=n[c],t=e.getStackIndicator(t,s,e.index,i),(l=null==(h=null===(o=r[i])||void 0===o?void 0:o[s])?void 0:h.points[t.key||""])&&a.call(e,l,h,c)})}function a$(t,e,i){var o=e.total?100/e.total:0;t[0]=aX(t[0]*o),t[1]=aX(t[1]*o),this.stackedYData[i]=t[1]}function aJ(t){(this.is("column")||this.is("columnrange"))&&(this.options.centerInCategory&&this.chart.series.length>1?aR.setStackedPoints.call(this,t,"group"):t.stacking.resetStacks())}function aQ(t,e){var i,o,r,n,s,a,h,l,c,d=e||this.options.stacking;if(d&&this.reserveSpace()&&(({group:"xAxis"})[d]||"yAxis")===t.coll){var p=this.getColumn("x",!0),u=this.getColumn(this.pointValKey||"y",!0),f=[],g=u.length,v=this.options,m=v.threshold||0,y=v.startFromThreshold?m:0,x=v.stack,b=e?""+this.type+",".concat(d):this.stackKey||"",k="-"+b,w=this.negStacks,M=t.stacking,S=M.stacks,T=M.oldStacks;for(M.stacksTouched+=1,c=0;c<g;c++){var C=p[c]||0,A=u[c],P=aY(A)&&A||0;l=(r=this.getStackIndicator(r,C,this.index)).key||"",S[h=(n=w&&P<(y?0:m))?k:b]||(S[h]={}),S[h][C]||((null===(i=T[h])||void 0===i?void 0:i[C])?(S[h][C]=T[h][C],S[h][C].total=null):S[h][C]=new az(t,t.options.stackLabels,!!n,C,x)),s=S[h][C],null!==A?(s.points[l]=s.points[this.index]=[a_(s.cumulative,y)],aH(s.cumulative)||(s.base=l),s.touched=M.stacksTouched,r.index>0&&!1===this.singleStacks&&(s.points[l][0]=s.points[this.index+","+C+",0"][0])):(delete s.points[l],delete s.points[this.index]);var O=s.total||0;"percent"===d?(a=n?b:k,O=w&&(null===(o=S[a])||void 0===o?void 0:o[C])?(a=S[a][C]).total=Math.max(a.total||0,O)+Math.abs(P):aX(O+Math.abs(P))):"group"===d?aY(A)&&O++:O=aX(O+P),"group"===d?s.cumulative=(O||1)-1:s.cumulative=aX(a_(s.cumulative,y)+P),s.total=O,null!==A&&(s.points[l].push(s.cumulative),f[c]=s.cumulative,s.hasValidPoints=!0)}"percent"===d&&(M.usePercentage=!0),"group"!==d&&(this.stackedYData=f),M.oldStacks={}}}var a0=function(){function t(t){this.oldStacks={},this.stacks={},this.stacksTouched=0,this.axis=t}return t.prototype.buildStacks=function(){var t,e,i=this.axis,o=i.series,r="xAxis"===i.coll,n=i.options.reversedStacks,s=o.length;for(this.resetStacks(),this.usePercentage=!1,e=s;e--;)t=o[n?e:s-e-1],r&&t.setGroupedPoints(i),t.setStackedPoints(i);if(!r)for(e=0;e<s;e++)o[e].modifyStacks();aF(i,"afterBuildStacks")},t.prototype.cleanStacks=function(){this.oldStacks&&(this.stacks=this.oldStacks,aG(this.stacks,function(t){aG(t,function(t){t.cumulative=t.total})}))},t.prototype.resetStacks=function(){var t=this;aG(this.stacks,function(e){aG(e,function(i,o){aY(i.touched)&&i.touched<t.stacksTouched?(i.destroy(),delete e[o]):(i.total=null,i.cumulative=null)})})},t.prototype.renderStackTotals=function(){var t,e=this.axis,i=e.chart,o=i.renderer,r=this.stacks,n=em(i,(null===(t=e.options.stackLabels)||void 0===t?void 0:t.animation)||!1),s=this.stackTotalGroup=this.stackTotalGroup||o.g("stack-labels").attr({zIndex:6,opacity:0}).add();s.translate(i.plotLeft,i.plotTop),aG(r,function(t){aG(t,function(t){t.render(s)})}),s.animate({opacity:1},n)},t}();(_||(_={})).compose=function(t,e,i){var o=e.prototype,r=i.prototype;o.getStacks||(aW(t,"init",aZ),aW(t,"destroy",aV),o.getStacks=aU,r.getStackIndicator=aq,r.modifyStacks=aK,r.percentStacker=a$,r.setGroupedPoints=aJ,r.setStackedPoints=aQ)};var a1=_,a2=(x=function(t,e){return(x=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}x(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),a3=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},a5=tP.defined,a6=tP.merge,a9=tP.isObject,a4=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a2(e,t),e.prototype.drawGraph=function(){var t=this,e=this.options,i=(this.gappedPath||this.getGraphPath).call(this),o=this.chart.styledMode;a3([this],this.zones,!0).forEach(function(r,n){var s,a=r.graph,h=a?"animate":"attr",l=r.dashStyle||e.dashStyle;a?(a.endX=t.preventGraphAnimation?null:i.xMap,a.animate({d:i})):i.length&&(r.graph=a=t.chart.renderer.path(i).addClass("highcharts-graph"+(n?" highcharts-zone-graph-".concat(n-1," "):" ")+(n&&r.className||"")).attr({zIndex:1}).add(t.group)),a&&!o&&(s={stroke:!n&&e.lineColor||r.color||t.color||"#cccccc","stroke-width":e.lineWidth||0,fill:t.fillGraph&&t.color||"none"},l?s.dashstyle=l:"square"!==e.linecap&&(s["stroke-linecap"]=s["stroke-linejoin"]="round"),a[h](s).shadow(e.shadow&&a6({filterUnits:"userSpaceOnUse"},a9(e.shadow)?e.shadow:{}))),a&&(a.startX=i.xMap,a.isArea=i.isArea)})},e.prototype.getGraphPath=function(t,e,i){var o,r=this,n=r.options,s=[],a=[],h=n.step,l=(t=t||r.points).reversed;return l&&t.reverse(),(h=({right:1,center:2})[h]||h&&3)&&l&&(h=4-h),(t=this.getValidPoints(t,!1,n.nullInteraction||!(n.connectNulls&&!e&&!i))).forEach(function(l,c){var d,p=l.plotX,u=l.plotY,f=t[c-1],g=l.isNull||"number"!=typeof u;(l.leftCliff||(null==f?void 0:f.rightCliff))&&!i&&(o=!0),g&&!a5(e)&&c>0?o=!n.connectNulls:g&&!e?o=!0:(0===c||o?d=[["M",l.plotX,l.plotY]]:r.getPointSpline?d=[r.getPointSpline(t,l,c)]:h?(d=1===h?[["L",f.plotX,u]]:2===h?[["L",(f.plotX+p)/2,f.plotY],["L",(f.plotX+p)/2,u]]:[["L",p,f.plotY]]).push(["L",p,u]):d=[["L",p,u]],a.push(l.x),h&&(a.push(l.x),2===h&&a.push(l.x)),s.push.apply(s,d),o=!1)}),s.xMap=a,r.graphPath=s,s},e.defaultOptions=a6(sw.defaultOptions,{legendSymbol:"lineMarker"}),e}(sw);n0.registerSeriesType("line",a4);var a8={threshold:0,legendSymbol:"areaMarker"},a7=(b=function(t,e){return(b=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}b(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),ht=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},he=n0.seriesTypes.line,hi=tP.extend,ho=tP.merge,hr=tP.objectEach,hn=tP.pick,hs=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a7(e,t),e.prototype.drawGraph=function(){var e=this;this.areaPath=[],t.prototype.drawGraph.apply(this);var i=this.areaPath,o=this.options;ht([this],this.zones,!0).forEach(function(t,r){var n,s={},a=t.fillColor||o.fillColor,h=t.area,l=h?"animate":"attr";h?(h.endX=e.preventGraphAnimation?null:i.xMap,h.animate({d:i})):(s.zIndex=0,(h=t.area=e.chart.renderer.path(i).addClass("highcharts-area"+(r?" highcharts-zone-area-".concat(r-1," "):" ")+(r&&t.className||"")).add(e.group)).isArea=!0),e.chart.styledMode||(s.fill=a||t.color||e.color,s["fill-opacity"]=a?1:null!==(n=o.fillOpacity)&&void 0!==n?n:.75,h.css({pointerEvents:e.stickyTracking?"none":"auto"})),h[l](s),h.startX=i.xMap,h.shiftUnit=o.step?2:1})},e.prototype.getGraphPath=function(t){var e,i,o,r=he.prototype.getGraphPath,n=this.options,s=n.stacking,a=this.yAxis,h=[],l=[],c=this.index,d=a.stacking.stacks[this.stackKey],p=n.threshold,u=Math.round(a.getThreshold(n.threshold)),f=hn(n.connectNulls,"percent"===s),g=function(i,o,r){var n,f,g=t[i],v=s&&d[g.x].points[c],m=g[r+"Null"]||0,y=g[r+"Cliff"]||0,x=!0;y||m?(n=(m?v[0]:v[1])+y,f=v[0]+y,x=!!m):!s&&t[o]&&t[o].isNull&&(n=f=p),void 0!==n&&(l.push({plotX:e,plotY:null===n?u:a.getThreshold(n),isNull:x,isCliff:!0}),h.push({plotX:e,plotY:null===f?u:a.getThreshold(f),doCurve:!1}))};t=t||this.points,s&&(t=this.getStackPoints(t));for(var v=0,m=t.length;v<m;++v)s||(t[v].leftCliff=t[v].rightCliff=t[v].leftNull=t[v].rightNull=void 0),i=t[v].isNull,e=hn(t[v].rectPlotX,t[v].plotX),o=s?hn(t[v].yBottom,u):u,i&&!f||(f||g(v,v-1,"left"),i&&!s&&f||(l.push(t[v]),h.push({x:v,plotX:e,plotY:o})),f||g(v,v+1,"right"));var y=r.call(this,l,!0,!0);h.reversed=!0;var x=r.call(this,h,!0,!0),b=x[0];b&&"M"===b[0]&&(x[0]=["L",b[1],b[2]]);var k=y.concat(x);k.length&&k.push(["Z"]);var w=r.call(this,l,!1,f);return this.chart.series.length>1&&s&&l.some(function(t){return t.isCliff})&&(k.hasStackedCliffs=w.hasStackedCliffs=!0),k.xMap=y.xMap,this.areaPath=k,w},e.prototype.getStackPoints=function(t){var e=this,i=[],o=[],r=this.xAxis,n=this.yAxis,s=n.stacking.stacks[this.stackKey],a={},h=n.series,l=h.length,c=n.options.reversedStacks?1:-1,d=h.indexOf(e);if(t=t||this.points,this.options.stacking){for(var p=0;p<t.length;p++)t[p].leftNull=t[p].rightNull=void 0,a[t[p].x]=t[p];hr(s,function(t,e){null!==t.total&&o.push(e)}),o.sort(function(t,e){return t-e});var u=h.map(function(t){return t.visible});o.forEach(function(t,p){var f,g,v=0;if(a[t]&&!a[t].isNull)i.push(a[t]),[-1,1].forEach(function(i){var r=1===i?"rightNull":"leftNull",n=s[o[p+i]],v=0;if(n)for(var m=d;m>=0&&m<l;){var y=h[m].index;!(f=n.points[y])&&(y===e.index?a[t][r]=!0:u[m]&&(g=s[t].points[y])&&(v-=g[1]-g[0])),m+=c}a[t][1===i?"rightCliff":"leftCliff"]=v});else{for(var m=d;m>=0&&m<l;){var y=h[m].index;if(f=s[t].points[y]){v=f[1];break}m+=c}v=hn(v,0),v=n.translate(v,0,1,0,1),i.push({isNull:!0,plotX:r.translate(t,0,0,0,1),x:t,plotY:v,yBottom:v})}})}return i},e.defaultOptions=ho(he.defaultOptions,a8),e}(he);hi(hs.prototype,{singleStacks:!1}),n0.registerSeriesType("area",hs);var ha=(k=function(t,e){return(k=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}k(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hh=n0.seriesTypes.line,hl=tP.merge,hc=tP.pick,hd=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ha(e,t),e.prototype.getPointSpline=function(t,e,i){var o,r,n,s,a=e.plotX||0,h=e.plotY||0,l=t[i-1],c=t[i+1];function d(t){return t&&!t.isNull&&!1!==t.doCurve&&!e.isCliff}if(d(l)&&d(c)){var p=l.plotX||0,u=l.plotY||0,f=c.plotX||0,g=c.plotY||0,v=0;o=(1.5*a+p)/2.5,r=(1.5*h+u)/2.5,n=(1.5*a+f)/2.5,s=(1.5*h+g)/2.5,n!==o&&(v=(s-r)*(n-a)/(n-o)+h-s),r+=v,s+=v,r>u&&r>h?(r=Math.max(u,h),s=2*h-r):r<u&&r<h&&(r=Math.min(u,h),s=2*h-r),s>g&&s>h?(s=Math.max(g,h),r=2*h-s):s<g&&s<h&&(s=Math.min(g,h),r=2*h-s),e.rightContX=n,e.rightContY=s,e.controlPoints={low:[o,r],high:[n,s]}}var m=["C",hc(l.rightContX,l.plotX,0),hc(l.rightContY,l.plotY,0),hc(o,a,0),hc(r,h,0),a,h];return l.rightContX=l.rightContY=void 0,m},e.defaultOptions=hl(hh.defaultOptions),e}(hh);n0.registerSeriesType("spline",hd);var hp=(w=function(t,e){return(w=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}w(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hu=n0.seriesTypes,hf=hu.area,hg=hu.area.prototype,hv=tP.extend,hm=tP.merge,hy=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hp(e,t),e.defaultOptions=hm(hd.defaultOptions,hf.defaultOptions),e}(hd);hv(hy.prototype,{getGraphPath:hg.getGraphPath,getStackPoints:hg.getStackPoints,drawGraph:hg.drawGraph}),n0.registerSeriesType("areaspline",hy);var hx={borderRadius:3,centerInCategory:!1,groupPadding:.2,marker:null,pointPadding:.1,minPointLength:0,cropThreshold:50,pointRange:null,states:{hover:{halo:!1,brightness:.1},select:{color:"#cccccc",borderColor:"#000000"}},dataLabels:{align:void 0,verticalAlign:void 0,y:void 0},startFromThreshold:!0,stickyTracking:!1,tooltip:{distance:6},threshold:0,borderColor:"#ffffff"},hb=(M=function(t,e){return(M=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}M(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hk=et.parse,hw=ti.noop,hM=tP.clamp,hS=tP.crisp,hT=tP.defined,hC=tP.extend,hA=tP.fireEvent,hP=tP.isArray,hO=tP.isNumber,hL=tP.merge,hE=tP.pick,hI=tP.objectEach,hD=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hb(e,t),e.prototype.animate=function(t){var e,i,o=this,r=this.yAxis,n=r.pos,s=r.reversed,a=o.options,h=this.chart,l=h.clipOffset,c=h.inverted,d={},p=c?"translateX":"translateY";t&&l?(d.scaleY=.001,i=hM(r.toPixels(a.threshold||0),n,n+r.len),c?d.translateX=(i+=s?-Math.floor(l[0]):Math.ceil(l[2]))-r.len:d.translateY=i+=s?Math.ceil(l[0]):-Math.floor(l[2]),o.clipBox&&o.setClip(),o.group.attr(d)):(e=Number(o.group.attr(p)),o.group.animate({scaleY:1},hC(ef(o.options.animation),{step:function(t,i){o.group&&(d[p]=e+i.pos*(n-e),o.group.attr(d))}})))},e.prototype.init=function(e,i){t.prototype.init.apply(this,arguments);var o=this;(e=o.chart).hasRendered&&e.series.forEach(function(t){t.type===o.type&&(t.isDirty=!0)})},e.prototype.getColumnMetrics=function(){var t,e,i,o=this,r=o.options,n=o.xAxis,s=o.yAxis,a=n.options.reversedStacks,h=n.reversed&&!a||!n.reversed&&a,l={},c=0;!1===r.grouping?c=1:o.chart.series.forEach(function(t){var e,r=t.yAxis,n=t.options;t.type===o.type&&t.reserveSpace()&&s.len===r.len&&s.pos===r.pos&&(n.stacking&&"group"!==n.stacking?(void 0===l[i=t.stackKey]&&(l[i]=c++),e=l[i]):!1!==n.grouping&&(e=c++),t.columnIndex=e)});var d=Math.min(Math.abs(n.transA)*(!(null===(t=n.brokenAxis)||void 0===t?void 0:t.hasBreaks)&&(null===(e=n.ordinal)||void 0===e?void 0:e.slope)||r.pointRange||n.closestPointRange||n.tickInterval||1),n.len),p=d*r.groupPadding,u=(d-2*p)/(c||1),f=Math.min(r.maxPointWidth||n.len,hE(r.pointWidth,u*(1-2*r.pointPadding))),g=(o.columnIndex||0)+ +!!h;return o.columnMetrics={width:f,offset:(u-f)/2+(p+g*u-d/2)*(h?-1:1),paddedWidth:u,columnCount:c},o.columnMetrics},e.prototype.crispCol=function(t,e,i,o){var r=this.borderWidth,n=this.chart.inverted;return o=hS(e+o,r,n)-(e=hS(e,r,n)),this.options.crisp&&(i=hS(t+i,r)-(t=hS(t,r))),{x:t,y:e,width:i,height:o}},e.prototype.adjustForMissingColumns=function(t,e,i,o){var r,n=this;if(!i.isNull&&o.columnCount>1){var s=this.xAxis.series.filter(function(t){return t.visible}).map(function(t){return t.index}),a=0,h=0;hI(null===(r=this.xAxis.stacking)||void 0===r?void 0:r.stacks,function(t){var e,o="number"==typeof i.x?null===(e=t[i.x.toString()])||void 0===e?void 0:e.points:void 0,r=null==o?void 0:o[n.index],l={};if(o&&hP(r)){var c=n.index,d=Object.keys(o).filter(function(t){return!t.match(",")&&o[t]&&o[t].length>1}).map(parseFloat).filter(function(t){return -1!==s.indexOf(t)}).filter(function(t){var e=n.chart.series[t].options,i=e.stacking&&e.stack;if(hT(i)){if(hO(l[i]))return c===t&&(c=l[i]),!1;l[i]=t}return!0}).sort(function(t,e){return e-t});a=d.indexOf(c),h=d.length}}),a=this.xAxis.reversed?h-1-a:a;var l=(h-1)*o.paddedWidth+e;t=(i.plotX||0)+l/2-e-a*o.paddedWidth}return t},e.prototype.translate=function(){var t=this,e=t.chart,i=t.options,o=t.dense=t.closestPointRange*t.xAxis.transA<2,r=t.borderWidth=hE(i.borderWidth,+!o),n=t.xAxis,s=t.yAxis,a=i.threshold,h=hE(i.minPointLength,5),l=t.getColumnMetrics(),c=l.width,d=t.pointXOffset=l.offset,p=t.dataMin,u=t.dataMax,f=t.translatedThreshold=s.getThreshold(a),g=t.barW=Math.max(c,1+2*r);i.pointPadding&&i.crisp&&(g=Math.ceil(g)),sw.prototype.translate.apply(t),t.points.forEach(function(o){var r,v=hE(o.yBottom,f),m=999+Math.abs(v),y=o.plotX||0,x=hM(o.plotY,-m,s.len+m),b=Math.min(x,v),k=Math.max(x,v)-b,w=c,M=y+d,S=g;h&&Math.abs(k)<h&&(k=h,r=!s.reversed&&!o.negative||s.reversed&&o.negative,hO(a)&&hO(u)&&o.y===a&&u<=a&&(s.min||0)<a&&(p!==u||(s.max||0)<=a)&&(r=!r,o.negative=!o.negative),b=Math.abs(b-f)>h?v-h:f-(r?h:0)),hT(o.options.pointWidth)&&(M-=Math.round(((w=S=Math.ceil(o.options.pointWidth))-c)/2)),i.centerInCategory&&(M=t.adjustForMissingColumns(M,w,o,l)),o.barX=M,o.pointWidth=w,o.tooltipPos=e.inverted?[hM(s.len+s.pos-e.plotLeft-x,s.pos-e.plotLeft,s.len+s.pos-e.plotLeft),n.len+n.pos-e.plotTop-M-S/2,k]:[n.left-e.plotLeft+M+S/2,hM(x+s.pos-e.plotTop,s.pos-e.plotTop,s.len+s.pos-e.plotTop),k],o.shapeType=t.pointClass.prototype.shapeType||"roundedRect",o.shapeArgs=t.crispCol(M,b,S,o.isNull?0:k)}),hA(this,"afterColumnTranslate")},e.prototype.drawGraph=function(){this.group[this.dense?"addClass":"removeClass"]("highcharts-dense-data")},e.prototype.pointAttribs=function(t,e){var i,o,r,n,s,a=this.options,h=this.pointAttrToOptions||{},l=h.stroke||"borderColor",c=h["stroke-width"]||"borderWidth",d=t&&t.color||this.color,p=t&&t[l]||a[l]||d,u=t&&t.options.dashStyle||a.dashStyle,f=t&&t[c]||a[c]||this[c]||0,g=(null==t?void 0:t.isNull)&&a.nullInteraction?0:null!==(o=null!==(i=null==t?void 0:t.opacity)&&void 0!==i?i:a.opacity)&&void 0!==o?o:1;t&&this.zones.length&&(n=t.getZone(),d=t.options.color||n&&(n.color||t.nonZonedColor)||this.color,n&&(p=n.borderColor||p,u=n.dashStyle||u,f=n.borderWidth||f)),e&&t&&(s=(r=hL(a.states[e],t.options.states&&t.options.states[e]||{})).brightness,d=r.color||void 0!==s&&hk(d).brighten(r.brightness).get()||d,p=r[l]||p,f=r[c]||f,u=r.dashStyle||u,g=hE(r.opacity,g));var v={fill:d,stroke:p,"stroke-width":f,opacity:g};return u&&(v.dashstyle=u),v},e.prototype.drawPoints=function(t){void 0===t&&(t=this.points);var e,i=this,o=this.chart,r=i.options,n=r.nullInteraction,s=o.renderer,a=r.animationLimit||250;t.forEach(function(t){var h=t.plotY,l=t.graphic,c=!!l,d=l&&o.pointCount<a?"animate":"attr";hO(h)&&(null!==t.y||n)?(e=t.shapeArgs,l&&t.hasNewShapeType()&&(l=l.destroy()),i.enabledDataSorting&&(t.startXPos=i.xAxis.reversed?-(e&&e.width||0):i.xAxis.width),!l&&(t.graphic=l=s[t.shapeType](e).add(t.group||i.group),l&&i.enabledDataSorting&&o.hasRendered&&o.pointCount<a&&(l.attr({x:t.startXPos}),c=!0,d="animate")),l&&c&&l[d](hL(e)),o.styledMode||l[d](i.pointAttribs(t,t.selected&&"select")).shadow(!1!==t.allowShadow&&r.shadow),l&&(l.addClass(t.getClassName(),!0),l.attr({visibility:t.visible?"inherit":"hidden"}))):l&&(t.graphic=l.destroy())})},e.prototype.drawTracker=function(t){void 0===t&&(t=this.points);var e,i=this,o=i.chart,r=o.pointer,n=function(t){null==r||r.normalize(t);var e=null==r?void 0:r.getPointFromEvent(t);r&&e&&i.options.enableMouseTracking&&(o.isInsidePlot(t.chartX-o.plotLeft,t.chartY-o.plotTop,{visiblePlotOnly:!0})||(null==r?void 0:r.inClass(t.target,"highcharts-data-label")))&&(r.isDirectTouch=!0,e.onMouseOver(t))};t.forEach(function(t){e=hP(t.dataLabels)?t.dataLabels:t.dataLabel?[t.dataLabel]:[],t.graphic&&(t.graphic.element.point=t),e.forEach(function(e){(e.div||e.element).point=t})}),i._hasTracking||(i.trackerGroups.forEach(function(t){i[t]&&(i[t].addClass("highcharts-tracker").on("mouseover",n).on("mouseout",function(t){null==r||r.onTrackerMouseOut(t)}).on("touchstart",n),!o.styledMode&&i.options.cursor&&i[t].css({cursor:i.options.cursor}))}),i._hasTracking=!0),hA(this,"afterDrawTracker")},e.prototype.remove=function(){var t=this,e=t.chart;e.hasRendered&&e.series.forEach(function(e){e.type===t.type&&(e.isDirty=!0)}),sw.prototype.remove.apply(t,arguments)},e.defaultOptions=hL(sw.defaultOptions,hx),e}(sw);hC(hD.prototype,{directTouch:!0,getSymbol:hw,negStacks:!0,trackerGroups:["group","dataLabelsGroup"]}),n0.registerSeriesType("column",hD);var hB=eU.format,hN=tP.defined,hz=tP.extend,hR=tP.fireEvent,hW=tP.getAlignFactor,hX=tP.isArray,hH=tP.isString,hj=tP.merge,hF=tP.objectEach,hY=tP.pick,hG=tP.pInt,h_=tP.splat;!function(t){function e(){return h(this).some(function(t){return null==t?void 0:t.enabled})}function i(t,e,i,o,r){var n,s=this.chart,a=this.enabledDataSorting,h=this.isCartesian&&s.inverted,l=t.plotX,c=t.plotY,d=i.rotation||0,p=hN(l)&&hN(c)&&s.isInsidePlot(l,Math.round(c),{inverted:h,paneCoordinates:!0,series:this}),u=0===d&&"justify"===hY(i.overflow,a?"none":"justify"),f=this.visible&&!1!==t.visible&&hN(l)&&(t.series.forceDL||a&&!u||p||hY(i.inside,!!this.options.stacking)&&o&&s.isInsidePlot(l,h?o.x+1:o.y+o.height-1,{inverted:h,paneCoordinates:!0,series:this})),g=t.pos();if(f&&g){var v,m=e.getBBox(),y=e.getBBox(void 0,0);if(o=hz({x:g[0],y:Math.round(g[1]),width:0,height:0},o||{}),"plotEdges"===i.alignTo&&this.isCartesian&&(o[h?"x":"y"]=0,o[h?"width":"height"]=(null===(n=this.yAxis)||void 0===n?void 0:n.len)||0),hz(i,{width:m.width,height:m.height}),v=o,a&&this.xAxis&&!u&&this.setDataLabelStartPos(t,e,r,p,v),e.align(hj(i,{width:y.width,height:y.height}),!1,o,!1),e.alignAttr.x+=hW(i.align)*(y.width-m.width),e.alignAttr.y+=hW(i.verticalAlign)*(y.height-m.height),e[e.placed?"animate":"attr"]({"text-align":e.alignAttr["text-align"]||"center",x:e.alignAttr.x+(m.width-y.width)/2,y:e.alignAttr.y+(m.height-y.height)/2,rotationOriginX:(e.width||0)/2,rotationOriginY:(e.height||0)/2}),u&&o.height>=0)this.justifyDataLabel(e,i,e.alignAttr,m,o,r);else if(hY(i.crop,!0)){var x=e.alignAttr,b=x.x,k=x.y;f=s.isInsidePlot(b,k,{paneCoordinates:!0,series:this})&&s.isInsidePlot(b+m.width-1,k+m.height-1,{paneCoordinates:!0,series:this})}i.shape&&!d&&e[r?"attr":"animate"]({anchorX:g[0],anchorY:g[1]})}r&&a&&(e.placed=!1),f||a&&!u?(e.show(),e.placed=!0):(e.hide(),e.placed=!1)}function o(){return this.plotGroup("dataLabelsGroup","data-labels",this.hasRendered?"inherit":"hidden",this.options.dataLabels.zIndex||6)}function r(t){var e=this.hasRendered||0,i=this.initDataLabelsGroup().attr({opacity:+e});return!e&&i&&(this.visible&&i.show(),this.options.animation?i.animate({opacity:1},t):i.attr({opacity:1})),i}function n(t){t=t||this.points;var e,i,o=this,r=o.chart,n=o.options,s=r.renderer,l=r.options.chart,c=l.backgroundColor,d=l.plotBackgroundColor,p=s.getContrast(hH(d)&&d||hH(c)&&c||"#000000"),u=h(o),f=u[0],g=f.animation,v=f.defer?em(r,g,o):{defer:0,duration:0};hR(this,"drawDataLabels"),(null===(e=o.hasDataLabels)||void 0===e?void 0:e.call(o))&&(i=this.initDataLabels(v),t.forEach(function(t){var e,h,l,c=t.dataLabels||[],d=t.color||o.color;h_(a(u,t.dlOptions||(null===(e=t.options)||void 0===e?void 0:e.dataLabels))).forEach(function(e,a){var h,l,u,f,g,v=e.enabled&&(t.visible||t.dataLabelOnHidden)&&(!t.isNull||t.dataLabelOnNull)&&function(t,e){var i=e.filter;if(i){var o=i.operator,r=t[i.property],n=i.value;return">"===o&&r>n||"<"===o&&r<n||">="===o&&r>=n||"<="===o&&r<=n||"=="===o&&r==n||"==="===o&&r===n||"!="===o&&r!=n||"!=="===o&&r!==n||!1}return!0}(t,e),m=e.backgroundColor,y=e.borderColor,x=e.distance,b=e.style,k=void 0===b?{}:b,w={},M=c[a],S=!M;v&&(u=hN(l=hY(e[t.formatPrefix+"Format"],e.format))?hB(l,t,r):(e[t.formatPrefix+"Formatter"]||e.formatter).call(t,e),f=e.rotation,!r.styledMode&&(k.color=hY(e.color,k.color,hH(o.color)?o.color:void 0,"#000000"),"contrast"===k.color?("none"!==m&&(g=m),t.contrastColor=s.getContrast("auto"!==g&&hH(g)&&g||(hH(d)?d:"")),k.color=g||!hN(x)&&e.inside||0>hG(x||0)||n.stacking?t.contrastColor:p):delete t.contrastColor,n.cursor&&(k.cursor=n.cursor)),w={r:e.borderRadius||0,rotation:f,padding:e.padding,zIndex:1},r.styledMode||(w.fill="auto"===m?t.color:m,w.stroke="auto"===y?t.color:y,w["stroke-width"]=e.borderWidth),hF(w,function(t,e){void 0===t&&delete w[e]})),!M||v&&hN(u)&&!!(M.div||(null===(h=M.text)||void 0===h?void 0:h.foreignObject))==!!e.useHTML&&(M.rotation&&e.rotation||M.rotation===e.rotation)||(M=void 0,S=!0),v&&hN(u)&&(M?w.text=u:(M=s.label(u,0,0,e.shape,void 0,void 0,e.useHTML,void 0,"data-label")).addClass(" highcharts-data-label-color-"+t.colorIndex+" "+(e.className||"")+(e.useHTML?" highcharts-tracker":"")),M&&(M.options=e,M.attr(w),r.styledMode?k.width&&M.css({width:k.width,textOverflow:k.textOverflow,whiteSpace:k.whiteSpace}):M.css(k).shadow(e.shadow),hR(M,"beforeAddingDataLabel",{labelOptions:e,point:t}),M.added||M.add(i),o.alignDataLabel(t,M,e,void 0,S),M.isActive=!0,c[a]&&c[a]!==M&&c[a].destroy(),c[a]=M))});for(var f=c.length;f--;)(null===(h=c[f])||void 0===h?void 0:h.isActive)?c[f].isActive=!1:(null===(l=c[f])||void 0===l||l.destroy(),c.splice(f,1));t.dataLabel=c[0],t.dataLabels=c})),hR(this,"afterDrawDataLabels")}function s(t,e,i,o,r,n){var s,a,h=this.chart,l=e.align,c=e.verticalAlign,d=t.box?0:t.padding||0,p=h.inverted?this.yAxis:this.xAxis,u=p?p.left-h.plotLeft:0,f=h.inverted?this.xAxis:this.yAxis,g=f?f.top-h.plotTop:0,v=e.x,m=void 0===v?0:v,y=e.y,x=void 0===y?0:y;return(s=(i.x||0)+d+u)<0&&("right"===l&&m>=0?(e.align="left",e.inside=!0):m-=s,a=!0),(s=(i.x||0)+o.width-d+u)>h.plotWidth&&("left"===l&&m<=0?(e.align="right",e.inside=!0):m+=h.plotWidth-s,a=!0),(s=i.y+d+g)<0&&("bottom"===c&&x>=0?(e.verticalAlign="top",e.inside=!0):x-=s,a=!0),(s=(i.y||0)+o.height-d+g)>h.plotHeight&&("top"===c&&x<=0?(e.verticalAlign="bottom",e.inside=!0):x+=h.plotHeight-s,a=!0),a&&(e.x=m,e.y=x,t.placed=!n,t.align(e,void 0,r)),a}function a(t,e){var i,o=[];if(hX(t)&&!hX(e))o=t.map(function(t){return hj(t,e)});else if(hX(e)&&!hX(t))o=e.map(function(e){return hj(t,e)});else if(hX(t)||hX(e)){if(hX(t)&&hX(e))for(i=Math.max(t.length,e.length);i--;)o[i]=hj(t[i],e[i])}else o=hj(t,e);return o}function h(t){var e,i,o=t.chart.options.plotOptions;return h_(a(a(null===(e=null==o?void 0:o.series)||void 0===e?void 0:e.dataLabels,null===(i=null==o?void 0:o[t.type])||void 0===i?void 0:i.dataLabels),t.options.dataLabels))}function l(t,e,i,o,r){var n=this.chart,s=n.inverted,a=this.xAxis,h=a.reversed,l=((s?e.height:e.width)||0)/2,c=t.pointWidth,d=c?c/2:0;e.startXPos=s?r.x:h?-l-d:a.width-l+d,e.startYPos=s?h?this.yAxis.height-l+d:-l-d:r.y,o?"hidden"===e.visibility&&(e.show(),e.attr({opacity:0}).animate({opacity:1})):e.attr({opacity:1}).animate({opacity:0},void 0,e.hide),n.hasRendered&&(i&&e.attr({x:e.startXPos,y:e.startYPos}),e.placed=!0)}t.compose=function(t){var h=t.prototype;h.initDataLabels||(h.initDataLabels=r,h.initDataLabelsGroup=o,h.alignDataLabel=i,h.drawDataLabels=n,h.justifyDataLabel=s,h.mergeArrays=a,h.setDataLabelStartPos=l,h.hasDataLabels=e)}}(U||(U={}));var hU=U,hV=ti.composed,hZ=n0.series,hq=tP.merge,hK=tP.pushUnique;!function(t){function e(t,e,i,o,r){var n,s,a,h,l,c,d,p=this.chart,u=this.options,f=p.inverted,g=(null===(n=this.xAxis)||void 0===n?void 0:n.len)||p.plotSizeX||0,v=(null===(s=this.yAxis)||void 0===s?void 0:s.len)||p.plotSizeY||0,m=t.dlBox||t.shapeArgs,y=null!==(a=t.below)&&void 0!==a?a:(t.plotY||0)>(null!==(h=this.translatedThreshold)&&void 0!==h?h:v),x=null!==(l=i.inside)&&void 0!==l?l:!!u.stacking;if(m){if(o=hq(m),"allow"!==i.overflow||!1!==i.crop||!1!==u.clip){o.y<0&&(o.height+=o.y,o.y=0);var b=o.y+o.height-v;b>0&&b<o.height-1&&(o.height-=b)}f&&(o={x:v-o.y-o.height,y:g-o.x-o.width,width:o.height,height:o.width}),x||(f?(o.x+=y?0:o.width,o.width=0):(o.y+=y?o.height:0,o.height=0))}null!==(c=i.align)&&void 0!==c||(i.align=!f||x?"center":y?"right":"left"),null!==(d=i.verticalAlign)&&void 0!==d||(i.verticalAlign=f||x?"middle":y?"top":"bottom"),hZ.prototype.alignDataLabel.call(this,t,e,i,o,r),i.inside&&t.contrastColor&&e.css({color:t.contrastColor})}t.compose=function(t){hU.compose(hZ),hK(hV,"ColumnDataLabel")&&(t.prototype.alignDataLabel=e)}}(V||(V={}));var h$=V,hJ=(S=function(t,e){return(S=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}S(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hQ=tP.extend,h0=tP.merge,h1=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hJ(e,t),e.defaultOptions=h0(hD.defaultOptions,{}),e}(hD);hQ(h1.prototype,{inverted:!0}),n0.registerSeriesType("bar",h1);var h2={lineWidth:0,findNearestPointBy:"xy",jitter:{x:0,y:0},marker:{enabled:!0},tooltip:{headerFormat:'<span style="color:{point.color}">●</span> <span style="font-size: 0.8em"> {series.name}</span><br/>',pointFormat:"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>"}},h3=(T=function(t,e){return(T=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}T(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),h5=n0.seriesTypes,h6=h5.column,h9=h5.line,h4=tP.addEvent,h8=tP.extend,h7=tP.merge,lt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return h3(e,t),e.prototype.applyJitter=function(){var t=this,e=this.options.jitter,i=this.points.length;e&&this.points.forEach(function(o,r){["x","y"].forEach(function(n,s){if(e[n]&&!o.isNull){var a="plot".concat(n.toUpperCase()),h=t[""+n+"Axis"],l=e[n]*h.transA;if(h&&!h.logarithmic){var c,d=Math.max(0,(o[a]||0)-l),p=Math.min(h.len,(o[a]||0)+l);o[a]=d+(p-d)*((c=1e4*Math.sin(r+s*i))-Math.floor(c)),"x"===n&&(o.clientX=o.plotX)}}})})},e.prototype.drawGraph=function(){this.options.lineWidth?t.prototype.drawGraph.call(this):this.graph&&(this.graph=this.graph.destroy())},e.defaultOptions=h7(h9.defaultOptions,h2),e}(h9);h8(lt.prototype,{drawTracker:h6.prototype.drawTracker,sorted:!1,requireSorting:!1,noSharedTooltip:!0,trackerGroups:["group","markerGroup","dataLabelsGroup"]}),h4(lt,"afterTranslate",function(){this.applyJitter()}),n0.registerSeriesType("scatter",lt);var le=ti.deg2rad,li=tP.fireEvent,lo=tP.isNumber,lr=tP.pick,ln=tP.relativeLength;(C=Z||(Z={})).getCenter=function(){var t,e,i,o=this.options,r=this.chart,n=2*(o.slicedOffset||0),s=r.plotWidth-2*n,a=r.plotHeight-2*n,h=o.center,l=Math.min(s,a),c=o.thickness,d=o.size,p=o.innerSize||0;"string"==typeof d&&(d=parseFloat(d)),"string"==typeof p&&(p=parseFloat(p));var u=[lr(null==h?void 0:h[0],"50%"),lr(null==h?void 0:h[1],"50%"),lr(d&&d<0?void 0:o.size,"100%"),lr(p&&p<0?void 0:o.innerSize||0,"0%")];for(!r.angular||this instanceof sw||(u[3]=0),e=0;e<4;++e)i=u[e],t=e<2||2===e&&/%$/.test(i),u[e]=ln(i,[s,a,l,u[2]][e])+(t?n:0);return u[3]>u[2]&&(u[3]=u[2]),lo(c)&&2*c<u[2]&&c>0&&(u[3]=u[2]-2*c),li(this,"afterGetCenter",{positions:u}),u},C.getStartAndEndRadians=function(t,e){var i=lo(t)?t:0,o=lo(e)&&e>i&&e-i<360?e:i+360;return{start:le*(i+-90),end:le*(o+-90)}};var ls=Z,la=(A=function(t,e){return(A=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}A(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),lh=function(){return(lh=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},ll=tP.addEvent,lc=tP.defined,ld=tP.extend,lp=tP.isNumber,lu=tP.pick,lf=tP.relativeLength,lg=function(t){function e(e,i,o){var r,n=t.call(this,e,i,o)||this;n.half=0,null!==(r=n.name)&&void 0!==r||(n.name=e.chart.options.lang.pieSliceName);var s=function(t){n.slice("select"===t.type)};return ll(n,"select",s),ll(n,"unselect",s),n}return la(e,t),e.prototype.getConnectorPath=function(t){var e=t.dataLabelPosition,i=t.options||{},o=i.connectorShape,r=this.connectorShapes[o]||o;return e&&r.call(this,lh(lh({},e.computed),{alignment:e.alignment}),e.connectorPosition,i)||[]},e.prototype.getTranslate=function(){return this.sliced&&this.slicedTranslation||{translateX:0,translateY:0}},e.prototype.haloPath=function(t){var e=this.shapeArgs;return this.sliced||!this.visible?[]:this.series.chart.renderer.symbols.arc(e.x,e.y,e.r+t,e.r+t,{innerR:e.r-1,start:e.start,end:e.end,borderRadius:e.borderRadius})},e.prototype.isValid=function(){return lp(this.y)&&this.y>=0},e.prototype.setVisible=function(t,e){void 0===e&&(e=!0),t!==this.visible&&this.update({visible:null!=t?t:!this.visible},e,void 0,!1)},e.prototype.slice=function(t,e,i){var o=this.series;ey(i,o.chart),e=lu(e,!0),this.sliced=this.options.sliced=t=lc(t)?t:!this.sliced,o.options.data[o.data.indexOf(this)]=this.options,this.graphic&&this.graphic.animate(this.getTranslate())},e}(ng);ld(lg.prototype,{connectorShapes:{fixedOffset:function(t,e,i){var o=e.breakAt,r=e.touchingSliceAt,n=i.softConnector?["C",t.x+("left"===t.alignment?-5:5),t.y,2*o.x-r.x,2*o.y-r.y,o.x,o.y]:["L",o.x,o.y];return[["M",t.x,t.y],n,["L",r.x,r.y]]},straight:function(t,e){var i=e.touchingSliceAt;return[["M",t.x,t.y],["L",i.x,i.y]]},crookedLine:function(t,e,i){var o=e.angle,r=void 0===o?this.angle||0:o,n=e.breakAt,s=e.touchingSliceAt,a=this.series,h=a.center,l=h[0],c=h[1],d=h[2]/2,p=a.chart,u=p.plotLeft,f=p.plotWidth,g="left"===t.alignment,v=t.x,m=t.y,y=n.x;if(i.crookDistance){var x=lf(i.crookDistance,1);y=g?l+d+(f+u-l-d)*(1-x):u+(l-d)*x}else y=l+(c-m)*Math.tan(r-Math.PI/2);var b=[["M",v,m]];return(g?y<=v&&y>=n.x:y>=v&&y<=n.x)&&b.push(["L",y,m]),b.push(["L",n.x,n.y],["L",s.x,s.y]),b}}});var lv={borderRadius:3,center:[null,null],clip:!1,colorByPoint:!0,dataLabels:{connectorPadding:5,connectorShape:"crookedLine",crookDistance:void 0,distance:30,enabled:!0,formatter:function(){return this.isNull?void 0:this.name},softConnector:!0,x:0},fillColor:void 0,ignoreHiddenPoint:!0,inactiveOtherPoints:!0,legendType:"point",marker:null,size:null,showInLegend:!1,slicedOffset:10,stickyTracking:!1,tooltip:{followPointer:!0},borderColor:"#ffffff",borderWidth:1,lineWidth:void 0,states:{hover:{brightness:.1}}},lm=(P=function(t,e){return(P=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}P(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),ly=ls.getStartAndEndRadians,lx=ti.noop,lb=tP.clamp,lk=tP.extend,lw=tP.fireEvent,lM=tP.merge,lS=tP.pick,lT=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return lm(e,t),e.prototype.animate=function(t){var e=this,i=e.points,o=e.startAngleRad;t||i.forEach(function(t){var i=t.graphic,r=t.shapeArgs;i&&r&&(i.attr({r:lS(t.startR,e.center&&e.center[3]/2),start:o,end:o}),i.animate({r:r.r,start:r.start,end:r.end},e.options.animation))})},e.prototype.drawEmpty=function(){var t,e,i=this.startAngleRad,o=this.endAngleRad,r=this.options;0===this.total&&this.center?(t=this.center[0],e=this.center[1],this.graph||(this.graph=this.chart.renderer.arc(t,e,this.center[1]/2,0,i,o).addClass("highcharts-empty-series").add(this.group)),this.graph.attr({d:iz.arc(t,e,this.center[2]/2,0,{start:i,end:o,innerR:this.center[3]/2})}),this.chart.styledMode||this.graph.attr({"stroke-width":r.borderWidth,fill:r.fillColor||"none",stroke:r.color||"#cccccc"})):this.graph&&(this.graph=this.graph.destroy())},e.prototype.drawPoints=function(){var t=this.chart.renderer;this.points.forEach(function(e){e.graphic&&e.hasNewShapeType()&&(e.graphic=e.graphic.destroy()),e.graphic||(e.graphic=t[e.shapeType](e.shapeArgs).add(e.series.group),e.delayedRendering=!0)})},e.prototype.generatePoints=function(){t.prototype.generatePoints.call(this),this.updateTotals()},e.prototype.getX=function(t,e,i,o){var r=this.center,n=this.radii?this.radii[i.index]||0:r[2]/2,s=o.dataLabelPosition,a=(null==s?void 0:s.distance)||0,h=Math.asin(lb((t-r[1])/(n+a),-1,1));return r[0]+Math.cos(h)*(n+a)*(e?-1:1)+(a>0?(e?-1:1)*(o.padding||0):0)},e.prototype.hasData=function(){return!!this.dataTable.rowCount},e.prototype.redrawPoints=function(){var t,e,i,o,r=this,n=r.chart;this.drawEmpty(),r.group&&!n.styledMode&&r.group.shadow(r.options.shadow),r.points.forEach(function(s){var a={};e=s.graphic,!s.isNull&&e?(o=s.shapeArgs,t=s.getTranslate(),n.styledMode||(i=r.pointAttribs(s,s.selected&&"select")),s.delayedRendering?(e.setRadialReference(r.center).attr(o).attr(t),n.styledMode||e.attr(i).attr({"stroke-linejoin":"round"}),s.delayedRendering=!1):(e.setRadialReference(r.center),n.styledMode||lM(!0,a,i),lM(!0,a,o,t),e.animate(a)),e.attr({visibility:s.visible?"inherit":"hidden"}),e.addClass(s.getClassName(),!0)):e&&(s.graphic=e.destroy())})},e.prototype.sortByAngle=function(t,e){t.sort(function(t,i){return void 0!==t.angle&&(i.angle-t.angle)*e})},e.prototype.translate=function(t){lw(this,"translate"),this.generatePoints();var e,i,o,r,n,s,a,h=this.options,l=h.slicedOffset,c=ly(h.startAngle,h.endAngle),d=this.startAngleRad=c.start,p=(this.endAngleRad=c.end)-d,u=this.points,f=h.ignoreHiddenPoint,g=u.length,v=0;for(t||(this.center=t=this.getCenter()),s=0;s<g;s++){a=u[s],e=d+v*p,a.isValid()&&(!f||a.visible)&&(v+=a.percentage/100),i=d+v*p;var m={x:t[0],y:t[1],r:t[2]/2,innerR:t[3]/2,start:Math.round(1e3*e)/1e3,end:Math.round(1e3*i)/1e3};a.shapeType="arc",a.shapeArgs=m,(o=(i+e)/2)>1.5*Math.PI?o-=2*Math.PI:o<-Math.PI/2&&(o+=2*Math.PI),a.slicedTranslation={translateX:Math.round(Math.cos(o)*l),translateY:Math.round(Math.sin(o)*l)},r=Math.cos(o)*t[2]/2,n=Math.sin(o)*t[2]/2,a.tooltipPos=[t[0]+.7*r,t[1]+.7*n],a.half=+(o<-Math.PI/2||o>Math.PI/2),a.angle=o}lw(this,"afterTranslate")},e.prototype.updateTotals=function(){var t,e,i=this.points,o=i.length,r=this.options.ignoreHiddenPoint,n=0;for(t=0;t<o;t++)(e=i[t]).isValid()&&(!r||e.visible)&&(n+=e.y);for(t=0,this.total=n;t<o;t++)(e=i[t]).percentage=n>0&&(e.visible||!r)?e.y/n*100:0,e.total=n},e.defaultOptions=lM(sw.defaultOptions,lv),e}(sw);lk(lT.prototype,{axisTypes:[],directTouch:!0,drawGraph:void 0,drawTracker:hD.prototype.drawTracker,getCenter:ls.getCenter,getSymbol:lx,invertible:!1,isCartesian:!1,noSharedTooltip:!0,pointAttribs:hD.prototype.pointAttribs,pointClass:lg,requireSorting:!1,searchPoint:lx,trackerGroups:["group","dataLabelsGroup"]}),n0.registerSeriesType("pie",lT);var lC=ti.composed,lA=ti.noop,lP=eJ.distribute,lO=n0.series,lL=tP.arrayMax,lE=tP.clamp,lI=tP.defined,lD=tP.pick,lB=tP.pushUnique,lN=tP.relativeLength;!function(t){var e={radialDistributionY:function(t,e){var i;return((null===(i=e.dataLabelPosition)||void 0===i?void 0:i.top)||0)+t.distributeBox.pos},radialDistributionX:function(t,e,i,o,r){var n=r.dataLabelPosition;return t.getX(i<((null==n?void 0:n.top)||0)+2||i>((null==n?void 0:n.bottom)||0)-2?o:i,e.half,e,r)},justify:function(t,e,i,o){var r;return o[0]+(t.half?-1:1)*(i+((null===(r=e.dataLabelPosition)||void 0===r?void 0:r.distance)||0))},alignToPlotEdges:function(t,e,i,o){var r=t.getBBox().width;return e?r+o:i-r-o},alignToConnectors:function(t,e,i,o){var r,n=0;return t.forEach(function(t){(r=t.dataLabel.getBBox().width)>n&&(n=r)}),e?n+o:i-n-o}};function i(t,e){var i=Math.PI/2,o=t.shapeArgs||{},r=o.start,n=void 0===r?0:r,s=o.end,a=void 0===s?0:s,h=t.angle||0;e>0&&n<i&&a>i&&h>i/2&&h<1.5*i&&(h=h<=i?Math.max(i/2,(n+i)/2):Math.min(1.5*i,(i+a)/2));var l=this.center,c=this.options,d=l[2]/2,p=Math.cos(h),u=Math.sin(h),f=l[0]+p*d,g=l[1]+u*d,v=Math.min((c.slicedOffset||0)+(c.borderWidth||0),e/5);return{natural:{x:f+p*e,y:g+u*e},computed:{},alignment:e<0?"center":t.half?"right":"left",connectorPosition:{angle:h,breakAt:{x:f+p*v,y:g+u*v},touchingSliceAt:{x:f,y:g}},distance:e}}function o(){var t,e,i,o,r=this,n=this,s=n.points,a=n.chart,h=a.plotWidth,l=a.plotHeight,c=a.plotLeft,d=Math.round(a.chartWidth/3),p=n.center,u=p[2]/2,f=p[1],g=[[],[]],v=[0,0,0,0],m=n.dataLabelPositioners,y=0;n.visible&&(null===(t=n.hasDataLabels)||void 0===t?void 0:t.call(n))&&(s.forEach(function(t){(t.dataLabels||[]).forEach(function(t){t.shortened&&(t.attr({width:"auto"}).css({width:"auto",textOverflow:"clip"}),t.shortened=!1)})}),lO.prototype.drawDataLabels.apply(n),s.forEach(function(t){(t.dataLabels||[]).forEach(function(e,i){var o,n=p[2]/2,s=e.options,a=lN((null==s?void 0:s.distance)||0,n);0===i&&g[t.half].push(t),!lI(null===(o=null==s?void 0:s.style)||void 0===o?void 0:o.width)&&e.getBBox().width>d&&(e.css({width:Math.round(.7*d)+"px"}),e.shortened=!0),e.dataLabelPosition=r.getDataLabelPosition(t,a),y=Math.max(y,a)})}),g.forEach(function(t,e){var r,s,d,g=t.length,x=[],b=0;g&&(n.sortByAngle(t,e-.5),y>0&&(r=Math.max(0,f-u-y),s=Math.min(f+u+y,a.plotHeight),t.forEach(function(t){(t.dataLabels||[]).forEach(function(e){var i,o=e.dataLabelPosition;o&&o.distance>0&&(o.top=Math.max(0,f-u-o.distance),o.bottom=Math.min(f+u+o.distance,a.plotHeight),b=e.getBBox().height||21,e.lineHeight=a.renderer.fontMetrics(e.text||e).h+2*e.padding,t.distributeBox={target:((null===(i=e.dataLabelPosition)||void 0===i?void 0:i.natural.y)||0)-o.top+e.lineHeight/2,size:b,rank:t.y},x.push(t.distributeBox))})}),lP(x,d=s+b-r,d/5)),t.forEach(function(r){(r.dataLabels||[]).forEach(function(s){var a=s.options||{},d=r.distributeBox,f=s.dataLabelPosition,g=(null==f?void 0:f.natural.y)||0,y=a.connectorPadding||0,b=s.lineHeight||21,k=(b-s.getBBox().height)/2,w=0,M=g,S="inherit";if(f){if(x&&lI(d)&&f.distance>0&&(void 0===d.pos?S="hidden":(o=d.size,M=m.radialDistributionY(r,s))),a.justify)w=m.justify(r,s,u,p);else switch(a.alignTo){case"connectors":w=m.alignToConnectors(t,e,h,c);break;case"plotEdges":w=m.alignToPlotEdges(s,e,h,c);break;default:w=m.radialDistributionX(n,r,M-k,g,s)}if(f.attribs={visibility:S,align:f.alignment},f.posAttribs={x:w+(a.x||0)+(({left:y,right:-y})[f.alignment]||0),y:M+(a.y||0)-b/2},f.computed.x=w,f.computed.y=M-k,lD(a.crop,!0)){i=s.getBBox().width;var T=void 0;w-i<y&&1===e?(T=Math.round(i-w+y),v[3]=Math.max(T,v[3])):w+i>h-y&&0===e&&(T=Math.round(w+i-h+y),v[1]=Math.max(T,v[1])),M-o/2<0?v[0]=Math.max(Math.round(-M+o/2),v[0]):M+o/2>l&&(v[2]=Math.max(Math.round(M+o/2-l),v[2])),f.sideOverflow=T}}})}))}),(0===lL(v)||this.verifyDataLabelOverflow(v))&&(this.placeDataLabels(),this.points.forEach(function(t){(t.dataLabels||[]).forEach(function(i){var o,r=i.options||{},s=r.connectorColor,h=r.connectorWidth,l=void 0===h?1:h,c=i.dataLabelPosition;if(l){var d=void 0;e=i.connector,c&&c.distance>0?(d=!e,e||(i.connector=e=a.renderer.path().addClass("highcharts-data-label-connector  highcharts-color-"+t.colorIndex+(t.className?" "+t.className:"")).add(n.dataLabelsGroup)),a.styledMode||e.attr({"stroke-width":l,stroke:s||t.color||"#666666"}),e[d?"attr":"animate"]({d:t.getConnectorPath(i)}),e.attr({visibility:null===(o=c.attribs)||void 0===o?void 0:o.visibility})):e&&(i.connector=e.destroy())}})})))}function r(){this.points.forEach(function(t){(t.dataLabels||[]).forEach(function(t){var e,i,o=t.dataLabelPosition;o?(o.sideOverflow&&(t.css({width:Math.max(t.getBBox().width-o.sideOverflow,0)+"px",textOverflow:(null===(i=null===(e=t.options)||void 0===e?void 0:e.style)||void 0===i?void 0:i.textOverflow)||"ellipsis"}),t.shortened=!0),t.attr(o.attribs),t[t.moved?"animate":"attr"](o.posAttribs),t.moved=!0):t&&t.attr({y:-9999})}),delete t.distributeBox},this)}function n(t){var e=this.center,i=this.options,o=i.center,r=i.minSize||80,n=r,s=null!==i.size;return!s&&(null!==o[0]?n=Math.max(e[2]-Math.max(t[1],t[3]),r):(n=Math.max(e[2]-t[1]-t[3],r),e[0]+=(t[3]-t[1])/2),null!==o[1]?n=lE(n,r,e[2]-Math.max(t[0],t[2])):(n=lE(n,r,e[2]-t[0]-t[2]),e[1]+=(t[0]-t[2])/2),n<e[2]?(e[2]=n,e[3]=Math.min(i.thickness?Math.max(0,n-2*i.thickness):Math.max(0,lN(i.innerSize||0,n)),n),this.translate(e),this.drawDataLabels&&this.drawDataLabels()):s=!0),s}t.compose=function(t){if(hU.compose(lO),lB(lC,"PieDataLabel")){var s=t.prototype;s.dataLabelPositioners=e,s.alignDataLabel=lA,s.drawDataLabels=o,s.getDataLabelPosition=i,s.placeDataLabels=r,s.verifyDataLabelOverflow=n}}}(q||(q={}));var lz=q;(O=K||(K={})).getCenterOfPoints=function(t){var e=t.reduce(function(t,e){return t.x+=e.x,t.y+=e.y,t},{x:0,y:0});return{x:e.x/t.length,y:e.y/t.length}},O.getDistanceBetweenPoints=function(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))},O.getAngleBetweenPoints=function(t,e){return Math.atan2(e.x-t.x,e.y-t.y)},O.pointInPolygon=function(t,e){var i,o,r=t.x,n=t.y,s=e.length,a=!1;for(i=0,o=s-1;i<s;o=i++){var h=e[i],l=h[0],c=h[1],d=e[o],p=d[0],u=d[1];c>n!=u>n&&r<(p-l)*(n-c)/(u-c)+l&&(a=!a)}return a};var lR=K.pointInPolygon,lW=tP.addEvent,lX=tP.getAlignFactor,lH=tP.fireEvent,lj=tP.objectEach,lF=tP.pick;function lY(t){for(var e,i,o,r,n,s=t.length,a=!1,h=0;h<s;h++)(e=t[h])&&(e.oldOpacity=e.opacity,e.newOpacity=1,e.absoluteBox=function(t){var e,i;if(t&&(!t.alignAttr||t.placed)){var o=t.box?0:t.padding||0,r=t.alignAttr||{x:t.attr("x"),y:t.attr("y")},n=t.getBBox(),s=n.height,a=n.polygon,h=n.width,l=lX(t.alignValue)*h;return t.width=h,t.height=s,{x:r.x+((null===(e=t.parentGroup)||void 0===e?void 0:e.translateX)||0)+o-l,y:r.y+((null===(i=t.parentGroup)||void 0===i?void 0:i.translateY)||0)+o,width:h-2*o,height:s-2*o,polygon:a}}}(e));t.sort(function(t,e){return(e.labelrank||0)-(t.labelrank||0)});for(var h=0;h<s;++h)for(var l=null==(r=(i=t[h])&&i.absoluteBox)?void 0:r.polygon,c=h+1;c<s;++c){n=(o=t[c])&&o.absoluteBox;var d=!1;if(r&&n&&i!==o&&0!==i.newOpacity&&0!==o.newOpacity&&"hidden"!==i.visibility&&"hidden"!==o.visibility){var p=n.polygon;if(l&&p&&l!==p?function(t,e){for(var i=0;i<t.length;i++){var o=t[i];if(lR({x:o[0],y:o[1]},e))return!0}return!1}(l,p)&&(d=!0):!(n.x>=r.x+r.width||n.x+n.width<=r.x||n.y>=r.y+r.height||n.y+n.height<=r.y)&&(d=!0),d){var u=i.labelrank<o.labelrank?i:o,f=u.text;u.newOpacity=0,(null==f?void 0:f.element.querySelector("textPath"))&&f.hide()}}}for(var g=0;g<t.length;g++)lG(t[g],this)&&(a=!0);a&&lH(this,"afterHideAllOverlappingLabels")}function lG(t,e){var i,o=!1;return t&&(i=t.newOpacity,t.oldOpacity!==i&&(t.hasClass("highcharts-data-label")?(t[i?"removeClass":"addClass"]("highcharts-data-label-hidden"),o=!0,t[t.isOld?"animate":"attr"]({opacity:i},void 0,function(){e.styledMode||t.css({pointerEvents:i?"auto":"none"})}),lH(e,"afterHideOverlappingLabel")):t.attr({opacity:i})),t.isOld=!0),o}function l_(){for(var t,e=this,i=[],o=0,r=e.labelCollectors||[];o<r.length;o++){var n=r[o];i=i.concat(n())}for(var s=0,a=e.yAxis||[];s<a.length;s++){var h=a[s];h.stacking&&h.options.stackLabels&&!h.options.stackLabels.allowOverlap&&lj(h.stacking.stacks,function(t){lj(t,function(t){t.label&&i.push(t.label)})})}for(var l=0,c=e.series||[];l<c.length;l++){var d=c[l];if(d.visible&&(null===(t=d.hasDataLabels)||void 0===t?void 0:t.call(d))){var p=function(t){for(var o=function(t){t.visible&&(t.dataLabels||[]).forEach(function(o){var r,n,s=o.options||{};o.labelrank=lF(s.labelrank,t.labelrank,null===(r=t.shapeArgs)||void 0===r?void 0:r.height),(null!==(n=s.allowOverlap)&&void 0!==n?n:Number(s.distance)>0)?(o.oldOpacity=o.opacity,o.newOpacity=1,lG(o,e)):i.push(o)})},r=0;r<t.length;r++)o(t[r])};p(d.nodes||[]),p(d.points)}}this.hideOverlappingLabels(i)}var lU={compose:function(t){var e=t.prototype;e.hideOverlappingLabels||(e.hideOverlappingLabels=lY,lW(t,"render",l_))}},lV=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},lZ=ti.noop,lq=tP.addEvent,lK=tP.extend,l$=tP.isObject,lJ=tP.merge,lQ=tP.relativeLength,l0={radius:0,scope:"stack",where:void 0},l1=lZ,l2=lZ;function l3(t,e,i,o,r){void 0===r&&(r={});var n=l1(t,e,i,o,r),s=r.innerR,a=void 0===s?0:s,h=r.r,l=void 0===h?i:h,c=r.start,d=r.end;if(r.open||!r.borderRadius)return n;for(var p=(void 0===d?0:d)-(void 0===c?0:c),u=Math.sin(p/2),f=Math.max(Math.min(lQ(r.borderRadius||0,l-a),(l-a)/2,l*u/(1+u)),0),g=Math.min(f,p/Math.PI*2*a),v=n.length-1;v--;)!function(t,e,i){var o,r,n,s=t[e],a=t[e+1];if("Z"===a[0]&&(a=t[0]),("M"===s[0]||"L"===s[0])&&"A"===a[0]?(o=s,r=a,n=!0):"A"===s[0]&&("M"===a[0]||"L"===a[0])&&(o=a,r=s),o&&r&&r.params){var h=r[1],l=r[5],c=r.params,d=c.start,p=c.end,u=c.cx,f=c.cy,g=l?h-i:h+i,v=g?Math.asin(i/g):0,m=l?v:-v,y=Math.cos(v)*g;n?(c.start=d+m,o[1]=u+y*Math.cos(d),o[2]=f+y*Math.sin(d),t.splice(e+1,0,["A",i,i,0,0,1,u+h*Math.cos(c.start),f+h*Math.sin(c.start)])):(c.end=p-m,r[6]=u+h*Math.cos(c.end),r[7]=f+h*Math.sin(c.end),t.splice(e+1,0,["A",i,i,0,0,1,u+y*Math.cos(p),f+y*Math.sin(p)])),r[4]=Math.abs(c.end-c.start)<Math.PI?0:1}}(n,v,v>1?g:f);return n}function l5(){var t,e;if(this.options.borderRadius&&!(this.chart.is3d&&this.chart.is3d()))for(var i=this.options,o=this.yAxis,r="percent"===i.stacking,n=null===(e=null===(t=t0.plotOptions)||void 0===t?void 0:t[this.type])||void 0===e?void 0:e.borderRadius,s=l6(i.borderRadius,l$(n)?n:{}),a=o.options.reversed,h=0,l=this.points;h<l.length;h++){var c=l[h],d=c.shapeArgs;if("roundedRect"===c.shapeType&&d){var p=d.width,u=void 0===p?0:p,f=d.height,g=void 0===f?0:f,v=d.y,m=void 0===v?0:v,y=g;if("stack"===s.scope&&c.stackTotal){var x=o.translate(r?100:c.stackTotal,!1,!0,!1,!0),b=o.translate(i.threshold||0,!1,!0,!1,!0),k=this.crispCol(0,Math.min(x,b),0,Math.abs(x-b));m=k.y,y=k.height}var w=(c.negative?-1:1)*(a?-1:1)==-1,M=s.where;!M&&this.is("waterfall")&&Math.abs((c.yBottom||0)-(this.translatedThreshold||0))>this.borderWidth&&(M="all"),M||(M="end");var S=Math.min(lQ(s.radius,u),u/2,"all"===M?g/2:1/0)||0;"end"===M&&(w&&(m-=S),y+=S),lK(d,{brBoxHeight:y,brBoxY:m,r:S})}}}function l6(t,e){return l$(t)||(t={radius:t||0}),lJ(l0,e,t)}function l9(){for(var t=l6(this.options.borderRadius),e=0,i=this.points;e<i.length;e++){var o=i[e].shapeArgs;o&&(o.borderRadius=lQ(t.radius,(o.r||0)-(o.innerR||0)))}}function l4(t,e,i,o,r){void 0===r&&(r={});var n=l2(t,e,i,o,r),s=r.r,a=void 0===s?0:s,h=r.brBoxHeight,l=void 0===h?o:h,c=r.brBoxY,d=void 0===c?e:c,p=e-d,u=d+l-(e+o),f=p-a>-.1?0:a,g=u-a>-.1?0:a,v=Math.max(f&&p,0),m=Math.max(g&&u,0),y=[t+f,e],x=[t+i-f,e],b=[t+i,e+f],k=[t+i,e+o-g],w=[t+i-g,e+o],M=[t+g,e+o],S=[t,e+o-g],T=[t,e+f],C=function(t,e){return Math.sqrt(Math.pow(t,2)-Math.pow(e,2))};if(v){var A=C(f,f-v);y[0]-=A,x[0]+=A,b[1]=T[1]=e+f-v}if(o<f-v){var A=C(f,f-v-o);b[0]=k[0]=t+i-f+A,w[0]=Math.min(b[0],w[0]),M[0]=Math.max(k[0],M[0]),S[0]=T[0]=t+f-A,b[1]=T[1]=e+o}if(m){var A=C(g,g-m);w[0]+=A,M[0]-=A,k[1]=S[1]=e+o-g+m}if(o<g-m){var A=C(g,g-m-o);b[0]=k[0]=t+i-g+A,x[0]=Math.min(b[0],x[0]),y[0]=Math.max(k[0],y[0]),S[0]=T[0]=t+g-A,k[1]=S[1]=e}return n.length=0,n.push(lV(["M"],y,!0),lV(["L"],x,!0),lV(["A",f,f,0,0,1],b,!0),lV(["L"],k,!0),lV(["A",g,g,0,0,1],w,!0),lV(["L"],M,!0),lV(["A",g,g,0,0,1],S,!0),lV(["L"],T,!0),lV(["A",f,f,0,0,1],y,!0),["Z"]),n}var l8=tP.diffObjects,l7=tP.extend,ct=tP.find,ce=tP.merge,ci=tP.pick,co=tP.uniqueKey;!function(t){function e(t,e){var i=t.condition;(i.callback||function(){return this.chartWidth<=ci(i.maxWidth,Number.MAX_VALUE)&&this.chartHeight<=ci(i.maxHeight,Number.MAX_VALUE)&&this.chartWidth>=ci(i.minWidth,0)&&this.chartHeight>=ci(i.minHeight,0)}).call(this)&&e.push(t._id)}function i(t,e){var i,o=this,r=this.options.responsive,n=this.currentResponsive,s=[];!e&&r&&r.rules&&r.rules.forEach(function(t){void 0===t._id&&(t._id=co()),o.matchResponsiveRule(t,s)},this);var a=ce.apply(void 0,s.map(function(t){return ct((null==r?void 0:r.rules)||[],function(e){return e._id===t})}).map(function(t){return null==t?void 0:t.chartOptions}));a.isResponsiveOptions=!0,s=s.toString()||void 0;var h=null==n?void 0:n.ruleIds;s===h||(n&&(this.currentResponsive=void 0,this.updatingResponsive=!0,this.update(n.undoOptions,t,!0),this.updatingResponsive=!1),s?((i=l8(a,this.options,!0,this.collectionsWithUpdate)).isResponsiveOptions=!0,this.currentResponsive={ruleIds:s,mergedOptions:a,undoOptions:i},this.updatingResponsive||this.update(a,t,!0)):this.currentResponsive=void 0)}t.compose=function(t){var o=t.prototype;return o.matchResponsiveRule||l7(o,{matchResponsiveRule:e,setResponsive:i}),t}}($||($={}));var cr=$;ti.AST=eI,ti.Axis=ru,ti.Chart=am,ti.Color=et,ti.DataLabel=hU,ti.DataTableCore=nY,ti.Fx=en,ti.HTMLElement=oP,ti.Legend=sY,ti.LegendSymbol=nZ,ti.OverlappingDataLabels=ti.OverlappingDataLabels||lU,ti.PlotLineOrBand=rW,ti.Point=ng,ti.Pointer=nN,ti.RendererRegistry=eV,ti.Series=sw,ti.SeriesRegistry=n0,ti.StackItem=az,ti.SVGElement=ib,ti.SVGRenderer=op,ti.Templating=eU,ti.Tick=oZ,ti.Time=tq,ti.Tooltip=r8,ti.animate=ev,ti.animObject=ef,ti.chart=am.chart,ti.color=et.parse,ti.dateFormat=eU.dateFormat,ti.defaultOptions=t0,ti.distribute=eJ.distribute,ti.format=eU.format,ti.getDeferredAnimation=em,ti.getOptions=function(){return t0},ti.numberFormat=eU.numberFormat,ti.seriesType=n0.seriesType,ti.setAnimation=ey,ti.setOptions=function(t){var e;return tJ(ti,"setOptions",{options:t}),tQ(!0,t0,t),t.time&&t1.update(t0.time),t.lang&&"locale"in t.lang&&t1.update({locale:t.lang.locale}),(null===(e=t.lang)||void 0===e?void 0:e.chartTitle)&&(t0.title=tK(tK({},t0.title),{text:t.lang.chartTitle})),t0},ti.stop=eg,ti.time=t1,ti.timers=en.timers,({compose:function(t,e,i){var o=t.types.pie;if(!e.symbolCustomAttribs.includes("borderRadius")){var r=i.prototype.symbols;lq(t,"afterColumnTranslate",l5,{order:9}),lq(o,"afterTranslate",l9),e.symbolCustomAttribs.push("borderRadius","brBoxHeight","brBoxY"),l1=r.arc,l2=r.roundedRect,r.arc=l3,r.roundedRect=l4}},optionsToObject:l6}).compose(ti.Series,ti.SVGElement,ti.SVGRenderer),h$.compose(ti.Series.types.column),hU.compose(ti.Series),ry.compose(ti.Axis),oP.compose(ti.SVGRenderer),sY.compose(ti.Chart),rw.compose(ti.Axis),lU.compose(ti.Chart),lz.compose(ti.Series.types.pie),rW.compose(ti.Chart,ti.Axis),nN.compose(ti.Chart),cr.compose(ti.Chart),aP.compose(ti.Axis,ti.Chart,ti.Series),a1.compose(ti.Axis,ti.Chart,ti.Series),r8.compose(ti.Pointer),tP.extend(ti,tP);var cn=(L=function(t,e){return(L=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}L(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),cs=ti.charts,ca=ti.composed,ch=ti.doc,cl=ti.noop,cc=ti.win,cd=tP.addEvent,cp=tP.attr,cu=tP.css,cf=tP.defined,cg=tP.objectEach,cv=tP.pick,cm=tP.pushUnique,cy=tP.removeEvent,cx={},cb=!!cc.PointerEvent;function ck(t,e,i,o){var r,n,s,a=null===(s=cs[null!==(n=nN.hoverChartIndex)&&void 0!==n?n:-1])||void 0===s?void 0:s.pointer;a&&("touch"===t.pointerType||t.pointerType===t.MSPOINTER_TYPE_TOUCH)&&(o(t),a[e]({type:i,target:t.currentTarget,preventDefault:cl,touches:((r=[]).item=function(t){return this[t]},cg(cx,function(t){r.push({pageX:t.pageX,pageY:t.pageY,target:t.target})}),r)}))}var cw=function(t){function e(e,i){var o=t.call(this,e,i)||this;return o.hasZoom&&cu(e.container,{"-ms-touch-action":"none","touch-action":"none"}),o}return cn(e,t),e.isRequired=function(){return!!(!cc.TouchEvent&&(cc.PointerEvent||cc.MSPointerEvent))},e.prototype.batchMSEvents=function(t){t(this.chart.container,cb?"pointerdown":"MSPointerDown",this.onContainerPointerDown),t(this.chart.container,cb?"pointermove":"MSPointerMove",this.onContainerPointerMove),t(ch,cb?"pointerup":"MSPointerUp",this.onDocumentPointerUp)},e.prototype.destroy=function(){this.batchMSEvents(cy),t.prototype.destroy.call(this)},e.prototype.inClass=function(t,e){for(var i,o=t;o;){if(i=cp(o,"class")){if(-1!==i.indexOf(e))return!0;if(-1!==i.indexOf("highcharts-container"))return!1}(o=o.parentNode)&&(o===document.documentElement||cf(o.nodeType)&&o.nodeType===document.nodeType)&&(o=null)}},e.prototype.onContainerPointerDown=function(t){ck(t,"onContainerTouchStart","touchstart",function(t){cx[t.pointerId]={pageX:t.pageX,pageY:t.pageY,target:t.currentTarget}})},e.prototype.onContainerPointerMove=function(t){ck(t,"onContainerTouchMove","touchmove",function(t){cx[t.pointerId]={pageX:t.pageX,pageY:t.pageY},cx[t.pointerId].target||(cx[t.pointerId].target=t.currentTarget)})},e.prototype.onDocumentPointerUp=function(t){ck(t,"onDocumentTouchEnd","touchend",function(t){delete cx[t.pointerId]})},e.prototype.setDOMEvents=function(){var e=this.chart.tooltip;t.prototype.setDOMEvents.call(this),(this.hasZoom||cv(null==e?void 0:e.options.followTouchMove,!0))&&this.batchMSEvents(cd)},e}(nN);(E=cw||(cw={})).compose=function(t){cm(ca,"Core.MSPointer")&&cd(t,"beforeRender",function(){this.pointer=new E(this,this.options)})};var cM=cw;cM.isRequired()&&(ti.Pointer=cM,cM.compose(ti.Chart)),ti.SVGRenderer.prototype.getShadowFilterContent=function(t){return[{tagName:"feFlood",attributes:{"flood-color":t.color,"flood-opacity":t.opacity,result:"flood"}},{tagName:"feComposite",attributes:{in:"flood",in2:"SourceAlpha",operator:"in",result:"shadowColor"}},{tagName:"feOffset",attributes:{dx:t.offsetX,dy:t.offsetY,result:"offsetShadow"}},{tagName:"feGaussianBlur",attributes:{in:"offsetShadow",stdDeviation:t.width/2,result:"blurredShadow"}},{tagName:"feMerge",children:[{tagName:"feMergeNode",attributes:{in:"blurredShadow"}},{tagName:"feMergeNode",attributes:{in:"SourceGraphic"}}]}]};var cS=ti;return te.default}()});