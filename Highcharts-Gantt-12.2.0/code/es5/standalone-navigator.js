!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("highcharts/highcharts",[],e):"object"==typeof exports?exports.highcharts=e():(t.Highcharts&&t.Highcharts.error(16,!0),t.Highcharts=e())}(this,function(){return function(){"use strict";var t,e,i,o,r,s,n,a,h,l,d,c,p,u,f,g,v,m,y,x,b,k,M,w,S,A,T,C,O,P,E,L,D,I,B,z,R,N,W,X,H,F,j,Y,G,_,U,V,Z,q,K,$,J={};J.d=function(t,e){for(var i in e)J.o(e,i)&&!J.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},J.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var Q={};J.d(Q,{default:function(){return ck}}),(t=E||(E={})).SVG_NS="http://www.w3.org/2000/svg",t.product="Highcharts",t.version="12.2.0",t.win="undefined"!=typeof window?window:{},t.doc=t.win.document,t.svg=!!(null===(i=null===(e=null===t.doc||void 0===t.doc?void 0:t.doc.createElementNS)||void 0===e?void 0:e.call(t.doc,t.SVG_NS,"svg"))||void 0===i?void 0:i.createSVGRect),t.pageLang=null===(r=null===(o=null===t.doc||void 0===t.doc?void 0:t.doc.documentElement)||void 0===o?void 0:o.closest("[lang]"))||void 0===r?void 0:r.lang,t.userAgent=(null===(s=t.win.navigator)||void 0===s?void 0:s.userAgent)||"",t.isChrome=t.win.chrome,t.isFirefox=-1!==t.userAgent.indexOf("Firefox"),t.isMS=/(edge|msie|trident)/i.test(t.userAgent)&&!t.win.opera,t.isSafari=!t.isChrome&&-1!==t.userAgent.indexOf("Safari"),t.isTouchDevice=/(Mobile|Android|Windows Phone)/.test(t.userAgent),t.isWebKit=-1!==t.userAgent.indexOf("AppleWebKit"),t.deg2rad=2*Math.PI/360,t.marginNames=["plotTop","marginRight","marginBottom","plotLeft"],t.noop=function(){},t.supportsPassiveEvents=function(){var e=!1;if(!t.isMS){var i=Object.defineProperty({},"passive",{get:function(){e=!0}});t.win.addEventListener&&t.win.removeEventListener&&(t.win.addEventListener("testPassive",t.noop,i),t.win.removeEventListener("testPassive",t.noop,i))}return e}(),t.charts=[],t.composed=[],t.dateFormats={},t.seriesTypes={},t.symbolSizes={},t.chartCount=0;var tt=E,te=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,s=e.length;r<s;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},ti=tt.charts,to=tt.doc,tr=tt.win;function ts(t,e,i,o){var r=e?"Highcharts error":"Highcharts warning";32===t&&(t=""+r+": Deprecated member");var s=tp(t),n=s?""+r+" #"+t+": www.highcharts.com/errors/"+t+"/":t.toString();if(void 0!==o){var a="";s&&(n+="?"),tM(o,function(t,e){a+="\n - ".concat(e,": ").concat(t),s&&(n+=encodeURI(e)+"="+encodeURI(t))}),n+=a}tS(tt,"displayError",{chart:i,code:t,message:n,params:o},function(){if(e)throw Error(n);tr.console&&-1===ts.messages.indexOf(n)&&console.warn(n)}),ts.messages.push(n)}function tn(t,e){return parseInt(t,e||10)}function ta(t){return"string"==typeof t}function th(t){var e=Object.prototype.toString.call(t);return"[object Array]"===e||"[object Array Iterator]"===e}function tl(t,e){return!!t&&"object"==typeof t&&(!e||!th(t))}function td(t){return tl(t)&&"number"==typeof t.nodeType}function tc(t){var e=null==t?void 0:t.constructor;return!!(tl(t,!0)&&!td(t)&&(null==e?void 0:e.name)&&"Object"!==e.name)}function tp(t){return"number"==typeof t&&!isNaN(t)&&t<1/0&&t>-1/0}function tu(t){return null!=t}function tf(t,e,i){var o,r=ta(e)&&!tu(i),s=function(e,i){tu(e)?t.setAttribute(i,e):r?(o=t.getAttribute(i))||"class"!==i||(o=t.getAttribute(i+"Name")):t.removeAttribute(i)};return ta(e)?s(i,e):tM(e,s),o}function tg(t){return th(t)?t:[t]}function tv(t,e){var i;for(i in t||(t={}),e)t[i]=e[i];return t}function tm(){for(var t=arguments,e=t.length,i=0;i<e;i++){var o=t[i];if(null!=o)return o}}function ty(t,e){tv(t.style,e)}function tx(t){return Math.pow(10,Math.floor(Math.log(t)/Math.LN10))}function tb(t,e){return t>1e14?t:parseFloat(t.toPrecision(e||14))}(ts||(ts={})).messages=[],Math.easeInOutSine=function(t){return -.5*(Math.cos(Math.PI*t)-1)};var tk=Array.prototype.find?function(t,e){return t.find(e)}:function(t,e){var i,o=t.length;for(i=0;i<o;i++)if(e(t[i],i))return t[i]};function tM(t,e,i){for(var o in t)Object.hasOwnProperty.call(t,o)&&e.call(i||t[o],t[o],o,t)}function tw(t,e,i){function o(e,i){var o=t.removeEventListener;o&&o.call(t,e,i,!1)}function r(i){var r,s;t.nodeName&&(e?(r={})[e]=!0:r=i,tM(r,function(t,e){if(i[e])for(s=i[e].length;s--;)o(e,i[e][s].fn)}))}var s="function"==typeof t&&t.prototype||t;if(Object.hasOwnProperty.call(s,"hcEvents")){var n=s.hcEvents;if(e){var a=n[e]||[];i?(n[e]=a.filter(function(t){return i!==t.fn}),o(e,i)):(r(n),n[e]=[])}else r(n),delete s.hcEvents}}function tS(t,e,i,o){if(i=i||{},(null==to?void 0:to.createEvent)&&(t.dispatchEvent||t.fireEvent&&t!==tt)){var r=to.createEvent("Events");r.initEvent(e,!0,!0),i=tv(r,i),t.dispatchEvent?t.dispatchEvent(i):t.fireEvent(e,i)}else if(t.hcEvents){i.target||tv(i,{preventDefault:function(){i.defaultPrevented=!0},target:t,type:e});for(var s=[],n=t,a=!1;n.hcEvents;)Object.hasOwnProperty.call(n,"hcEvents")&&n.hcEvents[e]&&(s.length&&(a=!0),s.unshift.apply(s,n.hcEvents[e])),n=Object.getPrototypeOf(n);a&&s.sort(function(t,e){return t.order-e.order}),s.forEach(function(e){!1===e.fn.call(t,i)&&i.preventDefault()})}o&&!i.defaultPrevented&&o.call(t,i)}var tA=(n=Math.random().toString(36).substring(2,9)+"-",a=0,function(){return"highcharts-"+(L?"":n)+a++});tr.jQuery&&(tr.jQuery.fn.highcharts=function(){var t=[].slice.call(arguments);if(this[0])return t[0]?(new tt[ta(t[0])?t.shift():"Chart"](this[0],t[0],t[1]),this):ti[tf(this[0],"data-highcharts-chart")]});var tT={addEvent:function(t,e,i,o){void 0===o&&(o={});var r="function"==typeof t&&t.prototype||t;Object.hasOwnProperty.call(r,"hcEvents")||(r.hcEvents={});var s=r.hcEvents;tt.Point&&t instanceof tt.Point&&t.series&&t.series.chart&&(t.series.chart.runTrackerClick=!0);var n=t.addEventListener;n&&n.call(t,e,i,!!tt.supportsPassiveEvents&&{passive:void 0===o.passive?-1!==e.indexOf("touch"):o.passive,capture:!1}),s[e]||(s[e]=[]);var a={fn:i,order:"number"==typeof o.order?o.order:1/0};return s[e].push(a),s[e].sort(function(t,e){return t.order-e.order}),function(){tw(t,e,i)}},arrayMax:function(t){for(var e=t.length,i=t[0];e--;)t[e]>i&&(i=t[e]);return i},arrayMin:function(t){for(var e=t.length,i=t[0];e--;)t[e]<i&&(i=t[e]);return i},attr:tf,clamp:function(t,e,i){return t>e?t<i?t:i:e},clearTimeout:function(t){tu(t)&&clearTimeout(t)},correctFloat:tb,createElement:function(t,e,i,o,r){var s=to.createElement(t);return e&&tv(s,e),r&&ty(s,{padding:"0",border:"none",margin:"0"}),i&&ty(s,i),o&&o.appendChild(s),s},crisp:function(t,e,i){void 0===e&&(e=0);var o=e%2/2,r=i?-1:1;return(Math.round(t*r-o)+o)*r},css:ty,defined:tu,destroyObjectProperties:function(t,e,i){tM(t,function(o,r){o!==e&&(null==o?void 0:o.destroy)&&o.destroy(),((null==o?void 0:o.destroy)||!i)&&delete t[r]})},diffObjects:function(t,e,i,o){var r={};return!function t(e,r,s,n){var a=i?r:e;tM(e,function(i,h){if(!n&&o&&o.indexOf(h)>-1&&r[h]){i=tg(i),s[h]=[];for(var l=0;l<Math.max(i.length,r[h].length);l++)r[h][l]&&(void 0===i[l]?s[h][l]=r[h][l]:(s[h][l]={},t(i[l],r[h][l],s[h][l],n+1)))}else tl(i,!0)&&!i.nodeType?(s[h]=th(i)?[]:{},t(i,r[h]||{},s[h],n+1),0===Object.keys(s[h]).length&&("colorAxis"!==h||0!==n)&&delete s[h]):(e[h]!==r[h]||h in e&&!(h in r))&&"__proto__"!==h&&"constructor"!==h&&(s[h]=a[h])})}(t,e,r,0),r},discardElement:function(t){var e;null===(e=null==t?void 0:t.parentElement)||void 0===e||e.removeChild(t)},erase:function(t,e){for(var i=t.length;i--;)if(t[i]===e){t.splice(i,1);break}},error:ts,extend:tv,extendClass:function(t,e){var i=function(){};return i.prototype=new t,tv(i.prototype,e),i},find:tk,fireEvent:tS,getAlignFactor:function(t){return void 0===t&&(t=""),({center:.5,right:1,middle:.5,bottom:1})[t]||0},getClosestDistance:function(t,e){var i,o,r,s,n=!e;return t.forEach(function(t){if(t.length>1)for(s=o=t.length-1;s>0;s--)(r=t[s]-t[s-1])<0&&!n?(null==e||e(),e=void 0):r&&(void 0===i||r<i)&&(i=r)}),i},getMagnitude:tx,getNestedProperty:function(t,e){for(var i=t.split(".");i.length&&tu(e);){var o=i.shift();if(void 0===o||"__proto__"===o)return;if("this"===o){var r=void 0;return tl(e)&&(r=e["@this"]),null!=r?r:e}var s=e[o.replace(/[\\'"]/g,"")];if(!tu(s)||"function"==typeof s||"number"==typeof s.nodeType||s===tr)return;e=s}return e},getStyle:function t(e,i,o){if("width"===i){var r,s,n=Math.min(e.offsetWidth,e.scrollWidth),a=null===(r=e.getBoundingClientRect)||void 0===r?void 0:r.call(e).width;return a<n&&a>=n-1&&(n=Math.floor(a)),Math.max(0,n-(t(e,"padding-left",!0)||0)-(t(e,"padding-right",!0)||0))}if("height"===i)return Math.max(0,Math.min(e.offsetHeight,e.scrollHeight)-(t(e,"padding-top",!0)||0)-(t(e,"padding-bottom",!0)||0));var h=tr.getComputedStyle(e,void 0);return h&&(s=h.getPropertyValue(i),tm(o,"opacity"!==i)&&(s=tn(s))),s},insertItem:function(t,e){var i,o=t.options.index,r=e.length;for(i=t.options.isInternal?r:0;i<r+1;i++)if(!e[i]||tp(o)&&o<tm(e[i].options.index,e[i]._i)||e[i].options.isInternal){e.splice(i,0,t);break}return i},isArray:th,isClass:tc,isDOMElement:td,isFunction:function(t){return"function"==typeof t},isNumber:tp,isObject:tl,isString:ta,merge:function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];var o,r=te([t],e,!0),s={},n=function(t,e){return"object"!=typeof t&&(t={}),tM(e,function(i,o){"__proto__"!==o&&"constructor"!==o&&(!tl(i,!0)||tc(i)||td(i)?t[o]=e[o]:t[o]=n(t[o]||{},i))}),t};!0===t&&(s=r[1],r=Array.prototype.slice.call(r,2));var a=r.length;for(o=0;o<a;o++)s=n(s,r[o]);return s},normalizeTickInterval:function(t,e,i,o,r){var s,n=t;i=tm(i,tx(t));var a=t/i;for(!e&&(e=r?[1,1.2,1.5,2,2.5,3,4,5,6,8,10]:[1,2,2.5,5,10],!1===o&&(1===i?e=e.filter(function(t){return t%1==0}):i<=.1&&(e=[1/i]))),s=0;s<e.length&&(n=e[s],(!r||!(n*i>=t))&&(r||!(a<=(e[s]+(e[s+1]||e[s]))/2)));s++);return tb(n*i,-Math.round(Math.log(.001)/Math.LN10))},objectEach:tM,offset:function(t){var e=to.documentElement,i=t.parentElement||t.parentNode?t.getBoundingClientRect():{top:0,left:0,width:0,height:0};return{top:i.top+(tr.pageYOffset||e.scrollTop)-(e.clientTop||0),left:i.left+(tr.pageXOffset||e.scrollLeft)-(e.clientLeft||0),width:i.width,height:i.height}},pad:function(t,e,i){return Array((e||2)+1-String(t).replace("-","").length).join(i||"0")+t},pick:tm,pInt:tn,pushUnique:function(t,e){return 0>t.indexOf(e)&&!!t.push(e)},relativeLength:function(t,e,i){return/%$/.test(t)?e*parseFloat(t)/100+(i||0):parseFloat(t)},removeEvent:tw,replaceNested:function(t){for(var e,i,o=[],r=1;r<arguments.length;r++)o[r-1]=arguments[r];do{e=t;for(var s=0;s<o.length;s++)i=o[s],t=t.replace(i[0],i[1])}while(t!==e);return t},splat:tg,stableSort:function(t,e){var i,o,r=t.length;for(o=0;o<r;o++)t[o].safeI=o;for(t.sort(function(t,o){return 0===(i=e(t,o))?t.safeI-o.safeI:i}),o=0;o<r;o++)delete t[o].safeI},syncTimeout:function(t,e,i){return e>0?setTimeout(t,e,i):(t.call(0,i),-1)},timeUnits:{millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5,week:6048e5,month:24192e5,year:314496e5},ucfirst:function(t){return ta(t)?t.substring(0,1).toUpperCase()+t.substring(1):String(t)},uniqueKey:tA,useSerialIds:function(t){return L=tm(t,L)},wrap:function(t,e,i){var o=t[e];t[e]=function(){var t=arguments,e=this;return i.apply(this,[function(){return o.apply(e,arguments.length?arguments:t)}].concat([].slice.call(arguments)))}}},tC=tt.pageLang,tO=tt.win,tP=tT.defined,tE=tT.error,tL=tT.extend,tD=tT.isNumber,tI=tT.isObject,tB=tT.isString,tz=tT.merge,tR=tT.objectEach,tN=tT.pad,tW=tT.splat,tX=tT.timeUnits,tH=tT.ucfirst,tF=tt.isSafari&&tO.Intl&&!tO.Intl.DateTimeFormat.prototype.formatRange,tj=function(){function t(t,e){this.options={timezone:"UTC"},this.variableTimezone=!1,this.Date=tO.Date,this.update(t),this.lang=e}return t.prototype.update=function(t){var e=this;void 0===t&&(t={}),this.dTLCache={},this.options=t=tz(!0,this.options,t);var i=t.timezoneOffset,o=t.useUTC;this.Date=t.Date||tO.Date||Date;var r=t.timezone;tP(o)&&(r=o?"UTC":void 0),i&&i%60==0&&(r="Etc/GMT"+(i>0?"+":"")+i/60),this.variableTimezone="UTC"!==r&&(null==r?void 0:r.indexOf("Etc/GMT"))!==0,this.timezone=r,["months","shortMonths","weekdays","shortWeekdays"].forEach(function(t){var i=/months/i.test(t),o=/short/.test(t),r={timeZone:"UTC"};r[i?"month":"weekday"]=o?"short":"long",e[t]=(i?[0,1,2,3,4,5,6,7,8,9,10,11]:[3,4,5,6,7,8,9]).map(function(t){return e.dateFormat(r,(i?31:1)*24*36e5*t)})})},t.prototype.toParts=function(t){var e=this.dateTimeFormat({weekday:"narrow",day:"numeric",month:"numeric",year:"numeric",hour:"numeric",minute:"numeric",second:"numeric"},t,"es").split(/(?:, | |\/|:)/g),i=e[0],o=e[1],r=e[2];return[e[3],+r-1,o,e[4],e[5],e[6],Math.floor(Number(t)||0)%1e3,"DLMXJVS".indexOf(i)].map(Number)},t.prototype.dateTimeFormat=function(t,e,i){void 0===i&&(i=this.options.locale||tC);var o,r=JSON.stringify(t)+i;tB(t)&&(t=this.str2dtf(t));var s=this.dTLCache[r];if(!s){null!==(o=t.timeZone)&&void 0!==o||(t.timeZone=this.timezone);try{s=new Intl.DateTimeFormat(i,t)}catch(e){/Invalid time zone/i.test(e.message)?(tE(34),t.timeZone="UTC",s=new Intl.DateTimeFormat(i,t)):tE(e.message,!1)}}return this.dTLCache[r]=s,(null==s?void 0:s.format(e))||""},t.prototype.str2dtf=function(t,e){void 0===e&&(e={});var i={L:{fractionalSecondDigits:3},S:{second:"2-digit"},M:{minute:"numeric"},H:{hour:"2-digit"},k:{hour:"numeric"},E:{weekday:"narrow"},a:{weekday:"short"},A:{weekday:"long"},d:{day:"2-digit"},e:{day:"numeric"},b:{month:"short"},B:{month:"long"},m:{month:"2-digit"},o:{month:"numeric"},y:{year:"2-digit"},Y:{year:"numeric"}};return Object.keys(i).forEach(function(o){-1!==t.indexOf(o)&&tL(e,i[o])}),e},t.prototype.makeTime=function(t,e,i,o,r,s,n){void 0===i&&(i=1),void 0===o&&(o=0);var a=this.Date.UTC(t,e,i,o,r||0,s||0,n||0);if("UTC"!==this.timezone){var h=this.getTimezoneOffset(a);if(a+=h,-1!==[2,3,8,9,10,11].indexOf(e)&&(o<5||o>20)){var l=this.getTimezoneOffset(a);h!==l?a+=l-h:h-36e5!==this.getTimezoneOffset(a-36e5)||tF||(a-=36e5)}}return a},t.prototype.parse=function(t){if(!tB(t))return null!=t?t:void 0;var e=(t=t.replace(/\//g,"-").replace(/(GMT|UTC)/,"")).indexOf("Z")>-1||/([+-][0-9]{2}):?[0-9]{2}$/.test(t),i=/^[0-9]{4}-[0-9]{2}(-[0-9]{2}|)$/.test(t);e||i||(t+="Z");var o=Date.parse(t);if(tD(o))return o+(!e||i?this.getTimezoneOffset(o):0)},t.prototype.getTimezoneOffset=function(t){if("UTC"!==this.timezone){var e=this.dateTimeFormat({timeZoneName:"shortOffset"},t,"en").split(/(GMT|:)/).map(Number),i=(e[0],e[1],e[2]),o=(e[3],e[4]),r=-(36e5*(i+(void 0===o?0:o)/60));if(tD(r))return r}return 0},t.prototype.dateFormat=function(t,e,i){var o,r=this.lang;if(!tP(e)||isNaN(e))return(null==r?void 0:r.invalidDate)||"";if(tB(t=null!=t?t:"%Y-%m-%d %H:%M:%S"))for(var s=/%\[([a-zA-Z]+)\]/g,n=void 0;n=s.exec(t);)t=t.replace(n[0],this.dateTimeFormat(n[1],e,null==r?void 0:r.locale));if(tB(t)&&-1!==t.indexOf("%")){var a=this,h=this.toParts(e),l=h[0],d=h[1],c=h[2],p=h[3],u=h[4],f=h[5],g=h[6],v=h[7],m=(null==r?void 0:r.weekdays)||this.weekdays,y=(null==r?void 0:r.shortWeekdays)||this.shortWeekdays,x=(null==r?void 0:r.months)||this.months,b=(null==r?void 0:r.shortMonths)||this.shortMonths;tR(tL({a:y?y[v]:m[v].substr(0,3),A:m[v],d:tN(c),e:tN(c,2," "),w:v,v:null!==(o=null==r?void 0:r.weekFrom)&&void 0!==o?o:"",b:b[d],B:x[d],m:tN(d+1),o:d+1,y:l.toString().substr(2,2),Y:l,H:tN(p),k:p,I:tN(p%12||12),l:p%12||12,M:tN(u),p:p<12?"AM":"PM",P:p<12?"am":"pm",S:tN(f),L:tN(g,3)},tt.dateFormats),function(i,o){if(tB(t))for(;-1!==t.indexOf("%"+o);)t=t.replace("%"+o,"function"==typeof i?i.call(a,e):i)})}else if(tI(t)){var k=(this.getTimezoneOffset(e)||0)/36e5,M=this.timezone||"Etc/GMT"+(k>=0?"+":"")+k,w=t.prefix,S=t.suffix;t=(void 0===w?"":w)+this.dateTimeFormat(tL({timeZone:M},t),e)+(void 0===S?"":S)}return i?tH(t):t},t.prototype.resolveDTLFormat=function(t){return tI(t,!0)?tI(t,!0)&&void 0===t.main?{main:t}:t:{main:(t=tW(t))[0],from:t[1],to:t[2]}},t.prototype.getDateFormat=function(t,e,i,o){var r=this.dateFormat("%m-%d %H:%M:%S.%L",e),s="01-01 00:00:00.000",n={millisecond:15,second:12,minute:9,hour:6,day:3},a="millisecond",h=a;for(a in tX){if(t&&t===tX.week&&+this.dateFormat("%w",e)===i&&r.substr(6)===s.substr(6)){a="week";break}if(t&&tX[a]>t){a=h;break}if(n[a]&&r.substr(n[a])!==s.substr(n[a]))break;"week"!==a&&(h=a)}return this.resolveDTLFormat(o[a]).main},t}(),tY=(h=function(t,e){return(h=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}h(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),tG=tT.defined,t_=tT.extend,tU=tT.timeUnits,tV=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return tY(e,t),e.prototype.getTimeTicks=function(t,e,i,o){var r,s=this,n=[],a={},h=t.count,l=void 0===h?1:h,d=t.unitRange,c=s.toParts(e),p=c[0],u=c[1],f=c[2],g=c[3],v=c[4],m=c[5],y=(e||0)%1e3;if(null!=o||(o=1),tG(e)){if(y=d>=tU.second?0:l*Math.floor(y/l),d>=tU.second&&(m=d>=tU.minute?0:l*Math.floor(m/l)),d>=tU.minute&&(v=d>=tU.hour?0:l*Math.floor(v/l)),d>=tU.hour&&(g=d>=tU.day?0:l*Math.floor(g/l)),d>=tU.day&&(f=d>=tU.month?1:Math.max(1,l*Math.floor(f/l))),d>=tU.month&&(u=d>=tU.year?0:l*Math.floor(u/l)),d>=tU.year&&(p-=p%l),d===tU.week){l&&(e=s.makeTime(p,u,f,g,v,m,y));var x=this.dateTimeFormat({timeZone:this.timezone,weekday:"narrow"},e,"es"),b="DLMXJVS".indexOf(x);f+=-b+o+(b<o?-7:0)}e=s.makeTime(p,u,f,g,v,m,y),s.variableTimezone&&tG(i)&&(r=i-e>4*tU.month||s.getTimezoneOffset(e)!==s.getTimezoneOffset(i));for(var k=e,M=1;k<i;)n.push(k),d===tU.year?k=s.makeTime(p+M*l,0):d===tU.month?k=s.makeTime(p,u+M*l):r&&(d===tU.day||d===tU.week)?k=s.makeTime(p,u,f+M*l*(d===tU.day?1:7)):r&&d===tU.hour&&l>1?k=s.makeTime(p,u,f,g+M*l):k+=d*l,M++;n.push(k),d<=tU.hour&&n.length<1e4&&n.forEach(function(t){t%18e5==0&&"000000000"===s.dateFormat("%H%M%S%L",t)&&(a[t]="day")})}return n.info=t_(t,{higherRanks:a,totalRange:d*l}),n},e}(tj),tZ=function(){return(tZ=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},tq=tt.isTouchDevice,tK=tT.fireEvent,t$=tT.merge,tJ={colors:["#2caffe","#544fc5","#00e272","#fe6a35","#6b8abc","#d568fb","#2ee0ca","#fa4b42","#feb56a","#91e8e1"],symbols:["circle","diamond","square","triangle","triangle-down"],lang:{weekFrom:"week from",chartTitle:"Chart title",locale:void 0,loading:"Loading...",months:void 0,seriesName:"Series {add index 1}",shortMonths:void 0,weekdays:void 0,numericSymbols:["k","M","G","T","P","E"],pieSliceName:"Slice",resetZoom:"Reset zoom",yAxisTitle:"Values",resetZoomTitle:"Reset zoom level 1:1"},global:{buttonTheme:{fill:"#f7f7f7",padding:8,r:2,stroke:"#cccccc","stroke-width":1,style:{color:"#333333",cursor:"pointer",fontSize:"0.8em",fontWeight:"normal"},states:{hover:{fill:"#e6e6e6"},select:{fill:"#e6e9ff",style:{color:"#000000",fontWeight:"bold"}},disabled:{style:{color:"#cccccc"}}}}},time:{Date:void 0,timezone:"UTC",timezoneOffset:0,useUTC:void 0},chart:{alignThresholds:!1,panning:{enabled:!1,type:"x"},styledMode:!1,borderRadius:0,colorCount:10,allowMutatingData:!0,ignoreHiddenSeries:!0,spacing:[10,10,15,10],resetZoomButton:{theme:{},position:{}},reflow:!0,type:"line",zooming:{singleTouch:!1,resetButton:{theme:{zIndex:6},position:{align:"right",x:-10,y:10}}},width:null,height:null,borderColor:"#334eff",backgroundColor:"#ffffff",plotBorderColor:"#cccccc"},title:{style:{color:"#333333",fontWeight:"bold"},text:"Chart title",margin:15,minScale:.67},subtitle:{style:{color:"#666666",fontSize:"0.8em"},text:""},caption:{margin:15,style:{color:"#666666",fontSize:"0.8em"},text:"",align:"left",verticalAlign:"bottom"},plotOptions:{},legend:{enabled:!0,align:"center",alignColumns:!0,className:"highcharts-no-tooltip",events:{},layout:"horizontal",itemMarginBottom:2,itemMarginTop:2,labelFormatter:function(){return this.name},borderColor:"#999999",borderRadius:0,navigation:{style:{fontSize:"0.8em"},activeColor:"#0022ff",inactiveColor:"#cccccc"},itemStyle:{color:"#333333",cursor:"pointer",fontSize:"0.8em",textDecoration:"none",textOverflow:"ellipsis"},itemHoverStyle:{color:"#000000"},itemHiddenStyle:{color:"#666666",textDecoration:"line-through"},shadow:!1,itemCheckboxStyle:{position:"absolute",width:"13px",height:"13px"},squareSymbol:!0,symbolPadding:5,verticalAlign:"bottom",x:0,y:0,title:{style:{fontSize:"0.8em",fontWeight:"bold"}}},loading:{labelStyle:{fontWeight:"bold",position:"relative",top:"45%"},style:{position:"absolute",backgroundColor:"#ffffff",opacity:.5,textAlign:"center"}},tooltip:{enabled:!0,animation:{duration:300,easing:function(t){return Math.sqrt(1-Math.pow(t-1,2))}},borderRadius:3,dateTimeLabelFormats:{millisecond:"%[AebHMSL]",second:"%[AebHMS]",minute:"%[AebHM]",hour:"%[AebHM]",day:"%[AebY]",week:"%v %[AebY]",month:"%[BY]",year:"%Y"},footerFormat:"",headerShape:"callout",hideDelay:500,padding:8,position:{x:0,y:3},shared:!1,snap:tq?25:10,headerFormat:'<span style="font-size: 0.8em">{ucfirst point.key}</span><br/>',pointFormat:'<span style="color:{point.color}">●</span> {series.name}: <b>{point.y}</b><br/>',backgroundColor:"#ffffff",borderWidth:void 0,stickOnContact:!1,style:{color:"#333333",cursor:"default",fontSize:"0.8em"},useHTML:!1},credits:{enabled:!0,href:"https://www.highcharts.com?credits",position:{align:"right",x:-10,verticalAlign:"bottom",y:-5},style:{cursor:"pointer",color:"#999999",fontSize:"0.6em"},text:"Highcharts.com"}},tQ=new tV(tJ.time,tJ.lang),t0=function(t){var e;return tK(tt,"setOptions",{options:t}),t$(!0,tJ,t),t.time&&tQ.update(tJ.time),t.lang&&"locale"in t.lang&&tQ.update({locale:t.lang.locale}),(null===(e=t.lang)||void 0===e?void 0:e.chartTitle)&&(tJ.title=tZ(tZ({},tJ.title),{text:t.lang.chartTitle})),tJ},t1=tt.win,t2=tT.isNumber,t3=tT.isString,t5=tT.merge,t6=tT.pInt,t9=tT.defined,t4=function(t,e,i){return"color-mix(in srgb,".concat(t,",").concat(e," ").concat(100*i,"%)")},t8=function(t){return t3(t)&&!!t&&"none"!==t},t7=function(){var t;function e(t){this.rgba=[NaN,NaN,NaN,NaN],this.input=t;var i,o,r,s,n=tt.Color;if(n&&n!==e)return new n(t);if("object"==typeof t&&void 0!==t.stops)this.stops=t.stops.map(function(t){return new e(t[1])});else if("string"==typeof t)for(this.input=t=e.names[t.toLowerCase()]||t,r=e.parsers.length;r--&&!o;)(i=(s=e.parsers[r]).regex.exec(t))&&(o=s.parse(i));o&&(this.rgba=o)}return e.parse=function(t){return t?new e(t):e.None},e.prototype.get=function(t){var e=this.input,i=this.rgba;if(this.output)return this.output;if("object"==typeof e&&void 0!==this.stops){var o=t5(e);return o.stops=[].slice.call(o.stops),this.stops.forEach(function(e,i){o.stops[i]=[o.stops[i][0],e.get(t)]}),o}return i&&t2(i[0])?"rgb"!==t&&(t||1!==i[3])?"a"===t?"".concat(i[3]):"rgba("+i.join(",")+")":"rgb("+i[0]+","+i[1]+","+i[2]+")":e},e.prototype.brighten=function(t){var i=this.rgba;if(this.stops)this.stops.forEach(function(e){e.brighten(t)});else if(t2(t)&&0!==t){if(t2(i[0]))for(var o=0;o<3;o++)i[o]+=t6(255*t),i[o]<0&&(i[o]=0),i[o]>255&&(i[o]=255);else e.useColorMix&&t8(this.input)&&(this.output=t4(this.input,t>0?"white":"black",Math.abs(t)))}return this},e.prototype.setOpacity=function(t){return this.rgba[3]=t,this},e.prototype.tweenTo=function(t,i){var o=this.rgba,r=t.rgba;if(!t2(o[0])||!t2(r[0]))return e.useColorMix&&t8(this.input)&&t8(t.input)&&i<.99?t4(this.input,t.input,i):t.input||"none";var s=1!==r[3]||1!==o[3],n=function(t,e){return t+(o[e]-t)*(1-i)},a=r.slice(0,3).map(n).map(Math.round);return s&&a.push(n(r[3],3)),(s?"rgba(":"rgb(")+a.join(",")+")"},e.names={white:"#ffffff",black:"#000000"},e.parsers=[{regex:/rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d?(?:\.\d+)?)\s*\)/,parse:function(t){return[t6(t[1]),t6(t[2]),t6(t[3]),parseFloat(t[4],10)]}},{regex:/rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)/,parse:function(t){return[t6(t[1]),t6(t[2]),t6(t[3]),1]}},{regex:/^#([a-f0-9])([a-f0-9])([a-f0-9])([a-f0-9])?$/i,parse:function(t){return[t6(t[1]+t[1],16),t6(t[2]+t[2],16),t6(t[3]+t[3],16),t9(t[4])?t6(t[4]+t[4],16)/255:1]}},{regex:/^#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})?$/i,parse:function(t){return[t6(t[1],16),t6(t[2],16),t6(t[3],16),t9(t[4])?t6(t[4],16)/255:1]}}],e.useColorMix=null===(t=t1.CSS)||void 0===t?void 0:t.supports("color","color-mix(in srgb,red,blue 9%)"),e.None=new e(""),e}(),et=t7.parse,ee=tt.win,ei=tT.isNumber,eo=tT.objectEach,er=function(){function t(t,e,i){this.pos=NaN,this.options=e,this.elem=t,this.prop=i}return t.prototype.dSetter=function(){var t=this.paths,e=null==t?void 0:t[0],i=null==t?void 0:t[1],o=this.now||0,r=[];if(1!==o&&e&&i){if(e.length===i.length&&o<1)for(var s=0;s<i.length;s++){for(var n=e[s],a=i[s],h=[],l=0;l<a.length;l++){var d=n[l],c=a[l];ei(d)&&ei(c)&&("A"!==a[0]||4!==l&&5!==l)?h[l]=d+o*(c-d):h[l]=c}r.push(h)}else r=i}else r=this.toD||[];this.elem.attr("d",r,void 0,!0)},t.prototype.update=function(){var t=this.elem,e=this.prop,i=this.now,o=this.options.step;this[e+"Setter"]?this[e+"Setter"]():t.attr?t.element&&t.attr(e,i,null,!0):t.style[e]=i+this.unit,o&&o.call(t,i,this)},t.prototype.run=function(e,i,o){var r=this,s=r.options,n=function(t){return!n.stopped&&r.step(t)},a=ee.requestAnimationFrame||function(t){setTimeout(t,13)},h=function(){for(var e=0;e<t.timers.length;e++)t.timers[e]()||t.timers.splice(e--,1);t.timers.length&&a(h)};e!==i||this.elem["forceAnimate:"+this.prop]?(this.startTime=+new Date,this.start=e,this.end=i,this.unit=o,this.now=this.start,this.pos=0,n.elem=this.elem,n.prop=this.prop,n()&&1===t.timers.push(n)&&a(h)):(delete s.curAnim[this.prop],s.complete&&0===Object.keys(s.curAnim).length&&s.complete.call(this.elem))},t.prototype.step=function(t){var e,i,o=+new Date,r=this.options,s=this.elem,n=r.complete,a=r.duration,h=r.curAnim;return s.attr&&!s.element?e=!1:t||o>=a+this.startTime?(this.now=this.end,this.pos=1,this.update(),h[this.prop]=!0,i=!0,eo(h,function(t){!0!==t&&(i=!1)}),i&&n&&n.call(s),e=!1):(this.pos=r.easing((o-this.startTime)/a),this.now=this.start+(this.end-this.start)*this.pos,this.update(),e=!0),e},t.prototype.initPath=function(t,e,i){var o,r,s,n,a=t.startX,h=t.endX,l=i.slice(),d=t.isArea,c=d?2:1,p=e&&i.length>e.length&&i.hasStackedCliffs,u=null==e?void 0:e.slice();if(!u||p)return[l,l];function f(t,e){for(;t.length<r;){var i=t[0],o=e[r-t.length];if(o&&"M"===i[0]&&("C"===o[0]?t[0]=["C",i[1],i[2],i[1],i[2],i[1],i[2]]:t[0]=["L",i[1],i[2]]),t.unshift(i),d){var s=t.pop();t.push(t[t.length-1],s)}}}function g(t){for(;t.length<r;){var e=t[Math.floor(t.length/c)-1].slice();if("C"===e[0]&&(e[1]=e[5],e[2]=e[6]),d){var i=t[Math.floor(t.length/c)].slice();t.splice(t.length/2,0,e,i)}else t.push(e)}}if(a&&h&&h.length){for(s=0;s<a.length;s++){if(a[s]===h[0]){o=s;break}if(a[0]===h[h.length-a.length+s]){o=s,n=!0;break}if(a[a.length-1]===h[h.length-a.length+s]){o=a.length-s;break}}void 0===o&&(u=[])}return u.length&&ei(o)&&(r=l.length+o*c,n?(f(u,l),g(l)):(f(l,u),g(u))),[u,l]},t.prototype.fillSetter=function(){t.prototype.strokeSetter.apply(this,arguments)},t.prototype.strokeSetter=function(){this.elem.attr(this.prop,et(this.start).tweenTo(et(this.end),this.pos),void 0,!0)},t.timers=[],t}(),es=tT.defined,en=tT.getStyle,ea=tT.isArray,eh=tT.isNumber,el=tT.isObject,ed=tT.merge,ec=tT.objectEach,ep=tT.pick;function eu(t){return el(t)?ed({duration:500,defer:0},t):{duration:500*!!t,defer:0}}function ef(t,e){for(var i=er.timers.length;i--;)er.timers[i].elem!==t||e&&e!==er.timers[i].prop||(er.timers[i].stopped=!0)}var eg=function(t,e,i){var o,r,s,n,a="";el(i)||(n=arguments,i={duration:n[2],easing:n[3],complete:n[4]}),eh(i.duration)||(i.duration=400),i.easing="function"==typeof i.easing?i.easing:Math[i.easing]||Math.easeInOutSine,i.curAnim=ed(e),ec(e,function(n,h){ef(t,h),s=new er(t,i,h),r=void 0,"d"===h&&ea(e.d)?(s.paths=s.initPath(t,t.pathArray,e.d),s.toD=e.d,o=0,r=1):t.attr?o=t.attr(h):(o=parseFloat(en(t,h))||0,"opacity"!==h&&(a="px")),r||(r=n),"string"==typeof r&&r.match("px")&&(r=r.replace(/px/g,"")),s.run(o,r,a)})},ev=function(t,e,i){var o=eu(e),r=i?[i]:t.series,s=0,n=0;return r.forEach(function(t){var i=eu(t.options.animation);s=el(e)&&es(e.defer)?o.defer:Math.max(s,i.duration+i.defer),n=Math.min(o.duration,i.duration)}),t.renderer.forExport&&(s=0),{defer:Math.max(0,s-n),duration:Math.min(s,n)}},em=function(t,e){e.renderer.globalAnimation=ep(t,e.options.chart.animation,!0)},ey=tt.SVG_NS,ex=tt.win,eb=tT.attr,ek=tT.createElement,eM=tT.css,ew=tT.error,eS=tT.isFunction,eA=tT.isString,eT=tT.objectEach,eC=tT.splat,eO=ex.trustedTypes,eP=eO&&eS(eO.createPolicy)&&eO.createPolicy("highcharts",{createHTML:function(t){return t}}),eE=eP?eP.createHTML(""):"",eL=function(){function t(t){this.nodes="string"==typeof t?this.parseMarkup(t):t}return t.filterUserAttributes=function(e){return eT(e,function(i,o){var r=!0;-1===t.allowedAttributes.indexOf(o)&&(r=!1),-1!==["background","dynsrc","href","lowsrc","src"].indexOf(o)&&(r=eA(i)&&t.allowedReferences.some(function(t){return 0===i.indexOf(t)})),r||(ew(33,!1,void 0,{"Invalid attribute in config":"".concat(o)}),delete e[o]),eA(i)&&e[o]&&(e[o]=i.replace(/</g,"&lt;"))}),e},t.parseStyle=function(t){return t.split(";").reduce(function(t,e){var i=e.split(":").map(function(t){return t.trim()}),o=i.shift();return o&&i.length&&(t[o.replace(/-([a-z])/g,function(t){return t[1].toUpperCase()})]=i.join(":")),t},{})},t.setElementHTML=function(e,i){e.innerHTML=t.emptyHTML,i&&new t(i).addToDOM(e)},t.prototype.addToDOM=function(e){return function e(i,o){var r;return eC(i).forEach(function(i){var s,n=i.tagName,a=i.textContent?tt.doc.createTextNode(i.textContent):void 0,h=t.bypassHTMLFiltering;if(n){if("#text"===n)s=a;else if(-1!==t.allowedTags.indexOf(n)||h){var l="svg"===n?ey:o.namespaceURI||ey,d=tt.doc.createElementNS(l,n),c=i.attributes||{};eT(i,function(t,e){"tagName"!==e&&"attributes"!==e&&"children"!==e&&"style"!==e&&"textContent"!==e&&(c[e]=t)}),eb(d,h?c:t.filterUserAttributes(c)),i.style&&eM(d,i.style),a&&d.appendChild(a),e(i.children||[],d),s=d}else ew(33,!1,void 0,{"Invalid tagName in config":n})}s&&o.appendChild(s),r=s}),r}(this.nodes,e)},t.prototype.parseMarkup=function(e){var i,o=[];e=e.trim().replace(/ style=(["'])/g," data-style=$1");try{i=new DOMParser().parseFromString(eP?eP.createHTML(e):e,"text/html")}catch(t){}if(!i){var r=ek("div");r.innerHTML=e,i={body:r}}var s=function(e,i){var o=e.nodeName.toLowerCase(),r={tagName:o};"#text"===o&&(r.textContent=e.textContent||"");var n=e.attributes;if(n){var a={};[].forEach.call(n,function(e){"data-style"===e.name?r.style=t.parseStyle(e.value):a[e.name]=e.value}),r.attributes=a}if(e.childNodes.length){var h=[];[].forEach.call(e.childNodes,function(t){s(t,h)}),h.length&&(r.children=h)}i.push(r)};return[].forEach.call(i.body.childNodes,function(t){return s(t,o)}),o},t.allowedAttributes=["alt","aria-controls","aria-describedby","aria-expanded","aria-haspopup","aria-hidden","aria-label","aria-labelledby","aria-live","aria-pressed","aria-readonly","aria-roledescription","aria-selected","class","clip-path","color","colspan","cx","cy","d","dx","dy","disabled","fill","filterUnits","flood-color","flood-opacity","height","href","id","in","in2","markerHeight","markerWidth","offset","opacity","operator","orient","padding","paddingLeft","paddingRight","patternUnits","r","radius","refX","refY","role","scope","slope","src","startOffset","stdDeviation","stroke","stroke-linecap","stroke-width","style","tableValues","result","rowspan","summary","target","tabindex","text-align","text-anchor","textAnchor","textLength","title","type","valign","width","x","x1","x2","xlink:href","y","y1","y2","zIndex"],t.allowedReferences=["https://","http://","mailto:","/","../","./","#"],t.allowedTags=["a","abbr","b","br","button","caption","circle","clipPath","code","dd","defs","div","dl","dt","em","feComponentTransfer","feComposite","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feMorphology","feOffset","feMerge","feMergeNode","filter","h1","h2","h3","h4","h5","h6","hr","i","img","li","linearGradient","marker","ol","p","path","pattern","pre","rect","small","span","stop","strong","style","sub","sup","svg","table","text","textPath","thead","title","tbody","tspan","td","th","tr","u","ul","#text"],t.emptyHTML=eE,t.bypassHTMLFiltering=!1,t}(),eD=tt.pageLang,eI=tT.extend,eB=tT.getNestedProperty,ez=tT.isArray,eR=tT.isNumber,eN=tT.isObject,eW=tT.isString,eX=tT.pick,eH={add:function(t,e){return t+e},divide:function(t,e){return 0!==e?t/e:""},eq:function(t,e){return t==e},each:function(t){var e=arguments[arguments.length-1];return!!ez(t)&&t.map(function(i,o){return eY(e.body,eI(eN(i)?i:{"@this":i},{"@index":o,"@first":0===o,"@last":o===t.length-1}))}).join("")},ge:function(t,e){return t>=e},gt:function(t,e){return t>e},if:function(t){return!!t},le:function(t,e){return t<=e},lt:function(t,e){return t<e},multiply:function(t,e){return t*e},ne:function(t,e){return t!=e},subtract:function(t,e){return t-e},ucfirst:tT.ucfirst,unless:function(t){return!t}},eF={},ej=function(t){return/^["'].+["']$/.test(t)};function eY(t,e,i){void 0===t&&(t="");for(var o,r,s,n,a=/\{([a-zA-Z\u00C0-\u017F\d:\.,;\-\/<>\[\]%_@+"'’= #\(\)]+)\}/g,h=/\(([a-zA-Z\u00C0-\u017F\d:\.,;\-\/<>\[\]%_@+"'= ]+)\)/g,l=[],d=/f$/,c=/\.(\d)/,p=(null===(o=null==i?void 0:i.options)||void 0===o?void 0:o.lang)||tJ.lang,u=(null==i?void 0:i.time)||tQ,f=(null==i?void 0:i.numberFormatter)||eG,g=function(t){var i;return void 0===t&&(t=""),"true"===t||"false"!==t&&((i=Number(t)).toString()===t?i:ej(t)?t.slice(1,-1):eB(t,e))},v=0;null!==(r=a.exec(t));){var m=r,y=h.exec(r[1]);y&&(r=y,n=!0),(null==s?void 0:s.isBlock)||(s={ctx:e,expression:r[1],find:r[0],isBlock:"#"===r[1].charAt(0),start:r.index,startInner:r.index+r[0].length,length:r[0].length});var x=(s.isBlock?m:r)[1].split(" ")[0].replace("#","");eH[x]&&(s.isBlock&&x===s.fn&&v++,s.fn||(s.fn=x));var b="else"===r[1];if(s.isBlock&&s.fn&&(r[1]==="/".concat(s.fn)||b)){if(v)!b&&v--;else{var k=s.startInner,M=t.substr(k,r.index-k);void 0===s.body?(s.body=M,s.startInner=r.index+r[0].length):s.elseBody=M,s.find+=M+r[0],b||(l.push(s),s=void 0)}}else s.isBlock||l.push(s);if(y&&!(null==s?void 0:s.isBlock))break}return l.forEach(function(o){var r,s,n=o.body,a=o.elseBody,l=o.expression,v=o.fn;if(v){var m=[o],y=[],x=l.length,b=0,k=void 0;for(s=0;s<=x;s++){var M=l.charAt(s);k||'"'!==M&&"'"!==M?k===M&&(k=""):k=M,k||" "!==M&&s!==x||(y.push(l.substr(b,s-b)),b=s+1)}for(s=eH[v].length;s--;)m.unshift(g(y[s+1]));r=eH[v].apply(e,m),o.isBlock&&"boolean"==typeof r&&(r=eY(r?n:a,e,i))}else{var w=ej(l)?[l]:l.split(":");if(r=g(w.shift()||""),w.length&&"number"==typeof r){var S=w.join(":");if(d.test(S)){var A=parseInt((S.match(c)||["","-1"])[1],10);null!==r&&(r=f(r,A,p.decimalPoint,S.indexOf(",")>-1?p.thousandsSep:""))}else r=u.dateFormat(S,r)}h.lastIndex=0,h.test(o.find)&&eW(r)&&(r='"'.concat(r,'"'))}t=t.replace(o.find,eX(r,""))}),n?eY(t,e,i):t}function eG(t,e,i,o){e*=1;var r,s,n,a,h=(t=+t||0).toString().split("e").map(Number),l=h[0],d=h[1],c=(null===(r=this===null||void 0===this?void 0:this.options)||void 0===r?void 0:r.lang)||tJ.lang,p=(t.toString().split(".")[1]||"").split("e")[0].length,u=e,f={};null!=i||(i=c.decimalPoint),null!=o||(o=c.thousandsSep),-1===e?e=Math.min(p,20):eR(e)?e&&d<0&&((a=e+d)>=0?(l=+l.toExponential(a).split("e")[0],e=a):(l=Math.floor(l),t=e<20?+(l*Math.pow(10,d)).toFixed(e):0,d=0)):e=2,d&&(null!=e||(e=2),t=l),eR(e)&&e>=0&&(f.minimumFractionDigits=e,f.maximumFractionDigits=e),""===o&&(f.useGrouping=!1);var g=o||i,v=g?"en":(this===null||void 0===this?void 0:this.locale)||c.locale||eD,m=JSON.stringify(f)+v;return n=(null!==(s=eF[m])&&void 0!==s?s:eF[m]=new Intl.NumberFormat(v,f)).format(t),g&&(n=n.replace(/([,\.])/g,"_$1").replace(/_\,/g,null!=o?o:",").replace("_.",null!=i?i:".")),(e||0!=+n)&&(!(d<0)||u)||(n="0"),d&&0!=+n&&(n+="e"+(d<0?"":"+")+d),n}var e_={dateFormat:function(t,e,i){return tQ.dateFormat(t,e,i)},format:eY,helpers:eH,numberFormat:eG};(l=D||(D={})).rendererTypes={},l.getRendererType=function(t){return void 0===t&&(t=d),l.rendererTypes[t]||l.rendererTypes[d]},l.registerRendererType=function(t,e,i){l.rendererTypes[t]=e,(!d||i)&&(d=t,tt.Renderer=e)};var eU=D,eV=tT.clamp,eZ=tT.pick,eq=tT.pushUnique,eK=tT.stableSort;(I||(I={})).distribute=function t(e,i,o){var r,s,n,a,h,l,d=e,c=d.reducedLen||i,p=function(t,e){return t.target-e.target},u=[],f=e.length,g=[],v=u.push,m=!0,y=0;for(r=f;r--;)y+=e[r].size;if(y>c){for(eK(e,function(t,e){return(e.rank||0)-(t.rank||0)}),n=(l=e[0].rank===e[e.length-1].rank)?f/2:-1,s=l?n:f-1;n&&y>c;)a=e[r=Math.floor(s)],eq(g,r)&&(y-=a.size),s+=n,l&&s>=e.length&&(n/=2,s=n);g.sort(function(t,e){return e-t}).forEach(function(t){return v.apply(u,e.splice(t,1))})}for(eK(e,p),e=e.map(function(t){return{size:t.size,targets:[t.target],align:eZ(t.align,.5)}});m;){for(r=e.length;r--;)a=e[r],h=(Math.min.apply(0,a.targets)+Math.max.apply(0,a.targets))/2,a.pos=eV(h-a.size*a.align,0,i-a.size);for(r=e.length,m=!1;r--;)r>0&&e[r-1].pos+e[r-1].size>e[r].pos&&(e[r-1].size+=e[r].size,e[r-1].targets=e[r-1].targets.concat(e[r].targets),e[r-1].align=.5,e[r-1].pos+e[r-1].size>i&&(e[r-1].pos=i-e[r-1].size),e.splice(r,1),m=!0)}return v.apply(d,u),r=0,e.some(function(e){var s=0;return(e.targets||[]).some(function(){return(d[r].pos=e.pos+s,void 0!==o&&Math.abs(d[r].pos-d[r].target)>o)?(d.slice(0,r+1).forEach(function(t){return delete t.pos}),d.reducedLen=(d.reducedLen||i)-.1*i,d.reducedLen>.1*i&&t(d,i,o),!0):(s+=d[r].size,r++,!1)})}),eK(d,p),d};var e$=I,eJ=tt.deg2rad,eQ=tt.doc,e0=tt.svg,e1=tt.SVG_NS,e2=tt.win,e3=tt.isFirefox,e5=tT.addEvent,e6=tT.attr,e9=tT.createElement,e4=tT.crisp,e8=tT.css,e7=tT.defined,it=tT.erase,ie=tT.extend,ii=tT.fireEvent,io=tT.getAlignFactor,ir=tT.isArray,is=tT.isFunction,ia=tT.isNumber,ih=tT.isObject,il=tT.isString,id=tT.merge,ic=tT.objectEach,ip=tT.pick,iu=tT.pInt,ig=tT.pushUnique,iv=tT.replaceNested,im=tT.syncTimeout,iy=tT.uniqueKey,ix=function(){function t(t,e){this.onEvents={},this.opacity=1,this.SVG_NS=e1,this.element="span"===e||"body"===e?e9(e):eQ.createElementNS(this.SVG_NS,e),this.renderer=t,this.styles={},ii(this,"afterInit")}return t.prototype._defaultGetter=function(t){var e=ip(this[t+"Value"],this[t],this.element?this.element.getAttribute(t):null,0);return/^-?[\d\.]+$/.test(e)&&(e=parseFloat(e)),e},t.prototype._defaultSetter=function(t,e,i){i.setAttribute(e,t)},t.prototype.add=function(t){var e,i=this.renderer,o=this.element;return t&&(this.parentGroup=t),void 0!==this.textStr&&"text"===this.element.nodeName&&i.buildText(this),this.added=!0,(!t||t.handleZ||this.zIndex)&&(e=this.zIndexSetter()),e||(t?t.element:i.box).appendChild(o),this.onAdd&&this.onAdd(),this},t.prototype.addClass=function(t,e){var i=e?"":this.attr("class")||"";return(t=(t||"").split(/ /g).reduce(function(t,e){return -1===i.indexOf(e)&&t.push(e),t},i?[i]:[]).join(" "))!==i&&this.attr("class",t),this},t.prototype.afterSetters=function(){this.doTransform&&(this.updateTransform(),this.doTransform=!1)},t.prototype.align=function(t,e,i,o){void 0===o&&(o=!0);var r=this.renderer,s=r.alignedObjects,n=!!t;t?(this.alignOptions=t,this.alignByTranslate=e,this.alignTo=i):(t=this.alignOptions||{},e=this.alignByTranslate,i=this.alignTo);var a=!i||il(i)?i||"renderer":void 0;a&&(n&&ig(s,this),i=void 0);var h=ip(i,r[a],r),l=(h.x||0)+(t.x||0)+((h.width||0)-(t.width||0))*io(t.align),d=(h.y||0)+(t.y||0)+((h.height||0)-(t.height||0))*io(t.verticalAlign),c={"text-align":null==t?void 0:t.align};return c[e?"translateX":"x"]=Math.round(l),c[e?"translateY":"y"]=Math.round(d),o&&(this[this.placed?"animate":"attr"](c),this.placed=!0),this.alignAttr=c,this},t.prototype.alignSetter=function(t){var e={left:"start",center:"middle",right:"end"};e[t]&&(this.alignValue=t,this.element.setAttribute("text-anchor",e[t]))},t.prototype.animate=function(t,e,i){var o=this,r=eu(ip(e,this.renderer.globalAnimation,!0)),s=r.defer;return eQ.hidden&&(r.duration=0),0!==r.duration?(i&&(r.complete=i),im(function(){o.element&&eg(o,t,r)},s)):(this.attr(t,void 0,i||r.complete),ic(t,function(t,e){r.step&&r.step.call(this,t,{prop:e,pos:1,elem:this})},this)),this},t.prototype.applyTextOutline=function(t){var e=this.element;-1!==t.indexOf("contrast")&&(t=t.replace(/contrast/g,this.renderer.getContrast(e.style.fill)));var i=t.indexOf(" "),o=t.substring(i+1),r=t.substring(0,i);if(r&&"none"!==r&&tt.svg){this.fakeTS=!0,r=r.replace(/(^[\d\.]+)(.*?)$/g,function(t,e,i){return 2*Number(e)+i}),this.removeTextOutline();var s=eQ.createElementNS(e1,"tspan");e6(s,{class:"highcharts-text-outline",fill:o,stroke:o,"stroke-width":r,"stroke-linejoin":"round"});var n=e.querySelector("textPath")||e;[].forEach.call(n.childNodes,function(t){var e=t.cloneNode(!0);e.removeAttribute&&["fill","stroke","stroke-width","stroke"].forEach(function(t){return e.removeAttribute(t)}),s.appendChild(e)});var a=0;[].forEach.call(n.querySelectorAll("text tspan"),function(t){a+=Number(t.getAttribute("dy"))});var h=eQ.createElementNS(e1,"tspan");h.textContent="​",e6(h,{x:Number(e.getAttribute("x")),dy:-a}),s.appendChild(h),n.insertBefore(s,n.firstChild)}},t.prototype.attr=function(e,i,o,r){var s,n,a,h=this.element,l=t.symbolCustomAttribs,d=this;return"string"==typeof e&&void 0!==i&&(s=e,(e={})[s]=i),"string"==typeof e?d=(this[e+"Getter"]||this._defaultGetter).call(this,e,h):(ic(e,function(t,i){a=!1,r||ef(this,i),this.symbolName&&-1!==l.indexOf(i)&&(n||(this.symbolAttr(e),n=!0),a=!0),this.rotation&&("x"===i||"y"===i)&&(this.doTransform=!0),a||(this[i+"Setter"]||this._defaultSetter).call(this,t,i,h)},this),this.afterSetters()),o&&o.call(this),d},t.prototype.clip=function(t){if(t&&!t.clipPath){var e=iy()+"-",i=this.renderer.createElement("clipPath").attr({id:e}).add(this.renderer.defs);ie(t,{clipPath:i,id:e,count:0}),t.add(i)}return this.attr("clip-path",t?"url(".concat(this.renderer.url,"#").concat(t.id,")"):"none")},t.prototype.crisp=function(t,e){e=Math.round(e||t.strokeWidth||0);var i=t.x||this.x||0,o=t.y||this.y||0,r=(t.width||this.width||0)+i,s=(t.height||this.height||0)+o,n=e4(i,e),a=e4(o,e);return ie(t,{x:n,y:a,width:e4(r,e)-n,height:e4(s,e)-a}),e7(t.strokeWidth)&&(t.strokeWidth=e),t},t.prototype.complexColor=function(t,e,i){var o,r,s,n,a,h,l,d,c,p,u,f=this.renderer,g=[];ii(this.renderer,"complexColor",{args:arguments},function(){if(t.radialGradient?r="radialGradient":t.linearGradient&&(r="linearGradient"),r){if(s=t[r],a=f.gradients,h=t.stops,c=i.radialReference,ir(s)&&(t[r]=s={x1:s[0],y1:s[1],x2:s[2],y2:s[3],gradientUnits:"userSpaceOnUse"}),"radialGradient"===r&&c&&!e7(s.gradientUnits)&&(n=s,s=id(s,f.getRadialAttr(c,n),{gradientUnits:"userSpaceOnUse"})),ic(s,function(t,e){"id"!==e&&g.push(e,t)}),ic(h,function(t){g.push(t)}),a[g=g.join(",")])p=a[g].attr("id");else{s.id=p=iy();var v=a[g]=f.createElement(r).attr(s).add(f.defs);v.radAttr=n,v.stops=[],h.forEach(function(t){0===t[1].indexOf("rgba")?(l=(o=t7.parse(t[1])).get("rgb"),d=o.get("a")):(l=t[1],d=1);var e=f.createElement("stop").attr({offset:t[0],"stop-color":l,"stop-opacity":d}).add(v);v.stops.push(e)})}u="url("+f.url+"#"+p+")",i.setAttribute(e,u),i.gradient=g,t.toString=function(){return u}}})},t.prototype.css=function(t){var e,i=this.styles,o={},r=this.element,s=!i;if(i&&ic(t,function(t,e){i&&i[e]!==t&&(o[e]=t,s=!0)}),s){i&&(t=ie(i,o)),null===t.width||"auto"===t.width?delete this.textWidth:"text"===r.nodeName.toLowerCase()&&t.width&&(e=this.textWidth=iu(t.width)),ie(this.styles,t),e&&!e0&&this.renderer.forExport&&delete t.width;var n=e3&&t.fontSize||null;n&&(ia(n)||/^\d+$/.test(n))&&(t.fontSize+="px");var a=id(t);r.namespaceURI===this.SVG_NS&&(["textOutline","textOverflow","whiteSpace","width"].forEach(function(t){return a&&delete a[t]}),a.color&&(a.fill=a.color,delete a.color)),e8(r,a)}return this.added&&("text"===this.element.nodeName&&this.renderer.buildText(this),t.textOutline&&this.applyTextOutline(t.textOutline)),this},t.prototype.dashstyleSetter=function(t){var e,i=this["stroke-width"];if("inherit"===i&&(i=1),t){var o=(t=t.toLowerCase()).replace("shortdashdotdot","3,1,1,1,1,1,").replace("shortdashdot","3,1,1,1").replace("shortdot","1,1,").replace("shortdash","3,1,").replace("longdash","8,3,").replace(/dot/g,"1,3,").replace("dash","4,3,").replace(/,$/,"").split(",");for(e=o.length;e--;)o[e]=""+iu(o[e])*ip(i,NaN);t=o.join(",").replace(/NaN/g,"none"),this.element.setAttribute("stroke-dasharray",t)}},t.prototype.destroy=function(){var t,e,i=this,o=i.element||{},r=i.renderer,s=o.ownerSVGElement,n="SPAN"===o.nodeName&&i.parentGroup||void 0;if(o.onclick=o.onmouseout=o.onmouseover=o.onmousemove=o.point=null,ef(i),i.clipPath&&s){var a=i.clipPath;[].forEach.call(s.querySelectorAll("[clip-path],[CLIP-PATH]"),function(t){t.getAttribute("clip-path").indexOf(a.element.id)>-1&&t.removeAttribute("clip-path")}),i.clipPath=a.destroy()}if(i.stops){for(e=0;e<i.stops.length;e++)i.stops[e].destroy();i.stops.length=0,i.stops=void 0}for(i.safeRemoveChild(o);(null==n?void 0:n.div)&&0===n.div.childNodes.length;)t=n.parentGroup,i.safeRemoveChild(n.div),delete n.div,n=t;i.alignOptions&&it(r.alignedObjects,i),ic(i,function(t,e){var o,r,s;((null===(o=i[e])||void 0===o?void 0:o.parentGroup)===i||-1!==["connector","foreignObject"].indexOf(e))&&(null===(s=null===(r=i[e])||void 0===r?void 0:r.destroy)||void 0===s||s.call(r)),delete i[e]})},t.prototype.dSetter=function(t,e,i){ir(t)&&("string"==typeof t[0]&&(t=this.renderer.pathToSegments(t)),this.pathArray=t,t=t.reduce(function(t,e,i){return(null==e?void 0:e.join)?(i?t+" ":"")+e.join(" "):(e||"").toString()},"")),/(NaN| {2}|^$)/.test(t)&&(t="M 0 0"),this[e]!==t&&(i.setAttribute(e,t),this[e]=t)},t.prototype.fillSetter=function(t,e,i){"string"==typeof t?i.setAttribute(e,t):t&&this.complexColor(t,e,i)},t.prototype.hrefSetter=function(t,e,i){i.setAttributeNS("http://www.w3.org/1999/xlink",e,t)},t.prototype.getBBox=function(e,i){var o,r,s,n,a=this.alignValue,h=this.element,l=this.renderer,d=this.styles,c=this.textStr,p=l.cache,u=l.cacheKeys,f=h.namespaceURI===this.SVG_NS,g=ip(i,this.rotation,0),v=l.styledMode?h&&t.prototype.getStyle.call(h,"font-size"):d.fontSize;if(e7(c)&&(-1===(n=c.toString()).indexOf("<")&&(n=n.replace(/\d/g,"0")),n+=["",l.rootFontSize,v,g,this.textWidth,a,d.lineClamp,d.textOverflow,d.fontWeight].join(",")),n&&!e&&(o=p[n]),!o||o.polygon){if(f||l.forExport){try{s=this.fakeTS&&function(t){var e=h.querySelector(".highcharts-text-outline");e&&e8(e,{display:t})},is(s)&&s("none"),o=h.getBBox?ie({},h.getBBox()):{width:h.offsetWidth,height:h.offsetHeight,x:0,y:0},is(s)&&s("")}catch(t){}(!o||o.width<0)&&(o={x:0,y:0,width:0,height:0})}else o=this.htmlGetBBox();r=o.height,f&&(o.height=r=({"11px,17":14,"13px,20":16})[""+(v||"")+",".concat(Math.round(r))]||r),g&&(o=this.getRotatedBox(o,g));var m={bBox:o};ii(this,"afterGetBBox",m),o=m.bBox}if(n&&(""===c||o.height>0)){for(;u.length>250;)delete p[u.shift()];p[n]||u.push(n),p[n]=o}return o},t.prototype.getRotatedBox=function(t,e){var i=t.x,o=t.y,r=t.width,s=t.height,n=this.alignValue,a=this.translateY,h=this.rotationOriginX,l=this.rotationOriginY,d=io(n),c=Number(this.element.getAttribute("y")||0)-(a?0:o),p=e*eJ,u=(e-90)*eJ,f=Math.cos(p),g=Math.sin(p),v=r*f,m=r*g,y=Math.cos(u),x=Math.sin(u),b=[void 0===h?0:h,void 0===l?0:l].map(function(t){return[t-t*f,t*g]}),k=b[0],M=k[0],w=k[1],S=b[1],A=S[0],T=i+d*(r-v)+M+S[1]+c*y,C=T+v,O=C-s*y,P=O-v,E=o+c-d*m-w+A+c*x,L=E+m,D=L-s*x,I=D-m,B=Math.min(T,C,O,P),z=Math.min(E,L,D,I),R=Math.max(T,C,O,P)-B,N=Math.max(E,L,D,I)-z;return{x:B,y:z,width:R,height:N,polygon:[[T,E],[C,L],[O,D],[P,I]]}},t.prototype.getStyle=function(t){return e2.getComputedStyle(this.element||this,"").getPropertyValue(t)},t.prototype.hasClass=function(t){return -1!==(""+this.attr("class")).split(" ").indexOf(t)},t.prototype.hide=function(){return this.attr({visibility:"hidden"})},t.prototype.htmlGetBBox=function(){return{height:0,width:0,x:0,y:0}},t.prototype.on=function(t,e){var i=this.onEvents;return i[t]&&i[t](),i[t]=e5(this.element,t,e),this},t.prototype.opacitySetter=function(t,e,i){var o=Number(Number(t).toFixed(3));this.opacity=o,i.setAttribute(e,o)},t.prototype.reAlign=function(){var t;(null===(t=this.alignOptions)||void 0===t?void 0:t.width)&&"left"!==this.alignOptions.align&&(this.alignOptions.width=this.getBBox().width,this.placed=!1,this.align())},t.prototype.removeClass=function(t){return this.attr("class",(""+this.attr("class")).replace(il(t)?new RegExp("(^| )".concat(t,"( |$)")):t," ").replace(/ +/g," ").trim())},t.prototype.removeTextOutline=function(){var t=this.element.querySelector("tspan.highcharts-text-outline");t&&this.safeRemoveChild(t)},t.prototype.safeRemoveChild=function(t){var e=t.parentNode;e&&e.removeChild(t)},t.prototype.setRadialReference=function(t){var e=this.element.gradient&&this.renderer.gradients[this.element.gradient]||void 0;return this.element.radialReference=t,(null==e?void 0:e.radAttr)&&e.animate(this.renderer.getRadialAttr(t,e.radAttr)),this},t.prototype.shadow=function(t){var e,i=this.renderer,o=id((null===(e=this.parentGroup)||void 0===e?void 0:e.rotation)===90?{offsetX:-1,offsetY:-1}:{},ih(t)?t:{}),r=i.shadowDefinition(o);return this.attr({filter:t?"url(".concat(i.url,"#").concat(r,")"):"none"})},t.prototype.show=function(t){return void 0===t&&(t=!0),this.attr({visibility:t?"inherit":"visible"})},t.prototype["stroke-widthSetter"]=function(t,e,i){this[e]=t,i.setAttribute(e,t)},t.prototype.strokeWidth=function(){if(!this.renderer.styledMode)return this["stroke-width"]||0;var t,e=this.getStyle("stroke-width"),i=0;return/px$/.test(e)?i=iu(e):""!==e&&(e6(t=eQ.createElementNS(e1,"rect"),{width:e,"stroke-width":0}),this.element.parentNode.appendChild(t),i=t.getBBox().width,t.parentNode.removeChild(t)),i},t.prototype.symbolAttr=function(e){var i=this;t.symbolCustomAttribs.forEach(function(t){i[t]=ip(e[t],i[t])}),i.attr({d:i.renderer.symbols[i.symbolName](i.x,i.y,i.width,i.height,i)})},t.prototype.textSetter=function(t){t!==this.textStr&&(delete this.textPxLength,this.textStr=t,this.added&&this.renderer.buildText(this),this.reAlign())},t.prototype.titleSetter=function(t){var e=this.element,i=e.getElementsByTagName("title")[0]||eQ.createElementNS(this.SVG_NS,"title");e.insertBefore?e.insertBefore(i,e.firstChild):e.appendChild(i),i.textContent=iv(ip(t,""),[/<[^>]*>/g,""]).replace(/&lt;/g,"<").replace(/&gt;/g,">")},t.prototype.toFront=function(){var t=this.element;return t.parentNode.appendChild(t),this},t.prototype.translate=function(t,e){return this.attr({translateX:t,translateY:e})},t.prototype.updateTransform=function(t){void 0===t&&(t="transform");var e,i,o,r,s=this.element,n=this.foreignObject,a=this.matrix,h=this.padding,l=this.rotation,d=void 0===l?0:l,c=this.rotationOriginX,p=this.rotationOriginY,u=this.scaleX,f=this.scaleY,g=this.text,v=this.translateX,m=this.translateY,y=["translate("+(void 0===v?0:v)+","+(void 0===m?0:m)+")"];e7(a)&&y.push("matrix("+a.join(",")+")"),!d||(y.push("rotate("+d+" "+(null!==(i=null!==(e=null!=c?c:s.getAttribute("x"))&&void 0!==e?e:this.x)&&void 0!==i?i:0)+" "+(null!==(r=null!==(o=null!=p?p:s.getAttribute("y"))&&void 0!==o?o:this.y)&&void 0!==r?r:0)+")"),(null==g?void 0:g.element.tagName)!=="SPAN"||(null==g?void 0:g.foreignObject)||g.attr({rotation:d,rotationOriginX:(c||0)-h,rotationOriginY:(p||0)-h})),(e7(u)||e7(f))&&y.push("scale("+ip(u,1)+" "+ip(f,1)+")"),y.length&&!(g||this).textPath&&((null==n?void 0:n.element)||s).setAttribute(t,y.join(" "))},t.prototype.visibilitySetter=function(t,e,i){"inherit"===t?i.removeAttribute(e):this[e]!==t&&i.setAttribute(e,t),this[e]=t},t.prototype.xGetter=function(t){return"circle"===this.element.nodeName&&("x"===t?t="cx":"y"===t&&(t="cy")),this._defaultGetter(t)},t.prototype.zIndexSetter=function(t,e){var i,o,r,s,n,a=this.renderer,h=this.parentGroup,l=(h||a).element||a.box,d=this.element,c=l===a.box,p=!1,u=this.added;if(e7(t)?(d.setAttribute("data-z-index",t),t*=1,this[e]===t&&(u=!1)):e7(this[e])&&d.removeAttribute("data-z-index"),this[e]=t,u){for((t=this.zIndex)&&h&&(h.handleZ=!0),n=(i=l.childNodes).length-1;n>=0&&!p;n--)s=!e7(r=(o=i[n]).getAttribute("data-z-index")),o!==d&&(t<0&&s&&!c&&!n?(l.insertBefore(d,i[n]),p=!0):(iu(r)<=t||s&&(!e7(t)||t>=0))&&(l.insertBefore(d,i[n+1]),p=!0));p||(l.insertBefore(d,i[3*!!c]),p=!0)}return p},t.symbolCustomAttribs=["anchorX","anchorY","clockwise","end","height","innerR","r","start","width","x","y"],t}();ix.prototype.strokeSetter=ix.prototype.fillSetter,ix.prototype.yGetter=ix.prototype.xGetter,ix.prototype.matrixSetter=ix.prototype.rotationOriginXSetter=ix.prototype.rotationOriginYSetter=ix.prototype.rotationSetter=ix.prototype.scaleXSetter=ix.prototype.scaleYSetter=ix.prototype.translateXSetter=ix.prototype.translateYSetter=ix.prototype.verticalAlignSetter=function(t,e){this[e]=t,this.doTransform=!0};var ib=(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),ik=tT.defined,iM=tT.extend,iw=tT.getAlignFactor,iS=tT.isNumber,iA=tT.merge,iT=tT.pick,iC=tT.removeEvent,iO=function(t){function e(i,o,r,s,n,a,h,l,d,c){var p,u=t.call(this,i,"g")||this;return u.paddingLeftSetter=u.paddingSetter,u.paddingRightSetter=u.paddingSetter,u.doUpdate=!1,u.textStr=o,u.x=r,u.y=s,u.anchorX=a,u.anchorY=h,u.baseline=d,u.className=c,u.addClass("button"===c?"highcharts-no-tooltip":"highcharts-label"),c&&u.addClass("highcharts-"+c),u.text=i.text(void 0,0,0,l).attr({zIndex:1}),"string"==typeof n&&((p=/^url\((.*?)\)$/.test(n))||u.renderer.symbols[n])&&(u.symbolKey=n),u.bBox=e.emptyBBox,u.padding=3,u.baselineOffset=0,u.needsBox=i.styledMode||p,u.deferredAttr={},u.alignFactor=0,u}return ib(e,t),e.prototype.alignSetter=function(t){var e=iw(t);this.textAlign=t,e!==this.alignFactor&&(this.alignFactor=e,this.bBox&&iS(this.xSetting)&&this.attr({x:this.xSetting}))},e.prototype.anchorXSetter=function(t,e){this.anchorX=t,this.boxAttr(e,Math.round(t)-this.getCrispAdjust()-this.xSetting)},e.prototype.anchorYSetter=function(t,e){this.anchorY=t,this.boxAttr(e,t-this.ySetting)},e.prototype.boxAttr=function(t,e){this.box?this.box.attr(t,e):this.deferredAttr[t]=e},e.prototype.css=function(t){if(t){var i={};t=iA(t),e.textProps.forEach(function(e){void 0!==t[e]&&(i[e]=t[e],delete t[e])}),this.text.css(i),"fontSize"in i||"fontWeight"in i?this.updateTextPadding():("width"in i||"textOverflow"in i)&&this.updateBoxSize()}return ix.prototype.css.call(this,t)},e.prototype.destroy=function(){iC(this.element,"mouseenter"),iC(this.element,"mouseleave"),this.text&&this.text.destroy(),this.box&&(this.box=this.box.destroy()),ix.prototype.destroy.call(this)},e.prototype.fillSetter=function(t,e){t&&(this.needsBox=!0),this.fill=t,this.boxAttr(e,t)},e.prototype.getBBox=function(t,e){this.textStr&&0===this.bBox.width&&0===this.bBox.height&&this.updateBoxSize();var i=this.padding,o=this.height,r=this.translateX,s=this.translateY,n=this.width,a=iT(this.paddingLeft,i),h=null!=e?e:this.rotation||0,l={width:void 0===n?0:n,height:void 0===o?0:o,x:(void 0===r?0:r)+this.bBox.x-a,y:(void 0===s?0:s)+this.bBox.y-i+this.baselineOffset};return h&&(l=this.getRotatedBox(l,h)),l},e.prototype.getCrispAdjust=function(){return(this.renderer.styledMode&&this.box?this.box.strokeWidth():this["stroke-width"]?parseInt(this["stroke-width"],10):0)%2/2},e.prototype.heightSetter=function(t){this.heightSetting=t,this.doUpdate=!0},e.prototype.afterSetters=function(){t.prototype.afterSetters.call(this),this.doUpdate&&(this.updateBoxSize(),this.doUpdate=!1)},e.prototype.onAdd=function(){this.text.add(this),this.attr({text:iT(this.textStr,""),x:this.x||0,y:this.y||0}),this.box&&ik(this.anchorX)&&this.attr({anchorX:this.anchorX,anchorY:this.anchorY})},e.prototype.paddingSetter=function(t,e){iS(t)?t!==this[e]&&(this[e]=t,this.updateTextPadding()):this[e]=void 0},e.prototype.rSetter=function(t,e){this.boxAttr(e,t)},e.prototype.strokeSetter=function(t,e){this.stroke=t,this.boxAttr(e,t)},e.prototype["stroke-widthSetter"]=function(t,e){t&&(this.needsBox=!0),this["stroke-width"]=t,this.boxAttr(e,t)},e.prototype["text-alignSetter"]=function(t){this.textAlign=this["text-align"]=t,this.updateTextPadding()},e.prototype.textSetter=function(t){void 0!==t&&this.text.attr({text:t}),this.updateTextPadding(),this.reAlign()},e.prototype.updateBoxSize=function(){var t,i=this.text,o={},r=this.padding,s=this.bBox=(!iS(this.widthSetting)||!iS(this.heightSetting)||this.textAlign)&&ik(i.textStr)?i.getBBox(void 0,0):e.emptyBBox;this.width=this.getPaddedWidth(),this.height=(this.heightSetting||s.height||0)+2*r;var n=this.renderer.fontMetrics(i);if(this.baselineOffset=r+Math.min((this.text.firstLineMetrics||n).b,s.height||1/0),this.heightSetting&&(this.baselineOffset+=(this.heightSetting-n.h)/2),this.needsBox&&!i.textPath){if(!this.box){var a=this.box=this.symbolKey?this.renderer.symbol(this.symbolKey):this.renderer.rect();a.addClass(("button"===this.className?"":"highcharts-label-box")+(this.className?" highcharts-"+this.className+"-box":"")),a.add(this)}o.x=t=this.getCrispAdjust(),o.y=(this.baseline?-this.baselineOffset:0)+t,o.width=Math.round(this.width),o.height=Math.round(this.height),this.box.attr(iM(o,this.deferredAttr)),this.deferredAttr={}}},e.prototype.updateTextPadding=function(){var t,e,i=this.text,o=i.styles.textAlign||this.textAlign;if(!i.textPath){this.updateBoxSize();var r=this.baseline?0:this.baselineOffset,s=(null!==(t=this.paddingLeft)&&void 0!==t?t:this.padding)+iw(o)*(null!==(e=this.widthSetting)&&void 0!==e?e:this.bBox.width);(s!==i.x||r!==i.y)&&(i.attr({align:o,x:s}),void 0!==r&&i.attr("y",r)),i.x=s,i.y=r}},e.prototype.widthSetter=function(t){this.widthSetting=iS(t)?t:void 0,this.doUpdate=!0},e.prototype.getPaddedWidth=function(){var t=this.padding,e=iT(this.paddingLeft,t),i=iT(this.paddingRight,t);return(this.widthSetting||this.bBox.width||0)+e+i},e.prototype.xSetter=function(t){this.x=t,this.alignFactor&&(t-=this.alignFactor*this.getPaddedWidth(),this["forceAnimate:x"]=!0),this.xSetting=Math.round(t),this.attr("translateX",this.xSetting)},e.prototype.ySetter=function(t){this.ySetting=this.y=Math.round(t),this.attr("translateY",this.ySetting)},e.emptyBBox={width:0,height:0,x:0,y:0},e.textProps=["color","direction","fontFamily","fontSize","fontStyle","fontWeight","lineClamp","lineHeight","textAlign","textDecoration","textOutline","textOverflow","whiteSpace","width"],e}(ix),iP=tT.defined,iE=tT.isNumber,iL=tT.pick;function iD(t,e,i,o,r){var s=[];if(r){var n=r.start||0,a=r.end||0,h=iL(r.r,i),l=iL(r.r,o||i),d=2e-4/(r.borderRadius?1:Math.max(h,1)),c=Math.abs(a-n-2*Math.PI)<d;c&&(n=Math.PI/2,a=2.5*Math.PI-d);var p=r.innerR,u=iL(r.open,c),f=Math.cos(n),g=Math.sin(n),v=Math.cos(a),m=Math.sin(a),y=iL(r.longArc,a-n-Math.PI<d?0:1),x=["A",h,l,0,y,iL(r.clockwise,1),t+h*v,e+l*m];x.params={start:n,end:a,cx:t,cy:e},s.push(["M",t+h*f,e+l*g],x),iP(p)&&((x=["A",p,p,0,y,iP(r.clockwise)?1-r.clockwise:0,t+p*f,e+p*g]).params={start:a,end:n,cx:t,cy:e},s.push(u?["M",t+p*v,e+p*m]:["L",t+p*v,e+p*m],x)),u||s.push(["Z"])}return s}function iI(t,e,i,o,r){return(null==r?void 0:r.r)?iB(t,e,i,o,r):[["M",t,e],["L",t+i,e],["L",t+i,e+o],["L",t,e+o],["Z"]]}function iB(t,e,i,o,r){var s=(null==r?void 0:r.r)||0;return[["M",t+s,e],["L",t+i-s,e],["A",s,s,0,0,1,t+i,e+s],["L",t+i,e+o-s],["A",s,s,0,0,1,t+i-s,e+o],["L",t+s,e+o],["A",s,s,0,0,1,t,e+o-s],["L",t,e+s],["A",s,s,0,0,1,t+s,e],["Z"]]}var iz={arc:iD,callout:function(t,e,i,o,r){var s=Math.min((null==r?void 0:r.r)||0,i,o),n=s+6,a=null==r?void 0:r.anchorX,h=(null==r?void 0:r.anchorY)||0,l=iB(t,e,i,o,{r:s});if(!iE(a)||a<i&&a>0&&h<o&&h>0)return l;if(t+a>i-n){if(h>e+n&&h<e+o-n)l.splice(3,1,["L",t+i,h-6],["L",t+i+6,h],["L",t+i,h+6],["L",t+i,e+o-s]);else if(a<i){var d=h<e+n,c=d?e:e+o,p=d?2:5;l.splice(p,0,["L",a,h],["L",t+i-s,c])}else l.splice(3,1,["L",t+i,o/2],["L",a,h],["L",t+i,o/2],["L",t+i,e+o-s])}else if(t+a<n){if(h>e+n&&h<e+o-n)l.splice(7,1,["L",t,h+6],["L",t-6,h],["L",t,h-6],["L",t,e+s]);else if(a>0){var d=h<e+n,c=d?e:e+o,p=d?1:6;l.splice(p,0,["L",a,h],["L",t+s,c])}else l.splice(7,1,["L",t,o/2],["L",a,h],["L",t,o/2],["L",t,e+s])}else h>o&&a<i-n?l.splice(5,1,["L",a+6,e+o],["L",a,e+o+6],["L",a-6,e+o],["L",t+s,e+o]):h<0&&a>n&&l.splice(1,1,["L",a-6,e],["L",a,e-6],["L",a+6,e],["L",i-s,e]);return l},circle:function(t,e,i,o){return iD(t+i/2,e+o/2,i/2,o/2,{start:.5*Math.PI,end:2.5*Math.PI,open:!1})},diamond:function(t,e,i,o){return[["M",t+i/2,e],["L",t+i,e+o/2],["L",t+i/2,e+o],["L",t,e+o/2],["Z"]]},rect:iI,roundedRect:iB,square:iI,triangle:function(t,e,i,o){return[["M",t+i/2,e],["L",t+i,e+o],["L",t,e+o],["Z"]]},"triangle-down":function(t,e,i,o){return[["M",t,e],["L",t+i,e],["L",t+i/2,e+o],["Z"]]}},iR=tt.doc,iN=tt.SVG_NS,iW=tt.win,iX=tT.attr,iH=tT.extend,iF=tT.fireEvent,ij=tT.isString,iY=tT.objectEach,iG=tT.pick,i_=function(t,e){return t.substring(0,e)+"…"},iU=function(){function t(t){var e=t.styles;this.renderer=t.renderer,this.svgElement=t,this.width=t.textWidth,this.textLineHeight=null==e?void 0:e.lineHeight,this.textOutline=null==e?void 0:e.textOutline,this.ellipsis=(null==e?void 0:e.textOverflow)==="ellipsis",this.lineClamp=null==e?void 0:e.lineClamp,this.noWrap=(null==e?void 0:e.whiteSpace)==="nowrap"}return t.prototype.buildSVG=function(){var t=this.svgElement,e=t.element,i=t.renderer,o=iG(t.textStr,"").toString(),r=-1!==o.indexOf("<"),s=e.childNodes,n=!t.added&&i.box,a=[o,this.ellipsis,this.noWrap,this.textLineHeight,this.textOutline,t.getStyle("font-size"),t.styles.lineClamp,this.width].join(",");if(a!==t.textCache){t.textCache=a,delete t.actualWidth;for(var h=s.length;h--;)e.removeChild(s[h]);if(r||this.ellipsis||this.width||t.textPath||-1!==o.indexOf(" ")&&(!this.noWrap||/<br.*?>/g.test(o))){if(""!==o){n&&n.appendChild(e);var l=new eL(o);this.modifyTree(l.nodes),l.addToDOM(e),this.modifyDOM(),this.ellipsis&&-1!==(e.textContent||"").indexOf("…")&&t.attr("title",this.unescapeEntities(t.textStr||"",["&lt;","&gt;"])),n&&n.removeChild(e)}}else e.appendChild(iR.createTextNode(this.unescapeEntities(o)));ij(this.textOutline)&&t.applyTextOutline&&t.applyTextOutline(this.textOutline)}},t.prototype.modifyDOM=function(){var t,e=this,i=this.svgElement,o=iX(i.element,"x");for(i.firstLineMetrics=void 0;t=i.element.firstChild;)if(/^[\s\u200B]*$/.test(t.textContent||" "))i.element.removeChild(t);else break;[].forEach.call(i.element.querySelectorAll("tspan.highcharts-br"),function(t,r){t.nextSibling&&t.previousSibling&&(0===r&&1===t.previousSibling.nodeType&&(i.firstLineMetrics=i.renderer.fontMetrics(t.previousSibling)),iX(t,{dy:e.getLineHeight(t.nextSibling),x:o}))});var r=this.width||0;if(r){var s=function(t,s){var n,a=t.textContent||"",h=a.replace(/([^\^])-/g,"$1- ").split(" "),l=!e.noWrap&&(h.length>1||i.element.childNodes.length>1),d=e.getLineHeight(s),c=Math.max(0,r-.8*d),p=0,u=i.actualWidth;if(l){for(var f=[],g=[];s.firstChild&&s.firstChild!==t;)g.push(s.firstChild),s.removeChild(s.firstChild);for(;h.length;)if(h.length&&!e.noWrap&&p>0&&(f.push(t.textContent||""),t.textContent=h.join(" ").replace(/- /g,"-")),e.truncate(t,void 0,h,0===p&&u||0,r,c,function(t,e){return h.slice(0,e).join(" ").replace(/- /g,"-")}),u=i.actualWidth,p++,e.lineClamp&&p>=e.lineClamp){h.length&&(e.truncate(t,t.textContent||"",void 0,0,r,c,i_),t.textContent=(null===(n=t.textContent)||void 0===n?void 0:n.replace("…",""))+"…");break}g.forEach(function(e){s.insertBefore(e,t)}),f.forEach(function(e){s.insertBefore(iR.createTextNode(e),t);var i=iR.createElementNS(iN,"tspan");i.textContent="​",iX(i,{dy:d,x:o}),s.insertBefore(i,t)})}else e.ellipsis&&a&&e.truncate(t,a,void 0,0,r,c,i_)},n=function(t){[].slice.call(t.childNodes).forEach(function(e){e.nodeType===iW.Node.TEXT_NODE?s(e,t):(-1!==e.className.baseVal.indexOf("highcharts-br")&&(i.actualWidth=0),n(e))})};n(i.element)}},t.prototype.getLineHeight=function(t){var e=t.nodeType===iW.Node.TEXT_NODE?t.parentElement:t;return this.textLineHeight?parseInt(this.textLineHeight.toString(),10):this.renderer.fontMetrics(e||this.svgElement.element).h},t.prototype.modifyTree=function(t){var e=this,i=function(o,r){var s=o.attributes,n=void 0===s?{}:s,a=o.children,h=o.style,l=void 0===h?{}:h,d=o.tagName,c=e.renderer.styledMode;if("b"===d||"strong"===d?c?n.class="highcharts-strong":l.fontWeight="bold":("i"===d||"em"===d)&&(c?n.class="highcharts-emphasized":l.fontStyle="italic"),(null==l?void 0:l.color)&&(l.fill=l.color),"br"===d){n.class="highcharts-br",o.textContent="​";var p=t[r+1];(null==p?void 0:p.textContent)&&(p.textContent=p.textContent.replace(/^ +/gm,""))}else"a"===d&&a&&a.some(function(t){return"#text"===t.tagName})&&(o.children=[{children:a,tagName:"tspan"}]);"#text"!==d&&"a"!==d&&(o.tagName="tspan"),iH(o,{attributes:n,style:l}),a&&a.filter(function(t){return"#text"!==t.tagName}).forEach(i)};t.forEach(i),iF(this.svgElement,"afterModifyTree",{nodes:t})},t.prototype.truncate=function(t,e,i,o,r,s,n){var a,h,l=this.svgElement,d=l.rotation,c=[],p=i&&!o?1:0,u=(e||i||"").length,f=u;i||(r=s);var g=function(e,r){var s=r||e,n=t.parentNode;if(n&&void 0===c[s]&&n.getSubStringLength)try{c[s]=o+n.getSubStringLength(0,i?s+1:s)}catch(t){}return c[s]};if(l.rotation=0,o+(h=g(t.textContent.length))>r){for(;p<=u;)f=Math.ceil((p+u)/2),i&&(a=n(i,f)),h=g(f,a&&a.length-1),p===u?p=u+1:h>r?u=f-1:p=f;0===u?t.textContent="":e&&u===e.length-1||(t.textContent=a||n(e||i,f)),this.ellipsis&&h>r&&this.truncate(t,t.textContent||"",void 0,0,r,s,i_)}i&&i.splice(0,f),l.actualWidth=h,l.rotation=d},t.prototype.unescapeEntities=function(t,e){return iY(this.renderer.escapes,function(i,o){e&&-1!==e.indexOf(i)||(t=t.toString().replace(RegExp(i,"g"),o))}),t},t}(),iV=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,s=e.length;r<s;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},iZ=tt.charts,iq=tt.deg2rad,iK=tt.doc,i$=tt.isFirefox,iJ=tt.isMS,iQ=tt.isWebKit,i0=tt.noop,i1=tt.SVG_NS,i2=tt.symbolSizes,i3=tt.win,i5=tT.addEvent,i6=tT.attr,i9=tT.createElement,i4=tT.crisp,i8=tT.css,i7=tT.defined,ot=tT.destroyObjectProperties,oe=tT.extend,oi=tT.isArray,oo=tT.isNumber,or=tT.isObject,os=tT.isString,on=tT.merge,oa=tT.pick,oh=tT.pInt,ol=tT.replaceNested,od=tT.uniqueKey,oc=function(){function t(t,e,i,o,r,s,n){this.x=0,this.y=0;var a,h,l=this.createElement("svg").attr({version:"1.1",class:"highcharts-root"}),d=l.element;n||l.css(this.getStyle(o||{})),t.appendChild(d),i6(t,"dir","ltr"),-1===t.innerHTML.indexOf("xmlns")&&i6(d,"xmlns",this.SVG_NS),this.box=d,this.boxWrapper=l,this.alignedObjects=[],this.url=this.getReferenceURL(),this.createElement("desc").add().element.appendChild(iK.createTextNode("Created with Highcharts 12.2.0")),this.defs=this.createElement("defs").add(),this.allowHTML=s,this.forExport=r,this.styledMode=n,this.gradients={},this.cache={},this.cacheKeys=[],this.imgCount=0,this.rootFontSize=l.getStyle("font-size"),this.setSize(e,i,!1),i$&&t.getBoundingClientRect&&((a=function(){i8(t,{left:0,top:0}),h=t.getBoundingClientRect(),i8(t,{left:Math.ceil(h.left)-h.left+"px",top:Math.ceil(h.top)-h.top+"px"})})(),this.unSubPixelFix=i5(i3,"resize",a))}return t.prototype.definition=function(t){return new eL([t]).addToDOM(this.defs.element)},t.prototype.getReferenceURL=function(){if((i$||iQ)&&iK.getElementsByTagName("base").length){if(!i7(B)){var t=od(),e=new eL([{tagName:"svg",attributes:{width:8,height:8},children:[{tagName:"defs",children:[{tagName:"clipPath",attributes:{id:t},children:[{tagName:"rect",attributes:{width:4,height:4}}]}]},{tagName:"rect",attributes:{id:"hitme",width:8,height:8,"clip-path":"url(#".concat(t,")"),fill:"rgba(0,0,0,0.001)"}}]}]).addToDOM(iK.body);i8(e,{position:"fixed",top:0,left:0,zIndex:9e5});var i=iK.elementFromPoint(6,6);B=(null==i?void 0:i.id)==="hitme",iK.body.removeChild(e)}if(B)return ol(i3.location.href.split("#")[0],[/<[^>]*>/g,""],[/([\('\)])/g,"\\$1"],[/ /g,"%20"])}return""},t.prototype.getStyle=function(t){return this.style=oe({fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", sans-serif',fontSize:"1rem"},t),this.style},t.prototype.setStyle=function(t){this.boxWrapper.css(this.getStyle(t))},t.prototype.isHidden=function(){return!this.boxWrapper.getBBox().width},t.prototype.destroy=function(){var t=this.defs;return this.box=null,this.boxWrapper=this.boxWrapper.destroy(),ot(this.gradients||{}),this.gradients=null,this.defs=t.destroy(),this.unSubPixelFix&&this.unSubPixelFix(),this.alignedObjects=null,null},t.prototype.createElement=function(t){return new this.Element(this,t)},t.prototype.getRadialAttr=function(t,e){return{cx:t[0]-t[2]/2+(e.cx||0)*t[2],cy:t[1]-t[2]/2+(e.cy||0)*t[2],r:(e.r||0)*t[2]}},t.prototype.shadowDefinition=function(t){var e=iV(["highcharts-drop-shadow-".concat(this.chartIndex)],Object.keys(t).map(function(e){return""+e+"-".concat(t[e])}),!0).join("-").toLowerCase().replace(/[^a-z\d\-]/g,""),i=on({color:"#000000",offsetX:1,offsetY:1,opacity:.15,width:5},t);return this.defs.element.querySelector("#".concat(e))||this.definition({tagName:"filter",attributes:{id:e,filterUnits:i.filterUnits},children:this.getShadowFilterContent(i)}),e},t.prototype.getShadowFilterContent=function(t){return[{tagName:"feDropShadow",attributes:{dx:t.offsetX,dy:t.offsetY,"flood-color":t.color,"flood-opacity":Math.min(5*t.opacity,1),stdDeviation:t.width/2}}]},t.prototype.buildText=function(t){new iU(t).buildSVG()},t.prototype.getContrast=function(t){var e=t7.parse(t).rgba,i=" clamp(0,calc(9e9*(0.5 - (0.2126*r + 0.7152*g + 0.0722*b))),1)";if(oo(e[0])||!t7.useColorMix){var o=e.map(function(t){var e=t/255;return e<=.04?e/12.92:Math.pow((e+.055)/1.055,2.4)}),r=.2126*o[0]+.7152*o[1]+.0722*o[2];return 1.05/(r+.05)>(r+.05)/.05?"#FFFFFF":"#000000"}return"color(from "+t+" srgb"+i+i+i+")"},t.prototype.button=function(t,e,i,o,r,s,n,a,h,l){void 0===r&&(r={});var d=this.label(t,e,i,h,void 0,void 0,l,void 0,"button"),c=this.styledMode,p=arguments,u=0;r=on(tJ.global.buttonTheme,r),c&&(delete r.fill,delete r.stroke,delete r["stroke-width"]);var f=r.states||{},g=r.style||{};delete r.states,delete r.style;var v=[eL.filterUserAttributes(r)],m=[g];return c||["hover","select","disabled"].forEach(function(t,e){v.push(on(v[0],eL.filterUserAttributes(p[e+5]||f[t]||{}))),m.push(v[e+1].style),delete v[e+1].style}),i5(d.element,iJ?"mouseover":"mouseenter",function(){3!==u&&d.setState(1)}),i5(d.element,iJ?"mouseout":"mouseleave",function(){3!==u&&d.setState(u)}),d.setState=function(t){if(void 0===t&&(t=0),1!==t&&(d.state=u=t),d.removeClass(/highcharts-button-(normal|hover|pressed|disabled)/).addClass("highcharts-button-"+["normal","hover","pressed","disabled"][t]),!c){d.attr(v[t]);var e=m[t];or(e)&&d.css(e)}},d.attr(v[0]),!c&&(d.css(oe({cursor:"default"},g)),l&&d.text.css({pointerEvents:"none"})),d.on("touchstart",function(t){return t.stopPropagation()}).on("click",function(t){3!==u&&(null==o||o.call(d,t))})},t.prototype.crispLine=function(t,e){var i=t[0],o=t[1];return i7(i[1])&&i[1]===o[1]&&(i[1]=o[1]=i4(i[1],e)),i7(i[2])&&i[2]===o[2]&&(i[2]=o[2]=i4(i[2],e)),t},t.prototype.path=function(t){var e=this.styledMode?{}:{fill:"none"};return oi(t)?e.d=t:or(t)&&oe(e,t),this.createElement("path").attr(e)},t.prototype.circle=function(t,e,i){var o=or(t)?t:void 0===t?{}:{x:t,y:e,r:i},r=this.createElement("circle");return r.xSetter=r.ySetter=function(t,e,i){i.setAttribute("c"+e,t)},r.attr(o)},t.prototype.arc=function(t,e,i,o,r,s){or(t)?(e=(n=t).y,i=n.r,o=n.innerR,r=n.start,s=n.end,t=n.x):n={innerR:o,start:r,end:s};var n,a=this.symbol("arc",t,e,i,i,n);return a.r=i,a},t.prototype.rect=function(t,e,i,o,r,s){var n=or(t)?t:void 0===t?{}:{x:t,y:e,r:r,width:Math.max(i||0,0),height:Math.max(o||0,0)},a=this.createElement("rect");return this.styledMode||(void 0!==s&&(n["stroke-width"]=s,oe(n,a.crisp(n))),n.fill="none"),a.rSetter=function(t,e,i){a.r=t,i6(i,{rx:t,ry:t})},a.rGetter=function(){return a.r||0},a.attr(n)},t.prototype.roundedRect=function(t){return this.symbol("roundedRect").attr(t)},t.prototype.setSize=function(t,e,i){this.width=t,this.height=e,this.boxWrapper.animate({width:t,height:e},{step:function(){this.attr({viewBox:"0 0 "+this.attr("width")+" "+this.attr("height")})},duration:oa(i,!0)?void 0:0}),this.alignElements()},t.prototype.g=function(t){var e=this.createElement("g");return t?e.attr({class:"highcharts-"+t}):e},t.prototype.image=function(t,e,i,o,r,s){var n={preserveAspectRatio:"none"};oo(e)&&(n.x=e),oo(i)&&(n.y=i),oo(o)&&(n.width=o),oo(r)&&(n.height=r);var a=this.createElement("image").attr(n),h=function(e){a.attr({href:t}),s.call(a,e)};if(s){a.attr({href:"data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="});var l=new i3.Image;i5(l,"load",h),l.src=t,l.complete&&h({})}else a.attr({href:t});return a},t.prototype.symbol=function(t,e,i,o,r,s){var n,a,h,l,d,c,p=this,u=/^url\((.*?)\)$/,f=u.test(t),g=!f&&(this.symbols[t]?t:"circle"),v=g&&this.symbols[g];if(v)"number"==typeof e&&(l=v.call(this.symbols,e||0,i||0,o||0,r||0,s)),h=this.path(l),p.styledMode||h.attr("fill","none"),oe(h,{symbolName:g||void 0,x:e,y:i,width:o,height:r}),s&&oe(h,s);else if(f){d=t.match(u)[1];var m=h=this.image(d);m.imgwidth=oa(null==s?void 0:s.width,null===(n=i2[d])||void 0===n?void 0:n.width),m.imgheight=oa(null==s?void 0:s.height,null===(a=i2[d])||void 0===a?void 0:a.height),c=function(t){return t.attr({width:t.width,height:t.height})},["width","height"].forEach(function(t){m[""+t+"Setter"]=function(t,e){this[e]=t;var i=this.alignByTranslate,o=this.element,r=this.width,n=this.height,a=this.imgwidth,h=this.imgheight,l="width"===e?a:h,d=1;s&&"within"===s.backgroundSize&&r&&n&&a&&h?(d=Math.min(r/a,n/h),i6(o,{width:Math.round(a*d),height:Math.round(h*d)})):o&&l&&o.setAttribute(e,l),!i&&a&&h&&this.translate(((r||0)-a*d)/2,((n||0)-h*d)/2)}}),i7(e)&&m.attr({x:e,y:i}),m.isImg=!0,m.symbolUrl=t,i7(m.imgwidth)&&i7(m.imgheight)?c(m):(m.attr({width:0,height:0}),i9("img",{onload:function(){var t=iZ[p.chartIndex];0===this.width&&(i8(this,{position:"absolute",top:"-999em"}),iK.body.appendChild(this)),i2[d]={width:this.width,height:this.height},m.imgwidth=this.width,m.imgheight=this.height,m.element&&c(m),this.parentNode&&this.parentNode.removeChild(this),p.imgCount--,p.imgCount||!t||t.hasLoaded||t.onload()},src:d}),this.imgCount++)}return h},t.prototype.clipRect=function(t,e,i,o){return this.rect(t,e,i,o,0)},t.prototype.text=function(t,e,i,o){var r={};if(o&&(this.allowHTML||!this.forExport))return this.html(t,e,i);r.x=Math.round(e||0),i&&(r.y=Math.round(i)),i7(t)&&(r.text=t);var s=this.createElement("text").attr(r);return o&&(!this.forExport||this.allowHTML)||(s.xSetter=function(t,e,i){for(var o=i.getElementsByTagName("tspan"),r=i.getAttribute(e),s=0,n=void 0;s<o.length;s++)(n=o[s]).getAttribute(e)===r&&n.setAttribute(e,t);i.setAttribute(e,t)}),s},t.prototype.fontMetrics=function(t){var e=oh(ix.prototype.getStyle.call(t,"font-size")||0),i=e<24?e+3:Math.round(1.2*e),o=Math.round(.8*i);return{h:i,b:o,f:e}},t.prototype.rotCorr=function(t,e,i){var o=t;return e&&i&&(o=Math.max(o*Math.cos(e*iq),4)),{x:-t/3*Math.sin(e*iq),y:o}},t.prototype.pathToSegments=function(t){for(var e=[],i=[],o={A:8,C:7,H:2,L:3,M:3,Q:5,S:5,T:3,V:2},r=0;r<t.length;r++)os(i[0])&&oo(t[r])&&i.length===o[i[0].toUpperCase()]&&t.splice(r,0,i[0].replace("M","L").replace("m","l")),"string"==typeof t[r]&&(i.length&&e.push(i.slice(0)),i.length=0),i.push(t[r]);return e.push(i.slice(0)),e},t.prototype.label=function(t,e,i,o,r,s,n,a,h){return new iO(this,t,e,i,o,r,s,n,a,h)},t.prototype.alignElements=function(){this.alignedObjects.forEach(function(t){return t.align()})},t}();oe(oc.prototype,{Element:ix,SVG_NS:i1,escapes:{"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"},symbols:iz,draw:i0}),eU.registerRendererType("svg",oc,!0);var op=(p=function(t,e){return(p=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}p(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),ou=function(){return(ou=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},of=tt.composed,og=tt.isFirefox,ov=tT.attr,om=tT.css,oy=tT.createElement,ox=tT.defined,ob=tT.extend,ok=tT.getAlignFactor,oM=tT.isNumber,ow=tT.pInt,oS=tT.pushUnique;function oA(t,e,i){var o,r=(null===(o=this.div)||void 0===o?void 0:o.style)||i.style;ix.prototype[""+e+"Setter"].call(this,t,e,i),r&&(r[e]=t)}var oT=function(t,e){var i;if(!t.div){var o=ov(t.element,"class"),r=t.css,s=oy("div",o?{className:o}:void 0,ou(ou({position:"absolute",left:""+(t.translateX||0)+"px",top:""+(t.translateY||0)+"px"},t.styles),{display:t.display,opacity:t.opacity,visibility:t.visibility}),(null===(i=t.parentGroup)||void 0===i?void 0:i.div)||e);t.classSetter=function(t,e,i){i.setAttribute("class",t),s.className=t},t.translateXSetter=t.translateYSetter=function(e,i){t[i]=e,s.style["translateX"===i?"left":"top"]=""+e+"px",t.doTransform=!0},t.opacitySetter=t.visibilitySetter=oA,t.css=function(e){return r.call(t,e),e.cursor&&(s.style.cursor=e.cursor),e.pointerEvents&&(s.style.pointerEvents=e.pointerEvents),t},t.on=function(){return ix.prototype.on.apply({element:s,onEvents:t.onEvents},arguments),t},t.div=s}return t.div},oC=function(t){function e(i,o){var r=t.call(this,i,o)||this;return e.useForeignObject?r.foreignObject=i.createElement("foreignObject").attr({zIndex:2}):r.css(ou({position:"absolute"},i.styledMode?{}:{fontFamily:i.style.fontFamily,fontSize:i.style.fontSize})),r.element.style.whiteSpace="nowrap",r}return op(e,t),e.compose=function(t){oS(of,this.compose)&&(t.prototype.html=function(t,i,o){return new e(this,"span").attr({text:t,x:Math.round(i),y:Math.round(o)})})},e.prototype.getSpanCorrection=function(t,e,i){this.xCorr=-t*i,this.yCorr=-e},e.prototype.css=function(t){var e,i=this.element,o="SPAN"===i.tagName&&t&&"width"in t,r=o&&t.width;return o&&(delete t.width,this.textWidth=ow(r)||void 0,e=!0),(null==t?void 0:t.textOverflow)==="ellipsis"&&(t.overflow="hidden",t.whiteSpace="nowrap"),(null==t?void 0:t.lineClamp)&&(t.display="-webkit-box",t.WebkitLineClamp=t.lineClamp,t.WebkitBoxOrient="vertical",t.overflow="hidden"),oM(Number(null==t?void 0:t.fontSize))&&(t.fontSize+="px"),ob(this.styles,t),om(i,t),e&&this.updateTransform(),this},e.prototype.htmlGetBBox=function(){var t=this.element;return{x:t.offsetLeft,y:t.offsetTop,width:t.offsetWidth,height:t.offsetHeight}},e.prototype.updateTransform=function(){if(!this.added){this.alignOnAdd=!0;return}var e,i=this.element,o=this.foreignObject,r=this.oldTextWidth,s=this.renderer,n=this.rotation,a=this.rotationOriginX,h=this.rotationOriginY,l=this.scaleX,d=this.scaleY,c=this.styles,p=c.display,u=void 0===p?"inline-block":p,f=c.whiteSpace,g=this.textAlign,v=void 0===g?"left":g,m=this.textWidth,y=this.translateX,x=this.translateY,b=this.x,k=void 0===b?0:b,M=this.y,w=void 0===M?0:M;if(o||om(i,{marginLeft:""+(void 0===y?0:y)+"px",marginTop:""+(void 0===x?0:x)+"px"}),"SPAN"===i.tagName){var S=[n,v,i.innerHTML,m,this.textAlign].join(","),A=-((null===(e=this.parentGroup)||void 0===e?void 0:e.padding)*1)||0,T=void 0;if(m!==r){var C=this.textPxLength?this.textPxLength:(om(i,{width:"",whiteSpace:f||"nowrap"}),i.offsetWidth),O=m||0,P=""===i.style.textOverflow&&i.style.webkitLineClamp;(O>r||C>O||P)&&(/[\-\s\u00AD]/.test(i.textContent||i.innerText)||"ellipsis"===i.style.textOverflow)&&(om(i,{width:(n||l||C>O||P)&&oM(m)?m+"px":"auto",display:u,whiteSpace:f||"normal"}),this.oldTextWidth=m)}o&&(om(i,{display:"inline-block",verticalAlign:"top"}),o.attr({width:s.width,height:s.height})),S!==this.cTT&&(T=s.fontMetrics(i).b,ox(n)&&!o&&(n!==(this.oldRotation||0)||v!==this.oldAlign)&&om(i,{transform:"rotate(".concat(n,"deg)"),transformOrigin:""+A+"% "+A+"px"}),this.getSpanCorrection(!ox(n)&&!this.textWidth&&this.textPxLength||i.offsetWidth,T,ok(v)));var E=this.xCorr,L=void 0===E?0:E,D=this.yCorr,I=void 0===D?0:D,B=(null!=a?a:k)-L-k-A,z=(null!=h?h:w)-I-w-A,R={left:""+(k+L)+"px",top:""+(w+I)+"px",textAlign:v,transformOrigin:""+B+"px "+z+"px"};(l||d)&&(R.transform="scale(".concat(null!=l?l:1,",").concat(null!=d?d:1,")")),o?(t.prototype.updateTransform.call(this),oM(k)&&oM(w)?(o.attr({x:k+L,y:w+I,width:i.offsetWidth+3,height:i.offsetHeight,"transform-origin":i.getAttribute("transform-origin")||"0 0"}),om(i,{display:u,textAlign:v})):og&&o.attr({width:0,height:0})):om(i,R),this.cTT=S,this.oldRotation=n,this.oldAlign=v}},e.prototype.add=function(e){var i=this.foreignObject,o=this.renderer,r=o.box.parentNode,s=[];if(i)i.add(e),t.prototype.add.call(this,o.createElement("body").attr({xmlns:"http://www.w3.org/1999/xhtml"}).css({background:"transparent",margin:"0 3px 0 0"}).add(i));else{var n=void 0;if(this.parentGroup=e,e&&!(n=e.div)){for(var a=e;a;)s.push(a),a=a.parentGroup;for(var h=0,l=s.reverse();h<l.length;h++)n=oT(l[h],r)}(n||r).appendChild(this.element)}return this.added=!0,this.alignOnAdd&&this.updateTransform(),this},e.prototype.textSetter=function(t){t!==this.textStr&&(delete this.bBox,delete this.oldTextWidth,eL.setElementHTML(this.element,null!=t?t:""),this.textStr=t,this.doTransform=!0)},e.prototype.alignSetter=function(t){this.alignValue=this.textAlign=t,this.doTransform=!0},e.prototype.xSetter=function(t,e){this[e]=t,this.doTransform=!0},e}(ix),oO=oC.prototype;oO.visibilitySetter=oO.opacitySetter=oA,oO.ySetter=oO.rotationSetter=oO.rotationOriginXSetter=oO.rotationOriginYSetter=oO.xSetter,(u=z||(z={})).xAxis={alignTicks:!0,allowDecimals:void 0,panningEnabled:!0,zIndex:2,zoomEnabled:!0,dateTimeLabelFormats:{millisecond:{main:"%[HMSL]",range:!1},second:{main:"%[HMS]",range:!1},minute:{main:"%[HM]",range:!1},hour:{main:"%[HM]",range:!1},day:{main:"%[eb]"},week:{main:"%[eb]"},month:{main:"%[bY]"},year:{main:"%Y"}},endOnTick:!1,gridLineDashStyle:"Solid",gridZIndex:1,labels:{autoRotationLimit:80,distance:15,enabled:!0,indentation:10,overflow:"justify",reserveSpace:void 0,rotation:void 0,staggerLines:0,step:0,useHTML:!1,zIndex:7,style:{color:"#333333",cursor:"default",fontSize:"0.8em",textOverflow:"ellipsis"}},maxPadding:.01,minorGridLineDashStyle:"Solid",minorTickLength:2,minorTickPosition:"outside",minorTicksPerMajor:5,minPadding:.01,offset:void 0,reversed:void 0,reversedStacks:!1,showEmpty:!0,showFirstLabel:!0,showLastLabel:!0,startOfWeek:1,startOnTick:!1,tickLength:10,tickPixelInterval:100,tickmarkPlacement:"between",tickPosition:"outside",title:{align:"middle",useHTML:!1,x:0,y:0,style:{color:"#666666",fontSize:"0.8em"}},visible:!0,minorGridLineColor:"#f2f2f2",minorGridLineWidth:1,minorTickColor:"#999999",lineColor:"#333333",lineWidth:1,gridLineColor:"#e6e6e6",gridLineWidth:void 0,tickColor:"#333333"},u.yAxis={reversedStacks:!0,endOnTick:!0,maxPadding:.05,minPadding:.05,tickPixelInterval:72,showLastLabel:!0,labels:{x:void 0},startOnTick:!0,title:{},stackLabels:{animation:{},allowOverlap:!1,enabled:!1,crop:!0,overflow:"justify",formatter:function(){return(0,this.axis.chart.numberFormatter)(this.total||0,-1)},style:{color:"#000000",fontSize:"0.7em",fontWeight:"bold",textOutline:"1px contrast"}},gridLineWidth:1,lineWidth:0};var oP=z,oE=tT.addEvent,oL=tT.isFunction,oD=tT.objectEach,oI=tT.removeEvent;(R||(R={})).registerEventOptions=function(t,e){t.eventOptions=t.eventOptions||{},oD(e.events,function(e,i){t.eventOptions[i]!==e&&(t.eventOptions[i]&&(oI(t,i,t.eventOptions[i]),delete t.eventOptions[i]),oL(e)&&(t.eventOptions[i]=e,oE(t,i,e,{order:0})))})};var oB=R,oz=tt.deg2rad,oR=tT.clamp,oN=tT.correctFloat,oW=tT.defined,oX=tT.destroyObjectProperties,oH=tT.extend,oF=tT.fireEvent,oj=tT.getAlignFactor,oY=tT.isNumber,oG=tT.merge,o_=tT.objectEach,oU=tT.pick,oV=function(){function t(t,e,i,o,r){this.isNew=!0,this.isNewLabel=!0,this.axis=t,this.pos=e,this.type=i||"",this.parameters=r||{},this.tickmarkOffset=this.parameters.tickmarkOffset,this.options=this.parameters.options,oF(this,"init"),i||o||this.addLabel()}return t.prototype.addLabel=function(){var t,e,i,o,r=this,s=r.axis,n=s.options,a=s.chart,h=s.categories,l=s.logarithmic,d=s.names,c=r.pos,p=oU(null===(t=r.options)||void 0===t?void 0:t.labels,n.labels),u=s.tickPositions,f=c===u[0],g=c===u[u.length-1],v=(!p.step||1===p.step)&&1===s.tickInterval,m=u.info,y=r.label,x=this.parameters.category||(h?oU(h[c],d[c],c):c);l&&oY(x)&&(x=oN(l.lin2log(x))),s.dateTime&&(m?e=(i=a.time.resolveDTLFormat(n.dateTimeLabelFormats[!n.grid&&m.higherRanks[c]||m.unitName])).main:oY(x)&&(e=s.dateTime.getXDateFormat(x,n.dateTimeLabelFormats||{}))),r.isFirst=f,r.isLast=g;var b={axis:s,chart:a,dateTimeLabelFormat:e,isFirst:f,isLast:g,pos:c,tick:r,tickPositionInfo:m,value:x};oF(this,"labelFormat",b);var k=function(t){return p.formatter?p.formatter.call(t,t):p.format?(t.text=s.defaultLabelFormatter.call(t),e_.format(p.format,t,a)):s.defaultLabelFormatter.call(t)},M=k.call(b,b),w=null==i?void 0:i.list;w?r.shortenLabel=function(){for(o=0;o<w.length;o++)if(oH(b,{dateTimeLabelFormat:w[o]}),y.attr({text:k.call(b,b)}),y.getBBox().width<s.getSlotWidth(r)-2*(p.padding||0))return;y.attr({text:""})}:r.shortenLabel=void 0,v&&s._addedPlotLB&&r.moveLabel(M,p),oW(y)||r.movedLabel?y&&y.textStr!==M&&!v&&(!y.textWidth||p.style.width||y.styles.width||y.css({width:null}),y.attr({text:M}),y.textPxLength=y.getBBox().width):(r.label=y=r.createLabel(M,p),r.rotation=0)},t.prototype.createLabel=function(t,e,i){var o=this.axis,r=o.chart,s=r.renderer,n=r.styledMode,a=e.style.whiteSpace,h=oW(t)&&e.enabled?s.text(t,null==i?void 0:i.x,null==i?void 0:i.y,e.useHTML).add(o.labelGroup):void 0;return h&&(n||h.css(oG(e.style)),h.textPxLength=h.getBBox().width,!n&&a&&h.css({whiteSpace:a})),h},t.prototype.destroy=function(){oX(this,this.axis)},t.prototype.getPosition=function(t,e,i,o){var r=this.axis,s=r.chart,n=o&&s.oldChartHeight||s.chartHeight,a={x:t?oN(r.translate(e+i,void 0,void 0,o)+r.transB):r.left+r.offset+(r.opposite?(o&&s.oldChartWidth||s.chartWidth)-r.right-r.left:0),y:t?n-r.bottom+r.offset-(r.opposite?r.height:0):oN(n-r.translate(e+i,void 0,void 0,o)-r.transB)};return a.y=oR(a.y,-1e9,1e9),oF(this,"afterGetPosition",{pos:a}),a},t.prototype.getLabelPosition=function(t,e,i,o,r,s,n,a){var h,l,d=this.axis,c=d.transA,p=d.isLinked&&d.linkedParent?d.linkedParent.reversed:d.reversed,u=d.staggerLines,f=d.tickRotCorr||{x:0,y:0},g=o||d.reserveSpaceDefault?0:-d.labelOffset*("center"===d.labelAlign?.5:1),v=r.distance,m={};return h=0===d.side?i.rotation?-v:-i.getBBox().height:2===d.side?f.y+v:Math.cos(i.rotation*oz)*(f.y-i.getBBox(!1,0).height/2),oW(r.y)&&(h=0===d.side&&d.horiz?r.y+h:r.y),t=t+oU(r.x,[0,1,0,-1][d.side]*v)+g+f.x-(s&&o?s*c*(p?-1:1):0),e=e+h-(s&&!o?s*c*(p?1:-1):0),u&&(l=n/(a||1)%u,d.opposite&&(l=u-l-1),e+=l*(d.labelOffset/u)),m.x=t,m.y=Math.round(e),oF(this,"afterGetLabelPosition",{pos:m,tickmarkOffset:s,index:n}),m},t.prototype.getLabelSize=function(){return this.label?this.label.getBBox()[this.axis.horiz?"height":"width"]:0},t.prototype.getMarkPath=function(t,e,i,o,r,s){return void 0===r&&(r=!1),s.crispLine([["M",t,e],["L",t+(r?0:-i),e+(r?i:0)]],o)},t.prototype.handleOverflow=function(t){var e,i,o=this.axis,r=o.options.labels,s=t.x,n=o.chart.chartWidth,a=o.chart.spacing,h=oU(o.labelLeft,Math.min(o.pos,a[3])),l=oU(o.labelRight,Math.max(o.isRadial?0:o.pos+o.len,n-a[1])),d=this.label,c=this.rotation,p=oj(o.labelAlign||d.attr("align")),u=d.getBBox().width,f=o.getSlotWidth(this),g=f,v=1;c||"justify"!==r.overflow?c<0&&s-p*u<h?i=Math.round(s/Math.cos(c*oz)-h):c>0&&s+p*u>l&&(i=Math.round((n-s)/Math.cos(c*oz))):(s-p*u<h?g=t.x+g*(1-p)-h:s+(1-p)*u>l&&(g=l-t.x+g*p,v=-1),(g=Math.min(f,g))<f&&"center"===o.labelAlign&&(t.x+=v*(f-g-p*(f-Math.min(u,g)))),(u>g||o.autoRotation&&(null===(e=null==d?void 0:d.styles)||void 0===e?void 0:e.width))&&(i=g)),i&&d&&(this.shortenLabel?this.shortenLabel():d.css(oH({},{width:Math.floor(i)+"px",lineClamp:+!o.isRadial})))},t.prototype.moveLabel=function(t,e){var i,o=this,r=o.label,s=o.axis,n=!1;r&&r.textStr===t?(o.movedLabel=r,n=!0,delete o.label):o_(s.ticks,function(e){n||e.isNew||e===o||!e.label||e.label.textStr!==t||(o.movedLabel=e.label,n=!0,e.labelPos=o.movedLabel.xy,delete e.label)}),!n&&(o.labelPos||r)&&(i=o.labelPos||r.xy,o.movedLabel=o.createLabel(t,e,i),o.movedLabel&&o.movedLabel.attr({opacity:0}))},t.prototype.render=function(t,e,i){var o,r=this.axis,s=r.horiz,n=this.pos,a=oU(this.tickmarkOffset,r.tickmarkOffset),h=this.getPosition(s,n,a,e),l=h.x,d=h.y,c=r.pos,p=c+r.len,u=s?l:d,f=oU(i,null===(o=this.label)||void 0===o?void 0:o.newOpacity,1);!r.chart.polar&&(oN(u)<c||u>p)&&(i=0),null!=i||(i=1),this.isActive=!0,this.renderGridLine(e,i),this.renderMark(h,i),this.renderLabel(h,e,f,t),this.isNew=!1,oF(this,"afterRender")},t.prototype.renderGridLine=function(t,e){var i,o=this.axis,r=o.options,s={},n=this.pos,a=this.type,h=oU(this.tickmarkOffset,o.tickmarkOffset),l=o.chart.renderer,d=this.gridLine,c=r.gridLineWidth,p=r.gridLineColor,u=r.gridLineDashStyle;"minor"===this.type&&(c=r.minorGridLineWidth,p=r.minorGridLineColor,u=r.minorGridLineDashStyle),d||(o.chart.styledMode||(s.stroke=p,s["stroke-width"]=c||0,s.dashstyle=u),a||(s.zIndex=1),t&&(e=0),this.gridLine=d=l.path().attr(s).addClass("highcharts-"+(a?a+"-":"")+"grid-line").add(o.gridGroup)),d&&(i=o.getPlotLinePath({value:n+h,lineWidth:d.strokeWidth(),force:"pass",old:t,acrossPanes:!1}))&&d[t||this.isNew?"attr":"animate"]({d:i,opacity:e})},t.prototype.renderMark=function(t,e){var i=this.axis,o=i.options,r=i.chart.renderer,s=this.type,n=i.tickSize(s?s+"Tick":"tick"),a=t.x,h=t.y,l=oU(o["minor"!==s?"tickWidth":"minorTickWidth"],!s&&i.isXAxis?1:0),d=o["minor"!==s?"tickColor":"minorTickColor"],c=this.mark,p=!c;n&&(i.opposite&&(n[0]=-n[0]),c||(this.mark=c=r.path().addClass("highcharts-"+(s?s+"-":"")+"tick").add(i.axisGroup),i.chart.styledMode||c.attr({stroke:d,"stroke-width":l})),c[p?"attr":"animate"]({d:this.getMarkPath(a,h,n[0],c.strokeWidth(),i.horiz,r),opacity:e}))},t.prototype.renderLabel=function(t,e,i,o){var r=this.axis,s=r.horiz,n=r.options,a=this.label,h=n.labels,l=h.step,d=oU(this.tickmarkOffset,r.tickmarkOffset),c=t.x,p=t.y,u=!0;a&&oY(c)&&(a.xy=t=this.getLabelPosition(c,p,a,s,h,d,o,l),(!this.isFirst||this.isLast||n.showFirstLabel)&&(!this.isLast||this.isFirst||n.showLastLabel)?!s||h.step||h.rotation||e||0===i||this.handleOverflow(t):u=!1,l&&o%l&&(u=!1),u&&oY(t.y)?(t.opacity=i,a[this.isNewLabel?"attr":"animate"](t).show(!0),this.isNewLabel=!1):(a.hide(),this.isNewLabel=!0))},t.prototype.replaceMovedLabel=function(){var t=this.label,e=this.axis;t&&!this.isNew&&(t.animate({opacity:0},void 0,t.destroy),delete this.label),e.isDirty=!0,this.label=this.movedLabel,delete this.movedLabel},t}(),oZ=oP.xAxis,oq=oP.yAxis,oK=oB.registerEventOptions,o$=tt.deg2rad,oJ=tT.arrayMax,oQ=tT.arrayMin,o0=tT.clamp,o1=tT.correctFloat,o2=tT.defined,o3=tT.destroyObjectProperties,o5=tT.erase,o6=tT.error,o9=tT.extend,o4=tT.fireEvent,o8=tT.getClosestDistance,o7=tT.insertItem,rt=tT.isArray,re=tT.isNumber,ri=tT.isString,ro=tT.merge,rr=tT.normalizeTickInterval,rs=tT.objectEach,rn=tT.pick,ra=tT.relativeLength,rh=tT.removeEvent,rl=tT.splat,rd=tT.syncTimeout,rc=function(t,e){return rr(e,void 0,void 0,rn(t.options.allowDecimals,e<.5||void 0!==t.tickAmount),!!t.tickAmount)};o9(tJ,{xAxis:oZ,yAxis:ro(oZ,oq)});var rp=function(){function t(t,e,i){this.init(t,e,i)}return t.prototype.init=function(t,e,i){void 0===i&&(i=this.coll);var o,r,s,n,a="xAxis"===i,h=this.isZAxis||(t.inverted?!a:a);this.chart=t,this.horiz=h,this.isXAxis=a,this.coll=i,o4(this,"init",{userOptions:e}),this.opposite=rn(e.opposite,this.opposite),this.side=rn(e.side,this.side,h?2*!this.opposite:this.opposite?1:3),this.setOptions(e);var l=this.options,d=l.labels;null!==(o=this.type)&&void 0!==o||(this.type=l.type||"linear"),null!==(r=this.uniqueNames)&&void 0!==r||(this.uniqueNames=null===(s=l.uniqueNames)||void 0===s||s),o4(this,"afterSetType"),this.userOptions=e,this.minPixelPadding=0,this.reversed=rn(l.reversed,this.reversed),this.visible=l.visible,this.zoomEnabled=l.zoomEnabled,this.hasNames="category"===this.type||!0===l.categories,this.categories=rt(l.categories)&&l.categories||(this.hasNames?[]:void 0),this.names||(this.names=[],this.names.keys={}),this.plotLinesAndBandsGroups={},this.positiveValuesOnly=!!this.logarithmic,this.isLinked=o2(l.linkedTo),this.ticks={},this.labelEdge=[],this.minorTicks={},this.plotLinesAndBands=[],this.alternateBands={},null!==(n=this.len)&&void 0!==n||(this.len=0),this.minRange=this.userMinRange=l.minRange||l.maxZoom,this.range=l.range,this.offset=l.offset||0,this.max=void 0,this.min=void 0;var c=rn(l.crosshair,rl(t.options.tooltip.crosshairs)[+!a]);this.crosshair=!0===c?{}:c,-1===t.axes.indexOf(this)&&(a?t.axes.splice(t.xAxis.length,0,this):t.axes.push(this),o7(this,t[this.coll])),t.orderItems(this.coll),this.series=this.series||[],t.inverted&&!this.isZAxis&&a&&!o2(this.reversed)&&(this.reversed=!0),this.labelRotation=re(d.rotation)?d.rotation:void 0,oK(this,l),o4(this,"afterInit")},t.prototype.setOptions=function(t){var e=this.horiz?{labels:{autoRotation:[-45],padding:3},margin:15}:{labels:{padding:1},title:{rotation:90*this.side}};this.options=ro(e,"yAxis"===this.coll?{title:{text:this.chart.options.lang.yAxisTitle}}:{},tJ[this.coll],t),o4(this,"afterSetOptions",{userOptions:t})},t.prototype.defaultLabelFormatter=function(){var t,e,i=this.axis,o=this.chart.numberFormatter,r=re(this.value)?this.value:NaN,s=i.chart.time,n=i.categories,a=this.dateTimeLabelFormat,h=tJ.lang,l=h.numericSymbols,d=h.numericSymbolMagnitude||1e3,c=i.logarithmic?Math.abs(r):i.tickInterval,p=null==l?void 0:l.length;if(n)e="".concat(this.value);else if(a)e=s.dateFormat(a,r,!0);else if(p&&l&&c>=1e3)for(;p--&&void 0===e;)c>=(t=Math.pow(d,p+1))&&10*r%t==0&&null!==l[p]&&0!==r&&(e=o(r/t,-1)+l[p]);return void 0===e&&(e=Math.abs(r)>=1e4?o(r,-1):o(r,-1,void 0,"")),e},t.prototype.getSeriesExtremes=function(){var t,e=this;o4(this,"getSeriesExtremes",null,function(){e.hasVisibleSeries=!1,e.dataMin=e.dataMax=e.threshold=void 0,e.softThreshold=!e.isXAxis,e.series.forEach(function(i){if(i.reserveSpace()){var o=i.options,r=void 0,s=o.threshold,n=void 0,a=void 0;if(e.hasVisibleSeries=!0,e.positiveValuesOnly&&0>=(s||0)&&(s=void 0),e.isXAxis)(r=i.getColumn("x")).length&&(r=e.logarithmic?r.filter(function(t){return t>0}):r,n=(t=i.getXExtremes(r)).min,a=t.max,re(n)||n instanceof Date||(r=r.filter(re),n=(t=i.getXExtremes(r)).min,a=t.max),r.length&&(e.dataMin=Math.min(rn(e.dataMin,n),n),e.dataMax=Math.max(rn(e.dataMax,a),a)));else{var h=i.applyExtremes();re(h.dataMin)&&(n=h.dataMin,e.dataMin=Math.min(rn(e.dataMin,n),n)),re(h.dataMax)&&(a=h.dataMax,e.dataMax=Math.max(rn(e.dataMax,a),a)),o2(s)&&(e.threshold=s),(!o.softThreshold||e.positiveValuesOnly)&&(e.softThreshold=!1)}}})}),o4(this,"afterGetSeriesExtremes")},t.prototype.translate=function(t,e,i,o,r,s){var n,a=this.linkedParent||this,h=o&&a.old?a.old.min:a.min;if(!re(h))return NaN;var l=a.minPixelPadding,d=(a.isOrdinal||(null===(n=a.brokenAxis)||void 0===n?void 0:n.hasBreaks)||a.logarithmic&&r)&&a.lin2val,c=1,p=0,u=o&&a.old?a.old.transA:a.transA,f=0;return u||(u=a.transA),i&&(c*=-1,p=a.len),a.reversed&&(c*=-1,p-=c*(a.sector||a.len)),e?(f=(t=t*c+p-l)/u+h,d&&(f=a.lin2val(f))):(d&&(t=a.val2lin(t)),f=c*(t-h)*u+p+c*l+(re(s)?u*s:0),a.isRadial||(f=o1(f))),f},t.prototype.toPixels=function(t,e){var i,o;return this.translate(null!==(o=null===(i=this.chart)||void 0===i?void 0:i.time.parse(t))&&void 0!==o?o:NaN,!1,!this.horiz,void 0,!0)+(e?0:this.pos)},t.prototype.toValue=function(t,e){return this.translate(t-(e?0:this.pos),!0,!this.horiz,void 0,!0)},t.prototype.getPlotLinePath=function(t){var e,i,o,r,s,n=this,a=n.chart,h=n.left,l=n.top,d=t.old,c=t.value,p=t.lineWidth,u=d&&a.oldChartHeight||a.chartHeight,f=d&&a.oldChartWidth||a.chartWidth,g=n.transB,v=t.translatedValue,m=t.force;function y(t,e,i){return"pass"!==m&&(t<e||t>i)&&(m?t=o0(t,e,i):s=!0),t}var x={value:c,lineWidth:p,old:d,force:m,acrossPanes:t.acrossPanes,translatedValue:v};return o4(this,"getPlotLinePath",x,function(t){e=o=(v=o0(v=rn(v,n.translate(c,void 0,void 0,d)),-1e9,1e9))+g,i=r=u-v-g,re(v)?n.horiz?(i=l,r=u-n.bottom+(n.options.isInternal?0:a.scrollablePixelsY||0),e=o=y(e,h,h+n.width)):(e=h,o=f-n.right+(a.scrollablePixelsX||0),i=r=y(i,l,l+n.height)):(s=!0,m=!1),t.path=s&&!m?void 0:a.renderer.crispLine([["M",e,i],["L",o,r]],p||1)}),x.path},t.prototype.getLinearTickPositions=function(t,e,i){var o,r,s,n=o1(Math.floor(e/t)*t),a=o1(Math.ceil(i/t)*t),h=[];if(o1(n+t)===n&&(s=20),this.single)return[e];for(o=n;o<=a&&(h.push(o),(o=o1(o+t,s))!==r);)r=o;return h},t.prototype.getMinorTickInterval=function(){var t=this.options,e=t.minorTicks,i=t.minorTickInterval;return!0===e?rn(i,"auto"):!1!==e?i:void 0},t.prototype.getMinorTickPositions=function(){var t,e,i=this.options,o=this.tickPositions,r=this.minorTickInterval,s=this.pointRangePadding||0,n=(this.min||0)-s,a=(this.max||0)+s,h=(null===(t=this.brokenAxis)||void 0===t?void 0:t.hasBreaks)?this.brokenAxis.unitLength:a-n,l=[];if(h&&h/r<this.len/3){var d=this.logarithmic;if(d)this.paddedTicks.forEach(function(t,e,i){e&&l.push.apply(l,d.getLogTickPositions(r,i[e-1],i[e],!0))});else if(this.dateTime&&"auto"===this.getMinorTickInterval())l=l.concat(this.getTimeTicks(this.dateTime.normalizeTimeTickInterval(r),n,a,i.startOfWeek));else for(e=n+(o[0]-n)%r;e<=a&&e!==l[0];e+=r)l.push(e)}return 0!==l.length&&this.trimTicks(l),l},t.prototype.adjustForMinRange=function(){var t,e,i,o,r,s,n,a=this.options,h=this.logarithmic,l=this.chart.time,d=this.max,c=this.min,p=this.minRange;this.isXAxis&&void 0===p&&!h&&(p=o2(a.min)||o2(a.max)||o2(a.floor)||o2(a.ceiling)?null:Math.min(5*(o8(this.series.map(function(t){var e=t.getColumn("x");return t.xIncrement?e.slice(0,2):e}))||0),this.dataMax-this.dataMin)),re(d)&&re(c)&&re(p)&&d-c<p&&(r=this.dataMax-this.dataMin>=p,o=(p-d+c)/2,s=[c-o,null!==(t=l.parse(a.min))&&void 0!==t?t:c-o],r&&(s[2]=h?h.log2lin(this.dataMin):this.dataMin),n=[(c=oJ(s))+p,null!==(e=l.parse(a.max))&&void 0!==e?e:c+p],r&&(n[2]=h?h.log2lin(this.dataMax):this.dataMax),(d=oQ(n))-c<p&&(s[0]=d-p,s[1]=null!==(i=l.parse(a.min))&&void 0!==i?i:d-p,c=oJ(s))),this.minRange=p,this.min=c,this.max=d},t.prototype.getClosest=function(){var t,e;if(this.categories)e=1;else{var i=[];this.series.forEach(function(t){var o=t.closestPointRange,r=t.getColumn("x");1===r.length?i.push(r[0]):t.sorted&&o2(o)&&t.reserveSpace()&&(e=o2(e)?Math.min(e,o):o)}),i.length&&(i.sort(function(t,e){return t-e}),t=o8([i]))}return t&&e?Math.min(t,e):t||e},t.prototype.nameToX=function(t){var e,i=rt(this.options.categories),o=i?this.categories:this.names,r=t.options.x;return t.series.requireSorting=!1,o2(r)||(r=this.uniqueNames&&o?i?o.indexOf(t.name):rn(o.keys[t.name],-1):t.series.autoIncrement()),-1===r?!i&&o&&(e=o.length):re(r)&&(e=r),void 0!==e?(this.names[e]=t.name,this.names.keys[t.name]=e):t.x&&(e=t.x),e},t.prototype.updateNames=function(){var t=this,e=this.names;e.length>0&&(Object.keys(e.keys).forEach(function(t){delete e.keys[t]}),e.length=0,this.minRange=this.userMinRange,(this.series||[]).forEach(function(e){e.xIncrement=null,(!e.points||e.isDirtyData)&&(t.max=Math.max(t.max||0,e.dataTable.rowCount-1),e.processData(),e.generatePoints());var i=e.getColumn("x").slice();e.data.forEach(function(e,o){var r=i[o];(null==e?void 0:e.options)&&void 0!==e.name&&void 0!==(r=t.nameToX(e))&&r!==e.x&&(i[o]=e.x=r)}),e.dataTable.setColumn("x",i)}))},t.prototype.setAxisTranslation=function(){var t,e,i,o=this,r=o.max-o.min,s=o.linkedParent,n=!!o.categories,a=o.isXAxis,h=o.axisPointRange||0,l=0,d=0,c=o.transA;(a||n||h)&&(e=o.getClosest(),s?(l=s.minPointOffset,d=s.pointRangePadding):o.series.forEach(function(t){var i=n?1:a?rn(t.options.pointRange,e,0):o.axisPointRange||0,r=t.options.pointPlacement;if(h=Math.max(h,i),!o.single||n){var s=t.is("xrange")?!a:a;l=Math.max(l,s&&ri(r)?0:i/2),d=Math.max(d,s&&"on"===r?0:i)}}),i=(null===(t=o.ordinal)||void 0===t?void 0:t.slope)&&e?o.ordinal.slope/e:1,o.minPointOffset=l*=i,o.pointRangePadding=d*=i,o.pointRange=Math.min(h,o.single&&n?1:r),a&&(o.closestPointRange=e)),o.translationSlope=o.transA=c=o.staticScale||o.len/(r+d||1),o.transB=o.horiz?o.left:o.bottom,o.minPixelPadding=c*l,o4(this,"afterSetAxisTranslation")},t.prototype.minFromRange=function(){var t=this.max,e=this.min;return re(t)&&re(e)&&t-e||void 0},t.prototype.setTickInterval=function(t){var e,i,o,r,s,n,a,h,l,d=this.categories,c=this.chart,p=this.dataMax,u=this.dataMin,f=this.dateTime,g=this.isXAxis,v=this.logarithmic,m=this.options,y=this.softThreshold,x=c.time,b=re(this.threshold)?this.threshold:void 0,k=this.minRange||0,M=m.ceiling,w=m.floor,S=m.linkedTo,A=m.softMax,T=m.softMin,C=re(S)&&(null===(e=c[this.coll])||void 0===e?void 0:e[S]),O=m.tickPixelInterval,P=m.maxPadding,E=m.minPadding,L=0,D=re(m.tickInterval)&&m.tickInterval>=0?m.tickInterval:void 0;if(f||d||C||this.getTickAmount(),h=rn(this.userMin,x.parse(m.min)),l=rn(this.userMax,x.parse(m.max)),C?(this.linkedParent=C,s=C.getExtremes(),this.min=rn(s.min,s.dataMin),this.max=rn(s.max,s.dataMax),this.type!==C.type&&o6(11,!0,c)):(y&&o2(b)&&re(p)&&re(u)&&(u>=b?(n=b,E=0):p<=b&&(a=b,P=0)),this.min=rn(h,n,u),this.max=rn(l,a,p)),re(this.max)&&re(this.min)&&(v&&(this.positiveValuesOnly&&!t&&0>=Math.min(this.min,rn(u,this.min))&&o6(10,!0,c),this.min=o1(v.log2lin(this.min),16),this.max=o1(v.log2lin(this.max),16)),this.range&&re(u)&&(this.userMin=this.min=h=Math.max(u,this.minFromRange()||0),this.userMax=l=this.max,this.range=void 0)),o4(this,"foundExtremes"),this.adjustForMinRange(),re(this.min)&&re(this.max)){if(!re(this.userMin)&&re(T)&&T<this.min&&(this.min=h=T),!re(this.userMax)&&re(A)&&A>this.max&&(this.max=l=A),d||this.axisPointRange||(null===(i=this.stacking)||void 0===i?void 0:i.usePercentage)||C||!(L=this.max-this.min)||(!o2(h)&&E&&(this.min-=L*E),o2(l)||!P||(this.max+=L*P)),!re(this.userMin)&&re(w)&&(this.min=Math.max(this.min,w)),!re(this.userMax)&&re(M)&&(this.max=Math.min(this.max,M)),y&&re(u)&&re(p)){var I=b||0;!o2(h)&&this.min<I&&u>=I?this.min=m.minRange?Math.min(I,this.max-k):I:!o2(l)&&this.max>I&&p<=I&&(this.max=m.minRange?Math.max(I,this.min+k):I)}!c.polar&&this.min>this.max&&(o2(m.min)?this.max=this.min:o2(m.max)&&(this.min=this.max)),L=this.max-this.min}if(this.min!==this.max&&re(this.min)&&re(this.max)?C&&!D&&O===C.options.tickPixelInterval?this.tickInterval=D=C.tickInterval:this.tickInterval=rn(D,this.tickAmount?L/Math.max(this.tickAmount-1,1):void 0,d?1:L*O/Math.max(this.len,O)):this.tickInterval=1,g&&!t){var B=this.min!==(null===(o=this.old)||void 0===o?void 0:o.min)||this.max!==(null===(r=this.old)||void 0===r?void 0:r.max);this.series.forEach(function(t){var e;t.forceCrop=null===(e=t.forceCropping)||void 0===e?void 0:e.call(t),t.processData(B)}),o4(this,"postProcessData",{hasExtremesChanged:B})}this.setAxisTranslation(),o4(this,"initialAxisTranslation"),this.pointRange&&!D&&(this.tickInterval=Math.max(this.pointRange,this.tickInterval));var z=rn(m.minTickInterval,f&&!this.series.some(function(t){return!t.sorted})?this.closestPointRange:0);!D&&z&&this.tickInterval<z&&(this.tickInterval=z),f||v||D||(this.tickInterval=rc(this,this.tickInterval)),this.tickAmount||(this.tickInterval=this.unsquish()),this.setTickPositions()},t.prototype.setTickPositions=function(){var t,e,i,o=this.options,r=o.tickPositions,s=o.tickPositioner,n=this.getMinorTickInterval(),a=!this.isPanning,h=a&&o.startOnTick,l=a&&o.endOnTick,d=[];if(this.tickmarkOffset=this.categories&&"between"===o.tickmarkPlacement&&1===this.tickInterval?.5:0,this.single=this.min===this.max&&o2(this.min)&&!this.tickAmount&&(this.min%1==0||!1!==o.allowDecimals),r)d=r.slice();else if(re(this.min)&&re(this.max)){if(!(null===(t=this.ordinal)||void 0===t?void 0:t.positions)&&(this.max-this.min)/this.tickInterval>Math.max(2*this.len,200))d=[this.min,this.max],o6(19,!1,this.chart);else if(this.dateTime)d=this.getTimeTicks(this.dateTime.normalizeTimeTickInterval(this.tickInterval,o.units),this.min,this.max,o.startOfWeek,null===(e=this.ordinal)||void 0===e?void 0:e.positions,this.closestPointRange,!0);else if(this.logarithmic)d=this.logarithmic.getLogTickPositions(this.tickInterval,this.min,this.max);else for(var c=this.tickInterval,p=c;p<=2*c;)if(d=this.getLinearTickPositions(this.tickInterval,this.min,this.max),this.tickAmount&&d.length>this.tickAmount)this.tickInterval=rc(this,p*=1.1);else break;d.length>this.len&&(d=[d[0],d[d.length-1]])[0]===d[1]&&(d.length=1),s&&(this.tickPositions=d,(i=s.apply(this,[this.min,this.max]))&&(d=i))}this.tickPositions=d,this.minorTickInterval="auto"===n&&this.tickInterval?this.tickInterval/o.minorTicksPerMajor:n,this.paddedTicks=d.slice(0),this.trimTicks(d,h,l),!this.isLinked&&re(this.min)&&re(this.max)&&(this.single&&d.length<2&&!this.categories&&!this.series.some(function(t){return t.is("heatmap")&&"between"===t.options.pointPlacement})&&(this.min-=.5,this.max+=.5),r||i||this.adjustTickAmount()),o4(this,"afterSetTickPositions")},t.prototype.trimTicks=function(t,e,i){var o=t[0],r=t[t.length-1],s=!this.isOrdinal&&this.minPointOffset||0;if(o4(this,"trimTicks"),!this.isLinked||!this.grid){if(e&&o!==-1/0)this.min=o;else for(;this.min-s>t[0];)t.shift();if(i)this.max=r;else for(;this.max+s<t[t.length-1];)t.pop();0===t.length&&o2(o)&&!this.options.tickPositions&&t.push((r+o)/2)}},t.prototype.alignToOthers=function(){var t,e=this,i=e.chart,o=[this],r=e.options,s=i.options.chart,n="yAxis"===this.coll&&s.alignThresholds,a=[];if(e.thresholdAlignment=void 0,(!1!==s.alignTicks&&r.alignTicks||n)&&!1!==r.startOnTick&&!1!==r.endOnTick&&!e.logarithmic){var h=function(t){var e=t.horiz,i=t.options;return[e?i.left:i.top,i.width,i.height,i.pane].join(",")},l=h(this);i[this.coll].forEach(function(i){var r=i.series;r.length&&r.some(function(t){return t.visible})&&i!==e&&h(i)===l&&(t=!0,o.push(i))})}if(t&&n){o.forEach(function(t){var i=t.getThresholdAlignment(e);re(i)&&a.push(i)});var d=a.length>1?a.reduce(function(t,e){return t+e},0)/a.length:void 0;o.forEach(function(t){t.thresholdAlignment=d})}return t},t.prototype.getThresholdAlignment=function(t){if((!re(this.dataMin)||this!==t&&this.series.some(function(t){return t.isDirty||t.isDirtyData}))&&this.getSeriesExtremes(),re(this.threshold)){var e=o0((this.threshold-(this.dataMin||0))/((this.dataMax||0)-(this.dataMin||0)),0,1);return this.options.reversed&&(e=1-e),e}},t.prototype.getTickAmount=function(){var t=this.options,e=t.tickPixelInterval,i=t.tickAmount;o2(t.tickInterval)||i||!(this.len<e)||this.isRadial||this.logarithmic||!t.startOnTick||!t.endOnTick||(i=2),!i&&this.alignToOthers()&&(i=Math.ceil(this.len/e)+1),i<4&&(this.finalTickAmt=i,i=5),this.tickAmount=i},t.prototype.adjustTickAmount=function(){var t,e,i,o=this,r=o.finalTickAmt,s=o.max,n=o.min,a=o.options,h=o.tickPositions,l=o.tickAmount,d=o.thresholdAlignment,c=null==h?void 0:h.length,p=rn(o.threshold,o.softThreshold?0:null),u=o.tickInterval,f=function(){return h.push(o1(h[h.length-1]+u))},g=function(){return h.unshift(o1(h[0]-u))};if(re(d)&&(i=d<.5?Math.ceil(d*(l-1)):Math.floor(d*(l-1)),a.reversed&&(i=l-1-i)),o.hasData()&&re(n)&&re(s)){var v=function(){o.transA*=(c-1)/(l-1),o.min=a.startOnTick?h[0]:Math.min(n,h[0]),o.max=a.endOnTick?h[h.length-1]:Math.max(s,h[h.length-1])};if(re(i)&&re(o.threshold)){for(;h[i]!==p||h.length!==l||h[0]>n||h[h.length-1]<s;){for(h.length=0,h.push(o.threshold);h.length<l;)void 0===h[i]||h[i]>o.threshold?g():f();if(u>8*o.tickInterval)break;u*=2}v()}else if(c<l){for(;h.length<l;)h.length%2||n===p?f():g();v()}if(o2(r)){for(e=t=h.length;e--;)(3===r&&e%2==1||r<=2&&e>0&&e<t-1)&&h.splice(e,1);o.finalTickAmt=void 0}}},t.prototype.setScale=function(){var t,e,i,o,r,s=this.coll,n=this.stacking,a=!1,h=!1;this.series.forEach(function(t){var e;a=a||t.isDirtyData||t.isDirty,h=h||(null===(e=t.xAxis)||void 0===e?void 0:e.isDirty)||!1}),this.setAxisSize();var l=this.len!==(null===(t=this.old)||void 0===t?void 0:t.len);l||a||h||this.isLinked||this.forceRedraw||this.userMin!==(null===(e=this.old)||void 0===e?void 0:e.userMin)||this.userMax!==(null===(i=this.old)||void 0===i?void 0:i.userMax)||this.alignToOthers()?(n&&"yAxis"===s&&n.buildStacks(),this.forceRedraw=!1,this.userMinRange||(this.minRange=void 0),this.getSeriesExtremes(),this.setTickInterval(),n&&"xAxis"===s&&n.buildStacks(),this.isDirty||(this.isDirty=l||this.min!==(null===(o=this.old)||void 0===o?void 0:o.min)||this.max!==(null===(r=this.old)||void 0===r?void 0:r.max))):n&&n.cleanStacks(),a&&delete this.allExtremes,o4(this,"afterSetScale")},t.prototype.setExtremes=function(t,e,i,o,r){var s=this;void 0===i&&(i=!0);var n=this.chart;this.series.forEach(function(t){delete t.kdTree}),o4(this,"setExtremes",r=o9(r,{min:t=n.time.parse(t),max:e=n.time.parse(e)}),function(t){s.userMin=t.min,s.userMax=t.max,s.eventArgs=t,i&&n.redraw(o)})},t.prototype.setAxisSize=function(){var t=this.chart,e=this.options,i=e.offsets||[0,0,0,0],o=this.horiz,r=this.width=Math.round(ra(rn(e.width,t.plotWidth-i[3]+i[1]),t.plotWidth)),s=this.height=Math.round(ra(rn(e.height,t.plotHeight-i[0]+i[2]),t.plotHeight)),n=this.top=Math.round(ra(rn(e.top,t.plotTop+i[0]),t.plotHeight,t.plotTop)),a=this.left=Math.round(ra(rn(e.left,t.plotLeft+i[3]),t.plotWidth,t.plotLeft));this.bottom=t.chartHeight-s-n,this.right=t.chartWidth-r-a,this.len=Math.max(o?r:s,0),this.pos=o?a:n},t.prototype.getExtremes=function(){var t=this.logarithmic;return{min:t?o1(t.lin2log(this.min)):this.min,max:t?o1(t.lin2log(this.max)):this.max,dataMin:this.dataMin,dataMax:this.dataMax,userMin:this.userMin,userMax:this.userMax}},t.prototype.getThreshold=function(t){var e=this.logarithmic,i=e?e.lin2log(this.min):this.min,o=e?e.lin2log(this.max):this.max;return null===t||t===-1/0?t=i:t===1/0?t=o:i>t?t=i:o<t&&(t=o),this.translate(t,0,1,0,1)},t.prototype.autoLabelAlign=function(t){var e=(rn(t,0)-90*this.side+720)%360,i={align:"center"};return o4(this,"autoLabelAlign",i,function(t){e>15&&e<165?t.align="right":e>195&&e<345&&(t.align="left")}),i.align},t.prototype.tickSize=function(t){var e,i=this.options,o=rn(i["tick"===t?"tickWidth":"minorTickWidth"],"tick"===t&&this.isXAxis&&!this.categories?1:0),r=i["tick"===t?"tickLength":"minorTickLength"];o&&r&&("inside"===i[t+"Position"]&&(r=-r),e=[r,o]);var s={tickSize:e};return o4(this,"afterTickSize",s),s.tickSize},t.prototype.labelMetrics=function(){var t=this.chart.renderer,e=this.ticks,i=e[Object.keys(e)[0]]||{};return this.chart.renderer.fontMetrics(i.label||i.movedLabel||t.box)},t.prototype.unsquish=function(){var t,e,i=this.options.labels,o=i.padding||0,r=this.horiz,s=this.tickInterval,n=this.len/((+!!this.categories+this.max-this.min)/s),a=i.rotation,h=o1(.8*this.labelMetrics().h),l=Math.max(this.max-this.min,0),d=function(t){var e=(t+2*o)/(n||1);return(e=e>1?Math.ceil(e):1)*s>l&&t!==1/0&&n!==1/0&&l&&(e=Math.ceil(l/s)),o1(e*s)},c=s,p=Number.MAX_VALUE;if(r){if(!i.staggerLines&&(re(a)?e=[a]:n<i.autoRotationLimit&&(e=i.autoRotation)),e)for(var u=void 0,f=void 0,g=0,v=e;g<v.length;g++){var m=v[g];(m===a||m&&m>=-90&&m<=90)&&(f=(u=d(Math.abs(h/Math.sin(o$*m))))+Math.abs(m/360))<p&&(p=f,t=m,c=u)}}else c=d(.75*h);return this.autoRotation=e,this.labelRotation=rn(t,re(a)?a:0),i.step?s:c},t.prototype.getSlotWidth=function(t){var e=this.chart,i=this.horiz,o=this.options.labels,r=Math.max(this.tickPositions.length-+!this.categories,1),s=e.margin[3];if(t&&re(t.slotWidth))return t.slotWidth;if(i&&o.step<2&&!this.isRadial)return o.rotation?0:(this.staggerLines||1)*this.len/r;if(!i){var n=o.style.width;if(void 0!==n)return parseInt(String(n),10);if(s)return s-e.spacing[3]}return .33*e.chartWidth},t.prototype.renderUnsquish=function(){var t,e=this.chart,i=e.renderer,o=this.tickPositions,r=this.ticks,s=this.options.labels,n=s.style,a=this.horiz,h=this.getSlotWidth(),l=Math.max(1,Math.round(h-(a?2*(s.padding||0):s.distance||0))),d={},c=this.labelMetrics(),p=n.lineClamp,u=null!=p?p:Math.floor(this.len/(o.length*c.h))||1,f=0;ri(s.rotation)||(d.rotation=s.rotation||0),o.forEach(function(t){var e,i=r[t];i.movedLabel&&i.replaceMovedLabel();var o=(null===(e=i.label)||void 0===e?void 0:e.textPxLength)||0;o>f&&(f=o)}),this.maxLabelLength=f,this.autoRotation?f>l&&f>c.h?d.rotation=this.labelRotation:this.labelRotation=0:h&&(t=l),d.rotation&&(t=f>.5*e.chartHeight?.33*e.chartHeight:f,p||(u=1)),this.labelAlign=s.align||this.autoLabelAlign(this.labelRotation),this.labelAlign&&(d.align=this.labelAlign),o.forEach(function(e){var i=r[e],o=null==i?void 0:i.label,s=n.width,a={};o&&(o.attr(d),i.shortenLabel?i.shortenLabel():t&&!s&&"nowrap"!==n.whiteSpace&&(t<(o.textPxLength||0)||"SPAN"===o.element.tagName)?o.css(o9(a,{width:""+t+"px",lineClamp:u})):!o.styles.width||a.width||s||o.css({width:"auto"}),i.rotation=d.rotation)},this),this.tickRotCorr=i.rotCorr(c.b,this.labelRotation||0,0!==this.side)},t.prototype.hasData=function(){return this.series.some(function(t){return t.hasData()})||this.options.showEmpty&&o2(this.min)&&o2(this.max)},t.prototype.addTitle=function(t){var e,i=this.chart.renderer,o=this.horiz,r=this.opposite,s=this.options.title,n=this.chart.styledMode;this.axisTitle||((e=s.textAlign)||(e=(o?{low:"left",middle:"center",high:"right"}:{low:r?"right":"left",middle:"center",high:r?"left":"right"})[s.align]),this.axisTitle=i.text(s.text||"",0,0,s.useHTML).attr({zIndex:7,rotation:s.rotation||0,align:e}).addClass("highcharts-axis-title"),n||this.axisTitle.css(ro(s.style)),this.axisTitle.add(this.axisGroup),this.axisTitle.isNew=!0),n||s.style.width||this.isRadial||this.axisTitle.css({width:this.len+"px"}),this.axisTitle[t?"show":"hide"](t)},t.prototype.generateTick=function(t){var e=this.ticks;e[t]?e[t].addLabel():e[t]=new oV(this,t)},t.prototype.createGroups=function(){var t=this,e=this.axisParent,i=this.chart,o=this.coll,r=this.options,s=i.renderer,n=function(i,n,a){return s.g(i).attr({zIndex:a}).addClass("highcharts-".concat(o.toLowerCase()).concat(n," ")+(t.isRadial?"highcharts-radial-axis".concat(n," "):"")+(r.className||"")).add(e)};this.axisGroup||(this.gridGroup=n("grid","-grid",r.gridZIndex),this.axisGroup=n("axis","",r.zIndex),this.labelGroup=n("axis-labels","-labels",r.labels.zIndex))},t.prototype.getOffset=function(){var t,e,i,o,r=this,s=r.chart,n=r.horiz,a=r.options,h=r.side,l=r.ticks,d=r.tickPositions,c=r.coll,p=s.inverted&&!r.isZAxis?[1,0,3,2][h]:h,u=r.hasData(),f=a.title,g=a.labels,v=re(a.crossing),m=s.axisOffset,y=s.clipOffset,x=[-1,1,1,-1][h],b=0,k=0,M=0;if(r.showAxis=t=u||a.showEmpty,r.staggerLines=r.horiz&&g.staggerLines||void 0,r.createGroups(),u||r.isLinked?(d.forEach(function(t){r.generateTick(t)}),r.renderUnsquish(),r.reserveSpaceDefault=0===h||2===h||({1:"left",3:"right"})[h]===r.labelAlign,rn(g.reserveSpace,!v&&null,"center"===r.labelAlign||null,r.reserveSpaceDefault)&&d.forEach(function(t){M=Math.max(l[t].getLabelSize(),M)}),r.staggerLines&&(M*=r.staggerLines),r.labelOffset=M*(r.opposite?-1:1)):rs(l,function(t,e){t.destroy(),delete l[e]}),(null==f?void 0:f.text)&&!1!==f.enabled&&(r.addTitle(t),t&&!v&&!1!==f.reserveSpace&&(r.titleOffset=b=r.axisTitle.getBBox()[n?"height":"width"],k=o2(e=f.offset)?0:rn(f.margin,n?5:10))),r.renderLine(),r.offset=x*rn(a.offset,m[h]?m[h]+(a.margin||0):0),r.tickRotCorr=r.tickRotCorr||{x:0,y:0},o=0===h?-r.labelMetrics().h:2===h?r.tickRotCorr.y:0,i=Math.abs(M)+k,M&&(i-=o,i+=x*(n?rn(g.y,r.tickRotCorr.y+x*g.distance):rn(g.x,x*g.distance))),r.axisTitleMargin=rn(e,i),r.getMaxLabelDimensions&&(r.maxLabelDimensions=r.getMaxLabelDimensions(l,d)),"colorAxis"!==c&&y){var w=this.tickSize("tick");m[h]=Math.max(m[h],(r.axisTitleMargin||0)+b+x*r.offset,i,(null==d?void 0:d.length)&&w?w[0]+x*r.offset:0);var S=!r.axisLine||a.offset?0:r.axisLine.strokeWidth()/2;y[p]=Math.max(y[p],S)}o4(this,"afterGetOffset")},t.prototype.getLinePath=function(t){var e=this.chart,i=this.opposite,o=this.offset,r=this.horiz,s=this.left+(i?this.width:0)+o,n=e.chartHeight-this.bottom-(i?this.height:0)+o;return i&&(t*=-1),e.renderer.crispLine([["M",r?this.left:s,r?n:this.top],["L",r?e.chartWidth-this.right:s,r?n:e.chartHeight-this.bottom]],t)},t.prototype.renderLine=function(){this.axisLine||(this.axisLine=this.chart.renderer.path().addClass("highcharts-axis-line").add(this.axisGroup),this.chart.styledMode||this.axisLine.attr({stroke:this.options.lineColor,"stroke-width":this.options.lineWidth,zIndex:7}))},t.prototype.getTitlePosition=function(t){var e=this.horiz,i=this.left,o=this.top,r=this.len,s=this.options.title,n=e?i:o,a=this.opposite,h=this.offset,l=s.x,d=s.y,c=this.chart.renderer.fontMetrics(t),p=t?Math.max(t.getBBox(!1,0).height-c.h-1,0):0,u={low:n+(e?0:r),middle:n+r/2,high:n+(e?r:0)}[s.align],f=(e?o+this.height:i)+(e?1:-1)*(a?-1:1)*(this.axisTitleMargin||0)+[-p,p,c.f,-p][this.side],g={x:e?u+l:f+(a?this.width:0)+h+l,y:e?f+d-(a?this.height:0)+h:u+d};return o4(this,"afterGetTitlePosition",{titlePosition:g}),g},t.prototype.renderMinorTick=function(t,e){var i=this.minorTicks;i[t]||(i[t]=new oV(this,t,"minor")),e&&i[t].isNew&&i[t].render(null,!0),i[t].render(null,!1,1)},t.prototype.renderTick=function(t,e,i){var o,r=this.isLinked,s=this.ticks;(!r||t>=this.min&&t<=this.max||(null===(o=this.grid)||void 0===o?void 0:o.isColumn))&&(s[t]||(s[t]=new oV(this,t)),i&&s[t].isNew&&s[t].render(e,!0,-1),s[t].render(e))},t.prototype.render=function(){var t,e,i=this,o=i.chart,r=i.logarithmic,s=o.renderer,n=i.options,a=i.isLinked,h=i.tickPositions,l=i.axisTitle,d=i.ticks,c=i.minorTicks,p=i.alternateBands,u=n.stackLabels,f=n.alternateGridColor,g=n.crossing,v=i.tickmarkOffset,m=i.axisLine,y=i.showAxis,x=eu(s.globalAnimation);if(i.labelEdge.length=0,i.overlap=!1,[d,c,p].forEach(function(t){rs(t,function(t){t.isActive=!1})}),re(g)){var b=this.isXAxis?o.yAxis[0]:o.xAxis[0],k=[1,-1,-1,1][this.side];if(b){var M=b.toPixels(g,!0);i.horiz&&(M=b.len-M),i.offset=k*M}}if(i.hasData()||a){var w=i.chart.hasRendered&&i.old&&re(i.old.min);i.minorTickInterval&&!i.categories&&i.getMinorTickPositions().forEach(function(t){i.renderMinorTick(t,w)}),h.length&&(h.forEach(function(t,e){i.renderTick(t,e,w)}),v&&(0===i.min||i.single)&&(d[-1]||(d[-1]=new oV(i,-1,null,!0)),d[-1].render(-1))),f&&h.forEach(function(s,n){e=void 0!==h[n+1]?h[n+1]+v:i.max-v,n%2==0&&s<i.max&&e<=i.max+(o.polar?-v:v)&&(p[s]||(p[s]=new tt.PlotLineOrBand(i,{})),t=s+v,p[s].options={from:r?r.lin2log(t):t,to:r?r.lin2log(e):e,color:f,className:"highcharts-alternate-grid"},p[s].render(),p[s].isActive=!0)}),i._addedPlotLB||(i._addedPlotLB=!0,(n.plotLines||[]).concat(n.plotBands||[]).forEach(function(t){i.addPlotBandOrLine(t)}))}[d,c,p].forEach(function(t){var e=[],i=x.duration;rs(t,function(t,i){t.isActive||(t.render(i,!1,0),t.isActive=!1,e.push(i))}),rd(function(){for(var i=e.length;i--;)t[e[i]]&&!t[e[i]].isActive&&(t[e[i]].destroy(),delete t[e[i]])},t!==p&&o.hasRendered&&i?i:0)}),m&&(m[m.isPlaced?"animate":"attr"]({d:this.getLinePath(m.strokeWidth())}),m.isPlaced=!0,m[y?"show":"hide"](y)),l&&y&&(l[l.isNew?"attr":"animate"](i.getTitlePosition(l)),l.isNew=!1),(null==u?void 0:u.enabled)&&i.stacking&&i.stacking.renderStackTotals(),i.old={len:i.len,max:i.max,min:i.min,transA:i.transA,userMax:i.userMax,userMin:i.userMin},i.isDirty=!1,o4(this,"afterRender")},t.prototype.redraw=function(){this.visible&&(this.render(),this.plotLinesAndBands.forEach(function(t){t.render()})),this.series.forEach(function(t){t.isDirty=!0})},t.prototype.getKeepProps=function(){return this.keepProps||t.keepProps},t.prototype.destroy=function(t){var e=this,i=e.plotLinesAndBands,o=this.eventOptions;if(o4(this,"destroy",{keepEvents:t}),t||rh(e),[e.ticks,e.minorTicks,e.alternateBands].forEach(function(t){o3(t)}),i)for(var r=i.length;r--;)i[r].destroy();for(var s in["axisLine","axisTitle","axisGroup","gridGroup","labelGroup","cross","scrollbar"].forEach(function(t){e[t]&&(e[t]=e[t].destroy())}),e.plotLinesAndBandsGroups)e.plotLinesAndBandsGroups[s]=e.plotLinesAndBandsGroups[s].destroy();rs(e,function(t,i){-1===e.getKeepProps().indexOf(i)&&delete e[i]}),this.eventOptions=o},t.prototype.drawCrosshair=function(t,e){var i,o,r,s,n,a,h=this.crosshair,l=null===(i=null==h?void 0:h.snap)||void 0===i||i,d=this.chart,c=this.cross;if(o4(this,"drawCrosshair",{e:t,point:e}),t||(t=null===(o=this.cross)||void 0===o?void 0:o.e),h&&!1!==(o2(e)||!l)){if(l?o2(e)&&(s=rn("colorAxis"!==this.coll?e.crosshairPos:null,this.isXAxis?e.plotX:this.len-e.plotY)):s=t&&(this.horiz?t.chartX-this.pos:this.len-t.chartY+this.pos),o2(s)&&(a={value:e&&(this.isXAxis?e.x:rn(e.stackY,e.y)),translatedValue:s},d.polar&&o9(a,{isCrosshair:!0,chartX:null==t?void 0:t.chartX,chartY:null==t?void 0:t.chartY,point:e}),r=this.getPlotLinePath(a)||null),!o2(r)){this.hideCrosshair();return}n=this.categories&&!this.isRadial,c||(this.cross=c=d.renderer.path().addClass("highcharts-crosshair highcharts-crosshair-"+(n?"category ":"thin ")+(h.className||"")).attr({zIndex:rn(h.zIndex,2)}).add(),!d.styledMode&&(c.attr({stroke:h.color||(n?t7.parse("#ccd3ff").setOpacity(.25).get():"#cccccc"),"stroke-width":rn(h.width,1)}).css({"pointer-events":"none"}),h.dashStyle&&c.attr({dashstyle:h.dashStyle}))),c.show().attr({d:r}),n&&!h.width&&c.attr({"stroke-width":this.transA}),this.cross.e=t}else this.hideCrosshair();o4(this,"afterDrawCrosshair",{e:t,point:e})},t.prototype.hideCrosshair=function(){this.cross&&this.cross.hide(),o4(this,"afterHideCrosshair")},t.prototype.update=function(t,e){var i=this.chart;t=ro(this.userOptions,t),this.destroy(!0),this.init(i,t),i.isDirtyBox=!0,rn(e,!0)&&i.redraw()},t.prototype.remove=function(t){for(var e=this.chart,i=this.coll,o=this.series,r=o.length;r--;)o[r]&&o[r].remove(!1);o5(e.axes,this),o5(e[i]||[],this),e.orderItems(i),this.destroy(),e.isDirtyBox=!0,rn(t,!0)&&e.redraw()},t.prototype.setTitle=function(t,e){this.update({title:t},e)},t.prototype.setCategories=function(t,e){this.update({categories:t},e)},t.keepProps=["coll","extKey","hcEvents","len","names","series","userMax","userMin"],t}(),ru=tT.addEvent,rf=tT.getMagnitude,rg=tT.normalizeTickInterval,rv=tT.timeUnits;!function(t){function e(){return this.chart.time.getTimeTicks.apply(this.chart.time,arguments)}function i(){if("datetime"!==this.type){this.dateTime=void 0;return}this.dateTime||(this.dateTime=new o(this))}t.compose=function(t){return t.keepProps.includes("dateTime")||(t.keepProps.push("dateTime"),t.prototype.getTimeTicks=e,ru(t,"afterSetType",i)),t};var o=function(){function t(t){this.axis=t}return t.prototype.normalizeTimeTickInterval=function(t,e){var i,o=e||[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2]],["week",[1,2]],["month",[1,2,3,4,6]],["year",null]],r=o[o.length-1],s=rv[r[0]],n=r[1];for(i=0;i<o.length&&(s=rv[(r=o[i])[0]],n=r[1],!o[i+1]||!(t<=(s*n[n.length-1]+rv[o[i+1][0]])/2));i++);s===rv.year&&t<5*s&&(n=[1,2,5]);var a=rg(t/s,n,"year"===r[0]?Math.max(rf(t/s),1):1);return{unitRange:s,count:a,unitName:r[0]}},t.prototype.getXDateFormat=function(t,e){var i=this.axis,o=i.chart.time;return i.closestPointRange?o.getDateFormat(i.closestPointRange,t,i.options.startOfWeek,e)||o.resolveDTLFormat(e.year).main:o.resolveDTLFormat(e.day).main},t}();t.Additions=o}(N||(N={}));var rm=N,ry=tT.addEvent,rx=tT.normalizeTickInterval,rb=tT.pick;!function(t){function e(){var t;"logarithmic"!==this.type?this.logarithmic=void 0:null!==(t=this.logarithmic)&&void 0!==t||(this.logarithmic=new o(this))}function i(){var t=this.logarithmic;t&&(this.lin2val=function(e){return t.lin2log(e)},this.val2lin=function(e){return t.log2lin(e)})}t.compose=function(t){return t.keepProps.includes("logarithmic")||(t.keepProps.push("logarithmic"),ry(t,"afterSetType",e),ry(t,"afterInit",i)),t};var o=function(){function t(t){this.axis=t}return t.prototype.getLogTickPositions=function(t,e,i,o){var r=this.axis,s=r.len,n=r.options,a=[];if(o||(this.minorAutoInterval=void 0),t>=.5)t=Math.round(t),a=r.getLinearTickPositions(t,e,i);else if(t>=.08){var h=Math.floor(e),l=void 0,d=void 0,c=void 0,p=void 0,u=void 0,f=void 0,g=void 0;for(l=t>.3?[1,2,4]:t>.15?[1,2,4,6,8]:[1,2,3,4,5,6,7,8,9],d=h;d<i+1&&!g;d++)for(c=0,p=l.length;c<p&&!g;c++)(u=this.log2lin(this.lin2log(d)*l[c]))>e&&(!o||f<=i)&&void 0!==f&&a.push(f),f>i&&(g=!0),f=u}else{var v=this.lin2log(e),m=this.lin2log(i),y=o?r.getMinorTickInterval():n.tickInterval,x=n.tickPixelInterval/(o?5:1),b=o?s/r.tickPositions.length:s;t=rx(t=rb("auto"===y?null:y,this.minorAutoInterval,(m-v)*x/(b||1))),a=r.getLinearTickPositions(t,v,m).map(this.log2lin),o||(this.minorAutoInterval=t/5)}return o||(r.tickInterval=t),a},t.prototype.lin2log=function(t){return Math.pow(10,t)},t.prototype.log2lin=function(t){return Math.log(t)/Math.LN10},t}();t.Additions=o}(W||(W={}));var rk=W,rM=tT.erase,rw=tT.extend,rS=tT.isNumber;!function(t){var e;function i(t){return this.addPlotBandOrLine(t,"plotBands")}function o(t,i){var o=this,r=this.userOptions,s=new e(this,t);if(this.visible&&(s=s.render()),s){if(this._addedPlotLB||(this._addedPlotLB=!0,(r.plotLines||[]).concat(r.plotBands||[]).forEach(function(t){o.addPlotBandOrLine(t)})),i){var n=r[i]||[];n.push(t),r[i]=n}this.plotLinesAndBands.push(s)}return s}function r(t){return this.addPlotBandOrLine(t,"plotLines")}function s(t,e,i){i=i||this.options;var o,r,s=this.getPlotLinePath({value:e,force:!0,acrossPanes:i.acrossPanes}),n=[],a=this.horiz,h=!rS(this.min)||!rS(this.max)||t<this.min&&e<this.min||t>this.max&&e>this.max,l=this.getPlotLinePath({value:t,force:!0,acrossPanes:i.acrossPanes}),d=1;if(l&&s)for(h&&(r=l.toString()===s.toString(),d=0),o=0;o<l.length;o+=2){var c=l[o],p=l[o+1],u=s[o],f=s[o+1];("M"===c[0]||"L"===c[0])&&("M"===p[0]||"L"===p[0])&&("M"===u[0]||"L"===u[0])&&("M"===f[0]||"L"===f[0])&&(a&&u[1]===c[1]?(u[1]+=d,f[1]+=d):a||u[2]!==c[2]||(u[2]+=d,f[2]+=d),n.push(["M",c[1],c[2]],["L",p[1],p[2]],["L",f[1],f[2]],["L",u[1],u[2]],["Z"])),n.isFlat=r}return n}function n(t){this.removePlotBandOrLine(t)}function a(t){var e=this.plotLinesAndBands,i=this.options,o=this.userOptions;if(e){for(var r=e.length;r--;)e[r].id===t&&e[r].destroy();[i.plotLines||[],o.plotLines||[],i.plotBands||[],o.plotBands||[]].forEach(function(e){var i;for(r=e.length;r--;)(null===(i=e[r])||void 0===i?void 0:i.id)===t&&rM(e,e[r])})}}function h(t){this.removePlotBandOrLine(t)}t.compose=function(t,l){var d=l.prototype;return d.addPlotBand||(e=t,rw(d,{addPlotBand:i,addPlotLine:r,addPlotBandOrLine:o,getPlotBandPath:s,removePlotBand:n,removePlotLine:h,removePlotBandOrLine:a})),l}}(X||(X={}));var rA=X,rT=function(){return(rT=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},rC=tT.addEvent,rO=tT.arrayMax,rP=tT.arrayMin,rE=tT.defined,rL=tT.destroyObjectProperties,rD=tT.erase,rI=tT.fireEvent,rB=tT.merge,rz=tT.objectEach,rR=tT.pick,rN=function(){function t(t,e){this.axis=t,this.options=e,this.id=e.id}return t.compose=function(e,i){return rC(e,"afterInit",function(){var t=this;this.labelCollectors.push(function(){for(var e,i=[],o=0,r=t.axes;o<r.length;o++)for(var s=r[o],n=0,a=s.plotLinesAndBands;n<a.length;n++){var h=a[n],l=h.label,d=h.options;!l||(null===(e=null==d?void 0:d.label)||void 0===e?void 0:e.allowOverlap)||i.push(l)}return i})}),rA.compose(t,i)},t.prototype.render=function(){var t=this;rI(this,"render");var e,i,o,r,s=this.axis,n=this.options,a=s.horiz,h=s.logarithmic,l=n.color,d=n.events,c=n.zIndex,p=void 0===c?0:c,u=s.chart,f=u.renderer,g=u.time,v={},m=g.parse(n.to),y=g.parse(n.from),x=g.parse(n.value),b=n.borderWidth,k=n.label,M=this.label,w=this.svgElem,S=[],A=rE(y)&&rE(m),T=rE(x),C=!w,O={class:"highcharts-plot-"+(A?"band ":"line ")+(n.className||"")},P=A?"bands":"lines";if(!s.chart.styledMode&&(T?(O.stroke=l||"#999999",O["stroke-width"]=rR(n.width,1),n.dashStyle&&(O.dashstyle=n.dashStyle)):A&&(O.fill=l||"#e6e9ff",b&&(O.stroke=n.borderColor,O["stroke-width"]=b))),v.zIndex=p,P+="-"+p,(r=s.plotLinesAndBandsGroups[P])||(s.plotLinesAndBandsGroups[P]=r=f.g("plot-"+P).attr(v).add()),w||(this.svgElem=w=f.path().attr(O).add(r)),rE(x))S=s.getPlotLinePath({value:null!==(e=null==h?void 0:h.log2lin(x))&&void 0!==e?e:x,lineWidth:w.strokeWidth(),acrossPanes:n.acrossPanes});else{if(!(rE(y)&&rE(m)))return;S=s.getPlotBandPath(null!==(i=null==h?void 0:h.log2lin(y))&&void 0!==i?i:y,null!==(o=null==h?void 0:h.log2lin(m))&&void 0!==o?o:m,n)}return!this.eventsAdded&&d&&(rz(d,function(e,i){null==w||w.on(i,function(e){d[i].apply(t,[e])})}),this.eventsAdded=!0),(C||!w.d)&&(null==S?void 0:S.length)?w.attr({d:S}):w&&(S?(w.show(),w.animate({d:S})):w.d&&(w.hide(),M&&(this.label=M=M.destroy()))),k&&(rE(k.text)||rE(k.formatter))&&(null==S?void 0:S.length)&&s.width>0&&s.height>0&&!S.isFlat?(k=rB(rT({align:a&&A?"center":void 0,x:a?!A&&4:10,verticalAlign:!a&&A?"middle":void 0,y:a?A?16:10:A?6:-4,rotation:a&&!A?90:0},A?{inside:!0}:{}),k),this.renderLabel(k,S,A,p)):M&&M.hide(),this},t.prototype.renderLabel=function(t,e,i,o){var r,s=this.axis,n=s.chart.renderer,a=t.inside,h=this.label;h||(this.label=h=n.text(this.getLabelText(t),0,0,t.useHTML).attr({align:t.textAlign||t.align,rotation:t.rotation,class:"highcharts-plot-"+(i?"band":"line")+"-label "+(t.className||""),zIndex:o}),s.chart.styledMode||h.css(rB({fontSize:"0.8em",textOverflow:i&&!a?"":"ellipsis"},t.style)),h.add());var l=e.xBounds||[e[0][1],e[1][1],i?e[2][1]:e[0][1]],d=e.yBounds||[e[0][2],e[1][2],i?e[2][2]:e[0][2]],c=rP(l),p=rP(d),u=rO(l)-c;h.align(t,!1,{x:c,y:p,width:u,height:rO(d)-p}),h.alignAttr.y-=n.fontMetrics(h).b,(!h.alignValue||"left"===h.alignValue||rE(a))&&h.css({width:((null===(r=t.style)||void 0===r?void 0:r.width)||(i&&a?u:90===h.rotation?s.height-(h.alignAttr.y-s.top):(t.clip?s.width:s.chart.chartWidth)-(h.alignAttr.x-s.left)))+"px"}),h.show(!0)},t.prototype.getLabelText=function(t){return rE(t.formatter)?t.formatter.call(this):t.text},t.prototype.destroy=function(){rD(this.axis.plotLinesAndBands,this),delete this.axis,rL(this)},t}(),rW=e_.format,rX=tt.composed,rH=tt.dateFormats,rF=tt.doc,rj=tt.isSafari,rY=e$.distribute,rG=tT.addEvent,r_=tT.clamp,rU=tT.css,rV=tT.discardElement,rZ=tT.extend,rq=tT.fireEvent,rK=tT.getAlignFactor,r$=tT.isArray,rJ=tT.isNumber,rQ=tT.isObject,r0=tT.isString,r1=tT.merge,r2=tT.pick,r3=tT.pushUnique,r5=tT.splat,r6=tT.syncTimeout,r9=function(){function t(t,e,i){this.allowShared=!0,this.crosshairs=[],this.distance=0,this.isHidden=!0,this.isSticky=!1,this.options={},this.outside=!1,this.chart=t,this.init(t,e),this.pointer=i}return t.prototype.bodyFormatter=function(t){return t.map(function(t){var e=t.series.tooltipOptions,i=t.formatPrefix||"point";return(e[i+"Formatter"]||t.tooltipFormatter).call(t,e[i+"Format"]||"")})},t.prototype.cleanSplit=function(t){this.chart.series.forEach(function(e){var i=null==e?void 0:e.tt;i&&(!i.isActive||t?e.tt=i.destroy():i.isActive=!1)})},t.prototype.defaultFormatter=function(t){var e,i=this.points||r5(this);return(e=(e=[t.headerFooterFormatter(i[0])]).concat(t.bodyFormatter(i))).push(t.headerFooterFormatter(i[0],!0)),e},t.prototype.destroy=function(){this.label&&(this.label=this.label.destroy()),this.split&&(this.cleanSplit(!0),this.tt&&(this.tt=this.tt.destroy())),this.renderer&&(this.renderer=this.renderer.destroy(),rV(this.container)),tT.clearTimeout(this.hideTimer)},t.prototype.getAnchor=function(t,e){var i,o,r=this.chart,s=this.pointer,n=r.inverted,a=r.plotTop,h=r.plotLeft;if((null===(i=(t=r5(t))[0].series)||void 0===i?void 0:i.yAxis)&&!t[0].series.yAxis.options.reversedStacks&&(t=t.slice().reverse()),this.followPointer&&e)void 0===e.chartX&&(e=s.normalize(e)),o=[e.chartX-h,e.chartY-a];else if(t[0].tooltipPos)o=t[0].tooltipPos;else{var l=0,d=0;t.forEach(function(t){var e=t.pos(!0);e&&(l+=e[0],d+=e[1])}),l/=t.length,d/=t.length,this.shared&&t.length>1&&e&&(n?l=e.chartX:d=e.chartY),o=[l-h,d-a]}return o.map(Math.round)},t.prototype.getClassName=function(t,e,i){var o=this.options,r=t.series,s=r.options;return[o.className,"highcharts-label",i&&"highcharts-tooltip-header",e?"highcharts-tooltip-box":"highcharts-tooltip",!i&&"highcharts-color-"+r2(t.colorIndex,r.colorIndex),null==s?void 0:s.className].filter(r0).join(" ")},t.prototype.getLabel=function(t){var e,i=void 0===t?{anchorX:0,anchorY:0}:t,o=i.anchorX,r=i.anchorY,s=this,n=this.chart.styledMode,a=this.options,h=this.split&&this.allowShared,l=this.container,d=this.chart.renderer;if(this.label){var c=!this.label.hasClass("highcharts-label");(!h&&c||h&&!c)&&this.destroy()}if(!this.label){if(this.outside){var p=this.chart,u=p.options.chart.style,f=eU.getRendererType();this.container=l=tt.doc.createElement("div"),l.className="highcharts-tooltip-container "+(p.renderTo.className.match(/(highcharts[a-zA-Z0-9-]+)\s?/gm)||""),rU(l,{position:"absolute",top:"1px",pointerEvents:"none",zIndex:Math.max(this.options.style.zIndex||0,((null==u?void 0:u.zIndex)||0)+3)}),this.renderer=d=new f(l,0,0,u,void 0,void 0,d.styledMode)}if(h?this.label=d.g("tooltip"):(this.label=d.label("",o,r,a.shape||"callout",void 0,void 0,a.useHTML,void 0,"tooltip").attr({padding:a.padding,r:a.borderRadius}),n||this.label.attr({fill:a.backgroundColor,"stroke-width":a.borderWidth||0}).css(a.style).css({pointerEvents:a.style.pointerEvents||(this.shouldStickOnContact()?"auto":"none")})),s.outside){var g=this.label;[g.xSetter,g.ySetter].forEach(function(t,e){g[e?"ySetter":"xSetter"]=function(i){t.call(g,s.distance),g[e?"y":"x"]=i,l&&(l.style[e?"top":"left"]=""+i+"px")}})}this.label.attr({zIndex:8}).shadow(null!==(e=a.shadow)&&void 0!==e?e:!a.fixed).add()}return l&&!l.parentElement&&tt.doc.body.appendChild(l),this.label},t.prototype.getPlayingField=function(){var t=rF.body,e=rF.documentElement,i=this.chart,o=this.distance,r=this.outside;return{width:r?Math.max(t.scrollWidth,e.scrollWidth,t.offsetWidth,e.offsetWidth,e.clientWidth)-2*o-2:i.chartWidth,height:r?Math.max(t.scrollHeight,e.scrollHeight,t.offsetHeight,e.offsetHeight,e.clientHeight):i.chartHeight}},t.prototype.getPosition=function(t,e,i){var o,r,s,n=this.distance,a=this.chart,h=this.outside,l=this.pointer,d=a.inverted,c=a.plotLeft,p=a.plotTop,u=a.polar,f=i.plotX,g=void 0===f?0:f,v=i.plotY,m=void 0===v?0:v,y={},x=d&&i.h||0,b=this.getPlayingField(),k=b.height,M=b.width,w=l.getChartPosition(),S=function(t){return t*w.scaleX},A=function(t){return t*w.scaleY},T=function(i){var o="x"===i;return[i,o?M:k,o?t:e].concat(h?[o?S(t):A(e),o?w.left-n+S(g+c):w.top-n+A(m+p),0,o?M:k]:[o?t:e,o?g+c:m+p,o?c:p,o?c+a.plotWidth:p+a.plotHeight])},C=T("y"),O=T("x"),P=!!i.negative;!u&&(null===(r=null===(o=a.hoverSeries)||void 0===o?void 0:o.yAxis)||void 0===r?void 0:r.reversed)&&(P=!P);var E=!this.followPointer&&r2(i.ttBelow,!u&&!d===P),L=function(t,e,i,o,r,s,a){var l=h?"y"===t?A(n):S(n):n,d=(i-o)/2,c=o<r-n,p=r+n+o<e,u=r-l-i+d,f=r+l-d;if(E&&p)y[t]=f;else if(!E&&c)y[t]=u;else if(c)y[t]=Math.min(a-o,u-x<0?u:u-x);else{if(!p)return y[t]=0,!1;y[t]=Math.max(s,f+x+i>e?f:f+x)}},D=function(t,e,i,o,r){if(r<n||r>e-n)return!1;r<i/2?y[t]=1:r>e-o/2?y[t]=e-o-2:y[t]=r-i/2},I=function(t){var e;C=(e=[O,C])[0],O=e[1],s=t},B=function(){!1!==L.apply(0,C)?!1!==D.apply(0,O)||s||(I(!0),B()):s?y.x=y.y=0:(I(!0),B())};return(d&&!u||this.len>1)&&I(),B(),y},t.prototype.getFixedPosition=function(t,e,i){var o,r=i.series,s=this.chart,n=this.options,a=this.split,h=n.position,l=h.relativeTo,d=n.shared||(null===(o=null==r?void 0:r.yAxis)||void 0===o?void 0:o.isRadial)&&("pane"===l||!l)?"plotBox":l,c="chart"===d?s.renderer:s[d]||s.getClipBox(r,!0);return{x:c.x+(c.width-t)*rK(h.align)+h.x,y:c.y+(c.height-e)*rK(h.verticalAlign)+(!a&&h.y||0)}},t.prototype.hide=function(t){var e=this;tT.clearTimeout(this.hideTimer),t=r2(t,this.options.hideDelay),this.isHidden||(this.hideTimer=r6(function(){var i=e.getLabel();e.getLabel().animate({opacity:0},{duration:t?150:t,complete:function(){i.hide(),e.container&&e.container.remove()}}),e.isHidden=!0},t))},t.prototype.init=function(t,e){this.chart=t,this.options=e,this.crosshairs=[],this.isHidden=!0,this.split=e.split&&!t.inverted&&!t.polar,this.shared=e.shared||this.split,this.outside=r2(e.outside,!!(t.scrollablePixelsX||t.scrollablePixelsY))},t.prototype.shouldStickOnContact=function(t){return!!(!this.followPointer&&this.options.stickOnContact&&(!t||this.pointer.inClass(t.target,"highcharts-tooltip")))},t.prototype.move=function(t,e,i,o){var r=this,s=this.followPointer,n=this.options,a=eu(!s&&!this.isHidden&&!n.fixed&&n.animation),h=s||(this.len||0)>1,l={x:t,y:e};h?l.anchorX=l.anchorY=NaN:(l.anchorX=i,l.anchorY=o),a.step=function(){return r.drawTracker()},this.getLabel().animate(l,a)},t.prototype.refresh=function(t,e){var i=this.chart,o=this.options,r=this.pointer,s=this.shared,n=r5(t),a=n[0],h=o.format,l=o.formatter||this.defaultFormatter,d=i.styledMode,c=this.allowShared;if(o.enabled&&a.series){tT.clearTimeout(this.hideTimer),this.allowShared=!(!r$(t)&&t.series&&t.series.noSharedTooltip),c=c&&!this.allowShared,this.followPointer=!this.split&&a.series.tooltipOptions.followPointer;var p=this.getAnchor(t,e),u=p[0],f=p[1];s&&this.allowShared&&(r.applyInactiveState(n),n.forEach(function(t){return t.setState("hover")}),a.points=n),this.len=n.length;var g=r0(h)?rW(h,a,i):l.call(a,this);a.points=void 0;var v=a.series;if(this.distance=r2(v.tooltipOptions.distance,16),!1===g)this.hide();else{if(this.split&&this.allowShared)this.renderSplit(g,n);else{var m=u,y=f;if(e&&r.isDirectTouch&&(m=e.chartX-i.plotLeft,y=e.chartY-i.plotTop),i.polar||!1===v.options.clip||n.some(function(t){return r.isDirectTouch||t.series.shouldShowTooltip(m,y)})){var x=this.getLabel(c&&this.tt||{});(!o.style.width||d)&&x.css({width:(this.outside?this.getPlayingField():i.spacingBox).width+"px"}),x.attr({class:this.getClassName(a),text:g&&g.join?g.join(""):g}),this.outside&&x.attr({x:r_(x.x||0,0,this.getPlayingField().width-(x.width||0)-1)}),d||x.attr({stroke:o.borderColor||a.color||v.color||"#666666"}),this.updatePosition({plotX:u,plotY:f,negative:a.negative,ttBelow:a.ttBelow,series:v,h:p[2]||0})}else{this.hide();return}}this.isHidden&&this.label&&this.label.attr({opacity:1}).show(),this.isHidden=!1}rq(this,"refresh")}},t.prototype.renderSplit=function(t,e){var i,o,r=this,s=this,n=s.chart,a=s.chart,h=a.chartWidth,l=a.chartHeight,d=a.plotHeight,c=a.plotLeft,p=a.plotTop,u=a.scrollablePixelsY,f=a.scrollablePixelsX,g=a.styledMode,v=s.distance,m=s.options,y=s.options,x=y.fixed,b=y.position,k=y.positioner,M=s.pointer,w=(null===(i=n.scrollablePlotArea)||void 0===i?void 0:i.scrollingContainer)||{},S=w.scrollLeft,A=void 0===S?0:S,T=w.scrollTop,C=void 0===T?0:T,O=s.outside&&"number"!=typeof f?rF.documentElement.getBoundingClientRect():{left:A,right:A+h,top:C,bottom:C+l},P=s.getLabel(),E=this.renderer||n.renderer,L=!!(null===(o=n.xAxis[0])||void 0===o?void 0:o.opposite),D=M.getChartPosition(),I=D.left,B=D.top,z=k||x,R=p+C,N=0,W=d-(void 0===u?0:u),X=function(t,e,i,o,r){if(void 0===o&&(o=[0,0]),void 0===r&&(r=!0),i.isHeader)a=L?0:W,n=r_(o[0]-t/2,O.left,O.right-t-(s.outside?I:0));else if(x&&i){var n,a,h=s.getFixedPosition(t,e,i);n=h.x,a=h.y-R}else a=o[1]-R,n=r_(n=r?o[0]-t-v:o[0]+v,r?n:O.left,O.right);return{x:n,y:a}};r0(t)&&(t=[!1,t]);var H=t.slice(0,e.length+1).reduce(function(t,i,o){if(!1!==i&&""!==i){var r=e[o-1]||{isHeader:!0,plotX:e[0].plotX,plotY:d,series:{}},n=r.isHeader,a=n?s:r.series,h=a.tt=function(t,e,i){var o,r=t,n=e.isHeader,a=e.series,h=a.tooltipOptions||m;if(!r){var l={padding:h.padding,r:h.borderRadius};g||(l.fill=h.backgroundColor,l["stroke-width"]=null!==(o=h.borderWidth)&&void 0!==o?o:x&&!n?0:1),r=E.label("",0,0,h[n?"headerShape":"shape"]||(x&&!n?"rect":"callout"),void 0,void 0,h.useHTML).addClass(s.getClassName(e,!0,n)).attr(l).add(P)}return r.isActive=!0,r.attr({text:i}),g||r.css(h.style).attr({stroke:h.borderColor||e.color||a.color||"#333333"}),r}(a.tt,r,i.toString()),l=h.getBBox(),u=l.width+h.strokeWidth();n&&(N=l.height,W+=N,L&&(R-=N));var f=function(t){var e,i,o=t.isHeader,r=t.plotX,s=void 0===r?0:r,n=t.plotY,a=void 0===n?0:n,h=t.series;if(o)e=Math.max(c+s,c),i=p+d/2;else{var l=h.xAxis,u=h.yAxis;e=l.pos+r_(s,-v,l.len+v),h.shouldShowTooltip(0,u.pos-p+a,{ignoreX:!0})&&(i=u.pos+a)}return{anchorX:e=r_(e,O.left-v,O.right+v),anchorY:i}}(r),y=f.anchorX,b=f.anchorY;if("number"==typeof b){var M=l.height+1,w=(k||X).call(s,u,M,r,[y,b]);t.push({align:z?0:void 0,anchorX:y,anchorY:b,boxWidth:u,point:r,rank:r2(w.rank,+!!n),size:M,target:w.y,tt:h,x:w.x})}else h.isActive=!1}return t},[]);!z&&H.some(function(t){var e=(s.outside?I:0)+t.anchorX;return e<O.left&&e+t.boxWidth<O.right||e<I-O.left+t.boxWidth&&O.right-e>e})&&(H=H.map(function(t){var e=X.call(r,t.boxWidth,t.size,t.point,[t.anchorX,t.anchorY],!1),i=e.x;return rZ(t,{target:e.y,x:i})})),s.cleanSplit(),rY(H,W);var F={left:I,right:I};H.forEach(function(t){var e=t.x,i=t.boxWidth,o=t.isHeader;!o&&(s.outside&&I+e<F.left&&(F.left=I+e),!o&&s.outside&&F.left+i>F.right&&(F.right=I+e))}),H.forEach(function(t){var e=t.x,i=t.anchorX,o=t.anchorY,r=t.pos,n=t.point.isHeader,a={visibility:void 0===r?"hidden":"inherit",x:e,y:(r||0)+R+(x&&b.y||0),anchorX:i,anchorY:o};if(s.outside&&e<i){var h=I-F.left;h>0&&(n||(a.x=e+h,a.anchorX=i+h),n&&(a.x=(F.right-F.left)/2,a.anchorX=i+h))}t.tt.attr(a)});var j=s.container,Y=s.outside,G=s.renderer;if(Y&&j&&G){var _=P.getBBox(),U=_.width,V=_.height,Z=_.x,q=_.y;G.setSize(U+Z,V+q,!1),j.style.left=F.left+"px",j.style.top=B+"px"}rj&&P.attr({opacity:1===P.opacity?.999:1})},t.prototype.drawTracker=function(){if(!this.shouldStickOnContact()){this.tracker&&(this.tracker=this.tracker.destroy());return}var t=this.chart,e=this.label,i=this.shared?t.hoverPoints:t.hoverPoint;if(e&&i){var o={x:0,y:0,width:0,height:0},r=this.getAnchor(i),s=e.getBBox();r[0]+=t.plotLeft-(e.translateX||0),r[1]+=t.plotTop-(e.translateY||0),o.x=Math.min(0,r[0]),o.y=Math.min(0,r[1]),o.width=r[0]<0?Math.max(Math.abs(r[0]),s.width-r[0]):Math.max(Math.abs(r[0]),s.width),o.height=r[1]<0?Math.max(Math.abs(r[1]),s.height-Math.abs(r[1])):Math.max(Math.abs(r[1]),s.height),this.tracker?this.tracker.attr(o):(this.tracker=e.renderer.rect(o).addClass("highcharts-tracker").add(e),t.styledMode||this.tracker.attr({fill:"rgba(0,0,0,0)"}))}},t.prototype.styledModeFormat=function(t){return t.replace('style="font-size: 0.8em"','class="highcharts-header"').replace(/style="color:{(point|series)\.color}"/g,'class="highcharts-color-{$1.colorIndex} {series.options.className} {point.options.className}"')},t.prototype.headerFooterFormatter=function(t,e){var i=t.series,o=i.tooltipOptions,r=i.xAxis,s=null==r?void 0:r.dateTime,n={isFooter:e,point:t},a=o.xDateFormat||"",h=o[e?"footerFormat":"headerFormat"];return rq(this,"headerFormatter",n,function(e){if(s&&!a&&rJ(t.key)&&(a=s.getXDateFormat(t.key,o.dateTimeLabelFormats)),s&&a){if(rQ(a)){var r=a;rH[0]=function(t){return i.chart.time.dateFormat(r,t)},a="%0"}(t.tooltipDateKeys||["key"]).forEach(function(t){h=h.replace(RegExp("point\\."+t+"([ \\)}])"),"(point.".concat(t,":").concat(a,")$1"))})}i.chart.styledMode&&(h=this.styledModeFormat(h)),e.text=rW(h,t,this.chart)}),n.text||""},t.prototype.update=function(t){this.destroy(),this.init(this.chart,r1(!0,this.options,t))},t.prototype.updatePosition=function(t){var e,i,o=this.chart,r=this.container,s=this.distance,n=this.options,a=this.pointer,h=this.renderer,l=this.getLabel(),d=l.height,c=void 0===d?0:d,p=l.width,u=void 0===p?0:p,f=n.fixed,g=n.positioner,v=a.getChartPosition(),m=v.left,y=v.top,x=v.scaleX,b=v.scaleY,k=(g||f&&this.getFixedPosition||this.getPosition).call(this,u,c,t),M=tt.doc,w=(t.plotX||0)+o.plotLeft,S=(t.plotY||0)+o.plotTop;if(h&&r){if(g||f){var A=(null===(e=o.scrollablePlotArea)||void 0===e?void 0:e.scrollingContainer)||{},T=A.scrollLeft,C=A.scrollTop;k.x+=(void 0===T?0:T)+m-s,k.y+=(void 0===C?0:C)+y-s}i=(n.borderWidth||0)+2*s+2,h.setSize(r_(u+i,0,M.documentElement.clientWidth)-1,c+i,!1),(1!==x||1!==b)&&(rU(r,{transform:"scale(".concat(x,", ").concat(b,")")}),w*=x,S*=b),w+=m-k.x,S+=y-k.y}this.move(Math.round(k.x),Math.round(k.y||0),w,S)},t}();(f=r9||(r9={})).compose=function(t){r3(rX,"Core.Tooltip")&&rG(t,"afterInit",function(){var t=this.chart;t.options.tooltip&&(t.tooltip=new f(t,t.options.tooltip,this))})};var r4=r9,r8=e_.format,r7=tT.addEvent,st=tT.crisp,se=tT.erase,si=tT.extend,so=tT.fireEvent,sr=tT.getNestedProperty,ss=tT.isArray,sn=tT.isFunction,sa=tT.isNumber,sh=tT.isObject,sl=tT.merge,sd=tT.pick,sc=tT.syncTimeout,sp=tT.removeEvent,su=tT.uniqueKey,sf=function(){function t(t,e,i){var o,r;this.formatPrefix="point",this.visible=!0,this.point=this,this.series=t,this.applyOptions(e,i),null!==(o=this.id)&&void 0!==o||(this.id=su()),this.resolveColor(),null!==(r=this.dataLabelOnNull)&&void 0!==r||(this.dataLabelOnNull=t.options.nullInteraction),t.chart.pointCount++,so(this,"afterInit")}return t.prototype.animateBeforeDestroy=function(){var t=this,e={x:t.startXPos,opacity:0},i=t.getGraphicalProps();i.singular.forEach(function(i){t[i]=t[i].animate("dataLabel"===i?{x:t[i].startXPos,y:t[i].startYPos,opacity:0}:e)}),i.plural.forEach(function(e){t[e].forEach(function(e){e.element&&e.animate(si({x:t.startXPos},e.startYPos?{x:e.startXPos,y:e.startYPos}:{}))})})},t.prototype.applyOptions=function(e,i){var o=this.series,r=o.options.pointValKey||o.pointValKey;return si(this,e=t.prototype.optionsToObject.call(this,e)),this.options=this.options?si(this.options,e):e,e.group&&delete this.group,e.dataLabels&&delete this.dataLabels,r&&(this.y=t.prototype.getNestedProperty.call(this,r)),this.selected&&(this.state="select"),"name"in this&&void 0===i&&o.xAxis&&o.xAxis.hasNames&&(this.x=o.xAxis.nameToX(this)),void 0===this.x&&o?this.x=null!=i?i:o.autoIncrement():sa(e.x)&&o.options.relativeXValue?this.x=o.autoIncrement(e.x):"string"==typeof this.x&&(null!=i||(i=o.chart.time.parse(this.x)),sa(i)&&(this.x=i)),this.isNull=this.isValid&&!this.isValid(),this.formatPrefix=this.isNull?"null":"point",this},t.prototype.destroy=function(){if(!this.destroyed){var t=this,e=t.series,i=e.chart,o=e.options.dataSorting,r=i.hoverPoints,s=eu(t.series.chart.renderer.globalAnimation),n=function(){for(var e in(t.graphic||t.graphics||t.dataLabel||t.dataLabels)&&(sp(t),t.destroyElements()),t)delete t[e]};t.legendItem&&i.legend.destroyItem(t),r&&(t.setState(),se(r,t),r.length||(i.hoverPoints=null)),t===i.hoverPoint&&t.onMouseOut(),(null==o?void 0:o.enabled)?(this.animateBeforeDestroy(),sc(n,s.duration)):n(),i.pointCount--}this.destroyed=!0},t.prototype.destroyElements=function(t){var e=this,i=e.getGraphicalProps(t);i.singular.forEach(function(t){e[t]=e[t].destroy()}),i.plural.forEach(function(t){e[t].forEach(function(t){(null==t?void 0:t.element)&&t.destroy()}),delete e[t]})},t.prototype.firePointEvent=function(t,e,i){var o=this,r=this.series.options;o.manageEvent(t),"click"===t&&r.allowPointSelect&&(i=function(t){!o.destroyed&&o.select&&o.select(null,t.ctrlKey||t.metaKey||t.shiftKey)}),so(o,t,e,i)},t.prototype.getClassName=function(){var t;return"highcharts-point"+(this.selected?" highcharts-point-select":"")+(this.negative?" highcharts-negative":"")+(this.isNull?" highcharts-null-point":"")+(void 0!==this.colorIndex?" highcharts-color-"+this.colorIndex:"")+(this.options.className?" "+this.options.className:"")+((null===(t=this.zone)||void 0===t?void 0:t.className)?" "+this.zone.className.replace("highcharts-negative",""):"")},t.prototype.getGraphicalProps=function(t){var e,i,o=this,r=[],s={singular:[],plural:[]};for((t=t||{graphic:1,dataLabel:1}).graphic&&r.push("graphic","connector"),t.dataLabel&&r.push("dataLabel","dataLabelPath","dataLabelUpper"),i=r.length;i--;)o[e=r[i]]&&s.singular.push(e);return["graphic","dataLabel"].forEach(function(e){var i=e+"s";t[e]&&o[i]&&s.plural.push(i)}),s},t.prototype.getNestedProperty=function(t){return t?0===t.indexOf("custom.")?sr(t,this.options):this[t]:void 0},t.prototype.getZone=function(){var t,e=this.series,i=e.zones,o=e.zoneAxis||"y",r=0;for(t=i[0];this[o]>=t.value;)t=i[++r];return this.nonZonedColor||(this.nonZonedColor=this.color),(null==t?void 0:t.color)&&!this.options.color?this.color=t.color:this.color=this.nonZonedColor,t},t.prototype.hasNewShapeType=function(){return(this.graphic&&(this.graphic.symbolName||this.graphic.element.nodeName))!==this.shapeType},t.prototype.isValid=function(){return(sa(this.x)||this.x instanceof Date)&&sa(this.y)},t.prototype.optionsToObject=function(e){var i,o,r=this.series,s=r.options.keys,n=s||r.pointArrayMap||["y"],a=n.length,h={},l=0,d=0;if(sa(e)||null===e)h[n[0]]=e;else if(ss(e))for(!s&&e.length>a&&("string"==(o=typeof e[0])?(null===(i=r.xAxis)||void 0===i?void 0:i.dateTime)?h.x=r.chart.time.parse(e[0]):h.name=e[0]:"number"===o&&(h.x=e[0]),l++);d<a;)s&&void 0===e[l]||(n[d].indexOf(".")>0?t.prototype.setNestedProperty(h,e[l],n[d]):h[n[d]]=e[l]),l++,d++;else"object"==typeof e&&(h=e,e.dataLabels&&(r.hasDataLabels=function(){return!0}),e.marker&&(r._hasPointMarkers=!0));return h},t.prototype.pos=function(t,e){if(void 0===e&&(e=this.plotY),!this.destroyed){var i=this.plotX,o=this.series,r=o.chart,s=o.xAxis,n=o.yAxis,a=0,h=0;if(sa(i)&&sa(e))return t&&(a=s?s.pos:r.plotLeft,h=n?n.pos:r.plotTop),r.inverted&&s&&n?[n.len-e+h,s.len-i+a]:[i+a,e+h]}},t.prototype.resolveColor=function(){var t,e,i,o=this.series,r=o.chart.options.chart,s=o.chart.styledMode,n=r.colorCount;delete this.nonZonedColor,o.options.colorByPoint?(s||(t=(e=o.options.colors||o.chart.options.colors)[o.colorCounter],n=e.length),i=o.colorCounter,o.colorCounter++,o.colorCounter===n&&(o.colorCounter=0)):(s||(t=o.color),i=o.colorIndex),this.colorIndex=sd(this.options.colorIndex,i),this.color=sd(this.options.color,t)},t.prototype.setNestedProperty=function(t,e,i){return i.split(".").reduce(function(t,i,o,r){var s=r.length-1===o;return t[i]=s?e:sh(t[i],!0)?t[i]:{},t[i]},t),t},t.prototype.shouldDraw=function(){return!this.isNull},t.prototype.tooltipFormatter=function(t){var e,i=this.series,o=i.chart,r=i.pointArrayMap,s=i.tooltipOptions,n=s.valueDecimals,a=void 0===n?"":n,h=s.valuePrefix,l=void 0===h?"":h,d=s.valueSuffix,c=void 0===d?"":d;return o.styledMode&&(t=(null===(e=o.tooltip)||void 0===e?void 0:e.styledModeFormat(t))||t),(void 0===r?["y"]:r).forEach(function(e){e="{point."+e,(l||c)&&(t=t.replace(RegExp(e+"}","g"),l+e+"}"+c)),t=t.replace(RegExp(e+"}","g"),e+":,."+a+"f}")}),r8(t,this,o)},t.prototype.update=function(t,e,i,o){var r,s=this,n=s.series,a=s.graphic,h=n.chart,l=n.options;function d(){s.applyOptions(t);var o=a&&s.hasMockGraphic,d=null===s.y?!o:o;a&&d&&(s.graphic=a.destroy(),delete s.hasMockGraphic),sh(t,!0)&&((null==a?void 0:a.element)&&t&&t.marker&&void 0!==t.marker.symbol&&(s.graphic=a.destroy()),(null==t?void 0:t.dataLabels)&&s.dataLabel&&(s.dataLabel=s.dataLabel.destroy())),r=s.index;for(var c={},p=0,u=n.dataColumnKeys();p<u.length;p++){var f=u[p];c[f]=s[f]}n.dataTable.setRow(c,r),l.data[r]=sh(l.data[r],!0)||sh(t,!0)?s.options:sd(t,l.data[r]),n.isDirty=n.isDirtyData=!0,!n.fixedBox&&n.hasCartesianSeries&&(h.isDirtyBox=!0),"point"===l.legendType&&(h.isDirtyLegend=!0),e&&h.redraw(i)}e=sd(e,!0),!1===o?d():s.firePointEvent("update",{options:t},d)},t.prototype.remove=function(t,e){this.series.removePoint(this.series.data.indexOf(this),t,e)},t.prototype.select=function(t,e){var i=this,o=i.series,r=o.chart;t=sd(t,!i.selected),this.selectedStaging=t,i.firePointEvent(t?"select":"unselect",{accumulate:e},function(){i.selected=i.options.selected=t,o.options.data[o.data.indexOf(i)]=i.options,i.setState(t&&"select"),e||r.getSelectedPoints().forEach(function(t){var e=t.series;t.selected&&t!==i&&(t.selected=t.options.selected=!1,e.options.data[e.data.indexOf(t)]=t.options,t.setState(r.hoverPoints&&e.options.inactiveOtherPoints?"inactive":""),t.firePointEvent("unselect"))})}),delete this.selectedStaging},t.prototype.onMouseOver=function(t){var e=this.series.chart,i=e.inverted,o=e.pointer;o&&(t=t?o.normalize(t):o.getChartCoordinatesFromPoint(this,i),o.runPointActions(t,this))},t.prototype.onMouseOut=function(){var t=this.series.chart;this.firePointEvent("mouseOut"),this.series.options.inactiveOtherPoints||(t.hoverPoints||[]).forEach(function(t){t.setState()}),t.hoverPoints=t.hoverPoint=null},t.prototype.manageEvent=function(t){var e,i,o,r,s,n,a,h=null===(e=sl(this.series.options.point,this.options).events)||void 0===e?void 0:e[t];!sn(h)||(null===(i=this.hcEvents)||void 0===i?void 0:i[t])&&(null===(r=null===(o=this.hcEvents)||void 0===o?void 0:o[t])||void 0===r?void 0:r.map(function(t){return t.fn}).indexOf(h))!==-1?this.importedUserEvent&&!h&&(null===(n=this.hcEvents)||void 0===n?void 0:n[t])&&(null===(a=this.hcEvents)||void 0===a?void 0:a[t].userEvent)&&(sp(this,t),delete this.hcEvents[t],Object.keys(this.hcEvents)||delete this.importedUserEvent):(null===(s=this.importedUserEvent)||void 0===s||s.call(this),this.importedUserEvent=r7(this,t,h),this.hcEvents&&(this.hcEvents[t].userEvent=!0))},t.prototype.setState=function(t,e){var i,o,r,s,n,a,h=this.series,l=this.state,d=h.options.states[t||"normal"]||{},c=tJ.plotOptions[h.type].marker&&h.options.marker,p=c&&!1===c.enabled,u=(null===(i=null==c?void 0:c.states)||void 0===i?void 0:i[t||"normal"])||{},f=!1===u.enabled,g=this.marker||{},v=h.chart,m=c&&h.markerAttribs,y=h.halo,x=h.stateMarkerGraphic;if(((t=t||"")!==this.state||e)&&(!this.selected||"select"===t)&&!1!==d.enabled&&(!t||!f&&(!p||!1!==u.enabled))&&(!t||!g.states||!g.states[t]||!1!==g.states[t].enabled)){if(this.state=t,m&&(r=h.markerAttribs(this,t)),this.graphic&&!this.hasMockGraphic){if(l&&this.graphic.removeClass("highcharts-point-"+l),t&&this.graphic.addClass("highcharts-point-"+t),!v.styledMode){s=h.pointAttribs(this,t),n=sd(v.options.chart.animation,d.animation);var b=s.opacity;h.options.inactiveOtherPoints&&sa(b)&&(this.dataLabels||[]).forEach(function(t){t&&!t.hasClass("highcharts-data-label-hidden")&&(t.animate({opacity:b},n),t.connector&&t.connector.animate({opacity:b},n))}),this.graphic.animate(s,n)}r&&this.graphic.animate(r,sd(v.options.chart.animation,u.animation,c.animation)),x&&x.hide()}else t&&u&&(a=g.symbol||h.symbol,x&&x.currentSymbol!==a&&(x=x.destroy()),r&&(x?x[e?"animate":"attr"]({x:r.x,y:r.y}):a&&(h.stateMarkerGraphic=x=v.renderer.symbol(a,r.x,r.y,r.width,r.height,sl(c,u)).add(h.markerGroup),x.currentSymbol=a)),!v.styledMode&&x&&"inactive"!==this.state&&x.attr(h.pointAttribs(this,t))),x&&(x[t&&this.isInside?"show":"hide"](),x.element.point=this,x.addClass(this.getClassName(),!0));var k=d.halo,M=this.graphic||x,w=(null==M?void 0:M.visibility)||"inherit";(null==k?void 0:k.size)&&M&&"hidden"!==w&&!this.isCluster?(y||(h.halo=y=v.renderer.path().add(M.parentGroup)),y.show()[e?"animate":"attr"]({d:this.haloPath(k.size)}),y.attr({class:"highcharts-halo highcharts-color-"+sd(this.colorIndex,h.colorIndex)+(this.className?" "+this.className:""),visibility:w,zIndex:-1}),y.point=this,v.styledMode||y.attr(si({fill:this.color||h.color,"fill-opacity":k.opacity},eL.filterUserAttributes(k.attributes||{})))):(null===(o=null==y?void 0:y.point)||void 0===o?void 0:o.haloPath)&&!y.point.destroyed&&y.animate({d:y.point.haloPath(0)},null,y.hide),so(this,"afterSetState",{state:t})}},t.prototype.haloPath=function(t){var e=this.pos();return e?this.series.chart.renderer.symbols.circle(st(e[0],1)-t,e[1]-t,2*t,2*t):[]},t}(),sg=function(){return(sg=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},sv=t7.parse,sm=tt.charts,sy=tt.composed,sx=tt.isTouchDevice,sb=tT.addEvent,sk=tT.attr,sM=tT.css,sw=tT.extend,sS=tT.find,sA=tT.fireEvent,sT=tT.isNumber,sC=tT.isObject,sO=tT.objectEach,sP=tT.offset,sE=tT.pick,sL=tT.pushUnique,sD=tT.splat,sI=function(){function t(t,e){var i;this.hasDragged=0,this.pointerCaptureEventsToUnbind=[],this.eventsToUnbind=[],this.options=e,this.chart=t,this.runChartClick=!!(null===(i=e.chart.events)||void 0===i?void 0:i.click),this.pinchDown=[],this.setDOMEvents(),sA(this,"afterInit")}return t.prototype.applyInactiveState=function(t){var e=this;void 0===t&&(t=[]);var i=[];t.forEach(function(t){var o=t.series;i.push(o),o.linkedParent&&i.push(o.linkedParent),o.linkedSeries&&i.push.apply(i,o.linkedSeries),o.navigatorSeries&&i.push(o.navigatorSeries),o.boosted&&o.markerGroup&&i.push.apply(i,e.chart.series.filter(function(t){return t.markerGroup===o.markerGroup}))}),this.chart.series.forEach(function(t){-1===i.indexOf(t)?t.setState("inactive",!0):t.options.inactiveOtherPoints&&t.setAllPointsToState("inactive")})},t.prototype.destroy=function(){var e=this;this.eventsToUnbind.forEach(function(t){return t()}),this.eventsToUnbind=[],!tt.chartCount&&(t.unbindDocumentMouseUp.forEach(function(t){return t.unbind()}),t.unbindDocumentMouseUp.length=0,t.unbindDocumentTouchEnd&&(t.unbindDocumentTouchEnd=t.unbindDocumentTouchEnd())),clearInterval(e.tooltipTimeout),sO(e,function(t,i){e[i]=void 0})},t.prototype.getSelectionMarkerAttrs=function(t,e){var i=this,o={args:{chartX:t,chartY:e},attrs:{},shapeType:"rect"};return sA(this,"getSelectionMarkerAttrs",o,function(o){var r,s=i.chart,n=i.zoomHor,a=i.zoomVert,h=s.mouseDownX,l=void 0===h?0:h,d=s.mouseDownY,c=void 0===d?0:d,p=o.attrs;p.x=s.plotLeft,p.y=s.plotTop,p.width=n?1:s.plotWidth,p.height=a?1:s.plotHeight,n&&(p.width=Math.max(1,Math.abs(r=t-l)),p.x=(r>0?0:r)+l),a&&(p.height=Math.max(1,Math.abs(r=e-c)),p.y=(r>0?0:r)+c)}),o},t.prototype.drag=function(t){var e,i=this.chart,o=i.mouseDownX,r=void 0===o?0:o,s=i.mouseDownY,n=void 0===s?0:s,a=i.options.chart,h=a.panning,l=a.panKey,d=a.selectionMarkerFill,c=i.plotLeft,p=i.plotTop,u=i.plotWidth,f=i.plotHeight,g=sC(h)?h.enabled:h,v=l&&t[""+l+"Key"],m=t.chartX,y=t.chartY,x=this.selectionMarker;if((!x||!x.touch)&&(m<c?m=c:m>c+u&&(m=c+u),y<p?y=p:y>p+f&&(y=p+f),this.hasDragged=Math.sqrt(Math.pow(r-m,2)+Math.pow(n-y,2)),this.hasDragged>10)){e=i.isInsidePlot(r-c,n-p,{visiblePlotOnly:!0});var b=this.getSelectionMarkerAttrs(m,y),k=b.shapeType,M=b.attrs;(i.hasCartesianSeries||i.mapView)&&this.hasZoom&&e&&!v&&!x&&(this.selectionMarker=x=i.renderer[k](),x.attr({class:"highcharts-selection-marker",zIndex:7}).add(),i.styledMode||x.attr({fill:d||sv("#334eff").setOpacity(.25).get()})),x&&x.attr(M),e&&!x&&g&&i.pan(t,h)}},t.prototype.dragStart=function(t){var e=this.chart;e.mouseIsDown=t.type,e.cancelClick=!1,e.mouseDownX=t.chartX,e.mouseDownY=t.chartY},t.prototype.getSelectionBox=function(t){var e={args:{marker:t},result:t.getBBox()};return sA(this,"getSelectionBox",e),e.result},t.prototype.drop=function(t){for(var e,i=this,o=this.chart,r=this.selectionMarker,s=0,n=o.axes;s<n.length;s++){var a=n[s];a.isPanning&&(a.isPanning=!1,(a.options.startOnTick||a.options.endOnTick||a.series.some(function(t){return t.boosted}))&&(a.forceRedraw=!0,a.setExtremes(a.userMin,a.userMax,!1),e=!0))}if(e&&o.redraw(),r&&t){if(this.hasDragged){var h=this.getSelectionBox(r);o.transform({axes:o.axes.filter(function(t){return t.zoomEnabled&&("xAxis"===t.coll&&i.zoomX||"yAxis"===t.coll&&i.zoomY)}),selection:sg({originalEvent:t,xAxis:[],yAxis:[]},h),from:h})}sT(o.index)&&(this.selectionMarker=r.destroy())}o&&sT(o.index)&&(sM(o.container,{cursor:o._cursor}),o.cancelClick=this.hasDragged>10,o.mouseIsDown=!1,this.hasDragged=0,this.pinchDown=[])},t.prototype.findNearestKDPoint=function(t,e,i){var o;return t.forEach(function(t){var r,s,n,a,h,l,d,c=!(t.noSharedTooltip&&e)&&0>t.options.findNearestPointBy.indexOf("y"),p=t.searchPoint(i,c);sC(p,!0)&&p.series&&(!sC(o,!0)||(h=(r=o).distX-p.distX,l=r.dist-p.dist,d=(null===(s=p.series.group)||void 0===s?void 0:s.zIndex)-(null===(n=r.series.group)||void 0===n?void 0:n.zIndex),(0!==h&&e?h:0!==l?l:0!==d?d:r.series.index>p.series.index?-1:1)>0))&&(o=p)}),o},t.prototype.getChartCoordinatesFromPoint=function(t,e){var i,o,r=t.series,s=r.xAxis,n=r.yAxis,a=t.shapeArgs;if(s&&n){var h=null!==(o=null!==(i=t.clientX)&&void 0!==i?i:t.plotX)&&void 0!==o?o:0,l=t.plotY||0;return t.isNode&&a&&sT(a.x)&&sT(a.y)&&(h=a.x,l=a.y),e?{chartX:n.len+n.pos-l,chartY:s.len+s.pos-h}:{chartX:h+s.pos,chartY:l+n.pos}}if((null==a?void 0:a.x)&&a.y)return{chartX:a.x,chartY:a.y}},t.prototype.getChartPosition=function(){if(this.chartPosition)return this.chartPosition;var t=this.chart.container,e=sP(t);this.chartPosition={left:e.left,top:e.top,scaleX:1,scaleY:1};var i=t.offsetHeight,o=t.offsetWidth;return o>2&&i>2&&(this.chartPosition.scaleX=e.width/o,this.chartPosition.scaleY=e.height/i),this.chartPosition},t.prototype.getCoordinates=function(t){for(var e={xAxis:[],yAxis:[]},i=0,o=this.chart.axes;i<o.length;i++){var r=o[i];e[r.isXAxis?"xAxis":"yAxis"].push({axis:r,value:r.toValue(t[r.horiz?"chartX":"chartY"])})}return e},t.prototype.getHoverData=function(t,e,i,o,r,s){var n,a=[],h=function(t){return t.visible&&!(!r&&t.directTouch)&&sE(t.options.enableMouseTracking,!0)},l=e,d={chartX:s?s.chartX:void 0,chartY:s?s.chartY:void 0,shared:r};sA(this,"beforeGetHoverData",d),n=l&&!l.stickyTracking?[l]:i.filter(function(t){return t.stickyTracking&&(d.filter||h)(t)});var c=o&&t||!s?t:this.findNearestKDPoint(n,r,s);return l=null==c?void 0:c.series,c&&(r&&!l.noSharedTooltip?(n=i.filter(function(t){return d.filter?d.filter(t):h(t)&&!t.noSharedTooltip})).forEach(function(t){var e,i=null===(e=t.options)||void 0===e?void 0:e.nullInteraction,o=sS(t.points,function(t){return t.x===c.x&&(!t.isNull||!!i)});sC(o)&&(t.boosted&&t.boost&&(o=t.boost.getPoint(o)),a.push(o))}):a.push(c)),sA(this,"afterGetHoverData",d={hoverPoint:c}),{hoverPoint:d.hoverPoint,hoverSeries:l,hoverPoints:a}},t.prototype.getPointFromEvent=function(t){for(var e,i=t.target;i&&!e;)e=i.point,i=i.parentNode;return e},t.prototype.onTrackerMouseOut=function(t){var e=this.chart,i=t.relatedTarget,o=e.hoverSeries;this.isDirectTouch=!1,!o||!i||o.stickyTracking||this.inClass(i,"highcharts-tooltip")||this.inClass(i,"highcharts-series-"+o.index)&&this.inClass(i,"highcharts-tracker")||o.onMouseOut()},t.prototype.inClass=function(t,e){for(var i,o=t;o;){if(i=sk(o,"class")){if(-1!==i.indexOf(e))return!0;if(-1!==i.indexOf("highcharts-container"))return!1}o=o.parentElement}},t.prototype.normalize=function(t,e){var i=t.touches,o=i?i.length?i.item(0):sE(i.changedTouches,t.changedTouches)[0]:t;e||(e=this.getChartPosition());var r=o.pageX-e.left,s=o.pageY-e.top;return sw(t,{chartX:Math.round(r/=e.scaleX),chartY:Math.round(s/=e.scaleY)})},t.prototype.onContainerClick=function(t){var e=this.chart,i=e.hoverPoint,o=this.normalize(t),r=e.plotLeft,s=e.plotTop;!e.cancelClick&&(i&&this.inClass(o.target,"highcharts-tracker")?(sA(i.series,"click",sw(o,{point:i})),e.hoverPoint&&i.firePointEvent("click",o)):(sw(o,this.getCoordinates(o)),e.isInsidePlot(o.chartX-r,o.chartY-s,{visiblePlotOnly:!0})&&sA(e,"click",o)))},t.prototype.onContainerMouseDown=function(t){var e,i=(1&(t.buttons||t.button))==1;t=this.normalize(t),tt.isFirefox&&0!==t.button&&this.onContainerMouseMove(t),(void 0===t.button||i)&&(this.zoomOption(t),i&&(null===(e=t.preventDefault)||void 0===e||e.call(t)),this.dragStart(t))},t.prototype.onContainerMouseLeave=function(e){var i=(sm[sE(t.hoverChartIndex,-1)]||{}).pointer;e=this.normalize(e),this.onContainerMouseMove(e),i&&!this.inClass(e.relatedTarget,"highcharts-tooltip")&&(i.reset(),i.chartPosition=void 0)},t.prototype.onContainerMouseEnter=function(){delete this.chartPosition},t.prototype.onContainerMouseMove=function(t){var e=this.chart,i=e.tooltip,o=this.normalize(t);this.setHoverChartIndex(t),("mousedown"===e.mouseIsDown||this.touchSelect(o))&&this.drag(o),!e.openMenu&&(this.inClass(o.target,"highcharts-tracker")||e.isInsidePlot(o.chartX-e.plotLeft,o.chartY-e.plotTop,{visiblePlotOnly:!0}))&&!(null==i?void 0:i.shouldStickOnContact(o))&&(this.inClass(o.target,"highcharts-no-tooltip")?this.reset(!1,0):this.runPointActions(o))},t.prototype.onDocumentTouchEnd=function(t){this.onDocumentMouseUp(t)},t.prototype.onContainerTouchMove=function(t){this.touchSelect(t)?this.onContainerMouseMove(t):this.touch(t)},t.prototype.onContainerTouchStart=function(t){this.touchSelect(t)?this.onContainerMouseDown(t):(this.zoomOption(t),this.touch(t,!0))},t.prototype.onDocumentMouseMove=function(t){var e=this.chart,i=e.tooltip,o=this.chartPosition,r=this.normalize(t,o);!o||e.isInsidePlot(r.chartX-e.plotLeft,r.chartY-e.plotTop,{visiblePlotOnly:!0})||(null==i?void 0:i.shouldStickOnContact(r))||r.target!==e.container.ownerDocument&&this.inClass(r.target,"highcharts-tracker")||this.reset()},t.prototype.onDocumentMouseUp=function(e){var i,o;null===(o=null===(i=sm[sE(t.hoverChartIndex,-1)])||void 0===i?void 0:i.pointer)||void 0===o||o.drop(e)},t.prototype.pinch=function(t){var e=this,i=this,o=i.chart,r=i.hasZoom,s=i.lastTouches,n=[].map.call(t.touches||[],function(t){return i.normalize(t)}),a=n.length,h=1===a&&(i.inClass(t.target,"highcharts-tracker")&&o.runTrackerClick||i.runChartClick),l=o.tooltip,d=1===a&&sE(null==l?void 0:l.options.followTouchMove,!0);a>1?i.initiated=!0:d&&(i.initiated=!1),r&&i.initiated&&!h&&!1!==t.cancelable&&t.preventDefault(),"touchstart"===t.type?(i.pinchDown=n,i.res=!0,o.mouseDownX=t.chartX):d?this.runPointActions(i.normalize(t)):s&&(sA(o,"touchpan",{originalEvent:t,touches:n},function(){var i=function(t){var e=t[0],i=t[1]||e;return{x:e.chartX,y:e.chartY,width:i.chartX-e.chartX,height:i.chartY-e.chartY}};o.transform({axes:o.axes.filter(function(t){return t.zoomEnabled&&(e.zoomHor&&t.horiz||e.zoomVert&&!t.horiz)}),to:i(n),from:i(s),trigger:t.type})}),i.res&&(i.res=!1,this.reset(!1,0))),i.lastTouches=n},t.prototype.reset=function(t,e){var i=this.chart,o=i.hoverSeries,r=i.hoverPoint,s=i.hoverPoints,n=i.tooltip,a=(null==n?void 0:n.shared)?s:r;t&&a&&sD(a).forEach(function(e){e.series.isCartesian&&void 0===e.plotX&&(t=!1)}),t?n&&a&&sD(a).length&&(n.refresh(a),n.shared&&s?s.forEach(function(t){t.setState(t.state,!0),t.series.isCartesian&&(t.series.xAxis.crosshair&&t.series.xAxis.drawCrosshair(null,t),t.series.yAxis.crosshair&&t.series.yAxis.drawCrosshair(null,t))}):r&&(r.setState(r.state,!0),i.axes.forEach(function(t){t.crosshair&&r.series[t.coll]===t&&t.drawCrosshair(null,r)}))):(r&&r.onMouseOut(),s&&s.forEach(function(t){t.setState()}),o&&o.onMouseOut(),n&&n.hide(e),this.unDocMouseMove&&(this.unDocMouseMove=this.unDocMouseMove()),i.axes.forEach(function(t){t.hideCrosshair()}),i.hoverPoints=i.hoverPoint=void 0)},t.prototype.runPointActions=function(e,i,o){var r,s=this.chart,n=s.series,a=(null===(r=s.tooltip)||void 0===r?void 0:r.options.enabled)?s.tooltip:void 0,h=!!a&&a.shared,l=i||s.hoverPoint,d=(null==l?void 0:l.series)||s.hoverSeries,c=(!e||"touchmove"!==e.type)&&(!!i||(null==d?void 0:d.directTouch)&&this.isDirectTouch),p=this.getHoverData(l,d,n,c,h,e);l=p.hoverPoint,d=p.hoverSeries;var u=p.hoverPoints,f=(null==d?void 0:d.tooltipOptions.followPointer)&&!d.tooltipOptions.split,g=h&&d&&!d.noSharedTooltip;if(l&&(o||l!==s.hoverPoint||(null==a?void 0:a.isHidden))){if((s.hoverPoints||[]).forEach(function(t){-1===u.indexOf(t)&&t.setState()}),s.hoverSeries!==d&&d.onMouseOver(),this.applyInactiveState(u),(u||[]).forEach(function(t){t.setState("hover")}),s.hoverPoint&&s.hoverPoint.firePointEvent("mouseOut"),!l.series)return;s.hoverPoints=u,s.hoverPoint=l,l.firePointEvent("mouseOver",void 0,function(){a&&l&&a.refresh(g?u:l,e)})}else if(f&&a&&!a.isHidden){var v=a.getAnchor([{}],e);s.isInsidePlot(v[0],v[1],{visiblePlotOnly:!0})&&a.updatePosition({plotX:v[0],plotY:v[1]})}this.unDocMouseMove||(this.unDocMouseMove=sb(s.container.ownerDocument,"mousemove",function(e){var i,o,r;return null===(r=null===(o=sm[null!==(i=t.hoverChartIndex)&&void 0!==i?i:-1])||void 0===o?void 0:o.pointer)||void 0===r?void 0:r.onDocumentMouseMove(e)}),this.eventsToUnbind.push(this.unDocMouseMove)),s.axes.forEach(function(t){var i,o,r,n=null===(o=null===(i=t.crosshair)||void 0===i?void 0:i.snap)||void 0===o||o;!n||(r=s.hoverPoint)&&r.series[t.coll]===t||(r=sS(u,function(e){var i;return(null===(i=e.series)||void 0===i?void 0:i[t.coll])===t})),r||!n?t.drawCrosshair(e,r):t.hideCrosshair()})},t.prototype.setDOMEvents=function(){var e=this,i=this.chart.container,o=i.ownerDocument;i.onmousedown=this.onContainerMouseDown.bind(this),i.onmousemove=this.onContainerMouseMove.bind(this),i.onclick=this.onContainerClick.bind(this),this.eventsToUnbind.push(sb(i,"mouseenter",this.onContainerMouseEnter.bind(this)),sb(i,"mouseleave",this.onContainerMouseLeave.bind(this))),t.unbindDocumentMouseUp.some(function(t){return t.doc===o})||t.unbindDocumentMouseUp.push({doc:o,unbind:sb(o,"mouseup",this.onDocumentMouseUp.bind(this))});for(var r=this.chart.renderTo.parentElement;r&&"BODY"!==r.tagName;)this.eventsToUnbind.push(sb(r,"scroll",function(){delete e.chartPosition})),r=r.parentElement;this.eventsToUnbind.push(sb(i,"touchstart",this.onContainerTouchStart.bind(this),{passive:!1}),sb(i,"touchmove",this.onContainerTouchMove.bind(this),{passive:!1})),t.unbindDocumentTouchEnd||(t.unbindDocumentTouchEnd=sb(o,"touchend",this.onDocumentTouchEnd.bind(this),{passive:!1})),this.setPointerCapture(),sb(this.chart,"redraw",this.setPointerCapture.bind(this))},t.prototype.setPointerCapture=function(){if(sx){var t,e,i=this.pointerCaptureEventsToUnbind,o=this.chart,r=o.container,s=sE(null===(t=o.options.tooltip)||void 0===t?void 0:t.followTouchMove,!0)&&o.series.some(function(t){return t.options.findNearestPointBy.indexOf("y")>-1});!this.hasPointerCapture&&s?(i.push(sb(r,"pointerdown",function(t){var e,i;(null===(e=t.target)||void 0===e?void 0:e.hasPointerCapture(t.pointerId))&&(null===(i=t.target)||void 0===i||i.releasePointerCapture(t.pointerId))}),sb(r,"pointermove",function(t){var e,i;null===(i=null===(e=o.pointer)||void 0===e?void 0:e.getPointFromEvent(t))||void 0===i||i.onMouseOver(t)})),o.styledMode||sM(r,{"touch-action":"none"}),r.className+=" highcharts-no-touch-action",this.hasPointerCapture=!0):this.hasPointerCapture&&!s&&(i.forEach(function(t){return t()}),i.length=0,o.styledMode||sM(r,{"touch-action":sE(null===(e=o.options.chart.style)||void 0===e?void 0:e["touch-action"],"manipulation")}),r.className=r.className.replace(" highcharts-no-touch-action",""),this.hasPointerCapture=!1)}},t.prototype.setHoverChartIndex=function(e){var i,o=this.chart,r=tt.charts[sE(t.hoverChartIndex,-1)];if(r&&r!==o){var s={relatedTarget:o.container};!e||(null==e?void 0:e.relatedTarget)||Object.assign({},e,s),null===(i=r.pointer)||void 0===i||i.onContainerMouseLeave(e||s)}(null==r?void 0:r.mouseIsDown)||(t.hoverChartIndex=o.index)},t.prototype.touch=function(t,e){var i,o=this.chart,r=this.pinchDown,s=void 0===r?[]:r;this.setHoverChartIndex(),1===(t=this.normalize(t)).touches.length?o.isInsidePlot(t.chartX-o.plotLeft,t.chartY-o.plotTop,{visiblePlotOnly:!0})&&!o.openMenu?(e&&this.runPointActions(t),"touchmove"===t.type&&(i=!!s[0]&&Math.pow(s[0].chartX-t.chartX,2)+Math.pow(s[0].chartY-t.chartY,2)>=16),sE(i,!0)&&this.pinch(t)):e&&this.reset():2===t.touches.length&&this.pinch(t)},t.prototype.touchSelect=function(t){return!!(this.chart.zooming.singleTouch&&t.touches&&1===t.touches.length)},t.prototype.zoomOption=function(t){var e,i,o=this.chart,r=o.inverted,s=o.zooming.type||"";/touch/.test(t.type)&&(s=sE(o.zooming.pinchType,s)),this.zoomX=e=/x/.test(s),this.zoomY=i=/y/.test(s),this.zoomHor=e&&!r||i&&r,this.zoomVert=i&&!r||e&&r,this.hasZoom=e||i},t.unbindDocumentMouseUp=[],t}();(g=sI||(sI={})).compose=function(t){sL(sy,"Core.Pointer")&&sb(t,"beforeRender",function(){this.pointer=new g(this,this.options)})};var sB=sI,sz=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,s=e.length;r<s;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))};(v=H||(H={})).setLength=function(t,e,i){return Array.isArray(t)?(t.length=e,t):t[i?"subarray":"slice"](0,e)},v.splice=function(t,e,i,o,r){if(void 0===r&&(r=[]),Array.isArray(t))return Array.isArray(r)||(r=Array.from(r)),{removed:t.splice.apply(t,sz([e,i],r,!1)),array:t};var s=Object.getPrototypeOf(t).constructor,n=t[o?"subarray":"slice"](e,e+i),a=new s(t.length-i+r.length);return a.set(t.subarray(0,e),0),a.set(r,e),a.set(t.subarray(e+i),e+r.length),{removed:n,array:a}};var sR=H,sN=sR.setLength,sW=sR.splice,sX=tT.fireEvent,sH=tT.objectEach,sF=tT.uniqueKey,sj=function(){function t(t){void 0===t&&(t={});var e=this;this.autoId=!t.id,this.columns={},this.id=t.id||sF(),this.modified=this,this.rowCount=0,this.versionTag=sF();var i=0;sH(t.columns||{},function(t,o){e.columns[o]=t.slice(),i=Math.max(i,t.length)}),this.applyRowCount(i)}return t.prototype.applyRowCount=function(t){var e=this;this.rowCount=t,sH(this.columns,function(i,o){i.length!==t&&(e.columns[o]=sN(i,t))})},t.prototype.deleteRows=function(t,e){var i=this;if(void 0===e&&(e=1),e>0&&t<this.rowCount){var o=0;sH(this.columns,function(r,s){i.columns[s]=sW(r,t,e).array,o=r.length}),this.rowCount=o}sX(this,"afterDeleteRows",{rowIndex:t,rowCount:e}),this.versionTag=sF()},t.prototype.getColumn=function(t,e){return this.columns[t]},t.prototype.getColumns=function(t,e){var i=this;return(t||Object.keys(this.columns)).reduce(function(t,e){return t[e]=i.columns[e],t},{})},t.prototype.getRow=function(t,e){var i=this;return(e||Object.keys(this.columns)).map(function(e){var o;return null===(o=i.columns[e])||void 0===o?void 0:o[t]})},t.prototype.setColumn=function(t,e,i,o){var r;void 0===e&&(e=[]),void 0===i&&(i=0),this.setColumns(((r={})[t]=e,r),i,o)},t.prototype.setColumns=function(t,e,i){var o=this,r=this.rowCount;sH(t,function(t,e){o.columns[e]=t.slice(),r=t.length}),this.applyRowCount(r),(null==i?void 0:i.silent)||(sX(this,"afterSetColumns"),this.versionTag=sF())},t.prototype.setRow=function(t,e,i,o){void 0===e&&(e=this.rowCount);var r=this.columns,s=i?this.rowCount+1:e+1;sH(t,function(t,n){var a=r[n]||(null==o?void 0:o.addColumns)!==!1&&Array(s);a&&(i?a=sW(a,e,0,!0,[t]).array:a[e]=t,r[n]=a)}),s>this.rowCount&&this.applyRowCount(s),(null==o?void 0:o.silent)||(sX(this,"afterSetRows"),this.versionTag=sF())},t}(),sY=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,s=e.length;r<s;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},sG=tT.extend,s_=tT.merge,sU=tT.pick;!function(t){function e(t,e,i){var o,r,s,n=this.legendItem=this.legendItem||{},a=this.chart,h=this.options,l=t.baseline,d=void 0===l?0:l,c=t.symbolWidth,p=t.symbolHeight,u=this.symbol||"circle",f=p/2,g=a.renderer,v=n.group,m=d-Math.round(((null===(o=t.fontMetrics)||void 0===o?void 0:o.b)||p)*(i?.4:.3)),y={},x=h.marker,b=0;if(a.styledMode||(y["stroke-width"]=Math.min(h.lineWidth||0,24),h.dashStyle?y.dashstyle=h.dashStyle:"square"===h.linecap||(y["stroke-linecap"]="round")),n.line=g.path().addClass("highcharts-graph").attr(y).add(v),i&&(n.area=g.path().addClass("highcharts-area").add(v)),y["stroke-linecap"]&&(b=Math.min(n.line.strokeWidth(),c)/2),c){var k=[["M",b,m],["L",c-b,m]];n.line.attr({d:k}),null===(r=n.area)||void 0===r||r.attr({d:sY(sY([],k,!0),[["L",c-b,d],["L",b,d]],!1)})}if(x&&!1!==x.enabled&&c){var M=Math.min(sU(x.radius,f),f);0===u.indexOf("url")&&(x=s_(x,{width:p,height:p}),M=0),n.symbol=s=g.symbol(u,c/2-M,m-M,2*M,2*M,sG({context:"legend"},x)).addClass("highcharts-point").add(v),s.isMarker=!0}}t.areaMarker=function(t,i){e.call(this,t,i,!0)},t.lineMarker=e,t.rectangle=function(t,e){var i=e.legendItem||{},o=t.options,r=t.symbolHeight,s=o.squareSymbol,n=s?r:t.symbolWidth;i.symbol=this.chart.renderer.rect(s?(t.symbolWidth-r)/2:0,t.baseline-r+1,n,r,sU(t.options.symbolRadius,r/2)).addClass("highcharts-point").attr({zIndex:3}).add(i.group)}}(F||(F={}));var sV=F,sZ={lineWidth:2,allowPointSelect:!1,crisp:!0,showCheckbox:!1,animation:{duration:1e3},enableMouseTracking:!0,events:{},marker:{enabledThreshold:2,lineColor:"#ffffff",lineWidth:0,radius:4,states:{normal:{animation:!0},hover:{animation:{duration:150},enabled:!0,radiusPlus:2,lineWidthPlus:1},select:{fillColor:"#cccccc",lineColor:"#000000",lineWidth:2}}},point:{events:{}},dataLabels:{animation:{},align:"center",borderWidth:0,defer:!0,formatter:function(){var t=this.series.chart.numberFormatter;return"number"!=typeof this.y?"":t(this.y,-1)},padding:5,style:{fontSize:"0.7em",fontWeight:"bold",color:"contrast",textOutline:"1px contrast"},verticalAlign:"bottom",x:0,y:0},cropThreshold:300,opacity:1,pointRange:0,softThreshold:!0,states:{normal:{animation:!0},hover:{animation:{duration:150},lineWidthPlus:1,marker:{},halo:{size:10,opacity:.25}},select:{animation:{duration:0}},inactive:{animation:{duration:150},opacity:.2}},stickyTracking:!0,turboThreshold:1e3,findNearestPointBy:"x"},sq=(m=function(t,e){return(m=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}m(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),sK=tT.extend,s$=tT.extendClass,sJ=tT.merge;!function(t){function e(e,i){var o=tJ.plotOptions||{},r=i.defaultOptions,s=i.prototype;return s.type=e,s.pointClass||(s.pointClass=sf),!t.seriesTypes[e]&&(r&&(o[e]=r),t.seriesTypes[e]=i,!0)}t.seriesTypes=tt.seriesTypes,t.registerSeriesType=e,t.seriesType=function(i,o,r,s,n){var a=tJ.plotOptions||{};if(o=o||"",a[i]=sJ(a[o],r),delete t.seriesTypes[i],e(i,s$(t.seriesTypes[o]||function(){},s)),t.seriesTypes[i].prototype.type=i,n){var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return sq(e,t),e}(sf);sK(h.prototype,n),t.seriesTypes[i].prototype.pointClass=h}return t.seriesTypes[i]}}(j||(j={}));var sQ=j,s0=function(){return(s0=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},s1=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,s=e.length;r<s;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},s2=oB.registerEventOptions,s3=tt.svg,s5=tt.win,s6=sQ.seriesTypes,s9=e_.format,s4=tT.arrayMax,s8=tT.arrayMin,s7=tT.clamp,nt=tT.correctFloat,ne=tT.crisp,ni=tT.defined,no=tT.destroyObjectProperties,nr=tT.diffObjects,ns=tT.erase,nn=tT.error,na=tT.extend,nh=tT.find,nl=tT.fireEvent,nd=tT.getClosestDistance,nc=tT.getNestedProperty,np=tT.insertItem,nu=tT.isArray,nf=tT.isNumber,ng=tT.isString,nv=tT.merge,nm=tT.objectEach,ny=tT.pick,nx=tT.removeEvent,nb=tT.syncTimeout,nk=function(){function t(){this.zoneAxis="y"}return t.prototype.init=function(t,e){nl(this,"init",{options:e}),null!==(i=this.dataTable)&&void 0!==i||(this.dataTable=new sj);var i,o,r,s,n,a=t.series;this.eventsToUnbind=[],this.chart=t,this.options=this.setOptions(e);var h=this.options,l=!1!==h.visible;this.linkedSeries=[],this.bindAxes(),na(this,{name:h.name,state:"",visible:l,selected:!0===h.selected}),s2(this,h);var d=h.events;((null==d?void 0:d.click)||(null===(r=null===(o=h.point)||void 0===o?void 0:o.events)||void 0===r?void 0:r.click)||h.allowPointSelect)&&(t.runTrackerClick=!0),this.getColor(),this.getSymbol(),this.isCartesian&&(t.hasCartesianSeries=!0),a.length&&(n=a[a.length-1]),this._i=ny(null==n?void 0:n._i,-1)+1,this.opacity=this.options.opacity,t.orderItems("series",np(this,a)),(null===(s=h.dataSorting)||void 0===s?void 0:s.enabled)?this.setDataSortingOptions():this.points||this.data||this.setData(h.data,!1),nl(this,"afterInit")},t.prototype.is=function(t){return s6[t]&&this instanceof s6[t]},t.prototype.bindAxes=function(){var t,e=this,i=e.options,o=e.chart;nl(this,"bindAxes",null,function(){(e.axisTypes||[]).forEach(function(r){(o[r]||[]).forEach(function(o){t=o.options,(ny(i[r],0)===o.index||void 0!==i[r]&&i[r]===t.id)&&(np(e,o.series),e[r]=o,o.isDirty=!0)}),e[r]||e.optionalAxis===r||nn(18,!0,o)})}),nl(this,"afterBindAxes")},t.prototype.hasData=function(){return this.visible&&void 0!==this.dataMax&&void 0!==this.dataMin||this.visible&&this.dataTable.rowCount>0},t.prototype.hasMarkerChanged=function(t,e){var i=t.marker,o=e.marker||{};return i&&(o.enabled&&!i.enabled||o.symbol!==i.symbol||o.height!==i.height||o.width!==i.width)},t.prototype.autoIncrement=function(t){var e,i,o,r=this.options,s=this.options,n=s.pointIntervalUnit,a=s.relativeXValue,h=this.chart.time,l=null!==(i=null!==(e=this.xIncrement)&&void 0!==e?e:h.parse(r.pointStart))&&void 0!==i?i:0;if(this.pointInterval=o=ny(this.pointInterval,r.pointInterval,1),a&&nf(t)&&(o*=t),n){var d=h.toParts(l);"day"===n?d[2]+=o:"month"===n?d[1]+=o:"year"===n&&(d[0]+=o),o=h.makeTime.apply(h,d)-l}return a&&nf(t)?l+o:(this.xIncrement=l+o,l)},t.prototype.setDataSortingOptions=function(){var t=this.options;na(this,{requireSorting:!1,sorted:!1,enabledDataSorting:!0,allowDG:!1}),ni(t.pointRange)||(t.pointRange=1)},t.prototype.setOptions=function(t){var e,i,o,r=this.chart,s=r.options.plotOptions,n=r.userOptions||{},a=nv(t),h=r.styledMode,l={plotOptions:s,userOptions:a};nl(this,"setOptions",l);var d=l.plotOptions[this.type],c=n.plotOptions||{},p=c.series||{},u=tJ.plotOptions[this.type]||{},f=c[this.type]||{};d.dataLabels=this.mergeArrays(u.dataLabels,d.dataLabels),this.userOptions=l.userOptions;var g=nv(d,s.series,f,a);this.tooltipOptions=nv(tJ.tooltip,null===(e=tJ.plotOptions.series)||void 0===e?void 0:e.tooltip,null==u?void 0:u.tooltip,r.userOptions.tooltip,null===(i=c.series)||void 0===i?void 0:i.tooltip,f.tooltip,a.tooltip),this.stickyTracking=ny(a.stickyTracking,f.stickyTracking,p.stickyTracking,!!this.tooltipOptions.shared&&!this.noSharedTooltip||g.stickyTracking),null===d.marker&&delete g.marker,this.zoneAxis=g.zoneAxis||"y";var v=this.zones=(g.zones||[]).map(function(t){return s0({},t)});return(g.negativeColor||g.negativeFillColor)&&!g.zones&&(o={value:g[this.zoneAxis+"Threshold"]||g.threshold||0,className:"highcharts-negative"},h||(o.color=g.negativeColor,o.fillColor=g.negativeFillColor),v.push(o)),v.length&&ni(v[v.length-1].value)&&v.push(h?{}:{color:this.color,fillColor:this.fillColor}),nl(this,"afterSetOptions",{options:g}),g},t.prototype.getName=function(){var t;return null!==(t=this.options.name)&&void 0!==t?t:s9(this.chart.options.lang.seriesName,this,this.chart)},t.prototype.getCyclic=function(t,e,i){var o,r,s=this.chart,n=""+t+"Index",a=""+t+"Counter",h=(null==i?void 0:i.length)||s.options.chart.colorCount;!e&&(ni(r=ny("color"===t?this.options.colorIndex:void 0,this[n]))?o=r:(s.series.length||(s[a]=0),o=s[a]%h,s[a]+=1),i&&(e=i[o])),void 0!==o&&(this[n]=o),this[t]=e},t.prototype.getColor=function(){this.chart.styledMode?this.getCyclic("color"):this.options.colorByPoint?this.color="#cccccc":this.getCyclic("color",this.options.color||tJ.plotOptions[this.type].color,this.chart.options.colors)},t.prototype.getPointsCollection=function(){return(this.hasGroupedData?this.points:this.data)||[]},t.prototype.getSymbol=function(){var t=this.options.marker;this.getCyclic("symbol",t.symbol,this.chart.options.symbols)},t.prototype.getColumn=function(t,e){return(e?this.dataTable.modified:this.dataTable).getColumn(t,!0)||[]},t.prototype.findPointIndex=function(t,e){var i,o,r,s,n=t.id,a=t.x,h=this.points,l=this.options.dataSorting,d=this.cropStart||0;if(n){var c=this.chart.get(n);c instanceof sf&&(o=c)}else if(this.linkedParent||this.enabledDataSorting||this.options.relativeXValue){var p=function(e){return!e.touched&&e.index===t.index};if((null==l?void 0:l.matchByName)?p=function(e){return!e.touched&&e.name===t.name}:this.options.relativeXValue&&(p=function(e){return!e.touched&&e.options.x===t.x}),!(o=nh(h,p)))return}return o&&void 0!==(s=null==o?void 0:o.index)&&(r=!0),void 0===s&&nf(a)&&(s=this.getColumn("x").indexOf(a,e)),-1!==s&&void 0!==s&&this.cropped&&(s=s>=d?s-d:s),!r&&nf(s)&&(null===(i=h[s])||void 0===i?void 0:i.touched)&&(s=void 0),s},t.prototype.updateData=function(t,e){var i,o,r,s,n,a=this,h=this.options,l=this.requireSorting,d=h.dataSorting,c=this.points,p=[],u=t.length===c.length,f=!0;if(this.xIncrement=null,t.forEach(function(t,e){var i,r,s=ni(t)&&a.pointClass.prototype.optionsToObject.call({series:a},t)||{},f=s.id,g=s.x;f||nf(g)?(-1===(r=a.findPointIndex(s,n))||void 0===r?p.push(t):c[r]&&t!==(null===(i=h.data)||void 0===i?void 0:i[r])?(c[r].update(t,!1,void 0,!1),c[r].touched=!0,l&&(n=r+1)):c[r]&&(c[r].touched=!0),(!u||e!==r||(null==d?void 0:d.enabled)||a.hasDerivedData)&&(o=!0)):p.push(t)},this),o)for(r=c.length;r--;)(s=c[r])&&!s.touched&&(null===(i=s.remove)||void 0===i||i.call(s,!1,e));else!u||(null==d?void 0:d.enabled)?f=!1:(t.forEach(function(t,e){t===c[e].y||c[e].destroyed||c[e].update(t,!1,void 0,!1)}),p.length=0);if(c.forEach(function(t){t&&(t.touched=!1)}),!f)return!1;p.forEach(function(t){a.addPoint(t,!1,void 0,void 0,!1)},this);var g=this.getColumn("x");return null===this.xIncrement&&g.length&&(this.xIncrement=s4(g),this.autoIncrement()),!0},t.prototype.dataColumnKeys=function(){return s1(["x"],this.pointArrayMap||["y"],!0)},t.prototype.setData=function(t,e,i,o){void 0===e&&(e=!0);var r,s,n,a,h,l,d,c=this.points,p=(null==c?void 0:c.length)||0,u=this.options,f=this.chart,g=u.dataSorting,v=this.xAxis,m=u.turboThreshold,y=this.dataTable,x=this.dataColumnKeys(),b=this.pointValKey||"y",k=(this.pointArrayMap||[]).length,M=u.keys,w=0,S=1;f.options.chart.allowMutatingData||(u.data&&delete this.options.data,this.userOptions.data&&delete this.userOptions.data,d=nv(!0,t));var A=(t=d||t||[]).length;if((null==g?void 0:g.enabled)&&(t=this.sortData(t)),f.options.chart.allowMutatingData&&!1!==o&&A&&p&&!this.cropped&&!this.hasGroupedData&&this.visible&&!this.boosted&&(l=this.updateData(t,i)),!l){this.xIncrement=null,this.colorCounter=0;var T=m&&A>m;if(T){var C=this.getFirstValidPoint(t),O=this.getFirstValidPoint(t,A-1,-1),P=function(t){return!!(nu(t)&&(M||nf(t[0])))};if(nf(C)&&nf(O)){for(var E=[],L=[],D=0,I=t;D<I.length;D++){var B=I[D];E.push(this.autoIncrement()),L.push(B)}y.setColumns(((r={x:E})[b]=L,r))}else if(P(C)&&P(O)){if(k){for(var z=+(C.length===k),R=Array(x.length).fill(0).map(function(){return[]}),N=0,W=t;N<W.length;N++){var X=W[N];z&&R[0].push(this.autoIncrement());for(var H=z;H<=k;H++)null===(n=R[H])||void 0===n||n.push(X[H-z])}y.setColumns(x.reduce(function(t,e,i){return t[e]=R[i],t},{}))}else{M&&(w=M.indexOf("x"),S=M.indexOf("y"),w=w>=0?w:0,S=S>=0?S:1),1===C.length&&(S=0);var F=[],L=[];if(w===S)for(var j=0,Y=t;j<Y.length;j++){var X=Y[j];F.push(this.autoIncrement()),L.push(X[S])}else for(var G=0,_=t;G<_.length;G++){var X=_[G];F.push(X[w]),L.push(X[S])}y.setColumns(((s={x:F})[b]=L,s))}}else T=!1}if(!T){var U=x.reduce(function(t,e){return t[e]=[],t},{});for(h=0;h<A;h++)for(var X=this.pointClass.prototype.applyOptions.apply({series:this},[t[h]]),V=0;V<x.length;V++){var Z=x[V];U[Z][h]=X[Z]}y.setColumns(U)}for(ng(this.getColumn("y")[0])&&nn(14,!0,f),this.data=[],this.options.data=this.userOptions.data=t,h=p;h--;)null===(a=c[h])||void 0===a||a.destroy();v&&(v.minRange=v.userMinRange),this.isDirty=f.isDirtyBox=!0,this.isDirtyData=!!c,i=!1}"point"===u.legendType&&(this.processData(),this.generatePoints()),e&&f.redraw(i)},t.prototype.sortData=function(t){var e=this,i=e.options.dataSorting.sortKey||"y",o=function(t,e){return ni(e)&&t.pointClass.prototype.optionsToObject.call({series:t},e)||{}};return t.forEach(function(i,r){t[r]=o(e,i),t[r].index=r},this),t.concat().sort(function(t,e){var o=nc(i,t),r=nc(i,e);return r<o?-1:+(r>o)}).forEach(function(t,e){t.x=e},this),e.linkedSeries&&e.linkedSeries.forEach(function(e){var i,r=e.options,s=r.data;(null===(i=r.dataSorting)||void 0===i?void 0:i.enabled)||!s||(s.forEach(function(i,r){s[r]=o(e,i),t[r]&&(s[r].x=t[r].x,s[r].index=r)}),e.setData(s,!1))}),t},t.prototype.getProcessedData=function(t){var e,i,o,r,s,n=this,a=n.dataTable,h=n.isCartesian,l=n.options,d=n.xAxis,c=l.cropThreshold,p=t||n.getExtremesFromAll,u=null==d?void 0:d.logarithmic,f=a.rowCount,g=0,v=n.getColumn("x"),m=a,y=!1;return d&&(r=(o=d.getExtremes()).min,s=o.max,y=!!(d.categories&&!d.names.length),h&&n.sorted&&!p&&(!c||f>c||n.forceCrop)&&(v[f-1]<r||v[0]>s?m=new sj:n.getColumn(n.pointValKey||"y").length&&(v[0]<r||v[f-1]>s)&&(m=(e=this.cropData(a,r,s)).modified,g=e.start,i=!0))),v=m.getColumn("x")||[],{modified:m,cropped:i,cropStart:g,closestPointRange:nd([u?v.map(u.log2lin):v],function(){return n.requireSorting&&!y&&nn(15,!1,n.chart)})}},t.prototype.processData=function(t){var e=this.xAxis,i=this.dataTable;if(this.isCartesian&&!this.isDirty&&!e.isDirty&&!this.yAxis.isDirty&&!t)return!1;var o=this.getProcessedData();i.modified=o.modified,this.cropped=o.cropped,this.cropStart=o.cropStart,this.closestPointRange=this.basePointRange=o.closestPointRange,nl(this,"afterProcessData")},t.prototype.cropData=function(t,e,i){var o,r,s=t.getColumn("x",!0)||[],n=s.length,a={},h=0,l=n;for(o=0;o<n;o++)if(s[o]>=e){h=Math.max(0,o-1);break}for(r=o;r<n;r++)if(s[r]>i){l=r+1;break}for(var d=0,c=this.dataColumnKeys();d<c.length;d++){var p=c[d],u=t.getColumn(p,!0);u&&(a[p]=u.slice(h,l))}return{modified:new sj({columns:a}),start:h,end:l}},t.prototype.generatePoints=function(){var t,e,i,o,r,s,n,a,h,l,d=this.options,c=this.processedData||d.data,p=this.dataTable.modified,u=this.getColumn("x",!0),f=this.pointClass,g=p.rowCount,v=this.cropStart||0,m=this.hasGroupedData,y=d.keys,x=[],b=(null===(t=d.dataGrouping)||void 0===t?void 0:t.groupAll)?v:0,k=null===(e=this.xAxis)||void 0===e?void 0:e.categories,M=this.pointArrayMap||["y"],w=this.dataColumnKeys(),S=this.data;if(!S&&!m){var A=[];A.length=(null==c?void 0:c.length)||0,S=this.data=A}for(y&&m&&(this.options.keys=!1),h=0;h<g;h++)n=v+h,m?((a=new f(this,p.getRow(h,w)||[])).dataGroup=this.groupMap[b+h],(null===(i=a.dataGroup)||void 0===i?void 0:i.options)&&(a.options=a.dataGroup.options,na(a,a.dataGroup.options),delete a.dataLabels)):(a=S[n],l=c?c[n]:p.getRow(h,M),a||void 0===l||(S[n]=a=new f(this,l,u[h]))),a&&(a.index=m?b+h:n,x[h]=a,a.category=null!==(o=null==k?void 0:k[a.x])&&void 0!==o?o:a.x,a.key=null!==(r=a.name)&&void 0!==r?r:a.category);if(this.options.keys=y,S&&(g!==(s=S.length)||m))for(h=0;h<s;h++)h!==v||m||(h+=g),S[h]&&(S[h].destroyElements(),S[h].plotX=void 0);this.data=S,this.points=x,nl(this,"afterGeneratePoints")},t.prototype.getXExtremes=function(t){return{min:s8(t),max:s4(t)}},t.prototype.getExtremes=function(t,e){var i,o,r,s,n=this.xAxis,a=this.yAxis,h=e||this.getExtremesFromAll||this.options.getExtremesFromAll,l=h&&this.cropped?this.dataTable:this.dataTable.modified,d=l.rowCount,c=t||this.stackedYData,p=c?[c]:(null===(i=this.keysAffectYAxis||this.pointArrayMap||["y"])||void 0===i?void 0:i.map(function(t){return l.getColumn(t,!0)||[]}))||[],u=this.getColumn("x",!0),f=[],g=this.requireSorting&&!this.is("column")?1:0,v=!!a&&a.positiveValuesOnly,m=h||this.cropped||!n,y=0,x=0;for(n&&(y=(o=n.getExtremes()).min,x=o.max),s=0;s<d;s++)if(r=u[s],m||(u[s+g]||r)>=y&&(u[s-g]||r)<=x)for(var b=0;b<p.length;b++){var k=p[b][s];nf(k)&&(k>0||!v)&&f.push(k)}var M={activeYData:f,dataMin:s8(f),dataMax:s4(f)};return nl(this,"afterGetExtremes",{dataExtremes:M}),M},t.prototype.applyExtremes=function(){var t=this.getExtremes();return this.dataMin=t.dataMin,this.dataMax=t.dataMax,t},t.prototype.getFirstValidPoint=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=1);for(var o=t.length,r=e;r>=0&&r<o;){if(ni(t[r]))return t[r];r+=i}},t.prototype.translate=function(){this.generatePoints();var t,e,i,o,r,s=this.options,n=s.stacking,a=this.xAxis,h=this.enabledDataSorting,l=this.yAxis,d=this.points,c=d.length,p=this.pointPlacementToXValue(),u=!!p,f=s.threshold,g=s.startFromThreshold?f:0,v=(null==s?void 0:s.nullInteraction)&&l.len,m=Number.MAX_VALUE;function y(t){return s7(t,-1e9,1e9)}for(e=0;e<c;e++){var x=d[e],b=x.x,k=void 0,M=void 0,w=x.y,S=x.low,A=n&&(null===(t=l.stacking)||void 0===t?void 0:t.stacks[(this.negStacks&&w<(g?0:f)?"-":"")+this.stackKey]);x.plotX=nf(i=a.translate(b,!1,!1,!1,!0,p))?nt(y(i)):void 0,n&&this.visible&&A&&A[b]&&(r=this.getStackIndicator(r,b,this.index),!x.isNull&&r.key&&(M=(k=A[b]).points[r.key]),k&&nu(M)&&(S=M[0],w=M[1],S===g&&r.key===A[b].base&&(S=ny(nf(f)?f:l.min)),l.positiveValuesOnly&&ni(S)&&S<=0&&(S=void 0),x.total=x.stackTotal=ny(k.total),x.percentage=ni(x.y)&&k.total?x.y/k.total*100:void 0,x.stackY=w,this.irregularWidths||k.setOffset(this.pointXOffset||0,this.barW||0,void 0,void 0,void 0,this.xAxis))),x.yBottom=ni(S)?y(l.translate(S,!1,!0,!1,!0)):void 0,this.dataModify&&(w=this.dataModify.modifyValue(w,e));var T=void 0;nf(w)&&void 0!==x.plotX?T=nf(T=l.translate(w,!1,!0,!1,!0))?y(T):void 0:!nf(w)&&v&&(T=v),x.plotY=T,x.isInside=this.isPointInside(x),x.clientX=u?nt(a.translate(b,!1,!1,!1,!0,p)):i,x.negative=(x.y||0)<(f||0),x.isNull||!1===x.visible||(void 0!==o&&(m=Math.min(m,Math.abs(i-o))),o=i),x.zone=this.zones.length?x.getZone():void 0,!x.graphic&&this.group&&h&&(x.isNew=!0)}this.closestPointRangePx=m,nl(this,"afterTranslate")},t.prototype.getValidPoints=function(t,e,i){var o=this.chart;return(t||this.points||[]).filter(function(t){var r=t.plotX,s=t.plotY;return!!((i||!t.isNull&&nf(s))&&(!e||o.isInsidePlot(r,s,{inverted:o.inverted})))&&!1!==t.visible})},t.prototype.getSharedClipKey=function(){return this.sharedClipKey=(this.options.xAxis||0)+","+(this.options.yAxis||0),this.sharedClipKey},t.prototype.setClip=function(){var t=this.chart,e=this.group,i=this.markerGroup,o=t.sharedClips,r=t.renderer,s=t.getClipBox(this),n=this.getSharedClipKey(),a=o[n];a?a.animate(s):o[n]=a=r.clipRect(s),e&&e.clip(!1===this.options.clip?void 0:a),i&&i.clip()},t.prototype.animate=function(t){var e=this.chart,i=this.group,o=this.markerGroup,r=e.inverted,s=eu(this.options.animation),n=[this.getSharedClipKey(),s.duration,s.easing,s.defer].join(","),a=e.sharedClips[n],h=e.sharedClips[n+"m"];if(t&&i){var l=e.getClipBox(this);if(a)a.attr("height",l.height);else{l.width=0,r&&(l.x=e.plotHeight),a=e.renderer.clipRect(l),e.sharedClips[n]=a;var d={x:-99,y:-99,width:r?e.plotWidth+199:99,height:r?99:e.plotHeight+199};h=e.renderer.clipRect(d),e.sharedClips[n+"m"]=h}i.clip(a),null==o||o.clip(h)}else if(a&&!a.hasClass("highcharts-animating")){var c=e.getClipBox(this),p=s.step;((null==o?void 0:o.element.childNodes.length)||e.series.length>1)&&(s.step=function(t,e){p&&p.apply(e,arguments),"width"===e.prop&&(null==h?void 0:h.element)&&h.attr(r?"height":"width",t+99)}),a.addClass("highcharts-animating").animate(c,s)}},t.prototype.afterAnimate=function(){var t=this;this.setClip(),nm(this.chart.sharedClips,function(e,i,o){e&&!t.chart.container.querySelector('[clip-path="url(#'.concat(e.id,')"]'))&&(e.destroy(),delete o[i])}),this.finishedAnimating=!0,nl(this,"afterAnimate")},t.prototype.drawPoints=function(t){void 0===t&&(t=this.points);var e,i,o,r,s,n,a,h=this.chart,l=h.styledMode,d=this.colorAxis,c=this.options,p=c.marker,u=c.nullInteraction,f=this[this.specialGroup||"markerGroup"],g=this.xAxis,v=ny(p.enabled,!g||!!g.isRadial||null,this.closestPointRangePx>=p.enabledThreshold*p.radius);if(!1!==p.enabled||this._hasPointMarkers)for(e=0;e<t.length;e++){r=(o=(i=t[e]).graphic)?"animate":"attr",s=i.marker||{},n=!!i.marker;var m=i.isNull;if((v&&!ni(s.enabled)||s.enabled)&&(!m||u)&&!1!==i.visible){var y=ny(s.symbol,this.symbol,"rect");a=this.markerAttribs(i,i.selected&&"select"),this.enabledDataSorting&&(i.startXPos=g.reversed?-(a.width||0):g.width);var x=!1!==i.isInside;if(!o&&x&&((a.width||0)>0||i.hasImage)&&(i.graphic=o=h.renderer.symbol(y,a.x,a.y,a.width,a.height,n?s:p).add(f),this.enabledDataSorting&&h.hasRendered&&(o.attr({x:i.startXPos}),r="animate")),o&&"animate"===r&&o[x?"show":"hide"](x).animate(a),o){var b=this.pointAttribs(i,l||!i.selected?void 0:"select");l?d&&o.css({fill:b.fill}):o[r](b)}o&&o.addClass(i.getClassName(),!0)}else o&&(i.graphic=o.destroy())}},t.prototype.markerAttribs=function(t,e){var i,o,r=this.options,s=r.marker,n=t.marker||{},a=n.symbol||s.symbol,h={},l=ny(n.radius,null==s?void 0:s.radius);e&&(i=s.states[e],l=ny(null==(o=n.states&&n.states[e])?void 0:o.radius,null==i?void 0:i.radius,l&&l+((null==i?void 0:i.radiusPlus)||0))),t.hasImage=a&&0===a.indexOf("url"),t.hasImage&&(l=0);var d=t.pos();return nf(l)&&d&&(r.crisp&&(d[0]=ne(d[0],t.hasImage?0:"rect"===a?(null==s?void 0:s.lineWidth)||0:1)),h.x=d[0]-l,h.y=d[1]-l),l&&(h.width=h.height=2*l),h},t.prototype.pointAttribs=function(t,e){var i,o,r,s,n,a=this.options,h=a.marker,l=null==t?void 0:t.options,d=(null==l?void 0:l.marker)||{},c=null==l?void 0:l.color,p=null==t?void 0:t.color,u=null===(i=null==t?void 0:t.zone)||void 0===i?void 0:i.color,f=this.color,g=ny(d.lineWidth,h.lineWidth),v=(null==t?void 0:t.isNull)&&a.nullInteraction?0:1;return f=c||u||p||f,s=d.fillColor||h.fillColor||f,n=d.lineColor||h.lineColor||f,e=e||"normal",o=h.states[e]||{},g=ny((r=d.states&&d.states[e]||{}).lineWidth,o.lineWidth,g+ny(r.lineWidthPlus,o.lineWidthPlus,0)),s=r.fillColor||o.fillColor||s,n=r.lineColor||o.lineColor||n,{stroke:n,"stroke-width":g,fill:s,opacity:v=ny(r.opacity,o.opacity,v)}},t.prototype.destroy=function(t){var e,i,o,r,s=this,n=s.chart,a=/AppleWebKit\/533/.test(s5.navigator.userAgent),h=s.data||[];for(nl(s,"destroy",{keepEventsForUpdate:t}),this.removeEvents(t),(s.axisTypes||[]).forEach(function(t){(null==(r=s[t])?void 0:r.series)&&(ns(r.series,s),r.isDirty=r.forceRedraw=!0)}),s.legendItem&&s.chart.legend.destroyItem(s),o=h.length;o--;)null===(i=null===(e=h[o])||void 0===e?void 0:e.destroy)||void 0===i||i.call(e);for(var l=0,d=s.zones;l<d.length;l++)no(d[l],void 0,!0);tT.clearTimeout(s.animationTimeout),nm(s,function(t,e){t instanceof ix&&!t.survive&&t[a&&"group"===e?"hide":"destroy"]()}),n.hoverSeries===s&&(n.hoverSeries=void 0),ns(n.series,s),n.orderItems("series"),nm(s,function(e,i){t&&"hcEvents"===i||delete s[i]})},t.prototype.applyZones=function(){var t=this.area,e=this.chart,i=this.graph,o=this.zones,r=this.points,s=this.xAxis,n=this.yAxis,a=this.zoneAxis,h=e.inverted,l=e.renderer,d=this[""+a+"Axis"],c=d||{},p=c.isXAxis,u=c.len,f=void 0===u?0:u,g=c.minPointOffset,v=void 0===g?0:g,m=((null==i?void 0:i.strokeWidth())||0)/2+1,y=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=0),h&&(i=f-i);var o=t.translated,r=void 0===o?0:o,s=t.lineClip,n=i-r;null==s||s.push(["L",e,Math.abs(n)<m?i-m*(n<=0?-1:1):r])};if(o.length&&(i||t)&&d&&nf(d.min)){var x=d.getExtremes().max+v,b=function(t){t.forEach(function(e,i){("M"===e[0]||"L"===e[0])&&(t[i]=[e[0],p?f-e[1]:e[1],p?e[2]:f-e[2]])})};if(o.forEach(function(t){t.lineClip=[],t.translated=s7(d.toPixels(ny(t.value,x),!0)||0,0,f)}),i&&!this.showLine&&i.hide(),t&&t.hide(),"y"===a&&r.length<s.len)for(var k=0;k<r.length;k++){var M=r[k],w=M.plotX,S=M.plotY,A=M.zone,T=A&&o[o.indexOf(A)-1];A&&y(A,w,S),T&&y(T,w,S)}var C=[],O=d.toPixels(d.getExtremes().min-v,!0);o.forEach(function(e){var o,r,a=e.lineClip||[],d=Math.round(e.translated||0);s.reversed&&a.reverse();var c=e.clip,u=e.simpleClip,f=0,g=0,v=s.len,m=n.len;p?(f=d,v=O):(g=d,m=O);var y=[["M",f,g],["L",v,g],["L",v,m],["L",f,m],["Z"]],x=s1(s1(s1(s1([y[0]],a,!0),[y[1],y[2]],!1),C,!0),[y[3],y[4]],!1);C=a.reverse(),O=d,h&&(b(x),t&&b(y)),c?(c.animate({d:x}),null==u||u.animate({d:y})):(c=e.clip=l.path(x),t&&(u=e.simpleClip=l.path(y))),i&&(null===(o=e.graph)||void 0===o||o.clip(c)),t&&(null===(r=e.area)||void 0===r||r.clip(u))})}else this.visible&&(i&&i.show(),t&&t.show())},t.prototype.plotGroup=function(t,e,i,o,r){var s=this[t],n=!s,a={visibility:i,zIndex:o||.1};return ni(this.opacity)&&!this.chart.styledMode&&"inactive"!==this.state&&(a.opacity=this.opacity),s||(this[t]=s=this.chart.renderer.g().add(r)),s.addClass("highcharts-"+e+" highcharts-series-"+this.index+" highcharts-"+this.type+"-series "+(ni(this.colorIndex)?"highcharts-color-"+this.colorIndex+" ":"")+(this.options.className||"")+(s.hasClass("highcharts-tracker")?" highcharts-tracker":""),!0),s.attr(a)[n?"attr":"animate"](this.getPlotBox(e)),s},t.prototype.getPlotBox=function(t){var e=this.xAxis,i=this.yAxis,o=this.chart,r=o.inverted&&!o.polar&&e&&this.invertible&&"series"===t;return o.inverted&&(e=i,i=this.xAxis),{translateX:e?e.left:o.plotLeft,translateY:i?i.top:o.plotTop,rotation:90*!!r,rotationOriginX:r?(e.len-i.len)/2:0,rotationOriginY:r?(e.len+i.len)/2:0,scaleX:r?-1:1,scaleY:1}},t.prototype.removeEvents=function(t){var e=this.eventsToUnbind;t||nx(this),e.length&&(e.forEach(function(t){t()}),e.length=0)},t.prototype.render=function(){var t,e,i,o,r,s=this,n=s.chart,a=s.options,h=s.hasRendered,l=eu(a.animation),d=s.visible?"inherit":"hidden",c=a.zIndex,p=n.seriesGroup,u=s.finishedAnimating?0:l.duration;nl(this,"render"),s.plotGroup("group","series",d,c,p),s.markerGroup=s.plotGroup("markerGroup","markers",d,c,p),!1!==a.clip&&s.setClip(),u&&(null===(t=s.animate)||void 0===t||t.call(s,!0)),s.drawGraph&&(s.drawGraph(),s.applyZones()),s.visible&&s.drawPoints(),null===(e=s.drawDataLabels)||void 0===e||e.call(s),null===(i=s.redrawPoints)||void 0===i||i.call(s),a.enableMouseTracking&&(null===(o=s.drawTracker)||void 0===o||o.call(s)),u&&(null===(r=s.animate)||void 0===r||r.call(s)),h||(u&&l.defer&&(u+=l.defer),s.animationTimeout=nb(function(){s.afterAnimate()},u||0)),s.isDirty=!1,s.hasRendered=!0,nl(s,"afterRender")},t.prototype.redraw=function(){var t=this.isDirty||this.isDirtyData;this.translate(),this.render(),t&&delete this.kdTree},t.prototype.reserveSpace=function(){return this.visible||!this.chart.options.chart.ignoreHiddenSeries},t.prototype.searchPoint=function(t,e){var i=this.xAxis,o=this.yAxis,r=this.chart.inverted;return this.searchKDTree({clientX:r?i.len-t.chartY+i.pos:t.chartX-i.pos,plotY:r?o.len-t.chartX+o.pos:t.chartY-o.pos},e,t)},t.prototype.buildKDTree=function(t){this.buildingKdTree=!0;var e=this,i=e.options,o=i.findNearestPointBy.indexOf("y")>-1?2:1;delete e.kdTree,nb(function(){e.kdTree=function t(i,o,r){var s,n,a=null==i?void 0:i.length;if(a)return s=e.kdAxisArray[o%r],i.sort(function(t,e){return(t[s]||0)-(e[s]||0)}),{point:i[n=Math.floor(a/2)],left:t(i.slice(0,n),o+1,r),right:t(i.slice(n+1),o+1,r)}}(e.getValidPoints(void 0,!e.directTouch,null==i?void 0:i.nullInteraction),o,o),e.buildingKdTree=!1},i.kdNow||(null==t?void 0:t.type)==="touchstart"?0:1)},t.prototype.searchKDTree=function(t,e,i,o,r){var s=this,n=this.kdAxisArray,a=n[0],h=n[1],l=e?"distX":"dist",d=(s.options.findNearestPointBy||"").indexOf("y")>-1?2:1,c=!!s.isBubble,p=o||function(t,e,i){var o=t[i]||0,r=e[i]||0;return[o===r&&t.index>e.index||o<r?t:e,!1]},u=r||function(t,e){return t<e};if(this.kdTree||this.buildingKdTree||this.buildKDTree(i),this.kdTree)return function t(e,i,o,r){var n,d,f,g,v,m,y,x,b,k,M=i.point,w=s.kdAxisArray[o%r],S=M,A=!1;d=e[a],f=M[a],g=ni(d)&&ni(f)?d-f:null,v=e[h],m=M[h],y=ni(v)&&ni(m)?v-m:0,x=c&&(null===(n=M.marker)||void 0===n?void 0:n.radius)||0,M.dist=Math.sqrt((g&&g*g||0)+y*y)-x,M.distX=ni(g)?Math.abs(g)-x:Number.MAX_VALUE;var T=(e[w]||0)-(M[w]||0)+(c&&(null===(k=M.marker)||void 0===k?void 0:k.radius)||0),C=T<0?"left":"right",O=T<0?"right":"left";return i[C]&&(S=(b=p(M,t(e,i[C],o+1,r),l))[0],A=b[1]),i[O]&&u(Math.sqrt(T*T),S[l],A)&&(S=p(S,t(e,i[O],o+1,r),l)[0]),S}(t,this.kdTree,d,d)},t.prototype.pointPlacementToXValue=function(){var t=this.options,e=this.xAxis,i=t.pointPlacement;return"between"===i&&(i=e.reversed?-.5:.5),nf(i)?i*(t.pointRange||e.pointRange):0},t.prototype.isPointInside=function(t){var e=this.chart,i=this.xAxis,o=this.yAxis,r=t.plotX,s=void 0===r?-1:r,n=t.plotY,a=void 0===n?-1:n;return a>=0&&a<=(o?o.len:e.plotHeight)&&s>=0&&s<=(i?i.len:e.plotWidth)},t.prototype.drawTracker=function(){var t,e=this,i=e.options,o=i.trackByArea,r=[].concat((o?e.areaPath:e.graphPath)||[]),s=e.chart,n=s.pointer,a=s.renderer,h=(null===(t=s.options.tooltip)||void 0===t?void 0:t.snap)||0,l=function(){i.enableMouseTracking&&s.hoverSeries!==e&&e.onMouseOver()},d="rgba(192,192,192,"+(s3?1e-4:.002)+")",c=e.tracker;c?c.attr({d:r}):e.graph&&(e.tracker=c=a.path(r).attr({visibility:e.visible?"inherit":"hidden",zIndex:2}).addClass(o?"highcharts-tracker-area":"highcharts-tracker-line").add(e.group),s.styledMode||c.attr({"stroke-linecap":"round","stroke-linejoin":"round",stroke:d,fill:o?d:"none","stroke-width":e.graph.strokeWidth()+(o?0:2*h)}),[e.tracker,e.markerGroup,e.dataLabelsGroup].forEach(function(t){t&&(t.addClass("highcharts-tracker").on("mouseover",l).on("mouseout",function(t){null==n||n.onTrackerMouseOut(t)}),i.cursor&&!s.styledMode&&t.css({cursor:i.cursor}),t.on("touchstart",l))})),nl(this,"afterDrawTracker")},t.prototype.addPoint=function(t,e,i,o,r){var s,n,a=this.options,h=this.chart,l=this.data,d=this.dataTable,c=this.xAxis,p=(null==c?void 0:c.hasNames)&&c.names,u=a.data,f=this.getColumn("x");e=ny(e,!0);var g={series:this};this.pointClass.prototype.applyOptions.apply(g,[t]);var v=g.x;if(n=f.length,this.requireSorting&&v<f[n-1])for(s=!0;n&&f[n-1]>v;)n--;d.setRow(g,n,!0,{addColumns:!1}),p&&g.name&&(p[v]=g.name),null==u||u.splice(n,0,t),(s||this.processedData)&&(this.data.splice(n,0,null),this.processData()),"point"===a.legendType&&this.generatePoints(),i&&(l[0]&&l[0].remove?l[0].remove(!1):([l,u].filter(ni).forEach(function(t){t.shift()}),d.deleteRows(0))),!1!==r&&nl(this,"addPoint",{point:g}),this.isDirty=!0,this.isDirtyData=!0,e&&h.redraw(o)},t.prototype.removePoint=function(t,e,i){var o=this,r=o.chart,s=o.data,n=o.points,a=o.dataTable,h=s[t],l=function(){[(null==n?void 0:n.length)===s.length?n:void 0,s,o.options.data].filter(ni).forEach(function(e){e.splice(t,1)}),a.deleteRows(t),null==h||h.destroy(),o.isDirty=!0,o.isDirtyData=!0,e&&r.redraw()};em(i,r),e=ny(e,!0),h?h.firePointEvent("remove",null,l):l()},t.prototype.remove=function(t,e,i,o){var r=this,s=r.chart;function n(){r.destroy(o),s.isDirtyLegend=s.isDirtyBox=!0,s.linkSeries(o),ny(t,!0)&&s.redraw(e)}!1!==i?nl(r,"remove",null,n):n()},t.prototype.update=function(e,i){nl(this,"update",{options:e=nr(e,this.userOptions)});var o,r,s,n,a,h,l=this,d=l.chart,c=l.userOptions,p=l.initialType||l.type,u=d.options.plotOptions,f=s6[p].prototype,g=l.finishedAnimating&&{animation:!1},v={},m=t.keepProps.slice(),y=e.type||c.type||d.options.chart.type,x=!(this.hasDerivedData||y&&y!==this.type||void 0!==e.keys||void 0!==e.pointStart||void 0!==e.pointInterval||void 0!==e.relativeXValue||e.joinBy||e.mapData||["dataGrouping","pointStart","pointInterval","pointIntervalUnit","keys"].some(function(t){return l.hasOptionChanged(t)}));y=y||p,x?(m.push.apply(m,t.keepPropsForPoints),!1!==e.visible&&m.push("area","graph"),l.parallelArrays.forEach(function(t){m.push(t+"Data")}),e.data&&(e.dataSorting&&na(l.options.dataSorting,e.dataSorting),this.setData(e.data,!1))):this.dataTable.modified=this.dataTable,e=nv(c,{index:void 0===c.index?l.index:c.index,pointStart:null!==(s=null!==(r=null===(o=null==u?void 0:u.series)||void 0===o?void 0:o.pointStart)&&void 0!==r?r:c.pointStart)&&void 0!==s?s:l.getColumn("x")[0]},!x&&{data:l.options.data},e,g),x&&e.data&&(e.data=l.options.data),(m=["group","markerGroup","dataLabelsGroup","transformGroup"].concat(m)).forEach(function(t){m[t]=l[t],delete l[t]});var b=!1;if(s6[y]){if(b=y!==l.type,l.remove(!1,!1,!1,!0),b){if(d.propFromSeries(),Object.setPrototypeOf)Object.setPrototypeOf(l,s6[y].prototype);else{var k=Object.hasOwnProperty.call(l,"hcEvents")&&l.hcEvents;for(h in f)l[h]=void 0;na(l,s6[y].prototype),k?l.hcEvents=k:delete l.hcEvents}}}else nn(17,!0,d,{missingModuleFor:y});if(m.forEach(function(t){l[t]=m[t]}),l.init(d,e),x&&this.points){!1===(a=l.options).visible?(v.graphic=1,v.dataLabel=1):(this.hasMarkerChanged(a,c)&&(v.graphic=1),(null===(n=l.hasDataLabels)||void 0===n?void 0:n.call(l))||(v.dataLabel=1));for(var M=0,w=this.points;M<w.length;M++){var S=w[M];(null==S?void 0:S.series)&&(S.resolveColor(),Object.keys(v).length&&S.destroyElements(v),!1===a.showInLegend&&S.legendItem&&d.legend.destroyItem(S))}}l.initialType=p,d.linkSeries(),d.setSortedData(),b&&l.linkedSeries.length&&(l.isDirtyData=!0),nl(this,"afterUpdate"),ny(i,!0)&&d.redraw(!!x&&void 0)},t.prototype.setName=function(t){this.name=this.options.name=this.userOptions.name=t,this.chart.isDirtyLegend=!0},t.prototype.hasOptionChanged=function(t){var e,i,o=this.chart,r=this.options[t],s=o.options.plotOptions,n=this.userOptions[t],a=ny(null===(e=null==s?void 0:s[this.type])||void 0===e?void 0:e[t],null===(i=null==s?void 0:s.series)||void 0===i?void 0:i[t]);return n&&!ni(a)?r!==n:r!==ny(a,r)},t.prototype.onMouseOver=function(){var t=this.chart,e=t.hoverSeries,i=t.pointer;null==i||i.setHoverChartIndex(),e&&e!==this&&e.onMouseOut(),this.options.events.mouseOver&&nl(this,"mouseOver"),this.setState("hover"),t.hoverSeries=this},t.prototype.onMouseOut=function(){var t=this.options,e=this.chart,i=e.tooltip,o=e.hoverPoint;e.hoverSeries=null,o&&o.onMouseOut(),this&&t.events.mouseOut&&nl(this,"mouseOut"),i&&!this.stickyTracking&&(!i.shared||this.noSharedTooltip)&&i.hide(),e.series.forEach(function(t){t.setState("",!0)})},t.prototype.setState=function(t,e){var i=this,o=i.options,r=i.graph,s=o.inactiveOtherPoints,n=o.states,a=ny(n[t||"normal"]&&n[t||"normal"].animation,i.chart.options.chart.animation),h=o.lineWidth,l=o.opacity;if(t=t||"",i.state!==t&&([i.group,i.markerGroup,i.dataLabelsGroup].forEach(function(e){e&&(i.state&&e.removeClass("highcharts-series-"+i.state),t&&e.addClass("highcharts-series-"+t))}),i.state=t,!i.chart.styledMode)){if(n[t]&&!1===n[t].enabled)return;if(t&&(h=n[t].lineWidth||h+(n[t].lineWidthPlus||0),l=ny(n[t].opacity,l)),r&&!r.dashstyle&&nf(h))for(var d=0,c=s1([r],this.zones.map(function(t){return t.graph}),!0);d<c.length;d++){var p=c[d];null==p||p.animate({"stroke-width":h},a)}s||[i.group,i.markerGroup,i.dataLabelsGroup,i.labelBySeries].forEach(function(t){t&&t.animate({opacity:l},a)})}e&&s&&i.points&&i.setAllPointsToState(t||void 0)},t.prototype.setAllPointsToState=function(t){this.points.forEach(function(e){e.setState&&e.setState(t)})},t.prototype.setVisible=function(t,e){var i,o=this,r=o.chart,s=r.options.chart.ignoreHiddenSeries,n=o.visible;o.visible=t=o.options.visible=o.userOptions.visible=void 0===t?!n:t;var a=t?"show":"hide";["group","dataLabelsGroup","markerGroup","tracker","tt"].forEach(function(t){var e;null===(e=o[t])||void 0===e||e[a]()}),(r.hoverSeries===o||(null===(i=r.hoverPoint)||void 0===i?void 0:i.series)===o)&&o.onMouseOut(),o.legendItem&&r.legend.colorizeItem(o,t),o.isDirty=!0,o.options.stacking&&r.series.forEach(function(t){t.options.stacking&&t.visible&&(t.isDirty=!0)}),o.linkedSeries.forEach(function(e){e.setVisible(t,!1)}),s&&(r.isDirtyBox=!0),nl(o,a),!1!==e&&r.redraw()},t.prototype.show=function(){this.setVisible(!0)},t.prototype.hide=function(){this.setVisible(!1)},t.prototype.select=function(t){this.selected=t=this.options.selected=void 0===t?!this.selected:t,this.checkbox&&(this.checkbox.checked=t),nl(this,t?"select":"unselect")},t.prototype.shouldShowTooltip=function(t,e,i){return void 0===i&&(i={}),i.series=this,i.visiblePlotOnly=!0,this.chart.isInsidePlot(t,e,i)},t.prototype.drawLegendSymbol=function(t,e){var i;null===(i=sV[this.options.legendSymbol||"rectangle"])||void 0===i||i.call(this,t,e)},t.defaultOptions=sZ,t.types=sQ.seriesTypes,t.registerType=sQ.registerSeriesType,t.keepProps=["colorIndex","eventOptions","navigatorSeries","symbolIndex","baseSeries"],t.keepPropsForPoints=["data","isDirtyData","isDirtyCanvas","points","dataTable","processedData","xIncrement","cropped","_hasPointMarkers","hasDataLabels","nodes","layout","level","mapMap","mapData","minY","maxY","minX","maxX","transformGroups"],t}();na(nk.prototype,{axisTypes:["xAxis","yAxis"],coll:"series",colorCounter:0,directTouch:!1,invertible:!0,isCartesian:!0,kdAxisArray:["clientX","plotY"],parallelArrays:["x","y"],pointClass:sf,requireSorting:!0,sorted:!0}),sQ.series=nk;var nM=oB.registerEventOptions,nw=tt.composed,nS=tt.marginNames,nA=e$.distribute,nT=e_.format,nC=tT.addEvent,nO=tT.createElement,nP=tT.css,nE=tT.defined,nL=tT.discardElement,nD=tT.find,nI=tT.fireEvent,nB=tT.isNumber,nz=tT.merge,nR=tT.pick,nN=tT.pushUnique,nW=tT.relativeLength,nX=tT.stableSort,nH=tT.syncTimeout,nF=function(){function t(t,e){var i=this;this.allItems=[],this.initialItemY=0,this.itemHeight=0,this.itemMarginBottom=0,this.itemMarginTop=0,this.itemX=0,this.itemY=0,this.lastItemY=0,this.lastLineHeight=0,this.legendHeight=0,this.legendWidth=0,this.maxItemWidth=0,this.maxLegendWidth=0,this.offsetWidth=0,this.padding=0,this.pages=[],this.symbolHeight=0,this.symbolWidth=0,this.titleHeight=0,this.totalItemWidth=0,this.widthOption=0,this.chart=t,this.setOptions(e),e.enabled&&(this.render(),nM(this,e),nC(this.chart,"endResize",function(){this.legend.positionCheckboxes()})),nC(this.chart,"render",function(){i.options.enabled&&i.proximate&&(i.proximatePositions(),i.positionItems())})}return t.prototype.setOptions=function(t){var e=nR(t.padding,8);this.options=t,this.chart.styledMode||(this.itemStyle=t.itemStyle,this.itemHiddenStyle=nz(this.itemStyle,t.itemHiddenStyle)),this.itemMarginTop=t.itemMarginTop,this.itemMarginBottom=t.itemMarginBottom,this.padding=e,this.initialItemY=e-5,this.symbolWidth=nR(t.symbolWidth,16),this.pages=[],this.proximate="proximate"===t.layout&&!this.chart.inverted,this.baseline=void 0},t.prototype.update=function(t,e){var i=this.chart;this.setOptions(nz(!0,this.options,t)),"events"in this.options&&nM(this,this.options),this.destroy(),i.isDirtyLegend=i.isDirtyBox=!0,nR(e,!0)&&i.redraw(),nI(this,"afterUpdate",{redraw:e})},t.prototype.colorizeItem=function(t,e){var i,o=t.color,r=t.legendItem||{},s=r.area,n=r.group,a=r.label,h=r.line,l=r.symbol;if((t instanceof nk||t instanceof sf)&&(t.color=(null===(i=t.options)||void 0===i?void 0:i.legendSymbolColor)||o),null==n||n[e?"removeClass":"addClass"]("highcharts-legend-item-hidden"),!this.chart.styledMode){var d=this.itemHiddenStyle,c=void 0===d?{}:d,p=c.color,u=t.options,f=u.fillColor,g=u.fillOpacity,v=u.lineColor,m=u.marker,y=function(t){return!e&&(t.fill&&(t.fill=p),t.stroke&&(t.stroke=p)),t};null==a||a.css(nz(e?this.itemStyle:c)),null==h||h.attr(y({stroke:v||t.color})),l&&l.attr(y(m&&l.isMarker?t.pointAttribs():{fill:t.color})),null==s||s.attr(y({fill:f||t.color,"fill-opacity":f?1:null!=g?g:.75}))}t.color=o,nI(this,"afterColorizeItem",{item:t,visible:e})},t.prototype.positionItems=function(){this.allItems.forEach(this.positionItem,this),this.chart.isResizing||this.positionCheckboxes()},t.prototype.positionItem=function(t){var e=this,i=t.legendItem||{},o=i.group,r=i.x,s=void 0===r?0:r,n=i.y,a=void 0===n?0:n,h=this.options,l=h.symbolPadding,d=!h.rtl,c=t.checkbox;if(null==o?void 0:o.element){var p={translateX:d?s:this.legendWidth-s-2*l-4,translateY:a};o[nE(o.translateY)?"animate":"attr"](p,void 0,function(){nI(e,"afterPositionItem",{item:t})})}c&&(c.x=s,c.y=a)},t.prototype.destroyItem=function(t){for(var e=t.checkbox,i=t.legendItem||{},o=0,r=["group","label","line","symbol"];o<r.length;o++){var s=r[o];i[s]&&(i[s]=i[s].destroy())}e&&nL(e),t.legendItem=void 0},t.prototype.destroy=function(){for(var t=0,e=this.getAllItems();t<e.length;t++){var i=e[t];this.destroyItem(i)}for(var o=0,r=["clipRect","up","down","pager","nav","box","title","group"];o<r.length;o++){var s=r[o];this[s]&&(this[s]=this[s].destroy())}this.display=null},t.prototype.positionCheckboxes=function(){var t,e,i=null===(t=this.group)||void 0===t?void 0:t.alignAttr,o=this.clipHeight||this.legendHeight,r=this.titleHeight;i&&(e=i.translateY,this.allItems.forEach(function(t){var s,n=t.checkbox;n&&(s=e+r+n.y+(this.scrollOffset||0)+3,nP(n,{left:i.translateX+t.checkboxOffset+n.x-20+"px",top:s+"px",display:this.proximate||s>e-6&&s<e+o-6?"":"none"}))},this))},t.prototype.renderTitle=function(){var t,e=this.options,i=this.padding,o=e.title,r=0;o.text&&(this.title||(this.title=this.chart.renderer.label(o.text,i-3,i-4,void 0,void 0,void 0,e.useHTML,void 0,"legend-title").attr({zIndex:1}),this.chart.styledMode||this.title.css(o.style),this.title.add(this.group)),o.width||this.title.css({width:this.maxLegendWidth+"px"}),r=(t=this.title.getBBox()).height,this.offsetWidth=t.width,this.contentGroup.attr({translateY:r})),this.titleHeight=r},t.prototype.setText=function(t){var e=this.options;t.legendItem.label.attr({text:e.labelFormat?nT(e.labelFormat,t,this.chart):e.labelFormatter.call(t)})},t.prototype.renderItem=function(t){var e,i=t.legendItem=t.legendItem||{},o=this.chart,r=o.renderer,s=this.options,n="horizontal"===s.layout,a=this.symbolWidth,h=s.symbolPadding||0,l=this.itemStyle,d=this.itemHiddenStyle,c=n?nR(s.itemDistance,20):0,p=!s.rtl,u=!t.series,f=!u&&t.series.drawLegendSymbol?t.series:t,g=f.options,v=!!this.createCheckboxForItem&&g&&g.showCheckbox,m=s.useHTML,y=t.options.className,x=i.label,b=a+h+c+20*!!v;!x&&(i.group=r.g("legend-item").addClass("highcharts-"+f.type+"-series highcharts-color-"+t.colorIndex+(y?" "+y:"")+(u?" highcharts-series-"+t.index:"")).attr({zIndex:1}).add(this.scrollGroup),i.label=x=r.text("",p?a+h:-h,this.baseline||0,m),o.styledMode||x.css(nz(t.visible?l:d)),x.attr({align:p?"left":"right",zIndex:2}).add(i.group),!this.baseline&&(this.fontMetrics=r.fontMetrics(x),this.baseline=this.fontMetrics.f+3+this.itemMarginTop,x.attr("y",this.baseline),this.symbolHeight=nR(s.symbolHeight,this.fontMetrics.f),s.squareSymbol&&(this.symbolWidth=nR(s.symbolWidth,Math.max(this.symbolHeight,16)),b=this.symbolWidth+h+c+20*!!v,p&&x.attr("x",this.symbolWidth+h))),f.drawLegendSymbol(this,t),this.setItemEvents&&this.setItemEvents(t,x,m)),v&&!t.checkbox&&this.createCheckboxForItem&&this.createCheckboxForItem(t),this.colorizeItem(t,t.visible),(o.styledMode||!l.width)&&x.css({width:(s.itemWidth||this.widthOption||o.spacingBox.width)-b+"px"}),this.setText(t);var k=x.getBBox(),M=(null===(e=this.fontMetrics)||void 0===e?void 0:e.h)||0;t.itemWidth=t.checkboxOffset=s.itemWidth||i.labelWidth||k.width+b,this.maxItemWidth=Math.max(this.maxItemWidth,t.itemWidth),this.totalItemWidth+=t.itemWidth,this.itemHeight=t.itemHeight=Math.round(i.labelHeight||(k.height>1.5*M?k.height:M))},t.prototype.layoutItem=function(t){var e=this.options,i=this.padding,o="horizontal"===e.layout,r=t.itemHeight,s=this.itemMarginBottom,n=this.itemMarginTop,a=o?nR(e.itemDistance,20):0,h=this.maxLegendWidth,l=e.alignColumns&&this.totalItemWidth>h?this.maxItemWidth:t.itemWidth,d=t.legendItem||{};o&&this.itemX-i+l>h&&(this.itemX=i,this.lastLineHeight&&(this.itemY+=n+this.lastLineHeight+s),this.lastLineHeight=0),this.lastItemY=n+this.itemY+s,this.lastLineHeight=Math.max(r,this.lastLineHeight),d.x=this.itemX,d.y=this.itemY,o?this.itemX+=l:(this.itemY+=n+r+s,this.lastLineHeight=r),this.offsetWidth=this.widthOption||Math.max((o?this.itemX-i-(t.checkbox?0:a):l)+i,this.offsetWidth)},t.prototype.getAllItems=function(){var t=[];return this.chart.series.forEach(function(e){var i,o=null==e?void 0:e.options;e&&nR(o.showInLegend,!nE(o.linkedTo)&&void 0,!0)&&(t=t.concat((null===(i=e.legendItem)||void 0===i?void 0:i.labels)||("point"===o.legendType?e.data:e)))}),nI(this,"afterGetAllItems",{allItems:t}),t},t.prototype.getAlignment=function(){var t=this.options;return this.proximate?t.align.charAt(0)+"tv":t.floating?"":t.align.charAt(0)+t.verticalAlign.charAt(0)+t.layout.charAt(0)},t.prototype.adjustMargins=function(t,e){var i=this.chart,o=this.options,r=this.getAlignment();r&&[/(lth|ct|rth)/,/(rtv|rm|rbv)/,/(rbh|cb|lbh)/,/(lbv|lm|ltv)/].forEach(function(s,n){s.test(r)&&!nE(t[n])&&(i[nS[n]]=Math.max(i[nS[n]],i.legend[(n+1)%2?"legendHeight":"legendWidth"]+[1,-1,-1,1][n]*o[n%2?"x":"y"]+nR(o.margin,12)+e[n]+(i.titleOffset[n]||0)))})},t.prototype.proximatePositions=function(){var t,e=this.chart,i=[],o="left"===this.options.align;this.allItems.forEach(function(t){var r,s,n,a,h=o;t.yAxis&&(t.xAxis.options.reversed&&(h=!h),t.points&&(r=nD(h?t.points:t.points.slice(0).reverse(),function(t){return nB(t.plotY)})),s=this.itemMarginTop+t.legendItem.label.getBBox().height+this.itemMarginBottom,a=t.yAxis.top-e.plotTop,n=t.visible?(r?r.plotY:t.yAxis.height)+(a-.3*s):a+t.yAxis.height,i.push({target:n,size:s,item:t}))},this);for(var r=0,s=nA(i,e.plotHeight);r<s.length;r++){var n=s[r];t=n.item.legendItem||{},nB(n.pos)&&(t.y=e.plotTop-e.spacing[0]+n.pos)}},t.prototype.render=function(){var t,e,i,o,r=this.chart,s=r.renderer,n=this.options,a=this.padding,h=this.getAllItems(),l=this.group,d=this.box;this.itemX=a,this.itemY=this.initialItemY,this.offsetWidth=0,this.lastItemY=0,this.widthOption=nW(n.width,r.spacingBox.width-a),o=r.spacingBox.width-2*a-n.x,["rm","lm"].indexOf(this.getAlignment().substring(0,2))>-1&&(o/=2),this.maxLegendWidth=this.widthOption||o,l||(this.group=l=s.g("legend").addClass(n.className||"").attr({zIndex:7}).add(),this.contentGroup=s.g().attr({zIndex:1}).add(l),this.scrollGroup=s.g().add(this.contentGroup)),this.renderTitle(),nX(h,function(t,e){var i,o;return((null===(i=t.options)||void 0===i?void 0:i.legendIndex)||0)-((null===(o=e.options)||void 0===o?void 0:o.legendIndex)||0)}),n.reversed&&h.reverse(),this.allItems=h,this.display=t=!!h.length,this.lastLineHeight=0,this.maxItemWidth=0,this.totalItemWidth=0,this.itemHeight=0,h.forEach(this.renderItem,this),h.forEach(this.layoutItem,this),e=(this.widthOption||this.offsetWidth)+a,i=this.lastItemY+this.lastLineHeight+this.titleHeight,i=this.handleOverflow(i)+a,d||(this.box=d=s.rect().addClass("highcharts-legend-box").attr({r:n.borderRadius}).add(l)),r.styledMode||d.attr({stroke:n.borderColor,"stroke-width":n.borderWidth||0,fill:n.backgroundColor||"none"}).shadow(n.shadow),e>0&&i>0&&d[d.placed?"animate":"attr"](d.crisp.call({},{x:0,y:0,width:e,height:i},d.strokeWidth())),l[t?"show":"hide"](),r.styledMode&&"none"===l.getStyle("display")&&(e=i=0),this.legendWidth=e,this.legendHeight=i,t&&this.align(),this.proximate||this.positionItems(),nI(this,"afterRender")},t.prototype.align=function(t){void 0===t&&(t=this.chart.spacingBox);var e=this.chart,i=this.options,o=t.y;/(lth|ct|rth)/.test(this.getAlignment())&&e.titleOffset[0]>0?o+=e.titleOffset[0]:/(lbh|cb|rbh)/.test(this.getAlignment())&&e.titleOffset[2]>0&&(o-=e.titleOffset[2]),o!==t.y&&(t=nz(t,{y:o})),e.hasRendered||(this.group.placed=!1),this.group.align(nz(i,{width:this.legendWidth,height:this.legendHeight,verticalAlign:this.proximate?"top":i.verticalAlign}),!0,t)},t.prototype.handleOverflow=function(t){var e,i,o,r,s=this,n=this.chart,a=n.renderer,h=this.options,l=h.y,d="top"===h.verticalAlign,c=this.padding,p=h.maxHeight,u=h.navigation,f=nR(u.animation,!0),g=u.arrowSize||12,v=this.pages,m=this.allItems,y=function(t){"number"==typeof t?M.attr({height:t}):M&&(s.clipRect=M.destroy(),s.contentGroup.clip()),s.contentGroup.div&&(s.contentGroup.div.style.clip=t?"rect("+c+"px,9999px,"+(c+t)+"px,0)":"auto")},x=function(t){return s[t]=a.circle(0,0,1.3*g).translate(g/2,g/2).add(k),n.styledMode||s[t].attr("fill","rgba(0,0,0,0.0001)"),s[t]},b=n.spacingBox.height+(d?-l:l)-c,k=this.nav,M=this.clipRect;return"horizontal"!==h.layout||"middle"===h.verticalAlign||h.floating||(b/=2),p&&(b=Math.min(b,p)),v.length=0,t&&b>0&&t>b&&!1!==u.enabled?(this.clipHeight=e=Math.max(b-20-this.titleHeight-c,0),this.currentPage=nR(this.currentPage,1),this.fullHeight=t,m.forEach(function(t,s){var n=(o=t.legendItem||{}).y||0,a=Math.round(o.label.getBBox().height),h=v.length;(!h||n-v[h-1]>e&&(i||n)!==v[h-1])&&(v.push(i||n),h++),o.pageIx=h-1,i&&r&&(r.pageIx=h-1),s===m.length-1&&n+a-v[h-1]>e&&n>v[h-1]&&(v.push(n),o.pageIx=h),n!==i&&(i=n),r=o}),M||(M=s.clipRect=a.clipRect(0,c-2,9999,0),s.contentGroup.clip(M)),y(e),k||(this.nav=k=a.g().attr({zIndex:1}).add(this.group),this.up=a.symbol("triangle",0,0,g,g).add(k),x("upTracker").on("click",function(){s.scroll(-1,f)}),this.pager=a.text("",15,10).addClass("highcharts-legend-navigation"),!n.styledMode&&u.style&&this.pager.css(u.style),this.pager.add(k),this.down=a.symbol("triangle-down",0,0,g,g).add(k),x("downTracker").on("click",function(){s.scroll(1,f)})),s.scroll(0),t=b):k&&(y(),this.nav=k.destroy(),this.scrollGroup.attr({translateY:1}),this.clipHeight=0),t},t.prototype.scroll=function(t,e){var i=this,o=this.chart,r=this.pages,s=r.length,n=this.clipHeight,a=this.options.navigation,h=this.pager,l=this.padding,d=this.currentPage+t;d>s&&(d=s),d>0&&(void 0!==e&&em(e,o),this.nav.attr({translateX:l,translateY:n+this.padding+7+this.titleHeight,visibility:"inherit"}),[this.up,this.upTracker].forEach(function(t){t.attr({class:1===d?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"})}),h.attr({text:d+"/"+s}),[this.down,this.downTracker].forEach(function(t){t.attr({x:18+this.pager.getBBox().width,class:d===s?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"})},this),o.styledMode||(this.up.attr({fill:1===d?a.inactiveColor:a.activeColor}),this.upTracker.css({cursor:1===d?"default":"pointer"}),this.down.attr({fill:d===s?a.inactiveColor:a.activeColor}),this.downTracker.css({cursor:d===s?"default":"pointer"})),this.scrollOffset=-r[d-1]+this.initialItemY,this.scrollGroup.animate({translateY:this.scrollOffset}),this.currentPage=d,this.positionCheckboxes(),nH(function(){nI(i,"afterScroll",{currentPage:d})},eu(nR(e,o.renderer.globalAnimation,!0)).duration))},t.prototype.setItemEvents=function(t,e,i){for(var o=this,r=t.legendItem||{},s=o.chart.renderer.boxWrapper,n=t instanceof sf,a=t instanceof nk,h="highcharts-legend-"+(n?"point":"series")+"-active",l=o.chart.styledMode,d=i?[e,r.symbol]:[r.group],c=function(e){o.allItems.forEach(function(i){t!==i&&[i].concat(i.linkedSeries||[]).forEach(function(t){t.setState(e,!n)})})},p=0;p<d.length;p++){var u=d[p];u&&u.on("mouseover",function(){t.visible&&c("inactive"),t.setState("hover"),t.visible&&s.addClass(h),l||e.css(o.options.itemHoverStyle)}).on("mouseout",function(){o.chart.styledMode||e.css(nz(t.visible?o.itemStyle:o.itemHiddenStyle)),c(""),s.removeClass(h),t.setState()}).on("click",function(e){var i=function(){t.setVisible&&t.setVisible(),c(t.visible?"inactive":"")};s.removeClass(h),nI(o,"itemClick",{browserEvent:e,legendItem:t},i),n?t.firePointEvent("legendItemClick",{browserEvent:e}):a&&nI(t,"legendItemClick",{browserEvent:e})})}},t.prototype.createCheckboxForItem=function(t){t.checkbox=nO("input",{type:"checkbox",className:"highcharts-legend-checkbox",checked:t.selected,defaultChecked:t.selected},this.options.itemCheckboxStyle,this.chart.container),nC(t.checkbox,"click",function(e){var i=e.target;nI(t.series||t,"checkboxClick",{checked:i.checked,item:t},function(){t.select()})})},t}();(y=nF||(nF={})).compose=function(t){nN(nw,"Core.Legend")&&nC(t,"beforeMargins",function(){this.legend=new y(this,this.options.legend)})};var nj=nF,nY=function(){return(nY=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},nG=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,s=e.length;r<s;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},n_=e_.numberFormat,nU=oB.registerEventOptions,nV=tt.charts,nZ=tt.doc,nq=tt.marginNames,nK=tt.svg,n$=tt.win,nJ=sQ.seriesTypes,nQ=tT.addEvent,n0=tT.attr,n1=tT.createElement,n2=tT.css,n3=tT.defined,n5=tT.diffObjects,n6=tT.discardElement,n9=tT.erase,n4=tT.error,n8=tT.extend,n7=tT.find,at=tT.fireEvent,ae=tT.getAlignFactor,ai=tT.getStyle,ao=tT.isArray,ar=tT.isNumber,as=tT.isObject,an=tT.isString,aa=tT.merge,ah=tT.objectEach,al=tT.pick,ad=tT.pInt,ac=tT.relativeLength,ap=tT.removeEvent,au=tT.splat,af=tT.syncTimeout,ag=tT.uniqueKey,av=function(){function t(t,e,i){this.sharedClips={};var o=nG([],arguments,!0);(an(t)||t.nodeName)&&(this.renderTo=o.shift()),this.init(o[0],o[1])}return t.chart=function(e,i,o){return new t(e,i,o)},t.prototype.setZoomOptions=function(){var t=this.options.chart,e=t.zooming;this.zooming=nY(nY({},e),{type:al(t.zoomType,e.type),key:al(t.zoomKey,e.key),pinchType:al(t.pinchType,e.pinchType),singleTouch:al(t.zoomBySingleTouch,e.singleTouch,!1),resetButton:aa(e.resetButton,t.resetZoomButton)})},t.prototype.init=function(t,e){at(this,"init",{args:arguments},function(){var i,o,r=aa(tJ,t),s=r.chart,n=this.renderTo||s.renderTo;this.userOptions=n8({},t),(this.renderTo=an(n)?nZ.getElementById(n):n)||n4(13,!0,this),this.margin=[],this.spacing=[],this.labelCollectors=[],this.callback=e,this.isResizing=0,this.options=r,this.axes=[],this.series=[],this.locale=null!==(i=r.lang.locale)&&void 0!==i?i:null===(o=this.renderTo.closest("[lang]"))||void 0===o?void 0:o.lang,this.time=new tV(n8(r.time||{},{locale:this.locale}),r.lang),r.time=this.time.options,this.numberFormatter=(s.numberFormatter||n_).bind(this),this.styledMode=s.styledMode,this.hasCartesianSeries=s.showAxes,this.index=nV.length,nV.push(this),tt.chartCount++,nU(this,s),this.xAxis=[],this.yAxis=[],this.pointCount=this.colorCounter=this.symbolCounter=0,this.setZoomOptions(),at(this,"afterInit"),this.firstRender()})},t.prototype.initSeries=function(t){var e=this.options.chart,i=t.type||e.type,o=nJ[i];o||n4(17,!0,this,{missingModuleFor:i});var r=new o;return"function"==typeof r.init&&r.init(this,t),r},t.prototype.setSortedData=function(){this.getSeriesOrderByLinks().forEach(function(t){t.points||t.data||!t.enabledDataSorting||t.setData(t.options.data,!1)})},t.prototype.getSeriesOrderByLinks=function(){return this.series.concat().sort(function(t,e){return t.linkedSeries.length||e.linkedSeries.length?e.linkedSeries.length-t.linkedSeries.length:0})},t.prototype.orderItems=function(t,e){void 0===e&&(e=0);var i=this[t],o=this.options[t]=au(this.options[t]).slice(),r=this.userOptions[t]=this.userOptions[t]?au(this.userOptions[t]).slice():[];if(this.hasRendered&&(o.splice(e),r.splice(e)),i)for(var s=e,n=i.length;s<n;++s){var a=i[s];a&&(a.index=s,a instanceof nk&&(a.name=a.getName()),a.options.isInternal||(o[s]=a.options,r[s]=a.userOptions))}},t.prototype.getClipBox=function(t,e){var i,o,r,s,n,a=this.inverted,h=t||{},l=h.xAxis,d=h.yAxis,c=aa(this.clipBox),p=c.x,u=c.y,f=c.width,g=c.height;return t&&(l&&l.len!==this.plotSizeX&&(f=l.len),d&&d.len!==this.plotSizeY&&(g=d.len),a&&!t.invertible&&(f=(i=[g,f])[0],g=i[1])),e&&(p+=null!==(r=null===(o=a?d:l)||void 0===o?void 0:o.pos)&&void 0!==r?r:this.plotLeft,u+=null!==(n=null===(s=a?l:d)||void 0===s?void 0:s.pos)&&void 0!==n?n:this.plotTop),{x:p,y:u,width:f,height:g}},t.prototype.isInsidePlot=function(t,e,i){void 0===i&&(i={});var o,r=this.inverted,s=this.plotBox,n=this.plotLeft,a=this.plotTop,h=this.scrollablePlotBox,l=i.visiblePlotOnly&&(null===(o=this.scrollablePlotArea)||void 0===o?void 0:o.scrollingContainer)||{},d=l.scrollLeft,c=void 0===d?0:d,p=l.scrollTop,u=void 0===p?0:p,f=i.series,g=i.visiblePlotOnly&&h||s,v=i.inverted?e:t,m=i.inverted?t:e,y={x:v,y:m,isInsidePlot:!0,options:i};if(!i.ignoreX){var x=f&&(r&&!this.polar?f.yAxis:f.xAxis)||{pos:n,len:1/0},b=i.paneCoordinates?x.pos+v:n+v;b>=Math.max(c+n,x.pos)&&b<=Math.min(c+n+g.width,x.pos+x.len)||(y.isInsidePlot=!1)}if(!i.ignoreY&&y.isInsidePlot){var k=!r&&i.axis&&!i.axis.isXAxis&&i.axis||f&&(r?f.xAxis:f.yAxis)||{pos:a,len:1/0},M=i.paneCoordinates?k.pos+m:a+m;M>=Math.max(u+a,k.pos)&&M<=Math.min(u+a+g.height,k.pos+k.len)||(y.isInsidePlot=!1)}return at(this,"afterIsInsidePlot",y),y.isInsidePlot},t.prototype.redraw=function(t){at(this,"beforeRedraw");var e,i,o,r,s=this.hasCartesianSeries?this.axes:this.colorAxis||[],n=this.series,a=this.pointer,h=this.legend,l=this.userOptions.legend,d=this.renderer,c=d.isHidden(),p=[],u=this.isDirtyBox,f=this.isDirtyLegend;for(d.rootFontSize=d.boxWrapper.getStyle("font-size"),this.setResponsive&&this.setResponsive(!1),em(!!this.hasRendered&&t,this),c&&this.temporaryDisplay(),this.layOutTitles(!1),o=n.length;o--;)if(((r=n[o]).options.stacking||r.options.centerInCategory)&&(i=!0,r.isDirty)){e=!0;break}if(e)for(o=n.length;o--;)(r=n[o]).options.stacking&&(r.isDirty=!0);n.forEach(function(t){t.isDirty&&("point"===t.options.legendType?("function"==typeof t.updateTotals&&t.updateTotals(),f=!0):l&&(l.labelFormatter||l.labelFormat)&&(f=!0)),t.isDirtyData&&at(t,"updatedData")}),f&&h&&h.options.enabled&&(h.render(),this.isDirtyLegend=!1),i&&this.getStacks(),s.forEach(function(t){t.updateNames(),t.setScale()}),this.getMargins(),s.forEach(function(t){t.isDirty&&(u=!0)}),s.forEach(function(t){var e=t.min+","+t.max;t.extKey!==e&&(t.extKey=e,p.push(function(){at(t,"afterSetExtremes",n8(t.eventArgs,t.getExtremes())),delete t.eventArgs})),(u||i)&&t.redraw()}),u&&this.drawChartBox(),at(this,"predraw"),n.forEach(function(t){(u||t.isDirty)&&t.visible&&t.redraw(),t.isDirtyData=!1}),a&&a.reset(!0),d.draw(),at(this,"redraw"),at(this,"render"),c&&this.temporaryDisplay(!0),p.forEach(function(t){t.call()})},t.prototype.get=function(t){var e=this.series;function i(e){return e.id===t||e.options&&e.options.id===t}for(var o=n7(this.axes,i)||n7(this.series,i),r=0;!o&&r<e.length;r++)o=n7(e[r].points||[],i);return o},t.prototype.createAxes=function(){var t=this.userOptions;at(this,"createAxes");for(var e=0,i=["xAxis","yAxis"];e<i.length;e++)for(var o=i[e],r=t[o]=au(t[o]||{}),s=0;s<r.length;s++)new rp(this,r[s],o);at(this,"afterCreateAxes")},t.prototype.getSelectedPoints=function(){return this.series.reduce(function(t,e){return e.getPointsCollection().forEach(function(e){al(e.selectedStaging,e.selected)&&t.push(e)}),t},[])},t.prototype.getSelectedSeries=function(){return this.series.filter(function(t){return t.selected})},t.prototype.setTitle=function(t,e,i){this.applyDescription("title",t),this.applyDescription("subtitle",e),this.applyDescription("caption",void 0),this.layOutTitles(i)},t.prototype.applyDescription=function(t,e){var i,o=this,r=this.options[t]=aa(this.options[t],e),s=this[t];s&&e&&(this[t]=s=s.destroy()),r&&!s&&((s=this.renderer.text(r.text,0,0,r.useHTML).attr({align:r.align,class:"highcharts-"+t,zIndex:r.zIndex||4}).css({textOverflow:"ellipsis",whiteSpace:"nowrap"}).add()).update=function(e,i){o.applyDescription(t,e),o.layOutTitles(i)},this.styledMode||s.css(n8("title"===t?{fontSize:this.options.isStock?"1em":"1.2em"}:{},r.style)),s.textPxLength=s.getBBox().width,s.css({whiteSpace:null===(i=r.style)||void 0===i?void 0:i.whiteSpace}),this[t]=s)},t.prototype.layOutTitles=function(t){var e,i,o,r,s=this;void 0===t&&(t=!0);var n=[0,0,0],a=this.options,h=this.renderer,l=this.spacingBox;["title","subtitle","caption"].forEach(function(t){var e,i=s[t],o=s.options[t],r=aa(l),a=(null==i?void 0:i.textPxLength)||0;if(i&&o){at(s,"layOutTitle",{alignTo:r,key:t,textPxLength:a});var d=h.fontMetrics(i),c=d.b,p=d.h,u=o.verticalAlign||"top",f="top"===u,g=f&&o.minScale||1,v="title"===t?f?-3:0:f?n[0]+2:0,m=Math.min(r.width/a,1),y=Math.max(g,m),x=aa({y:"bottom"===u?c:v+c},{align:"title"===t?m<g?"left":"center":null===(e=s.title)||void 0===e?void 0:e.alignValue},o),b=(o.width||(m>g?s.chartWidth:r.width)/y)+"px";i.alignValue!==x.align&&(i.placed=!1);var k=Math.round(i.css({width:b}).getBBox(o.useHTML).height);if(x.height=k,i.align(x,!1,r).attr({align:x.align,scaleX:y,scaleY:y,"transform-origin":""+(r.x+a*y*ae(x.align))+" ".concat(p)}),!o.floating){var M=k*(k<1.2*p?1:y);"top"===u?n[0]=Math.ceil(n[0]+M):"bottom"===u&&(n[2]=Math.ceil(n[2]+M))}}},this),n[0]&&"top"===((null===(e=a.title)||void 0===e?void 0:e.verticalAlign)||"top")&&(n[0]+=(null===(i=a.title)||void 0===i?void 0:i.margin)||0),n[2]&&(null===(o=a.caption)||void 0===o?void 0:o.verticalAlign)==="bottom"&&(n[2]+=(null===(r=a.caption)||void 0===r?void 0:r.margin)||0);var d=!this.titleOffset||this.titleOffset.join(",")!==n.join(",");this.titleOffset=n,at(this,"afterLayOutTitles"),!this.isDirtyBox&&d&&(this.isDirtyBox=this.isDirtyLegend=d,this.hasRendered&&t&&this.isDirtyBox&&this.redraw())},t.prototype.getContainerBox=function(){var t=this,e=[].map.call(this.renderTo.children,function(e){if(e!==t.container){var i=e.style.display;return e.style.display="none",[e,i]}}),i={width:ai(this.renderTo,"width",!0)||0,height:ai(this.renderTo,"height",!0)||0};return e.filter(Boolean).forEach(function(t){var e=t[0],i=t[1];e.style.display=i}),i},t.prototype.getChartSize=function(){var t,e=this.options.chart,i=e.width,o=e.height,r=this.getContainerBox(),s=r.height<=1||!(null===(t=this.renderTo.parentElement)||void 0===t?void 0:t.style.height)&&"100%"===this.renderTo.style.height;this.chartWidth=Math.max(0,i||r.width||600),this.chartHeight=Math.max(0,ac(o,this.chartWidth)||(s?400:r.height)),this.containerBox=r},t.prototype.temporaryDisplay=function(t){var e,i=this.renderTo;if(t)for(;null==i?void 0:i.style;)i.hcOrigStyle&&(n2(i,i.hcOrigStyle),delete i.hcOrigStyle),i.hcOrigDetached&&(nZ.body.removeChild(i),i.hcOrigDetached=!1),i=i.parentNode;else for(;(null==i?void 0:i.style)&&(nZ.body.contains(i)||i.parentNode||(i.hcOrigDetached=!0,nZ.body.appendChild(i)),("none"===ai(i,"display",!1)||i.hcOricDetached)&&(i.hcOrigStyle={display:i.style.display,height:i.style.height,overflow:i.style.overflow},e={display:"block",overflow:"hidden"},i!==this.renderTo&&(e.height=0),n2(i,e),i.offsetWidth||i.style.setProperty("display","block","important")),(i=i.parentNode)!==nZ.body););},t.prototype.setClassName=function(t){this.container.className="highcharts-container "+(t||"")},t.prototype.getContainer=function(){var t,e,i,o=this.options,r=o.chart,s="data-highcharts-chart",n=ag(),a=this.renderTo,h=ad(n0(a,s));ar(h)&&nV[h]&&nV[h].hasRendered&&nV[h].destroy(),n0(a,s,this.index),a.innerHTML=eL.emptyHTML,r.skipClone||a.offsetWidth||this.temporaryDisplay(),this.getChartSize();var l=this.chartHeight,d=this.chartWidth;n2(a,{overflow:"hidden"}),this.styledMode||(i=n8({position:"relative",overflow:"hidden",width:d+"px",height:l+"px",textAlign:"left",lineHeight:"normal",zIndex:0,"-webkit-tap-highlight-color":"rgba(0,0,0,0)",userSelect:"none","touch-action":"manipulation",outline:"none",padding:"0px"},r.style||{}));var c=n1("div",{id:n},i,a);this.container=c,this.getChartSize(),d===this.chartWidth||(d=this.chartWidth,this.styledMode||n2(c,{width:al(null===(t=r.style)||void 0===t?void 0:t.width,d+"px")})),this.containerBox=this.getContainerBox(),this._cursor=c.style.cursor;var p=r.renderer||!nK?eU.getRendererType(r.renderer):oc;if(this.renderer=new p(c,d,l,void 0,r.forExport,null===(e=o.exporting)||void 0===e?void 0:e.allowHTML,this.styledMode),em(void 0,this),this.setClassName(r.className),this.styledMode)for(var u in o.defs)this.renderer.definition(o.defs[u]);else this.renderer.setStyle(r.style);this.renderer.chartIndex=this.index,at(this,"afterGetContainer")},t.prototype.getMargins=function(t){var e,i=this.spacing,o=this.margin,r=this.titleOffset;this.resetMargins(),r[0]&&!n3(o[0])&&(this.plotTop=Math.max(this.plotTop,r[0]+i[0])),r[2]&&!n3(o[2])&&(this.marginBottom=Math.max(this.marginBottom,r[2]+i[2])),(null===(e=this.legend)||void 0===e?void 0:e.display)&&this.legend.adjustMargins(o,i),at(this,"getMargins"),t||this.getAxisMargins()},t.prototype.getAxisMargins=function(){var t=this,e=t.axisOffset=[0,0,0,0],i=t.colorAxis,o=t.margin,r=function(t){t.forEach(function(t){t.visible&&t.getOffset()})};t.hasCartesianSeries?r(t.axes):(null==i?void 0:i.length)&&r(i),nq.forEach(function(i,r){n3(o[r])||(t[i]+=e[r])}),t.setChartSize()},t.prototype.getOptions=function(){return n5(this.userOptions,tJ)},t.prototype.reflow=function(t){var e,i=this,o=i.containerBox,r=i.getContainerBox();null===(e=i.pointer)||void 0===e||delete e.chartPosition,!i.isPrinting&&!i.isResizing&&o&&r.width&&((r.width!==o.width||r.height!==o.height)&&(tT.clearTimeout(i.reflowTimeout),i.reflowTimeout=af(function(){i.container&&i.setSize(void 0,void 0,!1)},100*!!t)),i.containerBox=r)},t.prototype.setReflow=function(){var t=this,e=function(e){var i;(null===(i=t.options)||void 0===i?void 0:i.chart.reflow)&&t.hasLoaded&&t.reflow(e)};if("function"==typeof ResizeObserver)new ResizeObserver(e).observe(t.renderTo);else{var i=nQ(n$,"resize",e);nQ(this,"destroy",i)}},t.prototype.setSize=function(t,e,i){var o=this,r=o.renderer;o.isResizing+=1,em(i,o);var s=r.globalAnimation;o.oldChartHeight=o.chartHeight,o.oldChartWidth=o.chartWidth,void 0!==t&&(o.options.chart.width=t),void 0!==e&&(o.options.chart.height=e),o.getChartSize();var n=o.chartWidth,a=o.chartHeight,h=o.scrollablePixelsX,l=o.scrollablePixelsY;(o.isDirtyBox||n!==o.oldChartWidth||a!==o.oldChartHeight)&&(o.styledMode||(s?eg:n2)(o.container,{width:""+(n+(void 0===h?0:h))+"px",height:""+(a+(void 0===l?0:l))+"px"},s),o.setChartSize(!0),r.setSize(n,a,s),o.axes.forEach(function(t){t.isDirty=!0,t.setScale()}),o.isDirtyLegend=!0,o.isDirtyBox=!0,o.layOutTitles(),o.getMargins(),o.redraw(s),o.oldChartHeight=void 0,at(o,"resize"),setTimeout(function(){o&&at(o,"endResize")},eu(s).duration)),o.isResizing-=1},t.prototype.setChartSize=function(t){var e,i,o,r,s,n,a=this.chartHeight,h=this.chartWidth,l=this.inverted,d=this.spacing,c=this.renderer,p=this.clipOffset,u=Math[l?"floor":"round"];this.plotLeft=o=Math.round(this.plotLeft),this.plotTop=r=Math.round(this.plotTop),this.plotWidth=s=Math.max(0,Math.round(h-o-(null!==(e=this.marginRight)&&void 0!==e?e:0))),this.plotHeight=n=Math.max(0,Math.round(a-r-(null!==(i=this.marginBottom)&&void 0!==i?i:0))),this.plotSizeX=l?n:s,this.plotSizeY=l?s:n,this.spacingBox=c.spacingBox={x:d[3],y:d[0],width:h-d[3]-d[1],height:a-d[0]-d[2]},this.plotBox=c.plotBox={x:o,y:r,width:s,height:n},p&&(this.clipBox={x:u(p[3]),y:u(p[0]),width:u(this.plotSizeX-p[1]-p[3]),height:u(this.plotSizeY-p[0]-p[2])}),t||(this.axes.forEach(function(t){t.setAxisSize(),t.setAxisTranslation()}),c.alignElements()),at(this,"afterSetChartSize",{skipAxes:t})},t.prototype.resetMargins=function(){at(this,"resetMargins");var t=this,e=t.options.chart,i=e.plotBorderWidth||0,o=Math.round(i)/2;["margin","spacing"].forEach(function(i){var o=e[i],r=as(o)?o:[o,o,o,o];["Top","Right","Bottom","Left"].forEach(function(o,s){t[i][s]=al(e[i+o],r[s])})}),nq.forEach(function(e,i){t[e]=al(t.margin[i],t.spacing[i])}),t.axisOffset=[0,0,0,0],t.clipOffset=[o,o,o,o],t.plotBorderWidth=i},t.prototype.drawChartBox=function(){var t,e,i,o=this.options.chart,r=this.renderer,s=this.chartWidth,n=this.chartHeight,a=this.styledMode,h=this.plotBGImage,l=o.backgroundColor,d=o.plotBackgroundColor,c=o.plotBackgroundImage,p=this.plotLeft,u=this.plotTop,f=this.plotWidth,g=this.plotHeight,v=this.plotBox,m=this.clipRect,y=this.clipBox,x=this.chartBackground,b=this.plotBackground,k=this.plotBorder,M="animate";x||(this.chartBackground=x=r.rect().addClass("highcharts-background").add(),M="attr"),a?t=e=x.strokeWidth():(e=(t=o.borderWidth||0)+8*!!o.shadow,i={fill:l||"none"},(t||x["stroke-width"])&&(i.stroke=o.borderColor,i["stroke-width"]=t),x.attr(i).shadow(o.shadow)),x[M]({x:e/2,y:e/2,width:s-e-t%2,height:n-e-t%2,r:o.borderRadius}),M="animate",b||(M="attr",this.plotBackground=b=r.rect().addClass("highcharts-plot-background").add()),b[M](v),!a&&(b.attr({fill:d||"none"}).shadow(o.plotShadow),c&&(h?(c!==h.attr("href")&&h.attr("href",c),h.animate(v)):this.plotBGImage=r.image(c,p,u,f,g).add())),m?m.animate({width:y.width,height:y.height}):this.clipRect=r.clipRect(y),M="animate",k||(M="attr",this.plotBorder=k=r.rect().addClass("highcharts-plot-border").attr({zIndex:1}).add()),a||k.attr({stroke:o.plotBorderColor,"stroke-width":o.plotBorderWidth||0,fill:"none"}),k[M](k.crisp(v,-k.strokeWidth())),this.isDirtyBox=!1,at(this,"afterDrawChartBox")},t.prototype.propFromSeries=function(){var t,e,i,o=this,r=o.options.chart,s=o.options.series;["inverted","angular","polar"].forEach(function(n){for(e=nJ[r.type],i=r[n]||e&&e.prototype[n],t=null==s?void 0:s.length;!i&&t--;)(e=nJ[s[t].type])&&e.prototype[n]&&(i=!0);o[n]=i})},t.prototype.linkSeries=function(t){var e=this,i=e.series;i.forEach(function(t){t.linkedSeries.length=0}),i.forEach(function(t){var i=t.options.linkedTo;if(an(i)){var o=void 0;(o=":previous"===i?e.series[t.index-1]:e.get(i))&&o.linkedParent!==t&&(o.linkedSeries.push(t),t.linkedParent=o,o.enabledDataSorting&&t.setDataSortingOptions(),t.visible=al(t.options.visible,o.options.visible,t.visible))}}),at(this,"afterLinkSeries",{isUpdating:t})},t.prototype.renderSeries=function(){this.series.forEach(function(t){t.translate(),t.render()})},t.prototype.render=function(){var t,e,i=this.axes,o=this.colorAxis,r=this.renderer,s=this.options.chart.axisLayoutRuns||2,n=function(t){t.forEach(function(t){t.visible&&t.render()})},a=0,h=!0,l=0;this.setTitle(),at(this,"beforeMargins"),null===(t=this.getStacks)||void 0===t||t.call(this),this.getMargins(!0),this.setChartSize();for(var d=0;d<i.length;d++){var c=i[d],p=c.options,u=p.labels;if(this.hasCartesianSeries&&c.horiz&&c.visible&&u.enabled&&c.series.length&&"colorAxis"!==c.coll&&!this.polar){a=p.tickLength,c.createGroups();var f=new oV(c,0,"",!0),g=f.createLabel("x",u);if(f.destroy(),g&&al(u.reserveSpace,!ar(p.crossing))&&(a=g.getBBox().height+u.distance+Math.max(p.offset||0,0)),a){null==g||g.destroy();break}}}for(this.plotHeight=Math.max(this.plotHeight-a,0);(h||e||s>1)&&l<s;){for(var v=this.plotWidth,m=this.plotHeight,y=0;y<i.length;y++){var c=i[y];0===l?c.setScale():(c.horiz&&h||!c.horiz&&e)&&c.setTickInterval(!0)}0===l?this.getAxisMargins():this.getMargins(),h=v/this.plotWidth>(l?1:1.1),e=m/this.plotHeight>(l?1:1.05),l++}this.drawChartBox(),this.hasCartesianSeries?n(i):(null==o?void 0:o.length)&&n(o),this.seriesGroup||(this.seriesGroup=r.g("series-group").attr({zIndex:3}).shadow(this.options.chart.seriesGroupShadow).add()),this.renderSeries(),this.addCredits(),this.setResponsive&&this.setResponsive(),this.hasRendered=!0},t.prototype.addCredits=function(t){var e=this,i=aa(!0,this.options.credits,t);i.enabled&&!this.credits&&(this.credits=this.renderer.text(i.text+(this.mapCredits||""),0,0).addClass("highcharts-credits").on("click",function(){i.href&&(n$.location.href=i.href)}).attr({align:i.position.align,zIndex:8}),e.styledMode||this.credits.css(i.style),this.credits.add().align(i.position),this.credits.update=function(t){e.credits=e.credits.destroy(),e.addCredits(t)})},t.prototype.destroy=function(){var t,e,i,o=this,r=o.axes,s=o.series,n=o.container,a=null==n?void 0:n.parentNode;for(at(o,"destroy"),o.renderer.forExport?n9(nV,o):nV[o.index]=void 0,tt.chartCount--,o.renderTo.removeAttribute("data-highcharts-chart"),ap(o),i=r.length;i--;)r[i]=r[i].destroy();for(null===(e=null===(t=this.scroller)||void 0===t?void 0:t.destroy)||void 0===e||e.call(t),i=s.length;i--;)s[i]=s[i].destroy();["title","subtitle","chartBackground","plotBackground","plotBGImage","plotBorder","seriesGroup","clipRect","credits","pointer","rangeSelector","legend","resetZoomButton","tooltip","renderer"].forEach(function(t){var e,i;o[t]=null===(i=null===(e=o[t])||void 0===e?void 0:e.destroy)||void 0===i?void 0:i.call(e)}),n&&(n.innerHTML=eL.emptyHTML,ap(n),a&&n6(n)),ah(o,function(t,e){delete o[e]})},t.prototype.firstRender=function(){var t,e=this,i=e.options;e.getContainer(),e.resetMargins(),e.setChartSize(),e.propFromSeries(),e.createAxes();var o=ao(i.series)?i.series:[];i.series=[],o.forEach(function(t){e.initSeries(t)}),e.linkSeries(),e.setSortedData(),at(e,"beforeRender"),e.render(),null===(t=e.pointer)||void 0===t||t.getChartPosition(),e.renderer.imgCount||e.hasLoaded||e.onload(),e.temporaryDisplay(!0)},t.prototype.onload=function(){this.callbacks.concat([this.callback]).forEach(function(t){t&&void 0!==this.index&&t.apply(this,[this])},this),at(this,"load"),at(this,"render"),n3(this.index)&&this.setReflow(),this.warnIfA11yModuleNotLoaded(),this.hasLoaded=!0},t.prototype.warnIfA11yModuleNotLoaded=function(){var t=this.options,e=this.title;!t||this.accessibility||(this.renderer.boxWrapper.attr({role:"img","aria-label":((null==e?void 0:e.element.textContent)||"").replace(/</g,"&lt;")}),t.accessibility&&!1===t.accessibility.enabled||n4('Highcharts warning: Consider including the "accessibility.js" module to make your chart more usable for people with disabilities. Set the "accessibility.enabled" option to false to remove this warning. See https://www.highcharts.com/docs/accessibility/accessibility-module.',!1,this))},t.prototype.addSeries=function(t,e,i){var o,r=this;return t&&(e=al(e,!0),at(r,"addSeries",{options:t},function(){o=r.initSeries(t),r.isDirtyLegend=!0,r.linkSeries(),o.enabledDataSorting&&o.setData(t.data,!1),at(r,"afterAddSeries",{series:o}),e&&r.redraw(i)})),o},t.prototype.addAxis=function(t,e,i,o){return this.createAxis(e?"xAxis":"yAxis",{axis:t,redraw:i,animation:o})},t.prototype.addColorAxis=function(t,e,i){return this.createAxis("colorAxis",{axis:t,redraw:e,animation:i})},t.prototype.createAxis=function(t,e){var i=new rp(this,e.axis,t);return al(e.redraw,!0)&&this.redraw(e.animation),i},t.prototype.showLoading=function(t){var e=this,i=e.options,o=i.loading,r=function(){s&&n2(s,{left:e.plotLeft+"px",top:e.plotTop+"px",width:e.plotWidth+"px",height:e.plotHeight+"px"})},s=e.loadingDiv,n=e.loadingSpan;s||(e.loadingDiv=s=n1("div",{className:"highcharts-loading highcharts-loading-hidden"},null,e.container)),n||(e.loadingSpan=n=n1("span",{className:"highcharts-loading-inner"},null,s),nQ(e,"redraw",r)),s.className="highcharts-loading",eL.setElementHTML(n,al(t,i.lang.loading,"")),e.styledMode||(n2(s,n8(o.style,{zIndex:10})),n2(n,o.labelStyle),e.loadingShown||(n2(s,{opacity:0,display:""}),eg(s,{opacity:o.style.opacity||.5},{duration:o.showDuration||0}))),e.loadingShown=!0,r()},t.prototype.hideLoading=function(){var t=this.options,e=this.loadingDiv;e&&(e.className="highcharts-loading highcharts-loading-hidden",this.styledMode||eg(e,{opacity:0},{duration:t.loading.hideDuration||100,complete:function(){n2(e,{display:"none"})}})),this.loadingShown=!1},t.prototype.update=function(t,e,i,o){var r,s,n,a=this,h={credits:"addCredits",title:"setTitle",subtitle:"setSubtitle",caption:"setCaption"},l=t.isResponsiveOptions,d=[];at(a,"update",{options:t}),l||a.setResponsive(!1,!0),t=n5(t,a.options),a.userOptions=aa(a.userOptions,t);var c=t.chart;c&&(aa(!0,a.options.chart,c),this.setZoomOptions(),"className"in c&&a.setClassName(c.className),("inverted"in c||"polar"in c||"type"in c)&&(a.propFromSeries(),r=!0),"alignTicks"in c&&(r=!0),"events"in c&&nU(this,c),ah(c,function(t,e){-1!==a.propsRequireUpdateSeries.indexOf("chart."+e)&&(s=!0),-1!==a.propsRequireDirtyBox.indexOf(e)&&(a.isDirtyBox=!0),-1===a.propsRequireReflow.indexOf(e)||(a.isDirtyBox=!0,l||(n=!0))}),!a.styledMode&&c.style&&a.renderer.setStyle(a.options.chart.style||{})),!a.styledMode&&t.colors&&(this.options.colors=t.colors),ah(t,function(e,i){a[i]&&"function"==typeof a[i].update?a[i].update(e,!1):"function"==typeof a[h[i]]?a[h[i]](e):"colors"!==i&&-1===a.collectionsWithUpdate.indexOf(i)&&aa(!0,a.options[i],t[i]),"chart"!==i&&-1!==a.propsRequireUpdateSeries.indexOf(i)&&(s=!0)}),this.collectionsWithUpdate.forEach(function(e){t[e]&&(au(t[e]).forEach(function(t,o){var r,s=n3(t.id);s&&(r=a.get(t.id)),!r&&a[e]&&(r=a[e][al(t.index,o)])&&(s&&n3(r.options.id)||r.options.isInternal)&&(r=void 0),r&&r.coll===e&&(r.update(t,!1),i&&(r.touched=!0)),!r&&i&&a.collectionsWithInit[e]&&(a.collectionsWithInit[e][0].apply(a,[t].concat(a.collectionsWithInit[e][1]||[]).concat([!1])).touched=!0)}),i&&a[e].forEach(function(t){t.touched||t.options.isInternal?delete t.touched:d.push(t)}))}),d.forEach(function(t){t.chart&&t.remove&&t.remove(!1)}),r&&a.axes.forEach(function(t){t.update({},!1)}),s&&a.getSeriesOrderByLinks().forEach(function(t){t.chart&&t.update({},!1)},this);var p=null==c?void 0:c.width,u=c&&(an(c.height)?ac(c.height,p||a.chartWidth):c.height);n||ar(p)&&p!==a.chartWidth||ar(u)&&u!==a.chartHeight?a.setSize(p,u,o):al(e,!0)&&a.redraw(o),at(a,"afterUpdate",{options:t,redraw:e,animation:o})},t.prototype.setSubtitle=function(t,e){this.applyDescription("subtitle",t),this.layOutTitles(e)},t.prototype.setCaption=function(t,e){this.applyDescription("caption",t),this.layOutTitles(e)},t.prototype.showResetZoom=function(){var t=this,e=tJ.lang,i=t.zooming.resetButton,o=i.theme,r="chart"===i.relativeTo||"spacingBox"===i.relativeTo?null:"plotBox";function s(){t.zoomOut()}at(this,"beforeShowResetZoom",null,function(){t.resetZoomButton=t.renderer.button(e.resetZoom,null,null,s,o).attr({align:i.position.align,title:e.resetZoomTitle}).addClass("highcharts-reset-zoom").add().align(i.position,!1,r)}),at(this,"afterShowResetZoom")},t.prototype.zoomOut=function(){var t=this;at(this,"selection",{resetSelection:!0},function(){return t.transform({reset:!0,trigger:"zoom"})})},t.prototype.pan=function(t,e){var i=this,o="object"==typeof e?e:{enabled:e,type:"x"},r=o.type,s=r&&i[({x:"xAxis",xy:"axes",y:"yAxis"})[r]].filter(function(t){return t.options.panningEnabled&&!t.options.isInternal}),n=i.options.chart;(null==n?void 0:n.panning)&&(n.panning=o),at(this,"pan",{originalEvent:t},function(){i.transform({axes:s,event:t,to:{x:t.chartX-(i.mouseDownX||0),y:t.chartY-(i.mouseDownY||0)},trigger:"pan"}),n2(i.container,{cursor:"move"})})},t.prototype.transform=function(t){var e,i,o,r,s,n,a=this,h=t.axes,l=void 0===h?this.axes:h,d=t.event,c=t.from,p=void 0===c?{}:c,u=t.reset,f=t.selection,g=t.to,v=void 0===g?{}:g,m=t.trigger,y=this.inverted,x=this.time,b=!1;null===(i=this.hoverPoints)||void 0===i||i.forEach(function(t){return t.setState()});for(var k=0;k<l.length;k++){var M=l[k],w=M.horiz,S=M.len,A=M.minPointOffset,T=void 0===A?0:A,C=M.options,O=M.reversed,P=w?"width":"height",E=w?"x":"y",L=al(v[P],M.len),D=al(p[P],M.len),I=10>Math.abs(L)?1:L/D,B=(p[E]||0)+D/2-M.pos,z=B-((null!==(o=v[E])&&void 0!==o?o:M.pos)+L/2-M.pos)/I,R=O&&!y||!O&&y?-1:1;if(u||!(B<0)&&!(B>M.len)){var N=M.toValue(z,!0)+(f||M.isOrdinal?0:T*R),W=M.toValue(z+S/I,!0)-(f||M.isOrdinal?0:T*R||0),X=M.allExtremes;if(N>W&&(N=(e=[W,N])[0],W=e[1]),1===I&&!u&&"yAxis"===M.coll&&!X){for(var H=0,F=M.series;H<F.length;H++){var j=F[H],Y=j.getExtremes(j.getProcessedData(!0).modified.getColumn("y")||[],!0);null!=X||(X={dataMin:Number.MAX_VALUE,dataMax:-Number.MAX_VALUE}),ar(Y.dataMin)&&ar(Y.dataMax)&&(X.dataMin=Math.min(Y.dataMin,X.dataMin),X.dataMax=Math.max(Y.dataMax,X.dataMax))}M.allExtremes=X}var G=n8(M.getExtremes(),X||{}),_=G.dataMin,U=G.dataMax,V=G.min,Z=G.max,q=x.parse(C.min),K=x.parse(C.max),$=null!=_?_:q,J=null!=U?U:K,Q=W-N,tt=M.categories?0:Math.min(Q,J-$),te=$-tt*(n3(q)?0:C.minPadding),ti=J+tt*(n3(K)?0:C.maxPadding),to=M.allowZoomOutside||1===I||"zoom"!==m&&I>1,tr=Math.min(null!=q?q:te,te,to?V:te),ts=Math.max(null!=K?K:ti,ti,to?Z:ti);(!M.isOrdinal||M.options.overscroll||1!==I||u)&&(N<tr&&(N=tr,I>=1&&(W=N+Q)),W>ts&&(W=ts,I>=1&&(N=W-Q)),(u||M.series.length&&(N!==V||W!==Z)&&N>=tr&&W<=ts)&&(f?f[M.coll].push({axis:M,min:N,max:W}):(M.isPanning="zoom"!==m,M.isPanning&&(n=!0),M.setExtremes(u?void 0:N,u?void 0:W,!1,!1,{move:z,trigger:m,scale:I}),!u&&(N>tr||W<ts)&&"mousewheel"!==m&&(s=!0)),b=!0),d&&(this[w?"mouseDownX":"mouseDownY"]=d[w?"chartX":"chartY"]))}}return b&&(f?at(this,"selection",f,function(){delete t.selection,t.trigger="zoom",a.transform(t)}):(!s||n||this.resetZoomButton?!s&&this.resetZoomButton&&(this.resetZoomButton=this.resetZoomButton.destroy()):this.showResetZoom(),this.redraw("zoom"===m&&(null!==(r=this.options.chart.animation)&&void 0!==r?r:this.pointCount<100)))),b},t}();n8(av.prototype,{callbacks:[],collectionsWithInit:{xAxis:[av.prototype.addAxis,[!0]],yAxis:[av.prototype.addAxis,[!1]],series:[av.prototype.addSeries]},collectionsWithUpdate:["xAxis","yAxis","series"],propsRequireDirtyBox:["backgroundColor","borderColor","borderWidth","borderRadius","plotBackgroundColor","plotBackgroundImage","plotBorderColor","plotBorderWidth","plotShadow","shadow"],propsRequireReflow:["margin","marginTop","marginRight","marginBottom","marginLeft","spacing","spacingTop","spacingRight","spacingBottom","spacingLeft"],propsRequireUpdateSeries:["chart.inverted","chart.polar","chart.ignoreHiddenSeries","chart.type","colors","plotOptions","time","tooltip"]});var am=tt.composed,ay=tT.addEvent,ax=tT.createElement,ab=tT.css,ak=tT.defined,aM=tT.erase,aw=tT.merge,aS=tT.pushUnique;function aA(){var t=this.scrollablePlotArea;(this.scrollablePixelsX||this.scrollablePixelsY)&&!t&&(this.scrollablePlotArea=t=new aC(this)),null==t||t.applyFixed()}function aT(){this.chart.scrollablePlotArea&&(this.chart.scrollablePlotArea.isDirty=!0)}var aC=function(){function t(t){var e,i,o,r=t.options.chart,s=eU.getRendererType(),n=r.scrollablePlotArea||{},a=this.moveFixedElements.bind(this),h={WebkitOverflowScrolling:"touch",overflowX:"hidden",overflowY:"hidden"};t.scrollablePixelsX&&(h.overflowX="auto"),t.scrollablePixelsY&&(h.overflowY="auto"),this.chart=t;var l=this.parentDiv=ax("div",{className:"highcharts-scrolling-parent"},{position:"relative"},t.renderTo),d=this.scrollingContainer=ax("div",{className:"highcharts-scrolling"},h,l),c=this.innerContainer=ax("div",{className:"highcharts-inner-container"},void 0,d),p=this.fixedDiv=ax("div",{className:"highcharts-fixed"},{position:"absolute",overflow:"hidden",pointerEvents:"none",zIndex:((null===(e=r.style)||void 0===e?void 0:e.zIndex)||0)+2,top:0},void 0,!0),u=this.fixedRenderer=new s(p,t.chartWidth,t.chartHeight,r.style);this.mask=u.path().attr({fill:r.backgroundColor||"#fff","fill-opacity":null!==(i=n.opacity)&&void 0!==i?i:.85,zIndex:-1}).addClass("highcharts-scrollable-mask").add(),d.parentNode.insertBefore(p,d),ab(t.renderTo,{overflow:"visible"}),ay(t,"afterShowResetZoom",a),ay(t,"afterApplyDrilldown",a),ay(t,"afterLayOutTitles",a),ay(d,"scroll",function(){var e=t.pointer,i=t.hoverPoint;e&&(delete e.chartPosition,i&&(o=i),e.runPointActions(void 0,o,!0))}),c.appendChild(t.container)}return t.compose=function(t,e,i){var o=this;aS(am,this.compose)&&(ay(t,"afterInit",aT),ay(e,"afterSetChartSize",function(t){return o.afterSetSize(t.target,t)}),ay(e,"render",aA),ay(i,"show",aT))},t.afterSetSize=function(t,e){var i,o,r,s=t.options.chart.scrollablePlotArea||{},n=s.minWidth,a=s.minHeight,h=t.clipBox,l=t.plotBox,d=t.inverted;if(!t.renderer.forExport&&(n?(t.scrollablePixelsX=i=Math.max(0,n-t.chartWidth),i&&(t.scrollablePlotBox=aw(t.plotBox),l.width=t.plotWidth+=i,h[d?"height":"width"]+=i,r=!0)):a&&(t.scrollablePixelsY=o=Math.max(0,a-t.chartHeight),ak(o)&&(t.scrollablePlotBox=aw(t.plotBox),l.height=t.plotHeight+=o,h[d?"width":"height"]+=o,r=!1)),ak(r)&&!e.skipAxes))for(var c=0,p=t.axes;c<p.length;c++){var u=p[c];(u.horiz===r||t.hasParallelCoordinates&&"yAxis"===u.coll)&&(u.setAxisSize(),u.setAxisTranslation())}},t.prototype.applyFixed=function(){var t,e=this.chart,i=this.fixedRenderer,o=this.isDirty,r=this.scrollingContainer,s=e.axisOffset,n=e.chartWidth,a=e.chartHeight,h=e.container,l=e.plotHeight,d=e.plotLeft,c=e.plotTop,p=e.plotWidth,u=e.scrollablePixelsX,f=void 0===u?0:u,g=e.scrollablePixelsY,v=void 0===g?0:g,m=e.options.chart.scrollablePlotArea||{},y=m.scrollPositionX,x=m.scrollPositionY,b=n+f,k=a+v;i.setSize(n,a),(null==o||o)&&(this.isDirty=!1,this.moveFixedElements()),ef(e.container),ab(h,{width:""+b+"px",height:""+k+"px"}),e.renderer.boxWrapper.attr({width:b,height:k,viewBox:[0,0,b,k].join(" ")}),null===(t=e.chartBackground)||void 0===t||t.attr({width:b,height:k}),ab(r,{width:""+n+"px",height:""+a+"px"}),ak(o)||(r.scrollLeft=f*(void 0===y?0:y),r.scrollTop=v*(void 0===x?0:x));var M=c-s[0]-1,w=d-s[3]-1,S=c+l+s[2]+1,A=d+p+s[1]+1,T=d+p-f,C=c+l-v,O=[["M",0,0]];f?O=[["M",0,M],["L",d-1,M],["L",d-1,S],["L",0,S],["Z"],["M",T,M],["L",n,M],["L",n,S],["L",T,S],["Z"]]:v&&(O=[["M",w,0],["L",w,c-1],["L",A,c-1],["L",A,0],["Z"],["M",w,C],["L",w,a],["L",A,a],["L",A,C],["Z"]]),"adjustHeight"!==e.redrawTrigger&&this.mask.attr({d:O})},t.prototype.moveFixedElements=function(){var e,i=this.chart,o=i.container,r=i.inverted,s=i.scrollablePixelsX,n=i.scrollablePixelsY,a=this.fixedRenderer,h=t.fixedSelectors;if(s&&!r?e=".highcharts-yaxis":s&&r?e=".highcharts-xaxis":n&&!r?e=".highcharts-xaxis":n&&r&&(e=".highcharts-yaxis"),e&&!(this.chart.hasParallelCoordinates&&".highcharts-yaxis"===e))for(var l=0,d=[""+e+":not(.highcharts-radial-axis)",""+e+"-labels:not(.highcharts-radial-axis-labels)"];l<d.length;l++){var c=d[l];aS(h,c)}else for(var p=0,u=[".highcharts-xaxis",".highcharts-yaxis"];p<u.length;p++)for(var f=u[p],g=0,v=[""+f+":not(.highcharts-radial-axis)",""+f+"-labels:not(.highcharts-radial-axis-labels)"];g<v.length;g++){var c=v[g];aM(h,c)}for(var m=0;m<h.length;m++){var c=h[m];[].forEach.call(o.querySelectorAll(c),function(t){(t.namespaceURI===a.SVG_NS?a.box:a.box.parentNode).appendChild(t),t.style.pointerEvents="auto"})}},t.fixedSelectors=[".highcharts-breadcrumbs-group",".highcharts-contextbutton",".highcharts-caption",".highcharts-credits",".highcharts-drillup-button",".highcharts-legend",".highcharts-legend-checkbox",".highcharts-navigator-series",".highcharts-navigator-xaxis",".highcharts-navigator-yaxis",".highcharts-navigator",".highcharts-range-selector-group",".highcharts-reset-zoom",".highcharts-scrollbar",".highcharts-subtitle",".highcharts-title"],t}(),aO=e_.format,aP=sQ.series,aE=tT.destroyObjectProperties,aL=tT.fireEvent,aD=tT.getAlignFactor,aI=tT.isNumber,aB=tT.pick,az=function(){function t(t,e,i,o,r){var s=t.chart.inverted,n=t.reversed;this.axis=t;var a=this.isNegative=!!i!=!!n;this.options=e=e||{},this.x=o,this.total=null,this.cumulative=null,this.points={},this.hasValidPoints=!1,this.stack=r,this.leftCliff=0,this.rightCliff=0,this.alignOptions={align:e.align||(s?a?"left":"right":"center"),verticalAlign:e.verticalAlign||(s?"middle":a?"bottom":"top"),y:e.y,x:e.x},this.textAlign=e.textAlign||(s?a?"right":"left":"center")}return t.prototype.destroy=function(){aE(this,this.axis)},t.prototype.render=function(t){var e=this.axis.chart,i=this.options,o=i.format,r=o?aO(o,this,e):i.formatter.call(this);if(this.label)this.label.attr({text:r,visibility:"hidden"});else{this.label=e.renderer.label(r,null,void 0,i.shape,void 0,void 0,i.useHTML,!1,"stack-labels");var s={r:i.borderRadius||0,text:r,padding:aB(i.padding,5),visibility:"hidden"};e.styledMode||(s.fill=i.backgroundColor,s.stroke=i.borderColor,s["stroke-width"]=i.borderWidth,this.label.css(i.style||{})),this.label.attr(s),this.label.added||this.label.add(t)}this.label.labelrank=e.plotSizeY,aL(this,"afterRender")},t.prototype.setOffset=function(t,e,i,o,r,s){var n=this.alignOptions,a=this.axis,h=this.label,l=this.options,d=this.textAlign,c=a.chart,p=this.getStackBox({xOffset:t,width:e,boxBottom:i,boxTop:o,defaultX:r,xAxis:s}),u=n.verticalAlign;if(h&&p){var f=h.getBBox(void 0,0),g=h.padding,v="justify"===aB(l.overflow,"justify"),m=void 0;n.x=l.x||0,n.y=l.y||0;var y=this.adjustStackPosition({labelBox:f,verticalAlign:u,textAlign:d}),x=y.x,b=y.y;p.x-=x,p.y-=b,h.align(n,!1,p),(m=c.isInsidePlot(h.alignAttr.x+n.x+x,h.alignAttr.y+n.y+b))||(v=!1),v&&aP.prototype.justifyDataLabel.call(a,h,n,h.alignAttr,f,p),h.attr({x:h.alignAttr.x,y:h.alignAttr.y,rotation:l.rotation,rotationOriginX:f.width*aD(l.textAlign||"center"),rotationOriginY:f.height/2}),aB(!v&&l.crop,!0)&&(m=aI(h.x)&&aI(h.y)&&c.isInsidePlot(h.x-g+(h.width||0),h.y)&&c.isInsidePlot(h.x+g,h.y)),h[m?"show":"hide"]()}aL(this,"afterSetOffset",{xOffset:t,width:e})},t.prototype.adjustStackPosition=function(t){var e=t.labelBox,i=t.verticalAlign,o=t.textAlign;return{x:e.width/2+e.width/2*(2*aD(o)-1),y:e.height/2*2*(1-aD(i))}},t.prototype.getStackBox=function(t){var e=this.axis,i=e.chart,o=t.boxTop,r=t.defaultX,s=t.xOffset,n=t.width,a=t.boxBottom,h=e.stacking.usePercentage?100:aB(o,this.total,0),l=e.toPixels(h),d=t.xAxis||i.xAxis[0],c=aB(r,d.translate(this.x))+s,p=Math.abs(l-e.toPixels(a||aI(e.min)&&e.logarithmic&&e.logarithmic.lin2log(e.min)||0)),u=i.inverted,f=this.isNegative;return u?{x:(f?l:l-p)-i.plotLeft,y:d.height-c-n+d.top-i.plotTop,width:p,height:n}:{x:c+d.transB-i.plotLeft,y:(f?l-p:l)-i.plotTop,width:n,height:p}},t}(),aR=sQ.series.prototype,aN=tT.addEvent,aW=tT.correctFloat,aX=tT.defined,aH=tT.destroyObjectProperties,aF=tT.fireEvent,aj=tT.isNumber,aY=tT.objectEach,aG=tT.pick;function a_(){var t=this.inverted;this.axes.forEach(function(t){var e;(null===(e=t.stacking)||void 0===e?void 0:e.stacks)&&t.hasVisibleSeries&&(t.stacking.oldStacks=t.stacking.stacks)}),this.series.forEach(function(e){var i,o=(null===(i=e.xAxis)||void 0===i?void 0:i.options)||{};e.options.stacking&&e.reserveSpace()&&(e.stackKey=[e.type,aG(e.options.stack,""),t?o.top:o.left,t?o.height:o.width].join(","))})}function aU(){var t,e=this.stacking;if(e){var i=e.stacks;aY(i,function(t,e){aH(t),delete i[e]}),null===(t=e.stackTotalGroup)||void 0===t||t.destroy()}}function aV(){this.stacking||(this.stacking=new aQ(this))}function aZ(t,e,i,o){return!aX(t)||t.x!==e||o&&t.stackKey!==o?t={x:e,index:0,key:o,stackKey:o}:t.index++,t.key=[i,e,t.index].join(","),t}function aq(){var t,e=this,i=e.yAxis,o=e.stackKey||"",r=i.stacking.stacks,s=e.getColumn("x",!0),n=e.options.stacking,a=e[n+"Stacker"];a&&[o,"-"+o].forEach(function(i){for(var o,n,h,l,d=s.length;d--;)n=s[d],t=e.getStackIndicator(t,n,e.index,i),(l=null==(h=null===(o=r[i])||void 0===o?void 0:o[n])?void 0:h.points[t.key||""])&&a.call(e,l,h,d)})}function aK(t,e,i){var o=e.total?100/e.total:0;t[0]=aW(t[0]*o),t[1]=aW(t[1]*o),this.stackedYData[i]=t[1]}function a$(t){(this.is("column")||this.is("columnrange"))&&(this.options.centerInCategory&&this.chart.series.length>1?aR.setStackedPoints.call(this,t,"group"):t.stacking.resetStacks())}function aJ(t,e){var i,o,r,s,n,a,h,l,d,c=e||this.options.stacking;if(c&&this.reserveSpace()&&(({group:"xAxis"})[c]||"yAxis")===t.coll){var p=this.getColumn("x",!0),u=this.getColumn(this.pointValKey||"y",!0),f=[],g=u.length,v=this.options,m=v.threshold||0,y=v.startFromThreshold?m:0,x=v.stack,b=e?""+this.type+",".concat(c):this.stackKey||"",k="-"+b,M=this.negStacks,w=t.stacking,S=w.stacks,A=w.oldStacks;for(w.stacksTouched+=1,d=0;d<g;d++){var T=p[d]||0,C=u[d],O=aj(C)&&C||0;l=(r=this.getStackIndicator(r,T,this.index)).key||"",S[h=(s=M&&O<(y?0:m))?k:b]||(S[h]={}),S[h][T]||((null===(i=A[h])||void 0===i?void 0:i[T])?(S[h][T]=A[h][T],S[h][T].total=null):S[h][T]=new az(t,t.options.stackLabels,!!s,T,x)),n=S[h][T],null!==C?(n.points[l]=n.points[this.index]=[aG(n.cumulative,y)],aX(n.cumulative)||(n.base=l),n.touched=w.stacksTouched,r.index>0&&!1===this.singleStacks&&(n.points[l][0]=n.points[this.index+","+T+",0"][0])):(delete n.points[l],delete n.points[this.index]);var P=n.total||0;"percent"===c?(a=s?b:k,P=M&&(null===(o=S[a])||void 0===o?void 0:o[T])?(a=S[a][T]).total=Math.max(a.total||0,P)+Math.abs(O):aW(P+Math.abs(O))):"group"===c?aj(C)&&P++:P=aW(P+O),"group"===c?n.cumulative=(P||1)-1:n.cumulative=aW(aG(n.cumulative,y)+O),n.total=P,null!==C&&(n.points[l].push(n.cumulative),f[d]=n.cumulative,n.hasValidPoints=!0)}"percent"===c&&(w.usePercentage=!0),"group"!==c&&(this.stackedYData=f),w.oldStacks={}}}var aQ=function(){function t(t){this.oldStacks={},this.stacks={},this.stacksTouched=0,this.axis=t}return t.prototype.buildStacks=function(){var t,e,i=this.axis,o=i.series,r="xAxis"===i.coll,s=i.options.reversedStacks,n=o.length;for(this.resetStacks(),this.usePercentage=!1,e=n;e--;)t=o[s?e:n-e-1],r&&t.setGroupedPoints(i),t.setStackedPoints(i);if(!r)for(e=0;e<n;e++)o[e].modifyStacks();aF(i,"afterBuildStacks")},t.prototype.cleanStacks=function(){this.oldStacks&&(this.stacks=this.oldStacks,aY(this.stacks,function(t){aY(t,function(t){t.cumulative=t.total})}))},t.prototype.resetStacks=function(){var t=this;aY(this.stacks,function(e){aY(e,function(i,o){aj(i.touched)&&i.touched<t.stacksTouched?(i.destroy(),delete e[o]):(i.total=null,i.cumulative=null)})})},t.prototype.renderStackTotals=function(){var t,e=this.axis,i=e.chart,o=i.renderer,r=this.stacks,s=ev(i,(null===(t=e.options.stackLabels)||void 0===t?void 0:t.animation)||!1),n=this.stackTotalGroup=this.stackTotalGroup||o.g("stack-labels").attr({zIndex:6,opacity:0}).add();n.translate(i.plotLeft,i.plotTop),aY(r,function(t){aY(t,function(t){t.render(n)})}),n.animate({opacity:1},s)},t}();(Y||(Y={})).compose=function(t,e,i){var o=e.prototype,r=i.prototype;o.getStacks||(aN(t,"init",aV),aN(t,"destroy",aU),o.getStacks=a_,r.getStackIndicator=aZ,r.modifyStacks=aq,r.percentStacker=aK,r.setGroupedPoints=a$,r.setStackedPoints=aJ)};var a0=Y,a1=(x=function(t,e){return(x=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}x(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),a2=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,s=e.length;r<s;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},a3=tT.defined,a5=tT.merge,a6=tT.isObject,a9=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a1(e,t),e.prototype.drawGraph=function(){var t=this,e=this.options,i=(this.gappedPath||this.getGraphPath).call(this),o=this.chart.styledMode;a2([this],this.zones,!0).forEach(function(r,s){var n,a=r.graph,h=a?"animate":"attr",l=r.dashStyle||e.dashStyle;a?(a.endX=t.preventGraphAnimation?null:i.xMap,a.animate({d:i})):i.length&&(r.graph=a=t.chart.renderer.path(i).addClass("highcharts-graph"+(s?" highcharts-zone-graph-".concat(s-1," "):" ")+(s&&r.className||"")).attr({zIndex:1}).add(t.group)),a&&!o&&(n={stroke:!s&&e.lineColor||r.color||t.color||"#cccccc","stroke-width":e.lineWidth||0,fill:t.fillGraph&&t.color||"none"},l?n.dashstyle=l:"square"!==e.linecap&&(n["stroke-linecap"]=n["stroke-linejoin"]="round"),a[h](n).shadow(e.shadow&&a5({filterUnits:"userSpaceOnUse"},a6(e.shadow)?e.shadow:{}))),a&&(a.startX=i.xMap,a.isArea=i.isArea)})},e.prototype.getGraphPath=function(t,e,i){var o,r=this,s=r.options,n=[],a=[],h=s.step,l=(t=t||r.points).reversed;return l&&t.reverse(),(h=({right:1,center:2})[h]||h&&3)&&l&&(h=4-h),(t=this.getValidPoints(t,!1,s.nullInteraction||!(s.connectNulls&&!e&&!i))).forEach(function(l,d){var c,p=l.plotX,u=l.plotY,f=t[d-1],g=l.isNull||"number"!=typeof u;(l.leftCliff||(null==f?void 0:f.rightCliff))&&!i&&(o=!0),g&&!a3(e)&&d>0?o=!s.connectNulls:g&&!e?o=!0:(0===d||o?c=[["M",l.plotX,l.plotY]]:r.getPointSpline?c=[r.getPointSpline(t,l,d)]:h?(c=1===h?[["L",f.plotX,u]]:2===h?[["L",(f.plotX+p)/2,f.plotY],["L",(f.plotX+p)/2,u]]:[["L",p,f.plotY]]).push(["L",p,u]):c=[["L",p,u]],a.push(l.x),h&&(a.push(l.x),2===h&&a.push(l.x)),n.push.apply(n,c),o=!1)}),n.xMap=a,r.graphPath=n,n},e.defaultOptions=a5(nk.defaultOptions,{legendSymbol:"lineMarker"}),e}(nk);sQ.registerSeriesType("line",a9);var a4={threshold:0,legendSymbol:"areaMarker"},a8=(b=function(t,e){return(b=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}b(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),a7=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,s=e.length;r<s;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},ht=sQ.seriesTypes.line,he=tT.extend,hi=tT.merge,ho=tT.objectEach,hr=tT.pick,hs=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a8(e,t),e.prototype.drawGraph=function(){var e=this;this.areaPath=[],t.prototype.drawGraph.apply(this);var i=this.areaPath,o=this.options;a7([this],this.zones,!0).forEach(function(t,r){var s,n={},a=t.fillColor||o.fillColor,h=t.area,l=h?"animate":"attr";h?(h.endX=e.preventGraphAnimation?null:i.xMap,h.animate({d:i})):(n.zIndex=0,(h=t.area=e.chart.renderer.path(i).addClass("highcharts-area"+(r?" highcharts-zone-area-".concat(r-1," "):" ")+(r&&t.className||"")).add(e.group)).isArea=!0),e.chart.styledMode||(n.fill=a||t.color||e.color,n["fill-opacity"]=a?1:null!==(s=o.fillOpacity)&&void 0!==s?s:.75,h.css({pointerEvents:e.stickyTracking?"none":"auto"})),h[l](n),h.startX=i.xMap,h.shiftUnit=o.step?2:1})},e.prototype.getGraphPath=function(t){var e,i,o,r=ht.prototype.getGraphPath,s=this.options,n=s.stacking,a=this.yAxis,h=[],l=[],d=this.index,c=a.stacking.stacks[this.stackKey],p=s.threshold,u=Math.round(a.getThreshold(s.threshold)),f=hr(s.connectNulls,"percent"===n),g=function(i,o,r){var s,f,g=t[i],v=n&&c[g.x].points[d],m=g[r+"Null"]||0,y=g[r+"Cliff"]||0,x=!0;y||m?(s=(m?v[0]:v[1])+y,f=v[0]+y,x=!!m):!n&&t[o]&&t[o].isNull&&(s=f=p),void 0!==s&&(l.push({plotX:e,plotY:null===s?u:a.getThreshold(s),isNull:x,isCliff:!0}),h.push({plotX:e,plotY:null===f?u:a.getThreshold(f),doCurve:!1}))};t=t||this.points,n&&(t=this.getStackPoints(t));for(var v=0,m=t.length;v<m;++v)n||(t[v].leftCliff=t[v].rightCliff=t[v].leftNull=t[v].rightNull=void 0),i=t[v].isNull,e=hr(t[v].rectPlotX,t[v].plotX),o=n?hr(t[v].yBottom,u):u,i&&!f||(f||g(v,v-1,"left"),i&&!n&&f||(l.push(t[v]),h.push({x:v,plotX:e,plotY:o})),f||g(v,v+1,"right"));var y=r.call(this,l,!0,!0);h.reversed=!0;var x=r.call(this,h,!0,!0),b=x[0];b&&"M"===b[0]&&(x[0]=["L",b[1],b[2]]);var k=y.concat(x);k.length&&k.push(["Z"]);var M=r.call(this,l,!1,f);return this.chart.series.length>1&&n&&l.some(function(t){return t.isCliff})&&(k.hasStackedCliffs=M.hasStackedCliffs=!0),k.xMap=y.xMap,this.areaPath=k,M},e.prototype.getStackPoints=function(t){var e=this,i=[],o=[],r=this.xAxis,s=this.yAxis,n=s.stacking.stacks[this.stackKey],a={},h=s.series,l=h.length,d=s.options.reversedStacks?1:-1,c=h.indexOf(e);if(t=t||this.points,this.options.stacking){for(var p=0;p<t.length;p++)t[p].leftNull=t[p].rightNull=void 0,a[t[p].x]=t[p];ho(n,function(t,e){null!==t.total&&o.push(e)}),o.sort(function(t,e){return t-e});var u=h.map(function(t){return t.visible});o.forEach(function(t,p){var f,g,v=0;if(a[t]&&!a[t].isNull)i.push(a[t]),[-1,1].forEach(function(i){var r=1===i?"rightNull":"leftNull",s=n[o[p+i]],v=0;if(s)for(var m=c;m>=0&&m<l;){var y=h[m].index;!(f=s.points[y])&&(y===e.index?a[t][r]=!0:u[m]&&(g=n[t].points[y])&&(v-=g[1]-g[0])),m+=d}a[t][1===i?"rightCliff":"leftCliff"]=v});else{for(var m=c;m>=0&&m<l;){var y=h[m].index;if(f=n[t].points[y]){v=f[1];break}m+=d}v=hr(v,0),v=s.translate(v,0,1,0,1),i.push({isNull:!0,plotX:r.translate(t,0,0,0,1),x:t,plotY:v,yBottom:v})}})}return i},e.defaultOptions=hi(ht.defaultOptions,a4),e}(ht);he(hs.prototype,{singleStacks:!1}),sQ.registerSeriesType("area",hs);var hn=(k=function(t,e){return(k=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}k(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),ha=sQ.seriesTypes.line,hh=tT.merge,hl=tT.pick,hd=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hn(e,t),e.prototype.getPointSpline=function(t,e,i){var o,r,s,n,a=e.plotX||0,h=e.plotY||0,l=t[i-1],d=t[i+1];function c(t){return t&&!t.isNull&&!1!==t.doCurve&&!e.isCliff}if(c(l)&&c(d)){var p=l.plotX||0,u=l.plotY||0,f=d.plotX||0,g=d.plotY||0,v=0;o=(1.5*a+p)/2.5,r=(1.5*h+u)/2.5,s=(1.5*a+f)/2.5,n=(1.5*h+g)/2.5,s!==o&&(v=(n-r)*(s-a)/(s-o)+h-n),r+=v,n+=v,r>u&&r>h?(r=Math.max(u,h),n=2*h-r):r<u&&r<h&&(r=Math.min(u,h),n=2*h-r),n>g&&n>h?(n=Math.max(g,h),r=2*h-n):n<g&&n<h&&(n=Math.min(g,h),r=2*h-n),e.rightContX=s,e.rightContY=n,e.controlPoints={low:[o,r],high:[s,n]}}var m=["C",hl(l.rightContX,l.plotX,0),hl(l.rightContY,l.plotY,0),hl(o,a,0),hl(r,h,0),a,h];return l.rightContX=l.rightContY=void 0,m},e.defaultOptions=hh(ha.defaultOptions),e}(ha);sQ.registerSeriesType("spline",hd);var hc=(M=function(t,e){return(M=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}M(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hp=sQ.seriesTypes,hu=hp.area,hf=hp.area.prototype,hg=tT.extend,hv=tT.merge,hm=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hc(e,t),e.defaultOptions=hv(hd.defaultOptions,hu.defaultOptions),e}(hd);hg(hm.prototype,{getGraphPath:hf.getGraphPath,getStackPoints:hf.getStackPoints,drawGraph:hf.drawGraph}),sQ.registerSeriesType("areaspline",hm);var hy={borderRadius:3,centerInCategory:!1,groupPadding:.2,marker:null,pointPadding:.1,minPointLength:0,cropThreshold:50,pointRange:null,states:{hover:{halo:!1,brightness:.1},select:{color:"#cccccc",borderColor:"#000000"}},dataLabels:{align:void 0,verticalAlign:void 0,y:void 0},startFromThreshold:!0,stickyTracking:!1,tooltip:{distance:6},threshold:0,borderColor:"#ffffff"},hx=(w=function(t,e){return(w=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}w(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hb=t7.parse,hk=tt.noop,hM=tT.clamp,hw=tT.crisp,hS=tT.defined,hA=tT.extend,hT=tT.fireEvent,hC=tT.isArray,hO=tT.isNumber,hP=tT.merge,hE=tT.pick,hL=tT.objectEach,hD=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hx(e,t),e.prototype.animate=function(t){var e,i,o=this,r=this.yAxis,s=r.pos,n=r.reversed,a=o.options,h=this.chart,l=h.clipOffset,d=h.inverted,c={},p=d?"translateX":"translateY";t&&l?(c.scaleY=.001,i=hM(r.toPixels(a.threshold||0),s,s+r.len),d?c.translateX=(i+=n?-Math.floor(l[0]):Math.ceil(l[2]))-r.len:c.translateY=i+=n?Math.ceil(l[0]):-Math.floor(l[2]),o.clipBox&&o.setClip(),o.group.attr(c)):(e=Number(o.group.attr(p)),o.group.animate({scaleY:1},hA(eu(o.options.animation),{step:function(t,i){o.group&&(c[p]=e+i.pos*(s-e),o.group.attr(c))}})))},e.prototype.init=function(e,i){t.prototype.init.apply(this,arguments);var o=this;(e=o.chart).hasRendered&&e.series.forEach(function(t){t.type===o.type&&(t.isDirty=!0)})},e.prototype.getColumnMetrics=function(){var t,e,i,o=this,r=o.options,s=o.xAxis,n=o.yAxis,a=s.options.reversedStacks,h=s.reversed&&!a||!s.reversed&&a,l={},d=0;!1===r.grouping?d=1:o.chart.series.forEach(function(t){var e,r=t.yAxis,s=t.options;t.type===o.type&&t.reserveSpace()&&n.len===r.len&&n.pos===r.pos&&(s.stacking&&"group"!==s.stacking?(void 0===l[i=t.stackKey]&&(l[i]=d++),e=l[i]):!1!==s.grouping&&(e=d++),t.columnIndex=e)});var c=Math.min(Math.abs(s.transA)*(!(null===(t=s.brokenAxis)||void 0===t?void 0:t.hasBreaks)&&(null===(e=s.ordinal)||void 0===e?void 0:e.slope)||r.pointRange||s.closestPointRange||s.tickInterval||1),s.len),p=c*r.groupPadding,u=(c-2*p)/(d||1),f=Math.min(r.maxPointWidth||s.len,hE(r.pointWidth,u*(1-2*r.pointPadding))),g=(o.columnIndex||0)+ +!!h;return o.columnMetrics={width:f,offset:(u-f)/2+(p+g*u-c/2)*(h?-1:1),paddedWidth:u,columnCount:d},o.columnMetrics},e.prototype.crispCol=function(t,e,i,o){var r=this.borderWidth,s=this.chart.inverted;return o=hw(e+o,r,s)-(e=hw(e,r,s)),this.options.crisp&&(i=hw(t+i,r)-(t=hw(t,r))),{x:t,y:e,width:i,height:o}},e.prototype.adjustForMissingColumns=function(t,e,i,o){var r,s=this;if(!i.isNull&&o.columnCount>1){var n=this.xAxis.series.filter(function(t){return t.visible}).map(function(t){return t.index}),a=0,h=0;hL(null===(r=this.xAxis.stacking)||void 0===r?void 0:r.stacks,function(t){var e,o="number"==typeof i.x?null===(e=t[i.x.toString()])||void 0===e?void 0:e.points:void 0,r=null==o?void 0:o[s.index],l={};if(o&&hC(r)){var d=s.index,c=Object.keys(o).filter(function(t){return!t.match(",")&&o[t]&&o[t].length>1}).map(parseFloat).filter(function(t){return -1!==n.indexOf(t)}).filter(function(t){var e=s.chart.series[t].options,i=e.stacking&&e.stack;if(hS(i)){if(hO(l[i]))return d===t&&(d=l[i]),!1;l[i]=t}return!0}).sort(function(t,e){return e-t});a=c.indexOf(d),h=c.length}}),a=this.xAxis.reversed?h-1-a:a;var l=(h-1)*o.paddedWidth+e;t=(i.plotX||0)+l/2-e-a*o.paddedWidth}return t},e.prototype.translate=function(){var t=this,e=t.chart,i=t.options,o=t.dense=t.closestPointRange*t.xAxis.transA<2,r=t.borderWidth=hE(i.borderWidth,+!o),s=t.xAxis,n=t.yAxis,a=i.threshold,h=hE(i.minPointLength,5),l=t.getColumnMetrics(),d=l.width,c=t.pointXOffset=l.offset,p=t.dataMin,u=t.dataMax,f=t.translatedThreshold=n.getThreshold(a),g=t.barW=Math.max(d,1+2*r);i.pointPadding&&i.crisp&&(g=Math.ceil(g)),nk.prototype.translate.apply(t),t.points.forEach(function(o){var r,v=hE(o.yBottom,f),m=999+Math.abs(v),y=o.plotX||0,x=hM(o.plotY,-m,n.len+m),b=Math.min(x,v),k=Math.max(x,v)-b,M=d,w=y+c,S=g;h&&Math.abs(k)<h&&(k=h,r=!n.reversed&&!o.negative||n.reversed&&o.negative,hO(a)&&hO(u)&&o.y===a&&u<=a&&(n.min||0)<a&&(p!==u||(n.max||0)<=a)&&(r=!r,o.negative=!o.negative),b=Math.abs(b-f)>h?v-h:f-(r?h:0)),hS(o.options.pointWidth)&&(w-=Math.round(((M=S=Math.ceil(o.options.pointWidth))-d)/2)),i.centerInCategory&&(w=t.adjustForMissingColumns(w,M,o,l)),o.barX=w,o.pointWidth=M,o.tooltipPos=e.inverted?[hM(n.len+n.pos-e.plotLeft-x,n.pos-e.plotLeft,n.len+n.pos-e.plotLeft),s.len+s.pos-e.plotTop-w-S/2,k]:[s.left-e.plotLeft+w+S/2,hM(x+n.pos-e.plotTop,n.pos-e.plotTop,n.len+n.pos-e.plotTop),k],o.shapeType=t.pointClass.prototype.shapeType||"roundedRect",o.shapeArgs=t.crispCol(w,b,S,o.isNull?0:k)}),hT(this,"afterColumnTranslate")},e.prototype.drawGraph=function(){this.group[this.dense?"addClass":"removeClass"]("highcharts-dense-data")},e.prototype.pointAttribs=function(t,e){var i,o,r,s,n,a=this.options,h=this.pointAttrToOptions||{},l=h.stroke||"borderColor",d=h["stroke-width"]||"borderWidth",c=t&&t.color||this.color,p=t&&t[l]||a[l]||c,u=t&&t.options.dashStyle||a.dashStyle,f=t&&t[d]||a[d]||this[d]||0,g=(null==t?void 0:t.isNull)&&a.nullInteraction?0:null!==(o=null!==(i=null==t?void 0:t.opacity)&&void 0!==i?i:a.opacity)&&void 0!==o?o:1;t&&this.zones.length&&(s=t.getZone(),c=t.options.color||s&&(s.color||t.nonZonedColor)||this.color,s&&(p=s.borderColor||p,u=s.dashStyle||u,f=s.borderWidth||f)),e&&t&&(n=(r=hP(a.states[e],t.options.states&&t.options.states[e]||{})).brightness,c=r.color||void 0!==n&&hb(c).brighten(r.brightness).get()||c,p=r[l]||p,f=r[d]||f,u=r.dashStyle||u,g=hE(r.opacity,g));var v={fill:c,stroke:p,"stroke-width":f,opacity:g};return u&&(v.dashstyle=u),v},e.prototype.drawPoints=function(t){void 0===t&&(t=this.points);var e,i=this,o=this.chart,r=i.options,s=r.nullInteraction,n=o.renderer,a=r.animationLimit||250;t.forEach(function(t){var h=t.plotY,l=t.graphic,d=!!l,c=l&&o.pointCount<a?"animate":"attr";hO(h)&&(null!==t.y||s)?(e=t.shapeArgs,l&&t.hasNewShapeType()&&(l=l.destroy()),i.enabledDataSorting&&(t.startXPos=i.xAxis.reversed?-(e&&e.width||0):i.xAxis.width),!l&&(t.graphic=l=n[t.shapeType](e).add(t.group||i.group),l&&i.enabledDataSorting&&o.hasRendered&&o.pointCount<a&&(l.attr({x:t.startXPos}),d=!0,c="animate")),l&&d&&l[c](hP(e)),o.styledMode||l[c](i.pointAttribs(t,t.selected&&"select")).shadow(!1!==t.allowShadow&&r.shadow),l&&(l.addClass(t.getClassName(),!0),l.attr({visibility:t.visible?"inherit":"hidden"}))):l&&(t.graphic=l.destroy())})},e.prototype.drawTracker=function(t){void 0===t&&(t=this.points);var e,i=this,o=i.chart,r=o.pointer,s=function(t){null==r||r.normalize(t);var e=null==r?void 0:r.getPointFromEvent(t);r&&e&&i.options.enableMouseTracking&&(o.isInsidePlot(t.chartX-o.plotLeft,t.chartY-o.plotTop,{visiblePlotOnly:!0})||(null==r?void 0:r.inClass(t.target,"highcharts-data-label")))&&(r.isDirectTouch=!0,e.onMouseOver(t))};t.forEach(function(t){e=hC(t.dataLabels)?t.dataLabels:t.dataLabel?[t.dataLabel]:[],t.graphic&&(t.graphic.element.point=t),e.forEach(function(e){(e.div||e.element).point=t})}),i._hasTracking||(i.trackerGroups.forEach(function(t){i[t]&&(i[t].addClass("highcharts-tracker").on("mouseover",s).on("mouseout",function(t){null==r||r.onTrackerMouseOut(t)}).on("touchstart",s),!o.styledMode&&i.options.cursor&&i[t].css({cursor:i.options.cursor}))}),i._hasTracking=!0),hT(this,"afterDrawTracker")},e.prototype.remove=function(){var t=this,e=t.chart;e.hasRendered&&e.series.forEach(function(e){e.type===t.type&&(e.isDirty=!0)}),nk.prototype.remove.apply(t,arguments)},e.defaultOptions=hP(nk.defaultOptions,hy),e}(nk);hA(hD.prototype,{directTouch:!0,getSymbol:hk,negStacks:!0,trackerGroups:["group","dataLabelsGroup"]}),sQ.registerSeriesType("column",hD);var hI=e_.format,hB=tT.defined,hz=tT.extend,hR=tT.fireEvent,hN=tT.getAlignFactor,hW=tT.isArray,hX=tT.isString,hH=tT.merge,hF=tT.objectEach,hj=tT.pick,hY=tT.pInt,hG=tT.splat;!function(t){function e(){return h(this).some(function(t){return null==t?void 0:t.enabled})}function i(t,e,i,o,r){var s,n=this.chart,a=this.enabledDataSorting,h=this.isCartesian&&n.inverted,l=t.plotX,d=t.plotY,c=i.rotation||0,p=hB(l)&&hB(d)&&n.isInsidePlot(l,Math.round(d),{inverted:h,paneCoordinates:!0,series:this}),u=0===c&&"justify"===hj(i.overflow,a?"none":"justify"),f=this.visible&&!1!==t.visible&&hB(l)&&(t.series.forceDL||a&&!u||p||hj(i.inside,!!this.options.stacking)&&o&&n.isInsidePlot(l,h?o.x+1:o.y+o.height-1,{inverted:h,paneCoordinates:!0,series:this})),g=t.pos();if(f&&g){var v,m=e.getBBox(),y=e.getBBox(void 0,0);if(o=hz({x:g[0],y:Math.round(g[1]),width:0,height:0},o||{}),"plotEdges"===i.alignTo&&this.isCartesian&&(o[h?"x":"y"]=0,o[h?"width":"height"]=(null===(s=this.yAxis)||void 0===s?void 0:s.len)||0),hz(i,{width:m.width,height:m.height}),v=o,a&&this.xAxis&&!u&&this.setDataLabelStartPos(t,e,r,p,v),e.align(hH(i,{width:y.width,height:y.height}),!1,o,!1),e.alignAttr.x+=hN(i.align)*(y.width-m.width),e.alignAttr.y+=hN(i.verticalAlign)*(y.height-m.height),e[e.placed?"animate":"attr"]({"text-align":e.alignAttr["text-align"]||"center",x:e.alignAttr.x+(m.width-y.width)/2,y:e.alignAttr.y+(m.height-y.height)/2,rotationOriginX:(e.width||0)/2,rotationOriginY:(e.height||0)/2}),u&&o.height>=0)this.justifyDataLabel(e,i,e.alignAttr,m,o,r);else if(hj(i.crop,!0)){var x=e.alignAttr,b=x.x,k=x.y;f=n.isInsidePlot(b,k,{paneCoordinates:!0,series:this})&&n.isInsidePlot(b+m.width-1,k+m.height-1,{paneCoordinates:!0,series:this})}i.shape&&!c&&e[r?"attr":"animate"]({anchorX:g[0],anchorY:g[1]})}r&&a&&(e.placed=!1),f||a&&!u?(e.show(),e.placed=!0):(e.hide(),e.placed=!1)}function o(){return this.plotGroup("dataLabelsGroup","data-labels",this.hasRendered?"inherit":"hidden",this.options.dataLabels.zIndex||6)}function r(t){var e=this.hasRendered||0,i=this.initDataLabelsGroup().attr({opacity:+e});return!e&&i&&(this.visible&&i.show(),this.options.animation?i.animate({opacity:1},t):i.attr({opacity:1})),i}function s(t){t=t||this.points;var e,i,o=this,r=o.chart,s=o.options,n=r.renderer,l=r.options.chart,d=l.backgroundColor,c=l.plotBackgroundColor,p=n.getContrast(hX(c)&&c||hX(d)&&d||"#000000"),u=h(o),f=u[0],g=f.animation,v=f.defer?ev(r,g,o):{defer:0,duration:0};hR(this,"drawDataLabels"),(null===(e=o.hasDataLabels)||void 0===e?void 0:e.call(o))&&(i=this.initDataLabels(v),t.forEach(function(t){var e,h,l,d=t.dataLabels||[],c=t.color||o.color;hG(a(u,t.dlOptions||(null===(e=t.options)||void 0===e?void 0:e.dataLabels))).forEach(function(e,a){var h,l,u,f,g,v=e.enabled&&(t.visible||t.dataLabelOnHidden)&&(!t.isNull||t.dataLabelOnNull)&&function(t,e){var i=e.filter;if(i){var o=i.operator,r=t[i.property],s=i.value;return">"===o&&r>s||"<"===o&&r<s||">="===o&&r>=s||"<="===o&&r<=s||"=="===o&&r==s||"==="===o&&r===s||"!="===o&&r!=s||"!=="===o&&r!==s||!1}return!0}(t,e),m=e.backgroundColor,y=e.borderColor,x=e.distance,b=e.style,k=void 0===b?{}:b,M={},w=d[a],S=!w;v&&(u=hB(l=hj(e[t.formatPrefix+"Format"],e.format))?hI(l,t,r):(e[t.formatPrefix+"Formatter"]||e.formatter).call(t,e),f=e.rotation,!r.styledMode&&(k.color=hj(e.color,k.color,hX(o.color)?o.color:void 0,"#000000"),"contrast"===k.color?("none"!==m&&(g=m),t.contrastColor=n.getContrast("auto"!==g&&hX(g)&&g||(hX(c)?c:"")),k.color=g||!hB(x)&&e.inside||0>hY(x||0)||s.stacking?t.contrastColor:p):delete t.contrastColor,s.cursor&&(k.cursor=s.cursor)),M={r:e.borderRadius||0,rotation:f,padding:e.padding,zIndex:1},r.styledMode||(M.fill="auto"===m?t.color:m,M.stroke="auto"===y?t.color:y,M["stroke-width"]=e.borderWidth),hF(M,function(t,e){void 0===t&&delete M[e]})),!w||v&&hB(u)&&!!(w.div||(null===(h=w.text)||void 0===h?void 0:h.foreignObject))==!!e.useHTML&&(w.rotation&&e.rotation||w.rotation===e.rotation)||(w=void 0,S=!0),v&&hB(u)&&(w?M.text=u:(w=n.label(u,0,0,e.shape,void 0,void 0,e.useHTML,void 0,"data-label")).addClass(" highcharts-data-label-color-"+t.colorIndex+" "+(e.className||"")+(e.useHTML?" highcharts-tracker":"")),w&&(w.options=e,w.attr(M),r.styledMode?k.width&&w.css({width:k.width,textOverflow:k.textOverflow,whiteSpace:k.whiteSpace}):w.css(k).shadow(e.shadow),hR(w,"beforeAddingDataLabel",{labelOptions:e,point:t}),w.added||w.add(i),o.alignDataLabel(t,w,e,void 0,S),w.isActive=!0,d[a]&&d[a]!==w&&d[a].destroy(),d[a]=w))});for(var f=d.length;f--;)(null===(h=d[f])||void 0===h?void 0:h.isActive)?d[f].isActive=!1:(null===(l=d[f])||void 0===l||l.destroy(),d.splice(f,1));t.dataLabel=d[0],t.dataLabels=d})),hR(this,"afterDrawDataLabels")}function n(t,e,i,o,r,s){var n,a,h=this.chart,l=e.align,d=e.verticalAlign,c=t.box?0:t.padding||0,p=h.inverted?this.yAxis:this.xAxis,u=p?p.left-h.plotLeft:0,f=h.inverted?this.xAxis:this.yAxis,g=f?f.top-h.plotTop:0,v=e.x,m=void 0===v?0:v,y=e.y,x=void 0===y?0:y;return(n=(i.x||0)+c+u)<0&&("right"===l&&m>=0?(e.align="left",e.inside=!0):m-=n,a=!0),(n=(i.x||0)+o.width-c+u)>h.plotWidth&&("left"===l&&m<=0?(e.align="right",e.inside=!0):m+=h.plotWidth-n,a=!0),(n=i.y+c+g)<0&&("bottom"===d&&x>=0?(e.verticalAlign="top",e.inside=!0):x-=n,a=!0),(n=(i.y||0)+o.height-c+g)>h.plotHeight&&("top"===d&&x<=0?(e.verticalAlign="bottom",e.inside=!0):x+=h.plotHeight-n,a=!0),a&&(e.x=m,e.y=x,t.placed=!s,t.align(e,void 0,r)),a}function a(t,e){var i,o=[];if(hW(t)&&!hW(e))o=t.map(function(t){return hH(t,e)});else if(hW(e)&&!hW(t))o=e.map(function(e){return hH(t,e)});else if(hW(t)||hW(e)){if(hW(t)&&hW(e))for(i=Math.max(t.length,e.length);i--;)o[i]=hH(t[i],e[i])}else o=hH(t,e);return o}function h(t){var e,i,o=t.chart.options.plotOptions;return hG(a(a(null===(e=null==o?void 0:o.series)||void 0===e?void 0:e.dataLabels,null===(i=null==o?void 0:o[t.type])||void 0===i?void 0:i.dataLabels),t.options.dataLabels))}function l(t,e,i,o,r){var s=this.chart,n=s.inverted,a=this.xAxis,h=a.reversed,l=((n?e.height:e.width)||0)/2,d=t.pointWidth,c=d?d/2:0;e.startXPos=n?r.x:h?-l-c:a.width-l+c,e.startYPos=n?h?this.yAxis.height-l+c:-l-c:r.y,o?"hidden"===e.visibility&&(e.show(),e.attr({opacity:0}).animate({opacity:1})):e.attr({opacity:1}).animate({opacity:0},void 0,e.hide),s.hasRendered&&(i&&e.attr({x:e.startXPos,y:e.startYPos}),e.placed=!0)}t.compose=function(t){var h=t.prototype;h.initDataLabels||(h.initDataLabels=r,h.initDataLabelsGroup=o,h.alignDataLabel=i,h.drawDataLabels=s,h.justifyDataLabel=n,h.mergeArrays=a,h.setDataLabelStartPos=l,h.hasDataLabels=e)}}(G||(G={}));var h_=G,hU=tt.composed,hV=sQ.series,hZ=tT.merge,hq=tT.pushUnique;!function(t){function e(t,e,i,o,r){var s,n,a,h,l,d,c,p=this.chart,u=this.options,f=p.inverted,g=(null===(s=this.xAxis)||void 0===s?void 0:s.len)||p.plotSizeX||0,v=(null===(n=this.yAxis)||void 0===n?void 0:n.len)||p.plotSizeY||0,m=t.dlBox||t.shapeArgs,y=null!==(a=t.below)&&void 0!==a?a:(t.plotY||0)>(null!==(h=this.translatedThreshold)&&void 0!==h?h:v),x=null!==(l=i.inside)&&void 0!==l?l:!!u.stacking;if(m){if(o=hZ(m),"allow"!==i.overflow||!1!==i.crop||!1!==u.clip){o.y<0&&(o.height+=o.y,o.y=0);var b=o.y+o.height-v;b>0&&b<o.height-1&&(o.height-=b)}f&&(o={x:v-o.y-o.height,y:g-o.x-o.width,width:o.height,height:o.width}),x||(f?(o.x+=y?0:o.width,o.width=0):(o.y+=y?o.height:0,o.height=0))}null!==(d=i.align)&&void 0!==d||(i.align=!f||x?"center":y?"right":"left"),null!==(c=i.verticalAlign)&&void 0!==c||(i.verticalAlign=f||x?"middle":y?"top":"bottom"),hV.prototype.alignDataLabel.call(this,t,e,i,o,r),i.inside&&t.contrastColor&&e.css({color:t.contrastColor})}t.compose=function(t){h_.compose(hV),hq(hU,"ColumnDataLabel")&&(t.prototype.alignDataLabel=e)}}(_||(_={}));var hK=_,h$=(S=function(t,e){return(S=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}S(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hJ=tT.extend,hQ=tT.merge,h0=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return h$(e,t),e.defaultOptions=hQ(hD.defaultOptions,{}),e}(hD);hJ(h0.prototype,{inverted:!0}),sQ.registerSeriesType("bar",h0);var h1={lineWidth:0,findNearestPointBy:"xy",jitter:{x:0,y:0},marker:{enabled:!0},tooltip:{headerFormat:'<span style="color:{point.color}">●</span> <span style="font-size: 0.8em"> {series.name}</span><br/>',pointFormat:"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>"}},h2=(A=function(t,e){return(A=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}A(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),h3=sQ.seriesTypes,h5=h3.column,h6=h3.line,h9=tT.addEvent,h4=tT.extend,h8=tT.merge,h7=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return h2(e,t),e.prototype.applyJitter=function(){var t=this,e=this.options.jitter,i=this.points.length;e&&this.points.forEach(function(o,r){["x","y"].forEach(function(s,n){if(e[s]&&!o.isNull){var a="plot".concat(s.toUpperCase()),h=t[""+s+"Axis"],l=e[s]*h.transA;if(h&&!h.logarithmic){var d,c=Math.max(0,(o[a]||0)-l),p=Math.min(h.len,(o[a]||0)+l);o[a]=c+(p-c)*((d=1e4*Math.sin(r+n*i))-Math.floor(d)),"x"===s&&(o.clientX=o.plotX)}}})})},e.prototype.drawGraph=function(){this.options.lineWidth?t.prototype.drawGraph.call(this):this.graph&&(this.graph=this.graph.destroy())},e.defaultOptions=h8(h6.defaultOptions,h1),e}(h6);h4(h7.prototype,{drawTracker:h5.prototype.drawTracker,sorted:!1,requireSorting:!1,noSharedTooltip:!0,trackerGroups:["group","markerGroup","dataLabelsGroup"]}),h9(h7,"afterTranslate",function(){this.applyJitter()}),sQ.registerSeriesType("scatter",h7);var lt=tt.deg2rad,le=tT.fireEvent,li=tT.isNumber,lo=tT.pick,lr=tT.relativeLength;(T=U||(U={})).getCenter=function(){var t,e,i,o=this.options,r=this.chart,s=2*(o.slicedOffset||0),n=r.plotWidth-2*s,a=r.plotHeight-2*s,h=o.center,l=Math.min(n,a),d=o.thickness,c=o.size,p=o.innerSize||0;"string"==typeof c&&(c=parseFloat(c)),"string"==typeof p&&(p=parseFloat(p));var u=[lo(null==h?void 0:h[0],"50%"),lo(null==h?void 0:h[1],"50%"),lo(c&&c<0?void 0:o.size,"100%"),lo(p&&p<0?void 0:o.innerSize||0,"0%")];for(!r.angular||this instanceof nk||(u[3]=0),e=0;e<4;++e)i=u[e],t=e<2||2===e&&/%$/.test(i),u[e]=lr(i,[n,a,l,u[2]][e])+(t?s:0);return u[3]>u[2]&&(u[3]=u[2]),li(d)&&2*d<u[2]&&d>0&&(u[3]=u[2]-2*d),le(this,"afterGetCenter",{positions:u}),u},T.getStartAndEndRadians=function(t,e){var i=li(t)?t:0,o=li(e)&&e>i&&e-i<360?e:i+360;return{start:lt*(i+-90),end:lt*(o+-90)}};var ls=U,ln=(C=function(t,e){return(C=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}C(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),la=function(){return(la=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},lh=tT.addEvent,ll=tT.defined,ld=tT.extend,lc=tT.isNumber,lp=tT.pick,lu=tT.relativeLength,lf=function(t){function e(e,i,o){var r,s=t.call(this,e,i,o)||this;s.half=0,null!==(r=s.name)&&void 0!==r||(s.name=e.chart.options.lang.pieSliceName);var n=function(t){s.slice("select"===t.type)};return lh(s,"select",n),lh(s,"unselect",n),s}return ln(e,t),e.prototype.getConnectorPath=function(t){var e=t.dataLabelPosition,i=t.options||{},o=i.connectorShape,r=this.connectorShapes[o]||o;return e&&r.call(this,la(la({},e.computed),{alignment:e.alignment}),e.connectorPosition,i)||[]},e.prototype.getTranslate=function(){return this.sliced&&this.slicedTranslation||{translateX:0,translateY:0}},e.prototype.haloPath=function(t){var e=this.shapeArgs;return this.sliced||!this.visible?[]:this.series.chart.renderer.symbols.arc(e.x,e.y,e.r+t,e.r+t,{innerR:e.r-1,start:e.start,end:e.end,borderRadius:e.borderRadius})},e.prototype.isValid=function(){return lc(this.y)&&this.y>=0},e.prototype.setVisible=function(t,e){void 0===e&&(e=!0),t!==this.visible&&this.update({visible:null!=t?t:!this.visible},e,void 0,!1)},e.prototype.slice=function(t,e,i){var o=this.series;em(i,o.chart),e=lp(e,!0),this.sliced=this.options.sliced=t=ll(t)?t:!this.sliced,o.options.data[o.data.indexOf(this)]=this.options,this.graphic&&this.graphic.animate(this.getTranslate())},e}(sf);ld(lf.prototype,{connectorShapes:{fixedOffset:function(t,e,i){var o=e.breakAt,r=e.touchingSliceAt,s=i.softConnector?["C",t.x+("left"===t.alignment?-5:5),t.y,2*o.x-r.x,2*o.y-r.y,o.x,o.y]:["L",o.x,o.y];return[["M",t.x,t.y],s,["L",r.x,r.y]]},straight:function(t,e){var i=e.touchingSliceAt;return[["M",t.x,t.y],["L",i.x,i.y]]},crookedLine:function(t,e,i){var o=e.angle,r=void 0===o?this.angle||0:o,s=e.breakAt,n=e.touchingSliceAt,a=this.series,h=a.center,l=h[0],d=h[1],c=h[2]/2,p=a.chart,u=p.plotLeft,f=p.plotWidth,g="left"===t.alignment,v=t.x,m=t.y,y=s.x;if(i.crookDistance){var x=lu(i.crookDistance,1);y=g?l+c+(f+u-l-c)*(1-x):u+(l-c)*x}else y=l+(d-m)*Math.tan(r-Math.PI/2);var b=[["M",v,m]];return(g?y<=v&&y>=s.x:y>=v&&y<=s.x)&&b.push(["L",y,m]),b.push(["L",s.x,s.y],["L",n.x,n.y]),b}}});var lg={borderRadius:3,center:[null,null],clip:!1,colorByPoint:!0,dataLabels:{connectorPadding:5,connectorShape:"crookedLine",crookDistance:void 0,distance:30,enabled:!0,formatter:function(){return this.isNull?void 0:this.name},softConnector:!0,x:0},fillColor:void 0,ignoreHiddenPoint:!0,inactiveOtherPoints:!0,legendType:"point",marker:null,size:null,showInLegend:!1,slicedOffset:10,stickyTracking:!1,tooltip:{followPointer:!0},borderColor:"#ffffff",borderWidth:1,lineWidth:void 0,states:{hover:{brightness:.1}}},lv=(O=function(t,e){return(O=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}O(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),lm=ls.getStartAndEndRadians,ly=tt.noop,lx=tT.clamp,lb=tT.extend,lk=tT.fireEvent,lM=tT.merge,lw=tT.pick,lS=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return lv(e,t),e.prototype.animate=function(t){var e=this,i=e.points,o=e.startAngleRad;t||i.forEach(function(t){var i=t.graphic,r=t.shapeArgs;i&&r&&(i.attr({r:lw(t.startR,e.center&&e.center[3]/2),start:o,end:o}),i.animate({r:r.r,start:r.start,end:r.end},e.options.animation))})},e.prototype.drawEmpty=function(){var t,e,i=this.startAngleRad,o=this.endAngleRad,r=this.options;0===this.total&&this.center?(t=this.center[0],e=this.center[1],this.graph||(this.graph=this.chart.renderer.arc(t,e,this.center[1]/2,0,i,o).addClass("highcharts-empty-series").add(this.group)),this.graph.attr({d:iz.arc(t,e,this.center[2]/2,0,{start:i,end:o,innerR:this.center[3]/2})}),this.chart.styledMode||this.graph.attr({"stroke-width":r.borderWidth,fill:r.fillColor||"none",stroke:r.color||"#cccccc"})):this.graph&&(this.graph=this.graph.destroy())},e.prototype.drawPoints=function(){var t=this.chart.renderer;this.points.forEach(function(e){e.graphic&&e.hasNewShapeType()&&(e.graphic=e.graphic.destroy()),e.graphic||(e.graphic=t[e.shapeType](e.shapeArgs).add(e.series.group),e.delayedRendering=!0)})},e.prototype.generatePoints=function(){t.prototype.generatePoints.call(this),this.updateTotals()},e.prototype.getX=function(t,e,i,o){var r=this.center,s=this.radii?this.radii[i.index]||0:r[2]/2,n=o.dataLabelPosition,a=(null==n?void 0:n.distance)||0,h=Math.asin(lx((t-r[1])/(s+a),-1,1));return r[0]+Math.cos(h)*(s+a)*(e?-1:1)+(a>0?(e?-1:1)*(o.padding||0):0)},e.prototype.hasData=function(){return!!this.dataTable.rowCount},e.prototype.redrawPoints=function(){var t,e,i,o,r=this,s=r.chart;this.drawEmpty(),r.group&&!s.styledMode&&r.group.shadow(r.options.shadow),r.points.forEach(function(n){var a={};e=n.graphic,!n.isNull&&e?(o=n.shapeArgs,t=n.getTranslate(),s.styledMode||(i=r.pointAttribs(n,n.selected&&"select")),n.delayedRendering?(e.setRadialReference(r.center).attr(o).attr(t),s.styledMode||e.attr(i).attr({"stroke-linejoin":"round"}),n.delayedRendering=!1):(e.setRadialReference(r.center),s.styledMode||lM(!0,a,i),lM(!0,a,o,t),e.animate(a)),e.attr({visibility:n.visible?"inherit":"hidden"}),e.addClass(n.getClassName(),!0)):e&&(n.graphic=e.destroy())})},e.prototype.sortByAngle=function(t,e){t.sort(function(t,i){return void 0!==t.angle&&(i.angle-t.angle)*e})},e.prototype.translate=function(t){lk(this,"translate"),this.generatePoints();var e,i,o,r,s,n,a,h=this.options,l=h.slicedOffset,d=lm(h.startAngle,h.endAngle),c=this.startAngleRad=d.start,p=(this.endAngleRad=d.end)-c,u=this.points,f=h.ignoreHiddenPoint,g=u.length,v=0;for(t||(this.center=t=this.getCenter()),n=0;n<g;n++){a=u[n],e=c+v*p,a.isValid()&&(!f||a.visible)&&(v+=a.percentage/100),i=c+v*p;var m={x:t[0],y:t[1],r:t[2]/2,innerR:t[3]/2,start:Math.round(1e3*e)/1e3,end:Math.round(1e3*i)/1e3};a.shapeType="arc",a.shapeArgs=m,(o=(i+e)/2)>1.5*Math.PI?o-=2*Math.PI:o<-Math.PI/2&&(o+=2*Math.PI),a.slicedTranslation={translateX:Math.round(Math.cos(o)*l),translateY:Math.round(Math.sin(o)*l)},r=Math.cos(o)*t[2]/2,s=Math.sin(o)*t[2]/2,a.tooltipPos=[t[0]+.7*r,t[1]+.7*s],a.half=+(o<-Math.PI/2||o>Math.PI/2),a.angle=o}lk(this,"afterTranslate")},e.prototype.updateTotals=function(){var t,e,i=this.points,o=i.length,r=this.options.ignoreHiddenPoint,s=0;for(t=0;t<o;t++)(e=i[t]).isValid()&&(!r||e.visible)&&(s+=e.y);for(t=0,this.total=s;t<o;t++)(e=i[t]).percentage=s>0&&(e.visible||!r)?e.y/s*100:0,e.total=s},e.defaultOptions=lM(nk.defaultOptions,lg),e}(nk);lb(lS.prototype,{axisTypes:[],directTouch:!0,drawGraph:void 0,drawTracker:hD.prototype.drawTracker,getCenter:ls.getCenter,getSymbol:ly,invertible:!1,isCartesian:!1,noSharedTooltip:!0,pointAttribs:hD.prototype.pointAttribs,pointClass:lf,requireSorting:!1,searchPoint:ly,trackerGroups:["group","dataLabelsGroup"]}),sQ.registerSeriesType("pie",lS);var lA=tt.composed,lT=tt.noop,lC=e$.distribute,lO=sQ.series,lP=tT.arrayMax,lE=tT.clamp,lL=tT.defined,lD=tT.pick,lI=tT.pushUnique,lB=tT.relativeLength;!function(t){var e={radialDistributionY:function(t,e){var i;return((null===(i=e.dataLabelPosition)||void 0===i?void 0:i.top)||0)+t.distributeBox.pos},radialDistributionX:function(t,e,i,o,r){var s=r.dataLabelPosition;return t.getX(i<((null==s?void 0:s.top)||0)+2||i>((null==s?void 0:s.bottom)||0)-2?o:i,e.half,e,r)},justify:function(t,e,i,o){var r;return o[0]+(t.half?-1:1)*(i+((null===(r=e.dataLabelPosition)||void 0===r?void 0:r.distance)||0))},alignToPlotEdges:function(t,e,i,o){var r=t.getBBox().width;return e?r+o:i-r-o},alignToConnectors:function(t,e,i,o){var r,s=0;return t.forEach(function(t){(r=t.dataLabel.getBBox().width)>s&&(s=r)}),e?s+o:i-s-o}};function i(t,e){var i=Math.PI/2,o=t.shapeArgs||{},r=o.start,s=void 0===r?0:r,n=o.end,a=void 0===n?0:n,h=t.angle||0;e>0&&s<i&&a>i&&h>i/2&&h<1.5*i&&(h=h<=i?Math.max(i/2,(s+i)/2):Math.min(1.5*i,(i+a)/2));var l=this.center,d=this.options,c=l[2]/2,p=Math.cos(h),u=Math.sin(h),f=l[0]+p*c,g=l[1]+u*c,v=Math.min((d.slicedOffset||0)+(d.borderWidth||0),e/5);return{natural:{x:f+p*e,y:g+u*e},computed:{},alignment:e<0?"center":t.half?"right":"left",connectorPosition:{angle:h,breakAt:{x:f+p*v,y:g+u*v},touchingSliceAt:{x:f,y:g}},distance:e}}function o(){var t,e,i,o,r=this,s=this,n=s.points,a=s.chart,h=a.plotWidth,l=a.plotHeight,d=a.plotLeft,c=Math.round(a.chartWidth/3),p=s.center,u=p[2]/2,f=p[1],g=[[],[]],v=[0,0,0,0],m=s.dataLabelPositioners,y=0;s.visible&&(null===(t=s.hasDataLabels)||void 0===t?void 0:t.call(s))&&(n.forEach(function(t){(t.dataLabels||[]).forEach(function(t){t.shortened&&(t.attr({width:"auto"}).css({width:"auto",textOverflow:"clip"}),t.shortened=!1)})}),lO.prototype.drawDataLabels.apply(s),n.forEach(function(t){(t.dataLabels||[]).forEach(function(e,i){var o,s=p[2]/2,n=e.options,a=lB((null==n?void 0:n.distance)||0,s);0===i&&g[t.half].push(t),!lL(null===(o=null==n?void 0:n.style)||void 0===o?void 0:o.width)&&e.getBBox().width>c&&(e.css({width:Math.round(.7*c)+"px"}),e.shortened=!0),e.dataLabelPosition=r.getDataLabelPosition(t,a),y=Math.max(y,a)})}),g.forEach(function(t,e){var r,n,c,g=t.length,x=[],b=0;g&&(s.sortByAngle(t,e-.5),y>0&&(r=Math.max(0,f-u-y),n=Math.min(f+u+y,a.plotHeight),t.forEach(function(t){(t.dataLabels||[]).forEach(function(e){var i,o=e.dataLabelPosition;o&&o.distance>0&&(o.top=Math.max(0,f-u-o.distance),o.bottom=Math.min(f+u+o.distance,a.plotHeight),b=e.getBBox().height||21,e.lineHeight=a.renderer.fontMetrics(e.text||e).h+2*e.padding,t.distributeBox={target:((null===(i=e.dataLabelPosition)||void 0===i?void 0:i.natural.y)||0)-o.top+e.lineHeight/2,size:b,rank:t.y},x.push(t.distributeBox))})}),lC(x,c=n+b-r,c/5)),t.forEach(function(r){(r.dataLabels||[]).forEach(function(n){var a=n.options||{},c=r.distributeBox,f=n.dataLabelPosition,g=(null==f?void 0:f.natural.y)||0,y=a.connectorPadding||0,b=n.lineHeight||21,k=(b-n.getBBox().height)/2,M=0,w=g,S="inherit";if(f){if(x&&lL(c)&&f.distance>0&&(void 0===c.pos?S="hidden":(o=c.size,w=m.radialDistributionY(r,n))),a.justify)M=m.justify(r,n,u,p);else switch(a.alignTo){case"connectors":M=m.alignToConnectors(t,e,h,d);break;case"plotEdges":M=m.alignToPlotEdges(n,e,h,d);break;default:M=m.radialDistributionX(s,r,w-k,g,n)}if(f.attribs={visibility:S,align:f.alignment},f.posAttribs={x:M+(a.x||0)+(({left:y,right:-y})[f.alignment]||0),y:w+(a.y||0)-b/2},f.computed.x=M,f.computed.y=w-k,lD(a.crop,!0)){i=n.getBBox().width;var A=void 0;M-i<y&&1===e?(A=Math.round(i-M+y),v[3]=Math.max(A,v[3])):M+i>h-y&&0===e&&(A=Math.round(M+i-h+y),v[1]=Math.max(A,v[1])),w-o/2<0?v[0]=Math.max(Math.round(-w+o/2),v[0]):w+o/2>l&&(v[2]=Math.max(Math.round(w+o/2-l),v[2])),f.sideOverflow=A}}})}))}),(0===lP(v)||this.verifyDataLabelOverflow(v))&&(this.placeDataLabels(),this.points.forEach(function(t){(t.dataLabels||[]).forEach(function(i){var o,r=i.options||{},n=r.connectorColor,h=r.connectorWidth,l=void 0===h?1:h,d=i.dataLabelPosition;if(l){var c=void 0;e=i.connector,d&&d.distance>0?(c=!e,e||(i.connector=e=a.renderer.path().addClass("highcharts-data-label-connector  highcharts-color-"+t.colorIndex+(t.className?" "+t.className:"")).add(s.dataLabelsGroup)),a.styledMode||e.attr({"stroke-width":l,stroke:n||t.color||"#666666"}),e[c?"attr":"animate"]({d:t.getConnectorPath(i)}),e.attr({visibility:null===(o=d.attribs)||void 0===o?void 0:o.visibility})):e&&(i.connector=e.destroy())}})})))}function r(){this.points.forEach(function(t){(t.dataLabels||[]).forEach(function(t){var e,i,o=t.dataLabelPosition;o?(o.sideOverflow&&(t.css({width:Math.max(t.getBBox().width-o.sideOverflow,0)+"px",textOverflow:(null===(i=null===(e=t.options)||void 0===e?void 0:e.style)||void 0===i?void 0:i.textOverflow)||"ellipsis"}),t.shortened=!0),t.attr(o.attribs),t[t.moved?"animate":"attr"](o.posAttribs),t.moved=!0):t&&t.attr({y:-9999})}),delete t.distributeBox},this)}function s(t){var e=this.center,i=this.options,o=i.center,r=i.minSize||80,s=r,n=null!==i.size;return!n&&(null!==o[0]?s=Math.max(e[2]-Math.max(t[1],t[3]),r):(s=Math.max(e[2]-t[1]-t[3],r),e[0]+=(t[3]-t[1])/2),null!==o[1]?s=lE(s,r,e[2]-Math.max(t[0],t[2])):(s=lE(s,r,e[2]-t[0]-t[2]),e[1]+=(t[0]-t[2])/2),s<e[2]?(e[2]=s,e[3]=Math.min(i.thickness?Math.max(0,s-2*i.thickness):Math.max(0,lB(i.innerSize||0,s)),s),this.translate(e),this.drawDataLabels&&this.drawDataLabels()):n=!0),n}t.compose=function(t){if(h_.compose(lO),lI(lA,"PieDataLabel")){var n=t.prototype;n.dataLabelPositioners=e,n.alignDataLabel=lT,n.drawDataLabels=o,n.getDataLabelPosition=i,n.placeDataLabels=r,n.verifyDataLabelOverflow=s}}}(V||(V={}));var lz=V;(P=Z||(Z={})).getCenterOfPoints=function(t){var e=t.reduce(function(t,e){return t.x+=e.x,t.y+=e.y,t},{x:0,y:0});return{x:e.x/t.length,y:e.y/t.length}},P.getDistanceBetweenPoints=function(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))},P.getAngleBetweenPoints=function(t,e){return Math.atan2(e.x-t.x,e.y-t.y)},P.pointInPolygon=function(t,e){var i,o,r=t.x,s=t.y,n=e.length,a=!1;for(i=0,o=n-1;i<n;o=i++){var h=e[i],l=h[0],d=h[1],c=e[o],p=c[0],u=c[1];d>s!=u>s&&r<(p-l)*(s-d)/(u-d)+l&&(a=!a)}return a};var lR=Z.pointInPolygon,lN=tT.addEvent,lW=tT.getAlignFactor,lX=tT.fireEvent,lH=tT.objectEach,lF=tT.pick;function lj(t){for(var e,i,o,r,s,n=t.length,a=!1,h=0;h<n;h++)(e=t[h])&&(e.oldOpacity=e.opacity,e.newOpacity=1,e.absoluteBox=function(t){var e,i;if(t&&(!t.alignAttr||t.placed)){var o=t.box?0:t.padding||0,r=t.alignAttr||{x:t.attr("x"),y:t.attr("y")},s=t.getBBox(),n=s.height,a=s.polygon,h=s.width,l=lW(t.alignValue)*h;return t.width=h,t.height=n,{x:r.x+((null===(e=t.parentGroup)||void 0===e?void 0:e.translateX)||0)+o-l,y:r.y+((null===(i=t.parentGroup)||void 0===i?void 0:i.translateY)||0)+o,width:h-2*o,height:n-2*o,polygon:a}}}(e));t.sort(function(t,e){return(e.labelrank||0)-(t.labelrank||0)});for(var h=0;h<n;++h)for(var l=null==(r=(i=t[h])&&i.absoluteBox)?void 0:r.polygon,d=h+1;d<n;++d){s=(o=t[d])&&o.absoluteBox;var c=!1;if(r&&s&&i!==o&&0!==i.newOpacity&&0!==o.newOpacity&&"hidden"!==i.visibility&&"hidden"!==o.visibility){var p=s.polygon;if(l&&p&&l!==p?function(t,e){for(var i=0;i<t.length;i++){var o=t[i];if(lR({x:o[0],y:o[1]},e))return!0}return!1}(l,p)&&(c=!0):!(s.x>=r.x+r.width||s.x+s.width<=r.x||s.y>=r.y+r.height||s.y+s.height<=r.y)&&(c=!0),c){var u=i.labelrank<o.labelrank?i:o,f=u.text;u.newOpacity=0,(null==f?void 0:f.element.querySelector("textPath"))&&f.hide()}}}for(var g=0;g<t.length;g++)lY(t[g],this)&&(a=!0);a&&lX(this,"afterHideAllOverlappingLabels")}function lY(t,e){var i,o=!1;return t&&(i=t.newOpacity,t.oldOpacity!==i&&(t.hasClass("highcharts-data-label")?(t[i?"removeClass":"addClass"]("highcharts-data-label-hidden"),o=!0,t[t.isOld?"animate":"attr"]({opacity:i},void 0,function(){e.styledMode||t.css({pointerEvents:i?"auto":"none"})}),lX(e,"afterHideOverlappingLabel")):t.attr({opacity:i})),t.isOld=!0),o}function lG(){for(var t,e=this,i=[],o=0,r=e.labelCollectors||[];o<r.length;o++){var s=r[o];i=i.concat(s())}for(var n=0,a=e.yAxis||[];n<a.length;n++){var h=a[n];h.stacking&&h.options.stackLabels&&!h.options.stackLabels.allowOverlap&&lH(h.stacking.stacks,function(t){lH(t,function(t){t.label&&i.push(t.label)})})}for(var l=0,d=e.series||[];l<d.length;l++){var c=d[l];if(c.visible&&(null===(t=c.hasDataLabels)||void 0===t?void 0:t.call(c))){var p=function(t){for(var o=function(t){t.visible&&(t.dataLabels||[]).forEach(function(o){var r,s,n=o.options||{};o.labelrank=lF(n.labelrank,t.labelrank,null===(r=t.shapeArgs)||void 0===r?void 0:r.height),(null!==(s=n.allowOverlap)&&void 0!==s?s:Number(n.distance)>0)?(o.oldOpacity=o.opacity,o.newOpacity=1,lY(o,e)):i.push(o)})},r=0;r<t.length;r++)o(t[r])};p(c.nodes||[]),p(c.points)}}this.hideOverlappingLabels(i)}var l_={compose:function(t){var e=t.prototype;e.hideOverlappingLabels||(e.hideOverlappingLabels=lj,lN(t,"render",lG))}},lU=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,s=e.length;r<s;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},lV=tt.noop,lZ=tT.addEvent,lq=tT.extend,lK=tT.isObject,l$=tT.merge,lJ=tT.relativeLength,lQ={radius:0,scope:"stack",where:void 0},l0=lV,l1=lV;function l2(t,e,i,o,r){void 0===r&&(r={});var s=l0(t,e,i,o,r),n=r.innerR,a=void 0===n?0:n,h=r.r,l=void 0===h?i:h,d=r.start,c=r.end;if(r.open||!r.borderRadius)return s;for(var p=(void 0===c?0:c)-(void 0===d?0:d),u=Math.sin(p/2),f=Math.max(Math.min(lJ(r.borderRadius||0,l-a),(l-a)/2,l*u/(1+u)),0),g=Math.min(f,p/Math.PI*2*a),v=s.length-1;v--;)!function(t,e,i){var o,r,s,n=t[e],a=t[e+1];if("Z"===a[0]&&(a=t[0]),("M"===n[0]||"L"===n[0])&&"A"===a[0]?(o=n,r=a,s=!0):"A"===n[0]&&("M"===a[0]||"L"===a[0])&&(o=a,r=n),o&&r&&r.params){var h=r[1],l=r[5],d=r.params,c=d.start,p=d.end,u=d.cx,f=d.cy,g=l?h-i:h+i,v=g?Math.asin(i/g):0,m=l?v:-v,y=Math.cos(v)*g;s?(d.start=c+m,o[1]=u+y*Math.cos(c),o[2]=f+y*Math.sin(c),t.splice(e+1,0,["A",i,i,0,0,1,u+h*Math.cos(d.start),f+h*Math.sin(d.start)])):(d.end=p-m,r[6]=u+h*Math.cos(d.end),r[7]=f+h*Math.sin(d.end),t.splice(e+1,0,["A",i,i,0,0,1,u+y*Math.cos(p),f+y*Math.sin(p)])),r[4]=Math.abs(d.end-d.start)<Math.PI?0:1}}(s,v,v>1?g:f);return s}function l3(){var t,e;if(this.options.borderRadius&&!(this.chart.is3d&&this.chart.is3d()))for(var i=this.options,o=this.yAxis,r="percent"===i.stacking,s=null===(e=null===(t=tJ.plotOptions)||void 0===t?void 0:t[this.type])||void 0===e?void 0:e.borderRadius,n=l5(i.borderRadius,lK(s)?s:{}),a=o.options.reversed,h=0,l=this.points;h<l.length;h++){var d=l[h],c=d.shapeArgs;if("roundedRect"===d.shapeType&&c){var p=c.width,u=void 0===p?0:p,f=c.height,g=void 0===f?0:f,v=c.y,m=void 0===v?0:v,y=g;if("stack"===n.scope&&d.stackTotal){var x=o.translate(r?100:d.stackTotal,!1,!0,!1,!0),b=o.translate(i.threshold||0,!1,!0,!1,!0),k=this.crispCol(0,Math.min(x,b),0,Math.abs(x-b));m=k.y,y=k.height}var M=(d.negative?-1:1)*(a?-1:1)==-1,w=n.where;!w&&this.is("waterfall")&&Math.abs((d.yBottom||0)-(this.translatedThreshold||0))>this.borderWidth&&(w="all"),w||(w="end");var S=Math.min(lJ(n.radius,u),u/2,"all"===w?g/2:1/0)||0;"end"===w&&(M&&(m-=S),y+=S),lq(c,{brBoxHeight:y,brBoxY:m,r:S})}}}function l5(t,e){return lK(t)||(t={radius:t||0}),l$(lQ,e,t)}function l6(){for(var t=l5(this.options.borderRadius),e=0,i=this.points;e<i.length;e++){var o=i[e].shapeArgs;o&&(o.borderRadius=lJ(t.radius,(o.r||0)-(o.innerR||0)))}}function l9(t,e,i,o,r){void 0===r&&(r={});var s=l1(t,e,i,o,r),n=r.r,a=void 0===n?0:n,h=r.brBoxHeight,l=void 0===h?o:h,d=r.brBoxY,c=void 0===d?e:d,p=e-c,u=c+l-(e+o),f=p-a>-.1?0:a,g=u-a>-.1?0:a,v=Math.max(f&&p,0),m=Math.max(g&&u,0),y=[t+f,e],x=[t+i-f,e],b=[t+i,e+f],k=[t+i,e+o-g],M=[t+i-g,e+o],w=[t+g,e+o],S=[t,e+o-g],A=[t,e+f],T=function(t,e){return Math.sqrt(Math.pow(t,2)-Math.pow(e,2))};if(v){var C=T(f,f-v);y[0]-=C,x[0]+=C,b[1]=A[1]=e+f-v}if(o<f-v){var C=T(f,f-v-o);b[0]=k[0]=t+i-f+C,M[0]=Math.min(b[0],M[0]),w[0]=Math.max(k[0],w[0]),S[0]=A[0]=t+f-C,b[1]=A[1]=e+o}if(m){var C=T(g,g-m);M[0]+=C,w[0]-=C,k[1]=S[1]=e+o-g+m}if(o<g-m){var C=T(g,g-m-o);b[0]=k[0]=t+i-g+C,x[0]=Math.min(b[0],x[0]),y[0]=Math.max(k[0],y[0]),S[0]=A[0]=t+g-C,k[1]=S[1]=e}return s.length=0,s.push(lU(["M"],y,!0),lU(["L"],x,!0),lU(["A",f,f,0,0,1],b,!0),lU(["L"],k,!0),lU(["A",g,g,0,0,1],M,!0),lU(["L"],w,!0),lU(["A",g,g,0,0,1],S,!0),lU(["L"],A,!0),lU(["A",f,f,0,0,1],y,!0),["Z"]),s}var l4=tT.diffObjects,l8=tT.extend,l7=tT.find,dt=tT.merge,de=tT.pick,di=tT.uniqueKey;!function(t){function e(t,e){var i=t.condition;(i.callback||function(){return this.chartWidth<=de(i.maxWidth,Number.MAX_VALUE)&&this.chartHeight<=de(i.maxHeight,Number.MAX_VALUE)&&this.chartWidth>=de(i.minWidth,0)&&this.chartHeight>=de(i.minHeight,0)}).call(this)&&e.push(t._id)}function i(t,e){var i,o=this,r=this.options.responsive,s=this.currentResponsive,n=[];!e&&r&&r.rules&&r.rules.forEach(function(t){void 0===t._id&&(t._id=di()),o.matchResponsiveRule(t,n)},this);var a=dt.apply(void 0,n.map(function(t){return l7((null==r?void 0:r.rules)||[],function(e){return e._id===t})}).map(function(t){return null==t?void 0:t.chartOptions}));a.isResponsiveOptions=!0,n=n.toString()||void 0;var h=null==s?void 0:s.ruleIds;n===h||(s&&(this.currentResponsive=void 0,this.updatingResponsive=!0,this.update(s.undoOptions,t,!0),this.updatingResponsive=!1),n?((i=l4(a,this.options,!0,this.collectionsWithUpdate)).isResponsiveOptions=!0,this.currentResponsive={ruleIds:n,mergedOptions:a,undoOptions:i},this.updatingResponsive||this.update(a,t,!0)):this.currentResponsive=void 0)}t.compose=function(t){var o=t.prototype;return o.matchResponsiveRule||l8(o,{matchResponsiveRule:e,setResponsive:i}),t}}(q||(q={}));var dr=q;tt.AST=eL,tt.Axis=rp,tt.Chart=av,tt.Color=t7,tt.DataLabel=h_,tt.DataTableCore=sj,tt.Fx=er,tt.HTMLElement=oC,tt.Legend=nj,tt.LegendSymbol=sV,tt.OverlappingDataLabels=tt.OverlappingDataLabels||l_,tt.PlotLineOrBand=rN,tt.Point=sf,tt.Pointer=sB,tt.RendererRegistry=eU,tt.Series=nk,tt.SeriesRegistry=sQ,tt.StackItem=az,tt.SVGElement=ix,tt.SVGRenderer=oc,tt.Templating=e_,tt.Tick=oV,tt.Time=tV,tt.Tooltip=r4,tt.animate=eg,tt.animObject=eu,tt.chart=av.chart,tt.color=t7.parse,tt.dateFormat=e_.dateFormat,tt.defaultOptions=tJ,tt.distribute=e$.distribute,tt.format=e_.format,tt.getDeferredAnimation=ev,tt.getOptions=function(){return tJ},tt.numberFormat=e_.numberFormat,tt.seriesType=sQ.seriesType,tt.setAnimation=em,tt.setOptions=t0,tt.stop=ef,tt.time=tQ,tt.timers=er.timers,({compose:function(t,e,i){var o=t.types.pie;if(!e.symbolCustomAttribs.includes("borderRadius")){var r=i.prototype.symbols;lZ(t,"afterColumnTranslate",l3,{order:9}),lZ(o,"afterTranslate",l6),e.symbolCustomAttribs.push("borderRadius","brBoxHeight","brBoxY"),l0=r.arc,l1=r.roundedRect,r.arc=l2,r.roundedRect=l9}},optionsToObject:l5}).compose(tt.Series,tt.SVGElement,tt.SVGRenderer),hK.compose(tt.Series.types.column),h_.compose(tt.Series),rm.compose(tt.Axis),oC.compose(tt.SVGRenderer),nj.compose(tt.Chart),rk.compose(tt.Axis),l_.compose(tt.Chart),lz.compose(tt.Series.types.pie),rN.compose(tt.Chart,tt.Axis),sB.compose(tt.Chart),dr.compose(tt.Chart),aC.compose(tt.Axis,tt.Chart,tt.Series),a0.compose(tt.Axis,tt.Chart,tt.Series),r4.compose(tt.Pointer),tT.extend(tt,tT);var ds=tt.isTouchDevice,dn=tT.addEvent,da=tT.merge,dh=tT.pick,dl=[];function dd(){this.navigator&&this.navigator.setBaseSeries(null,!1)}function dc(){var t,e,i,o,r=this.legend,s=this.navigator;if(s){e=r&&r.options,i=s.xAxis,o=s.yAxis;var n=s.scrollbarHeight,a=s.scrollButtonSize;this.inverted?(s.left=s.opposite?this.chartWidth-n-s.height:this.spacing[3]+n,s.top=this.plotTop+a):(s.left=dh(i.left,this.plotLeft+a),s.top=s.navigatorOptions.top||this.chartHeight-s.height-n-((null===(t=this.scrollbar)||void 0===t?void 0:t.options.margin)||0)-this.spacing[2]-(this.rangeSelector&&this.extraBottomMargin?this.rangeSelector.getHeight():0)-(e&&"bottom"===e.verticalAlign&&"proximate"!==e.layout&&e.enabled&&!e.floating?r.legendHeight+dh(e.margin,10):0)-(this.titleOffset?this.titleOffset[2]:0)),i&&o&&(this.inverted?i.options.left=o.options.left=s.left:i.options.top=o.options.top=s.top,i.setAxisSize(),o.setAxisSize())}}function dp(t){!this.navigator&&!this.scroller&&(this.options.navigator.enabled||this.options.scrollbar.enabled)&&(this.scroller=this.navigator=new K(this),dh(t.redraw,!0)&&this.redraw(t.animation))}function du(){var t=this.options;(t.navigator.enabled||t.scrollbar.enabled)&&(this.scroller=this.navigator=new K(this))}function df(){var t=this.options,e=t.navigator,i=t.rangeSelector;if((e&&e.enabled||i&&i.enabled)&&(!ds&&"x"===this.zooming.type||ds&&"x"===this.zooming.pinchType))return!1}function dg(t){var e=t.navigator;if(e&&t.xAxis[0]){var i=t.xAxis[0].getExtremes();e.render(i.min,i.max)}}function dv(t){var e=t.options.navigator||{},i=t.options.scrollbar||{};!this.navigator&&!this.scroller&&(e.enabled||i.enabled)&&(da(!0,this.options.navigator,e),da(!0,this.options.scrollbar,i),delete t.options.navigator,delete t.options.scrollbar)}var dm=function(t,e){if(tT.pushUnique(dl,t)){var i=t.prototype;K=e,i.callbacks.push(dg),dn(t,"afterAddSeries",dd),dn(t,"afterSetChartSize",dc),dn(t,"afterUpdate",dp),dn(t,"beforeRender",du),dn(t,"beforeShowResetZoom",df),dn(t,"update",dv)}},dy=tt.isTouchDevice,dx=tT.addEvent,db=tT.correctFloat,dk=tT.defined,dM=tT.isNumber,dw=tT.pick;function dS(){this.navigatorAxis||(this.navigatorAxis=new dT(this))}function dA(t){var e,i=this.chart,o=i.options,r=o.navigator,s=this.navigatorAxis,n=i.zooming.pinchType,a=o.rangeSelector,h=i.zooming.type;if(this.isXAxis&&((null==r?void 0:r.enabled)||(null==a?void 0:a.enabled))){if("y"===h&&"zoom"===t.trigger)e=!1;else if(("zoom"===t.trigger&&"xy"===h||dy&&"xy"===n)&&this.options.range){var l=s.previousZoom;dk(t.min)?s.previousZoom=[this.min,this.max]:l&&(t.min=l[0],t.max=l[1],s.previousZoom=void 0)}}void 0!==e&&t.preventDefault()}var dT=function(){function t(t){this.axis=t}return t.compose=function(t){t.keepProps.includes("navigatorAxis")||(t.keepProps.push("navigatorAxis"),dx(t,"init",dS),dx(t,"setExtremes",dA))},t.prototype.destroy=function(){this.axis=void 0},t.prototype.toFixedRange=function(t,e,i,o){var r=this.axis,s=(r.pointRange||0)/2,n=dw(i,r.translate(t,!0,!r.horiz)),a=dw(o,r.translate(e,!0,!r.horiz));return dk(i)||(n=db(n+s)),dk(o)||(a=db(a-s)),dM(n)&&dM(a)||(n=a=void 0),{min:n,max:a}},t}(),dC=t7.parse,dO=sQ.seriesTypes,dP={height:40,margin:22,maskInside:!0,handles:{width:7,borderRadius:0,height:15,symbols:["navigator-handle","navigator-handle"],enabled:!0,lineWidth:1,backgroundColor:"#f2f2f2",borderColor:"#999999"},maskFill:dC("#667aff").setOpacity(.3).get(),outlineColor:"#999999",outlineWidth:1,series:{type:void 0===dO.areaspline?"line":"areaspline",fillOpacity:.05,lineWidth:1,compare:null,sonification:{enabled:!1},dataGrouping:{approximation:"average",enabled:!0,groupPixelWidth:2,firstAnchor:"firstPoint",anchor:"middle",lastAnchor:"lastPoint",units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2,3,4]],["week",[1,2,3]],["month",[1,3,6]],["year",null]]},dataLabels:{enabled:!1,zIndex:2},id:"highcharts-navigator-series",className:"highcharts-navigator-series",lineColor:null,marker:{enabled:!1},threshold:null},xAxis:{className:"highcharts-navigator-xaxis",tickLength:0,lineWidth:0,gridLineColor:"#e6e6e6",id:"navigator-x-axis",gridLineWidth:1,tickPixelInterval:200,labels:{align:"left",style:{color:"#000000",fontSize:"0.7em",opacity:.6,textOutline:"2px contrast"},x:3,y:-4},crosshair:!1},yAxis:{className:"highcharts-navigator-yaxis",gridLineWidth:0,startOnTick:!1,endOnTick:!1,minPadding:.1,id:"navigator-y-axis",maxPadding:.1,labels:{enabled:!1},crosshair:!1,title:{text:void 0},tickLength:0,tickWidth:0}},dE=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,s=e.length;r<s;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},dL=tT.relativeLength,dD={"navigator-handle":function(t,e,i,o,r){void 0===r&&(r={});var s=r.width?r.width/2:i,n=dL(r.borderRadius||0,Math.min(2*s,o));return dE([["M",-1.5,(o=r.height||o)/2-3.5],["L",-1.5,o/2+4.5],["M",.5,o/2-3.5],["L",.5,o/2+4.5]],iz.rect(-s-1,.5,2*s+1,o,{r:n}),!0)}},dI=tT.defined,dB=tt.composed,dz=eU.getRendererType,dR={setFixedRange:function(t){var e=this.xAxis[0];dI(e.dataMax)&&dI(e.dataMin)&&t?this.fixedRange=Math.min(t,e.dataMax-e.dataMin):this.fixedRange=t}}.setFixedRange,dN=tT.addEvent,dW=tT.extend,dX=tT.pushUnique;function dH(){this.chart.navigator&&!this.options.isInternal&&this.chart.navigator.setBaseSeries(null,!1)}var dF=function(t,e,i){dT.compose(e),dX(dB,"Navigator")&&(t.prototype.setFixedRange=dR,dW(dz().prototype.symbols,dD),dN(i,"afterUpdate",dH),t0({navigator:dP}))},dj=tt.composed,dY=tT.addEvent,dG=tT.defined,d_=tT.pick,dU=tT.pushUnique;!function(t){var e;function i(t){var e,i,o=d_(null===(e=t.options)||void 0===e?void 0:e.min,t.min),r=d_(null===(i=t.options)||void 0===i?void 0:i.max,t.max);return{axisMin:o,axisMax:r,scrollMin:dG(t.dataMin)?Math.min(o,t.min,t.dataMin,d_(t.threshold,1/0)):o,scrollMax:dG(t.dataMax)?Math.max(r,t.max,t.dataMax,d_(t.threshold,-1/0)):r}}function o(){var t=this.scrollbar,e=t&&!t.options.opposite,i=this.horiz?2:e?3:1;t&&(this.chart.scrollbarsOffsets=[0,0],this.chart.axisOffset[i]+=t.size+(t.options.margin||0))}function r(){var t,o,r=this;(null===(o=null===(t=r.options)||void 0===t?void 0:t.scrollbar)||void 0===o?void 0:o.enabled)&&(r.options.scrollbar.vertical=!r.horiz,r.options.startOnTick=r.options.endOnTick=!1,r.scrollbar=new e(r.chart.renderer,r.options.scrollbar,r.chart),dY(r.scrollbar,"changed",function(t){var e,o,s=i(r),n=s.axisMin,a=s.axisMax,h=s.scrollMin,l=s.scrollMax-h;if(dG(n)&&dG(a)){if(r.horiz&&!r.reversed||!r.horiz&&r.reversed?(e=h+l*this.to,o=h+l*this.from):(e=h+l*(1-this.from),o=h+l*(1-this.to)),this.shouldUpdateExtremes(t.DOMType)){var d="mousemove"!==t.DOMType&&"touchmove"!==t.DOMType&&void 0;r.setExtremes(o,e,!0,d,t)}else this.setRange(this.from,this.to)}}))}function s(){var t,e,o,r=i(this),s=r.scrollMin,n=r.scrollMax,a=this.scrollbar,h=this.axisTitleMargin+(this.titleOffset||0),l=this.chart.scrollbarsOffsets,d=this.options.margin||0;if(a&&l){if(this.horiz)this.opposite||(l[1]+=h),a.position(this.left,this.top+this.height+2+l[1]-(this.opposite?d:0),this.width,this.height),this.opposite||(l[1]+=d),t=1;else{this.opposite&&(l[0]+=h);var c=void 0;c=a.options.opposite?this.left+this.width+2+l[0]-(this.opposite?0:d):this.opposite?0:d,a.position(c,this.top,this.width,this.height),this.opposite&&(l[0]+=d),t=0}if(l[t]+=a.size+(a.options.margin||0),isNaN(s)||isNaN(n)||!dG(this.min)||!dG(this.max)||this.dataMin===this.dataMax)a.setRange(0,1);else if(this.min===this.max){var p=this.pointRange/(this.dataMax+1);e=p*this.min,o=p*(this.max+1),a.setRange(e,o)}else e=(this.min-s)/(n-s),o=(this.max-s)/(n-s),this.horiz&&!this.reversed||!this.horiz&&this.reversed?a.setRange(e,o):a.setRange(1-o,1-e)}}t.compose=function(t,i){dU(dj,"Axis.Scrollbar")&&(e=i,dY(t,"afterGetOffset",o),dY(t,"afterInit",r),dY(t,"afterRender",s))}}($||($={}));var dV=$,dZ={height:10,barBorderRadius:5,buttonBorderRadius:0,buttonsEnabled:!1,liveRedraw:void 0,margin:void 0,minWidth:6,opposite:!0,step:.2,zIndex:3,barBackgroundColor:"#cccccc",barBorderWidth:0,barBorderColor:"#cccccc",buttonArrowColor:"#333333",buttonBackgroundColor:"#e6e6e6",buttonBorderColor:"#cccccc",buttonBorderWidth:1,rifleColor:"none",trackBackgroundColor:"rgba(255, 255, 255, 0.001)",trackBorderColor:"#cccccc",trackBorderRadius:5,trackBorderWidth:1},dq=tT.addEvent,dK=tT.correctFloat,d$=tT.crisp,dJ=tT.defined,dQ=tT.destroyObjectProperties,d0=tT.fireEvent,d1=tT.merge,d2=tT.pick,d3=tT.removeEvent,d5=function(){function t(t,e,i){this._events=[],this.chartX=0,this.chartY=0,this.from=0,this.scrollbarButtons=[],this.scrollbarLeft=0,this.scrollbarStrokeWidth=1,this.scrollbarTop=0,this.size=0,this.to=0,this.trackBorderWidth=1,this.x=0,this.y=0,this.init(t,e,i)}return t.compose=function(e){dV.compose(e,t)},t.swapXY=function(t,e){return e&&t.forEach(function(t){for(var e,i=t.length,o=0;o<i;o+=2)"number"==typeof(e=t[o+1])&&(t[o+1]=t[o+2],t[o+2]=e)}),t},t.prototype.addEvents=function(){var t=this.options.inverted?[1,0]:[0,1],e=this.scrollbarButtons,i=this.scrollbarGroup.element,o=this.track.element,r=this.mouseDownHandler.bind(this),s=this.mouseMoveHandler.bind(this),n=this.mouseUpHandler.bind(this),a=[[e[t[0]].element,"click",this.buttonToMinClick.bind(this)],[e[t[1]].element,"click",this.buttonToMaxClick.bind(this)],[o,"click",this.trackClick.bind(this)],[i,"mousedown",r],[i.ownerDocument,"mousemove",s],[i.ownerDocument,"mouseup",n],[i,"touchstart",r],[i.ownerDocument,"touchmove",s],[i.ownerDocument,"touchend",n]];a.forEach(function(t){dq.apply(null,t)}),this._events=a},t.prototype.buttonToMaxClick=function(t){var e=(this.to-this.from)*d2(this.options.step,.2);this.updatePosition(this.from+e,this.to+e),d0(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})},t.prototype.buttonToMinClick=function(t){var e=dK(this.to-this.from)*d2(this.options.step,.2);this.updatePosition(dK(this.from-e),dK(this.to-e)),d0(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})},t.prototype.cursorToScrollbarPosition=function(t){var e=this.options,i=e.minWidth>this.calculatedWidth?e.minWidth:0;return{chartX:(t.chartX-this.x-this.xOffset)/(this.barWidth-i),chartY:(t.chartY-this.y-this.yOffset)/(this.barWidth-i)}},t.prototype.destroy=function(){var t=this,e=t.chart.scroller;t.removeEvents(),["track","scrollbarRifles","scrollbar","scrollbarGroup","group"].forEach(function(e){t[e]&&t[e].destroy&&(t[e]=t[e].destroy())}),e&&t===e.scrollbar&&(e.scrollbar=null,dQ(e.scrollbarButtons))},t.prototype.drawScrollbarButton=function(e){var i=this.renderer,o=this.scrollbarButtons,r=this.options,s=this.size,n=i.g().add(this.group);if(o.push(n),r.buttonsEnabled){var a=i.rect().addClass("highcharts-scrollbar-button").add(n);this.chart.styledMode||a.attr({stroke:r.buttonBorderColor,"stroke-width":r.buttonBorderWidth,fill:r.buttonBackgroundColor}),a.attr(a.crisp({x:-.5,y:-.5,width:s,height:s,r:r.buttonBorderRadius},a.strokeWidth()));var h=i.path(t.swapXY([["M",s/2+(e?-1:1),s/2-3],["L",s/2+(e?-1:1),s/2+3],["L",s/2+(e?2:-2),s/2]],r.vertical)).addClass("highcharts-scrollbar-arrow").add(o[e]);this.chart.styledMode||h.attr({fill:r.buttonArrowColor})}},t.prototype.init=function(t,e,i){this.scrollbarButtons=[],this.renderer=t,this.userOptions=e,this.options=d1(dZ,tJ.scrollbar,e),this.options.margin=d2(this.options.margin,10),this.chart=i,this.size=d2(this.options.size,this.options.height),e.enabled&&(this.render(),this.addEvents())},t.prototype.mouseDownHandler=function(t){var e,i=(null===(e=this.chart.pointer)||void 0===e?void 0:e.normalize(t))||t,o=this.cursorToScrollbarPosition(i);this.chartX=o.chartX,this.chartY=o.chartY,this.initPositions=[this.from,this.to],this.grabbedCenter=!0},t.prototype.mouseMoveHandler=function(t){var e,i,o=(null===(e=this.chart.pointer)||void 0===e?void 0:e.normalize(t))||t,r=this.options.vertical?"chartY":"chartX",s=this.initPositions||[];this.grabbedCenter&&(!t.touches||0!==t.touches[0][r])&&(i=this.cursorToScrollbarPosition(o)[r]-this[r],this.hasDragged=!0,this.updatePosition(s[0]+i,s[1]+i),this.hasDragged&&d0(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}))},t.prototype.mouseUpHandler=function(t){this.hasDragged&&d0(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}),this.grabbedCenter=this.hasDragged=this.chartX=this.chartY=null},t.prototype.position=function(t,e,i,o){var r=this.options,s=r.buttonsEnabled,n=r.margin,a=void 0===n?0:n,h=r.vertical,l=this.rendered?"animate":"attr",d=o,c=0;this.group.show(),this.x=t,this.y=e+this.trackBorderWidth,this.width=i,this.height=o,this.xOffset=d,this.yOffset=c,h?(this.width=this.yOffset=i=c=this.size,this.xOffset=d=0,this.yOffset=c=s?this.size:0,this.barWidth=o-(s?2*i:0),this.x=t+=a):(this.height=o=this.size,this.xOffset=d=s?this.size:0,this.barWidth=i-(s?2*o:0),this.y=this.y+a),this.group[l]({translateX:t,translateY:this.y}),this.track[l]({width:i,height:o}),this.scrollbarButtons[1][l]({translateX:h?0:i-d,translateY:h?o-c:0})},t.prototype.removeEvents=function(){this._events.forEach(function(t){d3.apply(null,t)}),this._events.length=0},t.prototype.render=function(){var e=this.renderer,i=this.options,o=this.size,r=this.chart.styledMode,s=e.g("scrollbar").attr({zIndex:i.zIndex}).hide().add();this.group=s,this.track=e.rect().addClass("highcharts-scrollbar-track").attr({r:i.trackBorderRadius||0,height:o,width:o}).add(s),r||this.track.attr({fill:i.trackBackgroundColor,stroke:i.trackBorderColor,"stroke-width":i.trackBorderWidth});var n=this.trackBorderWidth=this.track.strokeWidth();this.track.attr({x:-d$(0,n),y:-d$(0,n)}),this.scrollbarGroup=e.g().add(s),this.scrollbar=e.rect().addClass("highcharts-scrollbar-thumb").attr({height:o-n,width:o-n,r:i.barBorderRadius||0}).add(this.scrollbarGroup),this.scrollbarRifles=e.path(t.swapXY([["M",-3,o/4],["L",-3,2*o/3],["M",0,o/4],["L",0,2*o/3],["M",3,o/4],["L",3,2*o/3]],i.vertical)).addClass("highcharts-scrollbar-rifles").add(this.scrollbarGroup),r||(this.scrollbar.attr({fill:i.barBackgroundColor,stroke:i.barBorderColor,"stroke-width":i.barBorderWidth}),this.scrollbarRifles.attr({stroke:i.rifleColor,"stroke-width":1})),this.scrollbarStrokeWidth=this.scrollbar.strokeWidth(),this.scrollbarGroup.translate(-d$(0,this.scrollbarStrokeWidth),-d$(0,this.scrollbarStrokeWidth)),this.drawScrollbarButton(0),this.drawScrollbarButton(1)},t.prototype.setRange=function(t,e){var i,o,r=this.options,s=r.vertical,n=r.minWidth,a=this.barWidth,h=!this.rendered||this.hasDragged||this.chart.navigator&&this.chart.navigator.hasDragged?"attr":"animate";if(dJ(a)){var l=a*Math.min(e,1);i=Math.ceil(a*(t=Math.max(t,0))),this.calculatedWidth=o=dK(l-i),o<n&&(i=(a-n+o)*t,o=n);var d=Math.floor(i+this.xOffset+this.yOffset),c=o/2-.5;this.from=t,this.to=e,s?(this.scrollbarGroup[h]({translateY:d}),this.scrollbar[h]({height:o}),this.scrollbarRifles[h]({translateY:c}),this.scrollbarTop=d,this.scrollbarLeft=0):(this.scrollbarGroup[h]({translateX:d}),this.scrollbar[h]({width:o}),this.scrollbarRifles[h]({translateX:c}),this.scrollbarLeft=d,this.scrollbarTop=0),o<=12?this.scrollbarRifles.hide():this.scrollbarRifles.show(),!1===r.showFull&&(t<=0&&e>=1?this.group.hide():this.group.show()),this.rendered=!0}},t.prototype.shouldUpdateExtremes=function(t){return d2(this.options.liveRedraw,tt.svg&&!tt.isTouchDevice&&!this.chart.boosted)||"mouseup"===t||"touchend"===t||!dJ(t)},t.prototype.trackClick=function(t){var e,i=(null===(e=this.chart.pointer)||void 0===e?void 0:e.normalize(t))||t,o=this.to-this.from,r=this.y+this.scrollbarTop,s=this.x+this.scrollbarLeft;this.options.vertical&&i.chartY>r||!this.options.vertical&&i.chartX>s?this.updatePosition(this.from+o,this.to+o):this.updatePosition(this.from-o,this.to-o),d0(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})},t.prototype.update=function(t){this.destroy(),this.init(this.chart.renderer,d1(!0,this.options,t),this.chart)},t.prototype.updatePosition=function(t,e){e>1&&(t=dK(1-dK(e-t)),e=1),t<0&&(e=dK(e-t),t=0),this.from=t,this.to=e},t.defaultOptions=dZ,t}();tJ.scrollbar=d1(!0,d5.defaultOptions,tJ.scrollbar);var d6=function(){return(d6=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},d9=tt.isTouchDevice,d4=oc.prototype.symbols,d8=tT.addEvent,d7=tT.clamp,ct=tT.correctFloat,ce=tT.defined,ci=tT.destroyObjectProperties,co=tT.erase,cr=tT.extend,cs=tT.find,cn=tT.fireEvent,ca=tT.isArray,ch=tT.isNumber,cl=tT.merge,cd=tT.pick,cc=tT.removeEvent,cp=tT.splat;function cu(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];var o=[].filter.call(e,ch);if(o.length)return Math[t].apply(0,o)}var cf=function(){function t(t){this.isDirty=!1,this.scrollbarHeight=0,this.init(t)}return t.compose=function(e,i,o){dm(e,t),dF(e,i,o)},t.prototype.drawHandle=function(t,e,i,o){var r=this.navigatorOptions.handles.height;this.handles[e][o](i?{translateX:Math.round(this.left+this.height/2),translateY:Math.round(this.top+parseInt(t,10)+.5-r)}:{translateX:Math.round(this.left+parseInt(t,10)),translateY:Math.round(this.top+this.height/2-r/2-1)})},t.prototype.drawOutline=function(t,e,i,o){var r,s,n=this.navigatorOptions.maskInside,a=this.outline.strokeWidth(),h=a/2,l=a%2/2,d=this.scrollButtonSize,c=this.size,p=this.top,u=this.height,f=p-h,g=p+u,v=this.left;i?(r=p+e+l,e=p+t+l,s=[["M",v+u,p-d-l],["L",v+u,r],["L",v,r],["M",v,e],["L",v+u,e],["L",v+u,p+c+d]],n&&s.push(["M",v+u,r-h],["L",v+u,e+h])):(v-=d,t+=v+d-l,e+=v+d-l,s=[["M",v,f],["L",t,f],["L",t,g],["M",e,g],["L",e,f],["L",v+c+2*d,f]],n&&s.push(["M",t-h,f],["L",e+h,f])),this.outline[o]({d:s})},t.prototype.drawMasks=function(t,e,i,o){var r,s,n,a,h=this.left,l=this.top,d=this.height;i?(n=[h,h,h],a=[l,l+t,l+e],s=[d,d,d],r=[t,e-t,this.size-e]):(n=[h,h+t,h+e],a=[l,l,l],s=[t,e-t,this.size-e],r=[d,d,d]),this.shades.forEach(function(t,e){t[o]({x:n[e],y:a[e],width:s[e],height:r[e]})})},t.prototype.renderElements=function(){var t,e,i=this,o=i.navigatorOptions,r=o.maskInside,s=i.chart,n=s.inverted,a=s.renderer,h={cursor:n?"ns-resize":"ew-resize"},l=null!==(t=i.navigatorGroup)&&void 0!==t?t:i.navigatorGroup=a.g("navigator").attr({zIndex:8,visibility:"hidden"}).add();if([!r,r,!r].forEach(function(t,e){var r,n=null!==(r=i.shades[e])&&void 0!==r?r:i.shades[e]=a.rect().addClass("highcharts-navigator-mask"+(1===e?"-inside":"-outside")).add(l);s.styledMode||(n.attr({fill:t?o.maskFill:"rgba(0,0,0,0)"}),1===e&&n.css(h))}),i.outline||(i.outline=a.path().addClass("highcharts-navigator-outline").add(l)),s.styledMode||i.outline.attr({"stroke-width":o.outlineWidth,stroke:o.outlineColor}),null===(e=o.handles)||void 0===e?void 0:e.enabled){var d=o.handles,c=d.height,p=d.width;[0,1].forEach(function(t){var e,o=d.symbols[t];if(i.handles[t]&&i.handles[t].symbolUrl===o){if(!i.handles[t].isImg&&i.handles[t].symbolName!==o){var r=d4[o].call(d4,-p/2-1,0,p,c);i.handles[t].attr({d:r}),i.handles[t].symbolName=o}}else null===(e=i.handles[t])||void 0===e||e.destroy(),i.handles[t]=a.symbol(o,-p/2-1,0,p,c,d),i.handles[t].attr({zIndex:7-t}).addClass("highcharts-navigator-handle highcharts-navigator-handle-"+["left","right"][t]).add(l),i.addMouseEvents();s.inverted&&i.handles[t].attr({rotation:90,rotationOriginX:Math.floor(-p/2),rotationOriginY:(c+p)/2}),s.styledMode||i.handles[t].attr({fill:d.backgroundColor,stroke:d.borderColor,"stroke-width":d.lineWidth,width:d.width,height:d.height,x:-p/2-1,y:0}).css(h)})}},t.prototype.update=function(t,e){var i,o,r,s,n=this;void 0===e&&(e=!1);var a=this.chart,h=a.options.chart.inverted!==(null===(r=a.scrollbar)||void 0===r?void 0:r.options.vertical);if(cl(!0,a.options.navigator,t),this.navigatorOptions=a.options.navigator||{},this.setOpposite(),ce(t.enabled)||h)return this.destroy(),this.navigatorEnabled=t.enabled||this.navigatorEnabled,this.init(a);if(this.navigatorEnabled&&(this.isDirty=!0,!1===t.adaptToUpdatedData&&this.baseSeries.forEach(function(t){cc(t,"updatedData",n.updatedDataHandler)},this),t.adaptToUpdatedData&&this.baseSeries.forEach(function(t){t.eventsToUnbind.push(d8(t,"updatedData",n.updatedDataHandler))},this),(t.series||t.baseSeries)&&this.setBaseSeries(void 0,!1),t.height||t.xAxis||t.yAxis)){this.height=null!==(s=t.height)&&void 0!==s?s:this.height;var l=this.getXAxisOffsets();this.xAxis.update(d6(d6({},t.xAxis),((i={offsets:l})[a.inverted?"width":"height"]=this.height,i[a.inverted?"height":"width"]=void 0,i)),!1),this.yAxis.update(d6(d6({},t.yAxis),((o={})[a.inverted?"width":"height"]=this.height,o)),!1)}e&&a.redraw()},t.prototype.render=function(t,e,i,o){var r,s,n,a,h,l=this.chart,d=this.xAxis,c=d.pointRange||0,p=d.navigatorAxis.fake?l.xAxis[0]:d,u=this.navigatorEnabled,f=this.rendered,g=l.inverted,v=l.xAxis[0].minRange,m=l.xAxis[0].options.maxRange,y=this.scrollButtonSize,x=this.scrollbarHeight;if(!this.hasDragged||ce(i)){if(this.isDirty&&this.renderElements(),t=ct(t-c/2),e=ct(e+c/2),!ch(t)||!ch(e)){if(!f)return;i=0,o=cd(d.width,p.width)}this.left=cd(d.left,l.plotLeft+y+(g?l.plotWidth:0));var b=this.size=a=cd(d.len,(g?l.plotHeight:l.plotWidth)-2*y);r=g?x:a+2*y,i=cd(i,d.toPixels(t,!0)),o=cd(o,d.toPixels(e,!0)),ch(i)&&Math.abs(i)!==1/0||(i=0,o=r);var k=d.toValue(i,!0),M=d.toValue(o,!0),w=Math.abs(ct(M-k));w<v?this.grabbedLeft?i=d.toPixels(M-v-c,!0):this.grabbedRight&&(o=d.toPixels(k+v+c,!0)):ce(m)&&ct(w-c)>m&&(this.grabbedLeft?i=d.toPixels(M-m-c,!0):this.grabbedRight&&(o=d.toPixels(k+m+c,!0))),this.zoomedMax=d7(Math.max(i,o),0,b),this.zoomedMin=d7(this.fixedWidth?this.zoomedMax-this.fixedWidth:Math.min(i,o),0,b),this.range=this.zoomedMax-this.zoomedMin,b=Math.round(this.zoomedMax);var S=Math.round(this.zoomedMin);u&&(this.navigatorGroup.attr({visibility:"inherit"}),h=f&&!this.hasDragged?"animate":"attr",this.drawMasks(S,b,g,h),this.drawOutline(S,b,g,h),this.navigatorOptions.handles.enabled&&(this.drawHandle(S,0,g,h),this.drawHandle(b,1,g,h))),this.scrollbar&&(g?(n=this.top-y,s=this.left-x+(u||!p.opposite?0:(p.titleOffset||0)+p.axisTitleMargin),x=a+2*y):(n=this.top+(u?this.height:-x),s=this.left-y),this.scrollbar.position(s,n,r,x),this.scrollbar.setRange(this.zoomedMin/(a||1),this.zoomedMax/(a||1))),this.rendered=!0,this.isDirty=!1,cn(this,"afterRender")}},t.prototype.addMouseEvents=function(){var t,e,i=this,o=i.chart,r=o.container,s=[];i.mouseMoveHandler=t=function(t){i.onMouseMove(t)},i.mouseUpHandler=e=function(t){i.onMouseUp(t)},(s=i.getPartsEvents("mousedown")).push(d8(o.renderTo,"mousemove",t),d8(r.ownerDocument,"mouseup",e),d8(o.renderTo,"touchmove",t),d8(r.ownerDocument,"touchend",e)),s.concat(i.getPartsEvents("touchstart")),i.eventsToUnbind=s,i.series&&i.series[0]&&s.push(d8(i.series[0].xAxis,"foundExtremes",function(){o.navigator.modifyNavigatorAxisExtremes()}))},t.prototype.getPartsEvents=function(t){var e=this,i=[];return["shades","handles"].forEach(function(o){e[o].forEach(function(r,s){i.push(d8(r.element,t,function(t){e[o+"Mousedown"](t,s)}))})}),i},t.prototype.shadesMousedown=function(t,e){t=(null===(i=this.chart.pointer)||void 0===i?void 0:i.normalize(t))||t;var i,o,r,s,n,a=this.chart,h=this.xAxis,l=this.zoomedMin,d=this.size,c=this.range,p=this.left,u=t.chartX;a.inverted&&(u=t.chartY,p=this.top),1===e?(this.grabbedCenter=u,this.fixedWidth=c,this.dragOffset=u-l):(n=u-p-c/2,0===e?n=Math.max(0,n):2===e&&n+c>=d&&(n=d-c,this.reversedExtremes?(n-=c,r=this.getUnionExtremes().dataMin):o=this.getUnionExtremes().dataMax),n!==l&&(this.fixedWidth=c,ce((s=h.navigatorAxis.toFixedRange(n,n+c,r,o)).min)&&cn(this,"setRange",{min:Math.min(s.min,s.max),max:Math.max(s.min,s.max),redraw:!0,eventArguments:{trigger:"navigator"}})))},t.prototype.handlesMousedown=function(t,e){t=(null===(i=this.chart.pointer)||void 0===i?void 0:i.normalize(t))||t;var i,o=this.chart,r=o.xAxis[0],s=this.reversedExtremes;0===e?(this.grabbedLeft=!0,this.otherHandlePos=this.zoomedMax,this.fixedExtreme=s?r.min:r.max):(this.grabbedRight=!0,this.otherHandlePos=this.zoomedMin,this.fixedExtreme=s?r.max:r.min),o.setFixedRange(void 0)},t.prototype.onMouseMove=function(t){var e,i,o=this,r=o.chart,s=o.navigatorSize,n=o.range,a=o.dragOffset,h=r.inverted,l=o.left;(!t.touches||0!==t.touches[0].pageX)&&(i=(t=(null===(e=r.pointer)||void 0===e?void 0:e.normalize(t))||t).chartX,h&&(l=o.top,i=t.chartY),o.grabbedLeft?(o.hasDragged=!0,o.render(0,0,i-l,o.otherHandlePos)):o.grabbedRight?(o.hasDragged=!0,o.render(0,0,o.otherHandlePos,i-l)):o.grabbedCenter&&(o.hasDragged=!0,i<a?i=a:i>s+a-n&&(i=s+a-n),o.render(0,0,i-a,i-a+n)),o.hasDragged&&o.scrollbar&&cd(o.scrollbar.options.liveRedraw,!d9&&!this.chart.boosted)&&(t.DOMType=t.type,setTimeout(function(){o.onMouseUp(t)},0)))},t.prototype.onMouseUp=function(t){var e,i,o,r,s,n,a=this.chart,h=this.xAxis,l=this.scrollbar,d=t.DOMEvent||t,c=a.inverted,p=this.rendered&&!this.hasDragged?"animate":"attr";(this.hasDragged&&(!l||!l.hasDragged)||"scrollbar"===t.trigger)&&(o=this.getUnionExtremes(),this.zoomedMin===this.otherHandlePos?r=this.fixedExtreme:this.zoomedMax===this.otherHandlePos&&(s=this.fixedExtreme),this.zoomedMax===this.size&&(s=this.reversedExtremes?o.dataMin:o.dataMax),0===this.zoomedMin&&(r=this.reversedExtremes?o.dataMax:o.dataMin),ce((n=h.navigatorAxis.toFixedRange(this.zoomedMin,this.zoomedMax,r,s)).min)&&cn(this,"setRange",{min:Math.min(n.min,n.max),max:Math.max(n.min,n.max),redraw:!0,animation:!this.hasDragged&&null,eventArguments:{trigger:"navigator",triggerOp:"navigator-drag",DOMEvent:d}})),"mousemove"!==t.DOMType&&"touchmove"!==t.DOMType&&(this.grabbedLeft=this.grabbedRight=this.grabbedCenter=this.fixedWidth=this.fixedExtreme=this.otherHandlePos=this.hasDragged=this.dragOffset=null),this.navigatorEnabled&&ch(this.zoomedMin)&&ch(this.zoomedMax)&&(i=Math.round(this.zoomedMin),e=Math.round(this.zoomedMax),this.shades&&this.drawMasks(i,e,c,p),this.outline&&this.drawOutline(i,e,c,p),this.navigatorOptions.handles.enabled&&Object.keys(this.handles).length===this.handles.length&&(this.drawHandle(i,0,c,p),this.drawHandle(e,1,c,p)))},t.prototype.removeEvents=function(){this.eventsToUnbind&&(this.eventsToUnbind.forEach(function(t){t()}),this.eventsToUnbind=void 0),this.removeBaseSeriesEvents()},t.prototype.removeBaseSeriesEvents=function(){var t=this.baseSeries||[];this.navigatorEnabled&&t[0]&&(!1!==this.navigatorOptions.adaptToUpdatedData&&t.forEach(function(t){cc(t,"updatedData",this.updatedDataHandler)},this),t[0].xAxis&&cc(t[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes))},t.prototype.getXAxisOffsets=function(){return this.chart.inverted?[this.scrollButtonSize,0,-this.scrollButtonSize,0]:[0,-this.scrollButtonSize,0,this.scrollButtonSize]},t.prototype.init=function(t){var e,i,o=t.options,r=o.navigator||{},s=r.enabled,n=o.scrollbar||{},a=n.enabled,h=s&&r.height||0,l=a&&n.height||0,d=n.buttonsEnabled&&l||0;this.handles=[],this.shades=[],this.chart=t,this.setBaseSeries(),this.height=h,this.scrollbarHeight=l,this.scrollButtonSize=d,this.scrollbarEnabled=a,this.navigatorEnabled=s,this.navigatorOptions=r,this.scrollbarOptions=n,this.setOpposite();var c=this,p=c.baseSeries,u=t.xAxis.length,f=t.yAxis.length,g=p&&p[0]&&p[0].xAxis||t.xAxis[0]||{options:{}};if(t.isDirtyBox=!0,c.navigatorEnabled){var v=this.getXAxisOffsets();c.xAxis=new rp(t,cl({breaks:g.options.breaks,ordinal:g.options.ordinal,overscroll:g.options.overscroll},r.xAxis,{type:"datetime",yAxis:null===(e=r.yAxis)||void 0===e?void 0:e.id,index:u,isInternal:!0,offset:0,keepOrdinalPadding:!0,startOnTick:!1,endOnTick:!1,minPadding:g.options.ordinal?0:g.options.minPadding,maxPadding:g.options.ordinal?0:g.options.maxPadding,zoomEnabled:!1},t.inverted?{offsets:v,width:h}:{offsets:v,height:h}),"xAxis"),c.yAxis=new rp(t,cl(r.yAxis,{alignTicks:!1,offset:0,index:f,isInternal:!0,reversed:cd(r.yAxis&&r.yAxis.reversed,t.yAxis[0]&&t.yAxis[0].reversed,!1),zoomEnabled:!1},t.inverted?{width:h}:{height:h}),"yAxis"),p||r.series.data?c.updateNavigatorSeries(!1):0===t.series.length&&(c.unbindRedraw=d8(t,"beforeRedraw",function(){t.series.length>0&&!c.series&&(c.setBaseSeries(),c.unbindRedraw())})),c.reversedExtremes=t.inverted&&!c.xAxis.reversed||!t.inverted&&c.xAxis.reversed,c.renderElements(),c.addMouseEvents()}else c.xAxis={chart:t,navigatorAxis:{fake:!0},translate:function(e,i){var o=t.xAxis[0],r=o.getExtremes(),s=o.len-2*d,n=cu("min",o.options.min,r.dataMin),a=cu("max",o.options.max,r.dataMax)-n;return i?e*a/s+n:s*(e-n)/a},toPixels:function(t){return this.translate(t)},toValue:function(t){return this.translate(t,!0)}},c.xAxis.navigatorAxis.axis=c.xAxis,c.xAxis.navigatorAxis.toFixedRange=dT.prototype.toFixedRange.bind(c.xAxis.navigatorAxis);if(null===(i=t.options.scrollbar)||void 0===i?void 0:i.enabled){var m=cl(t.options.scrollbar,{vertical:t.inverted});ch(m.margin)||(m.margin=t.inverted?-3:3),t.scrollbar=c.scrollbar=new d5(t.renderer,m,t),d8(c.scrollbar,"changed",function(t){var e=c.size,i=e*this.to,o=e*this.from;c.hasDragged=c.scrollbar.hasDragged,c.render(0,0,o,i),this.shouldUpdateExtremes(t.DOMType)&&setTimeout(function(){c.onMouseUp(t)})})}c.addBaseSeriesEvents(),c.addChartEvents()},t.prototype.setOpposite=function(){var t=this.navigatorOptions,e=this.navigatorEnabled,i=this.chart;this.opposite=cd(t.opposite,!!(!e&&i.inverted))},t.prototype.getUnionExtremes=function(t){var e,i=this.chart.xAxis[0],o=this.chart.time,r=this.xAxis,s=r.options,n=i.options;return t&&null===i.dataMin||(e={dataMin:cd(o.parse(null==s?void 0:s.min),cu("min",o.parse(n.min),i.dataMin,r.dataMin,r.min)),dataMax:cd(o.parse(null==s?void 0:s.max),cu("max",o.parse(n.max),i.dataMax,r.dataMax,r.max))}),e},t.prototype.setBaseSeries=function(t,e){var i=this.chart,o=this.baseSeries=[];t=t||i.options&&i.options.navigator.baseSeries||(i.series.length?cs(i.series,function(t){return!t.options.isInternal}).index:0),(i.series||[]).forEach(function(e,i){!e.options.isInternal&&(e.options.showInNavigator||(i===t||e.options.id===t)&&!1!==e.options.showInNavigator)&&o.push(e)}),this.xAxis&&!this.xAxis.navigatorAxis.fake&&this.updateNavigatorSeries(!0,e)},t.prototype.updateNavigatorSeries=function(t,e){var i,o,r,s,n,a=this,h=a.chart,l=a.baseSeries,d={enableMouseTracking:!1,index:null,linkedTo:null,group:"nav",padXAxis:!1,xAxis:null===(i=this.navigatorOptions.xAxis)||void 0===i?void 0:i.id,yAxis:null===(o=this.navigatorOptions.yAxis)||void 0===o?void 0:o.id,showInLegend:!1,stacking:void 0,isInternal:!0,states:{inactive:{opacity:1}}},c=a.series=(a.series||[]).filter(function(t){var e=t.baseSeries;return!(0>l.indexOf(e))||(e&&(cc(e,"updatedData",a.updatedDataHandler),delete e.navigatorSeries),t.chart&&t.destroy(),!1)}),p=a.navigatorOptions.series;l&&l.length&&l.forEach(function(t){var i,o=t.navigatorSeries,u=cr({color:t.color,visible:t.visible},ca(p)?tJ.navigator.series:p);if(!o||!1!==a.navigatorOptions.adaptToUpdatedData){d.name="Navigator "+l.length,n=(r=t.options||{}).navigatorOptions||{},u.dataLabels=cp(u.dataLabels),(s=cl(r,d,u,n)).pointRange=cd(u.pointRange,n.pointRange,tJ.plotOptions[s.type||"line"].pointRange);var f=n.data||u.data;a.hasNavigatorData=a.hasNavigatorData||!!f,s.data=f||(null===(i=r.data)||void 0===i?void 0:i.slice(0)),o&&o.options?o.update(s,e):(t.navigatorSeries=h.initSeries(s),h.setSortedData(),t.navigatorSeries.baseSeries=t,c.push(t.navigatorSeries))}}),(p.data&&!(l&&l.length)||ca(p))&&(a.hasNavigatorData=!1,(p=cp(p)).forEach(function(t,e){d.name="Navigator "+(c.length+1),(s=cl(tJ.navigator.series,{color:h.series[e]&&!h.series[e].options.isInternal&&h.series[e].color||h.options.colors[e]||h.options.colors[0]},d,t)).data=t.data,s.data&&(a.hasNavigatorData=!0,c.push(h.initSeries(s)))})),t&&this.addBaseSeriesEvents()},t.prototype.addBaseSeriesEvents=function(){var t=this,e=this,i=e.baseSeries||[];i[0]&&i[0].xAxis&&i[0].eventsToUnbind.push(d8(i[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes)),i.forEach(function(o){o.eventsToUnbind.push(d8(o,"show",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!0,!1)})),o.eventsToUnbind.push(d8(o,"hide",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!1,!1)})),!1!==t.navigatorOptions.adaptToUpdatedData&&o.xAxis&&o.eventsToUnbind.push(d8(o,"updatedData",t.updatedDataHandler)),o.eventsToUnbind.push(d8(o,"remove",function(){i&&co(i,o),this.navigatorSeries&&e.series&&(co(e.series,this.navigatorSeries),ce(this.navigatorSeries.options)&&this.navigatorSeries.remove(!1),delete this.navigatorSeries)}))})},t.prototype.getBaseSeriesMin=function(t){return this.baseSeries.reduce(function(t,e){var i;return Math.min(t,null!==(i=e.getColumn("x")[0])&&void 0!==i?i:t)},t)},t.prototype.modifyNavigatorAxisExtremes=function(){var t=this.xAxis;if(void 0!==t.getExtremes){var e=this.getUnionExtremes(!0);e&&(e.dataMin!==t.min||e.dataMax!==t.max)&&(t.min=e.dataMin,t.max=e.dataMax)}},t.prototype.modifyBaseAxisExtremes=function(){var t,e,i,o=this.chart.navigator,r=this.getExtremes(),s=r.min,n=r.max,a=r.dataMin,h=r.dataMax,l=n-s,d=o.stickToMin,c=o.stickToMax,p=cd(null===(t=this.ordinal)||void 0===t?void 0:t.convertOverscroll(this.options.overscroll),0),u=o.series&&o.series[0],f=!!this.setExtremes;!(this.eventArgs&&"rangeSelectorButton"===this.eventArgs.trigger)&&(d&&(e=(i=a)+l),c&&(e=h+p,d||(i=Math.max(a,e-l,o.getBaseSeriesMin(u&&u.xData?u.xData[0]:-Number.MAX_VALUE)))),f&&(d||c)&&ch(i)&&(this.min=this.userMin=i,this.max=this.userMax=e)),o.stickToMin=o.stickToMax=null},t.prototype.updatedDataHandler=function(){var t=this.chart.navigator,e=this.navigatorSeries,i=t.reversedExtremes?0===Math.round(t.zoomedMin):Math.round(t.zoomedMax)>=Math.round(t.size);t.stickToMax=cd(this.chart.options.navigator&&this.chart.options.navigator.stickToMax,i),t.stickToMin=t.shouldStickToMin(this,t),e&&!t.hasNavigatorData&&(e.options.pointStart=this.getColumn("x")[0],e.setData(this.options.data,!1,null,!1))},t.prototype.shouldStickToMin=function(t,e){var i=e.getBaseSeriesMin(t.getColumn("x")[0]),o=t.xAxis,r=o.max,s=o.min,n=o.options.range,a=!0;return!!(ch(r)&&ch(s))&&(n&&r-i>0?r-i<n:s<=i)},t.prototype.addChartEvents=function(){this.eventsToUnbind||(this.eventsToUnbind=[]),this.eventsToUnbind.push(d8(this.chart,"redraw",function(){var t=this.navigator,e=t&&(t.baseSeries&&t.baseSeries[0]&&t.baseSeries[0].xAxis||this.xAxis[0]);e&&t.render(e.min,e.max)}),d8(this.chart,"getMargins",function(){var t,e=this.navigator,i=e.opposite?"plotTop":"marginBottom";this.inverted&&(i=e.opposite?"marginRight":"plotLeft"),this[i]=(this[i]||0)+(e.navigatorEnabled||!this.inverted?e.height+((null===(t=this.scrollbar)||void 0===t?void 0:t.options.margin)||0)+e.scrollbarHeight:0)+(e.navigatorOptions.margin||0)}),d8(t,"setRange",function(t){this.chart.xAxis[0].setExtremes(t.min,t.max,t.redraw,t.animation,t.eventArguments)}))},t.prototype.destroy=function(){var t=this;this.removeEvents(),this.xAxis&&(co(this.chart.xAxis,this.xAxis),co(this.chart.axes,this.xAxis)),this.yAxis&&(co(this.chart.yAxis,this.yAxis),co(this.chart.axes,this.yAxis)),(this.series||[]).forEach(function(t){t.destroy&&t.destroy()}),["series","xAxis","yAxis","shades","outline","scrollbarTrack","scrollbarRifles","scrollbarGroup","scrollbar","navigatorGroup","rendered"].forEach(function(e){t[e]&&t[e].destroy&&t[e].destroy(),t[e]=null}),[this.handles].forEach(function(t){ci(t)}),this.baseSeries.forEach(function(t){t.navigatorSeries=void 0}),this.navigatorEnabled=!1},t}(),cg={chart:{height:70,margin:[0,5,0,5]},exporting:{enabled:!1},legend:{enabled:!1},navigator:{enabled:!1},plotOptions:{series:{states:{hover:{enabled:!1}},marker:{enabled:!1}}},scrollbar:{enabled:!1},title:{text:""},tooltip:{enabled:!1},xAxis:{visible:!1},yAxis:{height:0,visible:!1}},cv=tT.merge,cm=tT.addEvent,cy=tT.fireEvent,cx=tT.pick,cb=function(){function t(t,e){this.boundAxes=[],this.userOptions=e,this.chartOptions=cv(tt.getOptions(),cg,e.chart,{navigator:e}),this.chartOptions.chart&&e.height&&(this.chartOptions.chart.height=e.height);var i=new av(t,this.chartOptions);i.options=cv(i.options,{navigator:{enabled:!0},scrollbar:{enabled:!0}}),this.chartOptions.navigator&&this.chartOptions.scrollbar&&(this.chartOptions.navigator.enabled=!0,this.chartOptions.scrollbar.enabled=!0),this.navigator=new cf(i),i.navigator=this.navigator,this.initNavigator()}return t.navigator=function(e,i){var o=new t(e,i);return tt.navigators?tt.navigators.push(o):tt.navigators=[o],o},t.prototype.bind=function(t,e){var i=this;void 0===e&&(e=!0);var o=this,r=t instanceof av?t.xAxis[0]:t;if(r instanceof rp){var s=this.navigator.xAxis,n=s.min,a=s.max,h=[];if(e){var l=cm(r,"setExtremes",function(t){("pan"===t.trigger||"zoom"===t.trigger||"mousewheel"===t.trigger)&&o.setRange(t.min,t.max,!0,"pan"!==t.trigger&&"mousewheel"!==t.trigger,{trigger:r})});h.push(l)}var d=cm(this.navigator,"setRange",function(t){r.setExtremes(t.min,t.max,t.redraw,t.animation)});h.push(d);var c=this.boundAxes.filter(function(t){return t.axis===r})[0];c||(c={axis:r,callbacks:[]},this.boundAxes.push(c)),c.callbacks=h,r.series.forEach(function(t){t.options.showInNavigator&&o.addSeries(t.options)}),r.setExtremes(n,a),cm(r,"destroy",function(t){t.keepEvents||i.unbind(r)})}},t.prototype.unbind=function(t){if(!t){this.boundAxes.forEach(function(t){t.callbacks.forEach(function(t){return t()})}),this.boundAxes.length=0;return}for(var e=t instanceof rp?t:t.xAxis[0],i=this.boundAxes.length-1;i>=0;i--)this.boundAxes[i].axis===e&&(this.boundAxes[i].callbacks.forEach(function(t){return t()}),this.boundAxes.splice(i,1))},t.prototype.destroy=function(){this.boundAxes.forEach(function(t){t.callbacks.forEach(function(t){return t()})}),this.boundAxes.length=0,this.navigator.destroy(),this.navigator.chart.destroy()},t.prototype.update=function(t,e){this.chartOptions=cv(this.chartOptions,t.height&&{chart:{height:t.height}},t.chart,{navigator:t}),this.navigator.chart.update(this.chartOptions,e)},t.prototype.redraw=function(){this.navigator.chart.redraw()},t.prototype.addSeries=function(t){this.navigator.chart.addSeries(cv(t,{showInNavigator:cx(t.showInNavigator,!0)})),this.navigator.setBaseSeries()},t.prototype.initNavigator=function(){var t,e=this.navigator;e.top=1,e.xAxis.setScale(),e.yAxis.setScale(),e.xAxis.render(),e.yAxis.render(),null===(t=e.series)||void 0===t||t.forEach(function(t){t.translate(),t.render(),t.redraw()});var i=this.getInitialExtremes(),o=i.min,r=i.max;e.chart.xAxis[0].userMin=o,e.chart.xAxis[0].userMax=r,e.render(o,r)},t.prototype.getRange=function(){var t=this.navigator.chart.xAxis[0].getExtremes(),e=t.min,i=t.max,o=this.navigator.xAxis.getExtremes(),r=o.userMin,s=o.userMax,n=o.min,a=o.max;return{min:cx(e,n),max:cx(i,a),dataMin:n,dataMax:a,userMin:r,userMax:s}},t.prototype.setRange=function(t,e,i,o,r){cy(this.navigator,"setRange",{min:t,max:e,redraw:i,animation:o,eventArguments:cv(r,{trigger:"navigator"})})},t.prototype.getInitialExtremes=function(){var t=this.navigator.xAxis.getExtremes();return{min:t.min,max:t.max}},t}();tt.StandaloneNavigator=tt.StandaloneNavigator||cb,tt.navigator=tt.StandaloneNavigator.navigator,dF(tt.Chart,tt.Axis,tt.Series);var ck=tt;return Q.default}()});