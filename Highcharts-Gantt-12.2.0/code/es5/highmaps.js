!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("highcharts/highcharts",[],e):"object"==typeof exports?exports.highcharts=e():(t.Highcharts&&t.Highcharts.error(16,!0),t.Highcharts=e())}(this,function(){return function(){"use strict";var t,e,i,o,r,n,s,a,l,h,c,d,p,u,f,g,v,m,y,x,b,w,M,k,S,T,C,A,P,O,L,E,I,D,B,z,j,N,R,W,X,_,F,Y,G,H,V,U,Z,q,K,$,J,Q,tt,te,ti,to,tr,tn,ts,ta,tl,th,tc,td,tp,tu,tf,tg,tv,tm,ty={};ty.d=function(t,e){for(var i in e)ty.o(e,i)&&!ty.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},ty.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var tx={};ty.d(tx,{default:function(){return fc}}),(t=V||(V={})).SVG_NS="http://www.w3.org/2000/svg",t.product="Highcharts",t.version="12.2.0",t.win="undefined"!=typeof window?window:{},t.doc=t.win.document,t.svg=!!(null===(i=null===(e=null===t.doc||void 0===t.doc?void 0:t.doc.createElementNS)||void 0===e?void 0:e.call(t.doc,t.SVG_NS,"svg"))||void 0===i?void 0:i.createSVGRect),t.pageLang=null===(r=null===(o=null===t.doc||void 0===t.doc?void 0:t.doc.documentElement)||void 0===o?void 0:o.closest("[lang]"))||void 0===r?void 0:r.lang,t.userAgent=(null===(n=t.win.navigator)||void 0===n?void 0:n.userAgent)||"",t.isChrome=t.win.chrome,t.isFirefox=-1!==t.userAgent.indexOf("Firefox"),t.isMS=/(edge|msie|trident)/i.test(t.userAgent)&&!t.win.opera,t.isSafari=!t.isChrome&&-1!==t.userAgent.indexOf("Safari"),t.isTouchDevice=/(Mobile|Android|Windows Phone)/.test(t.userAgent),t.isWebKit=-1!==t.userAgent.indexOf("AppleWebKit"),t.deg2rad=2*Math.PI/360,t.marginNames=["plotTop","marginRight","marginBottom","plotLeft"],t.noop=function(){},t.supportsPassiveEvents=function(){var e=!1;if(!t.isMS){var i=Object.defineProperty({},"passive",{get:function(){e=!0}});t.win.addEventListener&&t.win.removeEventListener&&(t.win.addEventListener("testPassive",t.noop,i),t.win.removeEventListener("testPassive",t.noop,i))}return e}(),t.charts=[],t.composed=[],t.dateFormats={},t.seriesTypes={},t.symbolSizes={},t.chartCount=0;var tb=V,tw=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},tM=tb.charts,tk=tb.doc,tS=tb.win;function tT(t,e,i,o){var r=e?"Highcharts error":"Highcharts warning";32===t&&(t=""+r+": Deprecated member");var n=tI(t),s=n?""+r+" #"+t+": www.highcharts.com/errors/"+t+"/":t.toString();if(void 0!==o){var a="";n&&(s+="?"),tF(o,function(t,e){a+="\n - ".concat(e,": ").concat(t),n&&(s+=encodeURI(e)+"="+encodeURI(t))}),s+=a}tG(tb,"displayError",{chart:i,code:t,message:s,params:o},function(){if(e)throw Error(s);tS.console&&-1===tT.messages.indexOf(s)&&console.warn(s)}),tT.messages.push(s)}function tC(t,e){return parseInt(t,e||10)}function tA(t){return"string"==typeof t}function tP(t){var e=Object.prototype.toString.call(t);return"[object Array]"===e||"[object Array Iterator]"===e}function tO(t,e){return!!t&&"object"==typeof t&&(!e||!tP(t))}function tL(t){return tO(t)&&"number"==typeof t.nodeType}function tE(t){var e=null==t?void 0:t.constructor;return!!(tO(t,!0)&&!tL(t)&&(null==e?void 0:e.name)&&"Object"!==e.name)}function tI(t){return"number"==typeof t&&!isNaN(t)&&t<1/0&&t>-1/0}function tD(t){return null!=t}function tB(t,e,i){var o,r=tA(e)&&!tD(i),n=function(e,i){tD(e)?t.setAttribute(i,e):r?(o=t.getAttribute(i))||"class"!==i||(o=t.getAttribute(i+"Name")):t.removeAttribute(i)};return tA(e)?n(i,e):tF(e,n),o}function tz(t){return tP(t)?t:[t]}function tj(t,e){var i;for(i in t||(t={}),e)t[i]=e[i];return t}function tN(){for(var t=arguments,e=t.length,i=0;i<e;i++){var o=t[i];if(null!=o)return o}}function tR(t,e){tj(t.style,e)}function tW(t){return Math.pow(10,Math.floor(Math.log(t)/Math.LN10))}function tX(t,e){return t>1e14?t:parseFloat(t.toPrecision(e||14))}(tT||(tT={})).messages=[],Math.easeInOutSine=function(t){return -.5*(Math.cos(Math.PI*t)-1)};var t_=Array.prototype.find?function(t,e){return t.find(e)}:function(t,e){var i,o=t.length;for(i=0;i<o;i++)if(e(t[i],i))return t[i]};function tF(t,e,i){for(var o in t)Object.hasOwnProperty.call(t,o)&&e.call(i||t[o],t[o],o,t)}function tY(t,e,i){function o(e,i){var o=t.removeEventListener;o&&o.call(t,e,i,!1)}function r(i){var r,n;t.nodeName&&(e?(r={})[e]=!0:r=i,tF(r,function(t,e){if(i[e])for(n=i[e].length;n--;)o(e,i[e][n].fn)}))}var n="function"==typeof t&&t.prototype||t;if(Object.hasOwnProperty.call(n,"hcEvents")){var s=n.hcEvents;if(e){var a=s[e]||[];i?(s[e]=a.filter(function(t){return i!==t.fn}),o(e,i)):(r(s),s[e]=[])}else r(s),delete n.hcEvents}}function tG(t,e,i,o){if(i=i||{},(null==tk?void 0:tk.createEvent)&&(t.dispatchEvent||t.fireEvent&&t!==tb)){var r=tk.createEvent("Events");r.initEvent(e,!0,!0),i=tj(r,i),t.dispatchEvent?t.dispatchEvent(i):t.fireEvent(e,i)}else if(t.hcEvents){i.target||tj(i,{preventDefault:function(){i.defaultPrevented=!0},target:t,type:e});for(var n=[],s=t,a=!1;s.hcEvents;)Object.hasOwnProperty.call(s,"hcEvents")&&s.hcEvents[e]&&(n.length&&(a=!0),n.unshift.apply(n,s.hcEvents[e])),s=Object.getPrototypeOf(s);a&&n.sort(function(t,e){return t.order-e.order}),n.forEach(function(e){!1===e.fn.call(t,i)&&i.preventDefault()})}o&&!i.defaultPrevented&&o.call(t,i)}var tH=(s=Math.random().toString(36).substring(2,9)+"-",a=0,function(){return"highcharts-"+(U?"":s)+a++});tS.jQuery&&(tS.jQuery.fn.highcharts=function(){var t=[].slice.call(arguments);if(this[0])return t[0]?(new tb[tA(t[0])?t.shift():"Chart"](this[0],t[0],t[1]),this):tM[tB(this[0],"data-highcharts-chart")]});var tV={addEvent:function(t,e,i,o){void 0===o&&(o={});var r="function"==typeof t&&t.prototype||t;Object.hasOwnProperty.call(r,"hcEvents")||(r.hcEvents={});var n=r.hcEvents;tb.Point&&t instanceof tb.Point&&t.series&&t.series.chart&&(t.series.chart.runTrackerClick=!0);var s=t.addEventListener;s&&s.call(t,e,i,!!tb.supportsPassiveEvents&&{passive:void 0===o.passive?-1!==e.indexOf("touch"):o.passive,capture:!1}),n[e]||(n[e]=[]);var a={fn:i,order:"number"==typeof o.order?o.order:1/0};return n[e].push(a),n[e].sort(function(t,e){return t.order-e.order}),function(){tY(t,e,i)}},arrayMax:function(t){for(var e=t.length,i=t[0];e--;)t[e]>i&&(i=t[e]);return i},arrayMin:function(t){for(var e=t.length,i=t[0];e--;)t[e]<i&&(i=t[e]);return i},attr:tB,clamp:function(t,e,i){return t>e?t<i?t:i:e},clearTimeout:function(t){tD(t)&&clearTimeout(t)},correctFloat:tX,createElement:function(t,e,i,o,r){var n=tk.createElement(t);return e&&tj(n,e),r&&tR(n,{padding:"0",border:"none",margin:"0"}),i&&tR(n,i),o&&o.appendChild(n),n},crisp:function(t,e,i){void 0===e&&(e=0);var o=e%2/2,r=i?-1:1;return(Math.round(t*r-o)+o)*r},css:tR,defined:tD,destroyObjectProperties:function(t,e,i){tF(t,function(o,r){o!==e&&(null==o?void 0:o.destroy)&&o.destroy(),((null==o?void 0:o.destroy)||!i)&&delete t[r]})},diffObjects:function(t,e,i,o){var r={};return!function t(e,r,n,s){var a=i?r:e;tF(e,function(i,l){if(!s&&o&&o.indexOf(l)>-1&&r[l]){i=tz(i),n[l]=[];for(var h=0;h<Math.max(i.length,r[l].length);h++)r[l][h]&&(void 0===i[h]?n[l][h]=r[l][h]:(n[l][h]={},t(i[h],r[l][h],n[l][h],s+1)))}else tO(i,!0)&&!i.nodeType?(n[l]=tP(i)?[]:{},t(i,r[l]||{},n[l],s+1),0===Object.keys(n[l]).length&&("colorAxis"!==l||0!==s)&&delete n[l]):(e[l]!==r[l]||l in e&&!(l in r))&&"__proto__"!==l&&"constructor"!==l&&(n[l]=a[l])})}(t,e,r,0),r},discardElement:function(t){var e;null===(e=null==t?void 0:t.parentElement)||void 0===e||e.removeChild(t)},erase:function(t,e){for(var i=t.length;i--;)if(t[i]===e){t.splice(i,1);break}},error:tT,extend:tj,extendClass:function(t,e){var i=function(){};return i.prototype=new t,tj(i.prototype,e),i},find:t_,fireEvent:tG,getAlignFactor:function(t){return void 0===t&&(t=""),({center:.5,right:1,middle:.5,bottom:1})[t]||0},getClosestDistance:function(t,e){var i,o,r,n,s=!e;return t.forEach(function(t){if(t.length>1)for(n=o=t.length-1;n>0;n--)(r=t[n]-t[n-1])<0&&!s?(null==e||e(),e=void 0):r&&(void 0===i||r<i)&&(i=r)}),i},getMagnitude:tW,getNestedProperty:function(t,e){for(var i=t.split(".");i.length&&tD(e);){var o=i.shift();if(void 0===o||"__proto__"===o)return;if("this"===o){var r=void 0;return tO(e)&&(r=e["@this"]),null!=r?r:e}var n=e[o.replace(/[\\'"]/g,"")];if(!tD(n)||"function"==typeof n||"number"==typeof n.nodeType||n===tS)return;e=n}return e},getStyle:function t(e,i,o){if("width"===i){var r,n,s=Math.min(e.offsetWidth,e.scrollWidth),a=null===(r=e.getBoundingClientRect)||void 0===r?void 0:r.call(e).width;return a<s&&a>=s-1&&(s=Math.floor(a)),Math.max(0,s-(t(e,"padding-left",!0)||0)-(t(e,"padding-right",!0)||0))}if("height"===i)return Math.max(0,Math.min(e.offsetHeight,e.scrollHeight)-(t(e,"padding-top",!0)||0)-(t(e,"padding-bottom",!0)||0));var l=tS.getComputedStyle(e,void 0);return l&&(n=l.getPropertyValue(i),tN(o,"opacity"!==i)&&(n=tC(n))),n},insertItem:function(t,e){var i,o=t.options.index,r=e.length;for(i=t.options.isInternal?r:0;i<r+1;i++)if(!e[i]||tI(o)&&o<tN(e[i].options.index,e[i]._i)||e[i].options.isInternal){e.splice(i,0,t);break}return i},isArray:tP,isClass:tE,isDOMElement:tL,isFunction:function(t){return"function"==typeof t},isNumber:tI,isObject:tO,isString:tA,merge:function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];var o,r=tw([t],e,!0),n={},s=function(t,e){return"object"!=typeof t&&(t={}),tF(e,function(i,o){"__proto__"!==o&&"constructor"!==o&&(!tO(i,!0)||tE(i)||tL(i)?t[o]=e[o]:t[o]=s(t[o]||{},i))}),t};!0===t&&(n=r[1],r=Array.prototype.slice.call(r,2));var a=r.length;for(o=0;o<a;o++)n=s(n,r[o]);return n},normalizeTickInterval:function(t,e,i,o,r){var n,s=t;i=tN(i,tW(t));var a=t/i;for(!e&&(e=r?[1,1.2,1.5,2,2.5,3,4,5,6,8,10]:[1,2,2.5,5,10],!1===o&&(1===i?e=e.filter(function(t){return t%1==0}):i<=.1&&(e=[1/i]))),n=0;n<e.length&&(s=e[n],(!r||!(s*i>=t))&&(r||!(a<=(e[n]+(e[n+1]||e[n]))/2)));n++);return tX(s*i,-Math.round(Math.log(.001)/Math.LN10))},objectEach:tF,offset:function(t){var e=tk.documentElement,i=t.parentElement||t.parentNode?t.getBoundingClientRect():{top:0,left:0,width:0,height:0};return{top:i.top+(tS.pageYOffset||e.scrollTop)-(e.clientTop||0),left:i.left+(tS.pageXOffset||e.scrollLeft)-(e.clientLeft||0),width:i.width,height:i.height}},pad:function(t,e,i){return Array((e||2)+1-String(t).replace("-","").length).join(i||"0")+t},pick:tN,pInt:tC,pushUnique:function(t,e){return 0>t.indexOf(e)&&!!t.push(e)},relativeLength:function(t,e,i){return/%$/.test(t)?e*parseFloat(t)/100+(i||0):parseFloat(t)},removeEvent:tY,replaceNested:function(t){for(var e,i,o=[],r=1;r<arguments.length;r++)o[r-1]=arguments[r];do{e=t;for(var n=0;n<o.length;n++)i=o[n],t=t.replace(i[0],i[1])}while(t!==e);return t},splat:tz,stableSort:function(t,e){var i,o,r=t.length;for(o=0;o<r;o++)t[o].safeI=o;for(t.sort(function(t,o){return 0===(i=e(t,o))?t.safeI-o.safeI:i}),o=0;o<r;o++)delete t[o].safeI},syncTimeout:function(t,e,i){return e>0?setTimeout(t,e,i):(t.call(0,i),-1)},timeUnits:{millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5,week:6048e5,month:24192e5,year:314496e5},ucfirst:function(t){return tA(t)?t.substring(0,1).toUpperCase()+t.substring(1):String(t)},uniqueKey:tH,useSerialIds:function(t){return U=tN(t,U)},wrap:function(t,e,i){var o=t[e];t[e]=function(){var t=arguments,e=this;return i.apply(this,[function(){return o.apply(e,arguments.length?arguments:t)}].concat([].slice.call(arguments)))}}},tU=tb.pageLang,tZ=tb.win,tq=tV.defined,tK=tV.error,t$=tV.extend,tJ=tV.isNumber,tQ=tV.isObject,t0=tV.isString,t1=tV.merge,t2=tV.objectEach,t3=tV.pad,t6=tV.splat,t5=tV.timeUnits,t9=tV.ucfirst,t8=tb.isSafari&&tZ.Intl&&!tZ.Intl.DateTimeFormat.prototype.formatRange,t4=function(){function t(t,e){this.options={timezone:"UTC"},this.variableTimezone=!1,this.Date=tZ.Date,this.update(t),this.lang=e}return t.prototype.update=function(t){var e=this;void 0===t&&(t={}),this.dTLCache={},this.options=t=t1(!0,this.options,t);var i=t.timezoneOffset,o=t.useUTC;this.Date=t.Date||tZ.Date||Date;var r=t.timezone;tq(o)&&(r=o?"UTC":void 0),i&&i%60==0&&(r="Etc/GMT"+(i>0?"+":"")+i/60),this.variableTimezone="UTC"!==r&&(null==r?void 0:r.indexOf("Etc/GMT"))!==0,this.timezone=r,["months","shortMonths","weekdays","shortWeekdays"].forEach(function(t){var i=/months/i.test(t),o=/short/.test(t),r={timeZone:"UTC"};r[i?"month":"weekday"]=o?"short":"long",e[t]=(i?[0,1,2,3,4,5,6,7,8,9,10,11]:[3,4,5,6,7,8,9]).map(function(t){return e.dateFormat(r,(i?31:1)*24*36e5*t)})})},t.prototype.toParts=function(t){var e=this.dateTimeFormat({weekday:"narrow",day:"numeric",month:"numeric",year:"numeric",hour:"numeric",minute:"numeric",second:"numeric"},t,"es").split(/(?:, | |\/|:)/g),i=e[0],o=e[1],r=e[2];return[e[3],+r-1,o,e[4],e[5],e[6],Math.floor(Number(t)||0)%1e3,"DLMXJVS".indexOf(i)].map(Number)},t.prototype.dateTimeFormat=function(t,e,i){void 0===i&&(i=this.options.locale||tU);var o,r=JSON.stringify(t)+i;t0(t)&&(t=this.str2dtf(t));var n=this.dTLCache[r];if(!n){null!==(o=t.timeZone)&&void 0!==o||(t.timeZone=this.timezone);try{n=new Intl.DateTimeFormat(i,t)}catch(e){/Invalid time zone/i.test(e.message)?(tK(34),t.timeZone="UTC",n=new Intl.DateTimeFormat(i,t)):tK(e.message,!1)}}return this.dTLCache[r]=n,(null==n?void 0:n.format(e))||""},t.prototype.str2dtf=function(t,e){void 0===e&&(e={});var i={L:{fractionalSecondDigits:3},S:{second:"2-digit"},M:{minute:"numeric"},H:{hour:"2-digit"},k:{hour:"numeric"},E:{weekday:"narrow"},a:{weekday:"short"},A:{weekday:"long"},d:{day:"2-digit"},e:{day:"numeric"},b:{month:"short"},B:{month:"long"},m:{month:"2-digit"},o:{month:"numeric"},y:{year:"2-digit"},Y:{year:"numeric"}};return Object.keys(i).forEach(function(o){-1!==t.indexOf(o)&&t$(e,i[o])}),e},t.prototype.makeTime=function(t,e,i,o,r,n,s){void 0===i&&(i=1),void 0===o&&(o=0);var a=this.Date.UTC(t,e,i,o,r||0,n||0,s||0);if("UTC"!==this.timezone){var l=this.getTimezoneOffset(a);if(a+=l,-1!==[2,3,8,9,10,11].indexOf(e)&&(o<5||o>20)){var h=this.getTimezoneOffset(a);l!==h?a+=h-l:l-36e5!==this.getTimezoneOffset(a-36e5)||t8||(a-=36e5)}}return a},t.prototype.parse=function(t){if(!t0(t))return null!=t?t:void 0;var e=(t=t.replace(/\//g,"-").replace(/(GMT|UTC)/,"")).indexOf("Z")>-1||/([+-][0-9]{2}):?[0-9]{2}$/.test(t),i=/^[0-9]{4}-[0-9]{2}(-[0-9]{2}|)$/.test(t);e||i||(t+="Z");var o=Date.parse(t);if(tJ(o))return o+(!e||i?this.getTimezoneOffset(o):0)},t.prototype.getTimezoneOffset=function(t){if("UTC"!==this.timezone){var e=this.dateTimeFormat({timeZoneName:"shortOffset"},t,"en").split(/(GMT|:)/).map(Number),i=(e[0],e[1],e[2]),o=(e[3],e[4]),r=-(36e5*(i+(void 0===o?0:o)/60));if(tJ(r))return r}return 0},t.prototype.dateFormat=function(t,e,i){var o,r=this.lang;if(!tq(e)||isNaN(e))return(null==r?void 0:r.invalidDate)||"";if(t0(t=null!=t?t:"%Y-%m-%d %H:%M:%S"))for(var n=/%\[([a-zA-Z]+)\]/g,s=void 0;s=n.exec(t);)t=t.replace(s[0],this.dateTimeFormat(s[1],e,null==r?void 0:r.locale));if(t0(t)&&-1!==t.indexOf("%")){var a=this,l=this.toParts(e),h=l[0],c=l[1],d=l[2],p=l[3],u=l[4],f=l[5],g=l[6],v=l[7],m=(null==r?void 0:r.weekdays)||this.weekdays,y=(null==r?void 0:r.shortWeekdays)||this.shortWeekdays,x=(null==r?void 0:r.months)||this.months,b=(null==r?void 0:r.shortMonths)||this.shortMonths;t2(t$({a:y?y[v]:m[v].substr(0,3),A:m[v],d:t3(d),e:t3(d,2," "),w:v,v:null!==(o=null==r?void 0:r.weekFrom)&&void 0!==o?o:"",b:b[c],B:x[c],m:t3(c+1),o:c+1,y:h.toString().substr(2,2),Y:h,H:t3(p),k:p,I:t3(p%12||12),l:p%12||12,M:t3(u),p:p<12?"AM":"PM",P:p<12?"am":"pm",S:t3(f),L:t3(g,3)},tb.dateFormats),function(i,o){if(t0(t))for(;-1!==t.indexOf("%"+o);)t=t.replace("%"+o,"function"==typeof i?i.call(a,e):i)})}else if(tQ(t)){var w=(this.getTimezoneOffset(e)||0)/36e5,M=this.timezone||"Etc/GMT"+(w>=0?"+":"")+w,k=t.prefix,S=t.suffix;t=(void 0===k?"":k)+this.dateTimeFormat(t$({timeZone:M},t),e)+(void 0===S?"":S)}return i?t9(t):t},t.prototype.resolveDTLFormat=function(t){return tQ(t,!0)?tQ(t,!0)&&void 0===t.main?{main:t}:t:{main:(t=t6(t))[0],from:t[1],to:t[2]}},t.prototype.getDateFormat=function(t,e,i,o){var r=this.dateFormat("%m-%d %H:%M:%S.%L",e),n="01-01 00:00:00.000",s={millisecond:15,second:12,minute:9,hour:6,day:3},a="millisecond",l=a;for(a in t5){if(t&&t===t5.week&&+this.dateFormat("%w",e)===i&&r.substr(6)===n.substr(6)){a="week";break}if(t&&t5[a]>t){a=l;break}if(s[a]&&r.substr(s[a])!==n.substr(s[a]))break;"week"!==a&&(l=a)}return this.resolveDTLFormat(o[a]).main},t}(),t7=(l=function(t,e){return(l=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),et=tV.defined,ee=tV.extend,ei=tV.timeUnits,eo=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return t7(e,t),e.prototype.getTimeTicks=function(t,e,i,o){var r,n=this,s=[],a={},l=t.count,h=void 0===l?1:l,c=t.unitRange,d=n.toParts(e),p=d[0],u=d[1],f=d[2],g=d[3],v=d[4],m=d[5],y=(e||0)%1e3;if(null!=o||(o=1),et(e)){if(y=c>=ei.second?0:h*Math.floor(y/h),c>=ei.second&&(m=c>=ei.minute?0:h*Math.floor(m/h)),c>=ei.minute&&(v=c>=ei.hour?0:h*Math.floor(v/h)),c>=ei.hour&&(g=c>=ei.day?0:h*Math.floor(g/h)),c>=ei.day&&(f=c>=ei.month?1:Math.max(1,h*Math.floor(f/h))),c>=ei.month&&(u=c>=ei.year?0:h*Math.floor(u/h)),c>=ei.year&&(p-=p%h),c===ei.week){h&&(e=n.makeTime(p,u,f,g,v,m,y));var x=this.dateTimeFormat({timeZone:this.timezone,weekday:"narrow"},e,"es"),b="DLMXJVS".indexOf(x);f+=-b+o+(b<o?-7:0)}e=n.makeTime(p,u,f,g,v,m,y),n.variableTimezone&&et(i)&&(r=i-e>4*ei.month||n.getTimezoneOffset(e)!==n.getTimezoneOffset(i));for(var w=e,M=1;w<i;)s.push(w),c===ei.year?w=n.makeTime(p+M*h,0):c===ei.month?w=n.makeTime(p,u+M*h):r&&(c===ei.day||c===ei.week)?w=n.makeTime(p,u,f+M*h*(c===ei.day?1:7)):r&&c===ei.hour&&h>1?w=n.makeTime(p,u,f,g+M*h):w+=c*h,M++;s.push(w),c<=ei.hour&&s.length<1e4&&s.forEach(function(t){t%18e5==0&&"000000000"===n.dateFormat("%H%M%S%L",t)&&(a[t]="day")})}return s.info=ee(t,{higherRanks:a,totalRange:c*h}),s},e}(t4),er=function(){return(er=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},en=tb.isTouchDevice,es=tV.fireEvent,ea=tV.merge,el={colors:["#2caffe","#544fc5","#00e272","#fe6a35","#6b8abc","#d568fb","#2ee0ca","#fa4b42","#feb56a","#91e8e1"],symbols:["circle","diamond","square","triangle","triangle-down"],lang:{weekFrom:"week from",chartTitle:"Chart title",locale:void 0,loading:"Loading...",months:void 0,seriesName:"Series {add index 1}",shortMonths:void 0,weekdays:void 0,numericSymbols:["k","M","G","T","P","E"],pieSliceName:"Slice",resetZoom:"Reset zoom",yAxisTitle:"Values",resetZoomTitle:"Reset zoom level 1:1"},global:{buttonTheme:{fill:"#f7f7f7",padding:8,r:2,stroke:"#cccccc","stroke-width":1,style:{color:"#333333",cursor:"pointer",fontSize:"0.8em",fontWeight:"normal"},states:{hover:{fill:"#e6e6e6"},select:{fill:"#e6e9ff",style:{color:"#000000",fontWeight:"bold"}},disabled:{style:{color:"#cccccc"}}}}},time:{Date:void 0,timezone:"UTC",timezoneOffset:0,useUTC:void 0},chart:{alignThresholds:!1,panning:{enabled:!1,type:"x"},styledMode:!1,borderRadius:0,colorCount:10,allowMutatingData:!0,ignoreHiddenSeries:!0,spacing:[10,10,15,10],resetZoomButton:{theme:{},position:{}},reflow:!0,type:"line",zooming:{singleTouch:!1,resetButton:{theme:{zIndex:6},position:{align:"right",x:-10,y:10}}},width:null,height:null,borderColor:"#334eff",backgroundColor:"#ffffff",plotBorderColor:"#cccccc"},title:{style:{color:"#333333",fontWeight:"bold"},text:"Chart title",margin:15,minScale:.67},subtitle:{style:{color:"#666666",fontSize:"0.8em"},text:""},caption:{margin:15,style:{color:"#666666",fontSize:"0.8em"},text:"",align:"left",verticalAlign:"bottom"},plotOptions:{},legend:{enabled:!0,align:"center",alignColumns:!0,className:"highcharts-no-tooltip",events:{},layout:"horizontal",itemMarginBottom:2,itemMarginTop:2,labelFormatter:function(){return this.name},borderColor:"#999999",borderRadius:0,navigation:{style:{fontSize:"0.8em"},activeColor:"#0022ff",inactiveColor:"#cccccc"},itemStyle:{color:"#333333",cursor:"pointer",fontSize:"0.8em",textDecoration:"none",textOverflow:"ellipsis"},itemHoverStyle:{color:"#000000"},itemHiddenStyle:{color:"#666666",textDecoration:"line-through"},shadow:!1,itemCheckboxStyle:{position:"absolute",width:"13px",height:"13px"},squareSymbol:!0,symbolPadding:5,verticalAlign:"bottom",x:0,y:0,title:{style:{fontSize:"0.8em",fontWeight:"bold"}}},loading:{labelStyle:{fontWeight:"bold",position:"relative",top:"45%"},style:{position:"absolute",backgroundColor:"#ffffff",opacity:.5,textAlign:"center"}},tooltip:{enabled:!0,animation:{duration:300,easing:function(t){return Math.sqrt(1-Math.pow(t-1,2))}},borderRadius:3,dateTimeLabelFormats:{millisecond:"%[AebHMSL]",second:"%[AebHMS]",minute:"%[AebHM]",hour:"%[AebHM]",day:"%[AebY]",week:"%v %[AebY]",month:"%[BY]",year:"%Y"},footerFormat:"",headerShape:"callout",hideDelay:500,padding:8,position:{x:0,y:3},shared:!1,snap:en?25:10,headerFormat:'<span style="font-size: 0.8em">{ucfirst point.key}</span><br/>',pointFormat:'<span style="color:{point.color}">●</span> {series.name}: <b>{point.y}</b><br/>',backgroundColor:"#ffffff",borderWidth:void 0,stickOnContact:!1,style:{color:"#333333",cursor:"default",fontSize:"0.8em"},useHTML:!1},credits:{enabled:!0,href:"https://www.highcharts.com?credits",position:{align:"right",x:-10,verticalAlign:"bottom",y:-5},style:{cursor:"pointer",color:"#999999",fontSize:"0.6em"},text:"Highcharts.com"}},eh=new eo(el.time,el.lang),ec=function(){return el},ed=function(t){var e;return es(tb,"setOptions",{options:t}),ea(!0,el,t),t.time&&eh.update(el.time),t.lang&&"locale"in t.lang&&eh.update({locale:t.lang.locale}),(null===(e=t.lang)||void 0===e?void 0:e.chartTitle)&&(el.title=er(er({},el.title),{text:t.lang.chartTitle})),el},ep=tb.win,eu=tV.isNumber,ef=tV.isString,eg=tV.merge,ev=tV.pInt,em=tV.defined,ey=function(t,e,i){return"color-mix(in srgb,".concat(t,",").concat(e," ").concat(100*i,"%)")},ex=function(t){return ef(t)&&!!t&&"none"!==t},eb=function(){var t;function e(t){this.rgba=[NaN,NaN,NaN,NaN],this.input=t;var i,o,r,n,s=tb.Color;if(s&&s!==e)return new s(t);if("object"==typeof t&&void 0!==t.stops)this.stops=t.stops.map(function(t){return new e(t[1])});else if("string"==typeof t)for(this.input=t=e.names[t.toLowerCase()]||t,r=e.parsers.length;r--&&!o;)(i=(n=e.parsers[r]).regex.exec(t))&&(o=n.parse(i));o&&(this.rgba=o)}return e.parse=function(t){return t?new e(t):e.None},e.prototype.get=function(t){var e=this.input,i=this.rgba;if(this.output)return this.output;if("object"==typeof e&&void 0!==this.stops){var o=eg(e);return o.stops=[].slice.call(o.stops),this.stops.forEach(function(e,i){o.stops[i]=[o.stops[i][0],e.get(t)]}),o}return i&&eu(i[0])?"rgb"!==t&&(t||1!==i[3])?"a"===t?"".concat(i[3]):"rgba("+i.join(",")+")":"rgb("+i[0]+","+i[1]+","+i[2]+")":e},e.prototype.brighten=function(t){var i=this.rgba;if(this.stops)this.stops.forEach(function(e){e.brighten(t)});else if(eu(t)&&0!==t){if(eu(i[0]))for(var o=0;o<3;o++)i[o]+=ev(255*t),i[o]<0&&(i[o]=0),i[o]>255&&(i[o]=255);else e.useColorMix&&ex(this.input)&&(this.output=ey(this.input,t>0?"white":"black",Math.abs(t)))}return this},e.prototype.setOpacity=function(t){return this.rgba[3]=t,this},e.prototype.tweenTo=function(t,i){var o=this.rgba,r=t.rgba;if(!eu(o[0])||!eu(r[0]))return e.useColorMix&&ex(this.input)&&ex(t.input)&&i<.99?ey(this.input,t.input,i):t.input||"none";var n=1!==r[3]||1!==o[3],s=function(t,e){return t+(o[e]-t)*(1-i)},a=r.slice(0,3).map(s).map(Math.round);return n&&a.push(s(r[3],3)),(n?"rgba(":"rgb(")+a.join(",")+")"},e.names={white:"#ffffff",black:"#000000"},e.parsers=[{regex:/rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d?(?:\.\d+)?)\s*\)/,parse:function(t){return[ev(t[1]),ev(t[2]),ev(t[3]),parseFloat(t[4],10)]}},{regex:/rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)/,parse:function(t){return[ev(t[1]),ev(t[2]),ev(t[3]),1]}},{regex:/^#([a-f0-9])([a-f0-9])([a-f0-9])([a-f0-9])?$/i,parse:function(t){return[ev(t[1]+t[1],16),ev(t[2]+t[2],16),ev(t[3]+t[3],16),em(t[4])?ev(t[4]+t[4],16)/255:1]}},{regex:/^#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})?$/i,parse:function(t){return[ev(t[1],16),ev(t[2],16),ev(t[3],16),em(t[4])?ev(t[4],16)/255:1]}}],e.useColorMix=null===(t=ep.CSS)||void 0===t?void 0:t.supports("color","color-mix(in srgb,red,blue 9%)"),e.None=new e(""),e}(),ew=eb.parse,eM=tb.win,ek=tV.isNumber,eS=tV.objectEach,eT=function(){function t(t,e,i){this.pos=NaN,this.options=e,this.elem=t,this.prop=i}return t.prototype.dSetter=function(){var t=this.paths,e=null==t?void 0:t[0],i=null==t?void 0:t[1],o=this.now||0,r=[];if(1!==o&&e&&i){if(e.length===i.length&&o<1)for(var n=0;n<i.length;n++){for(var s=e[n],a=i[n],l=[],h=0;h<a.length;h++){var c=s[h],d=a[h];ek(c)&&ek(d)&&("A"!==a[0]||4!==h&&5!==h)?l[h]=c+o*(d-c):l[h]=d}r.push(l)}else r=i}else r=this.toD||[];this.elem.attr("d",r,void 0,!0)},t.prototype.update=function(){var t=this.elem,e=this.prop,i=this.now,o=this.options.step;this[e+"Setter"]?this[e+"Setter"]():t.attr?t.element&&t.attr(e,i,null,!0):t.style[e]=i+this.unit,o&&o.call(t,i,this)},t.prototype.run=function(e,i,o){var r=this,n=r.options,s=function(t){return!s.stopped&&r.step(t)},a=eM.requestAnimationFrame||function(t){setTimeout(t,13)},l=function(){for(var e=0;e<t.timers.length;e++)t.timers[e]()||t.timers.splice(e--,1);t.timers.length&&a(l)};e!==i||this.elem["forceAnimate:"+this.prop]?(this.startTime=+new Date,this.start=e,this.end=i,this.unit=o,this.now=this.start,this.pos=0,s.elem=this.elem,s.prop=this.prop,s()&&1===t.timers.push(s)&&a(l)):(delete n.curAnim[this.prop],n.complete&&0===Object.keys(n.curAnim).length&&n.complete.call(this.elem))},t.prototype.step=function(t){var e,i,o=+new Date,r=this.options,n=this.elem,s=r.complete,a=r.duration,l=r.curAnim;return n.attr&&!n.element?e=!1:t||o>=a+this.startTime?(this.now=this.end,this.pos=1,this.update(),l[this.prop]=!0,i=!0,eS(l,function(t){!0!==t&&(i=!1)}),i&&s&&s.call(n),e=!1):(this.pos=r.easing((o-this.startTime)/a),this.now=this.start+(this.end-this.start)*this.pos,this.update(),e=!0),e},t.prototype.initPath=function(t,e,i){var o,r,n,s,a=t.startX,l=t.endX,h=i.slice(),c=t.isArea,d=c?2:1,p=e&&i.length>e.length&&i.hasStackedCliffs,u=null==e?void 0:e.slice();if(!u||p)return[h,h];function f(t,e){for(;t.length<r;){var i=t[0],o=e[r-t.length];if(o&&"M"===i[0]&&("C"===o[0]?t[0]=["C",i[1],i[2],i[1],i[2],i[1],i[2]]:t[0]=["L",i[1],i[2]]),t.unshift(i),c){var n=t.pop();t.push(t[t.length-1],n)}}}function g(t){for(;t.length<r;){var e=t[Math.floor(t.length/d)-1].slice();if("C"===e[0]&&(e[1]=e[5],e[2]=e[6]),c){var i=t[Math.floor(t.length/d)].slice();t.splice(t.length/2,0,e,i)}else t.push(e)}}if(a&&l&&l.length){for(n=0;n<a.length;n++){if(a[n]===l[0]){o=n;break}if(a[0]===l[l.length-a.length+n]){o=n,s=!0;break}if(a[a.length-1]===l[l.length-a.length+n]){o=a.length-n;break}}void 0===o&&(u=[])}return u.length&&ek(o)&&(r=h.length+o*d,s?(f(u,h),g(h)):(f(h,u),g(u))),[u,h]},t.prototype.fillSetter=function(){t.prototype.strokeSetter.apply(this,arguments)},t.prototype.strokeSetter=function(){this.elem.attr(this.prop,ew(this.start).tweenTo(ew(this.end),this.pos),void 0,!0)},t.timers=[],t}(),eC=tV.defined,eA=tV.getStyle,eP=tV.isArray,eO=tV.isNumber,eL=tV.isObject,eE=tV.merge,eI=tV.objectEach,eD=tV.pick;function eB(t){return eL(t)?eE({duration:500,defer:0},t):{duration:500*!!t,defer:0}}function ez(t,e){for(var i=eT.timers.length;i--;)eT.timers[i].elem!==t||e&&e!==eT.timers[i].prop||(eT.timers[i].stopped=!0)}var ej=function(t,e,i){var o,r,n,s,a="";eL(i)||(s=arguments,i={duration:s[2],easing:s[3],complete:s[4]}),eO(i.duration)||(i.duration=400),i.easing="function"==typeof i.easing?i.easing:Math[i.easing]||Math.easeInOutSine,i.curAnim=eE(e),eI(e,function(s,l){ez(t,l),n=new eT(t,i,l),r=void 0,"d"===l&&eP(e.d)?(n.paths=n.initPath(t,t.pathArray,e.d),n.toD=e.d,o=0,r=1):t.attr?o=t.attr(l):(o=parseFloat(eA(t,l))||0,"opacity"!==l&&(a="px")),r||(r=s),"string"==typeof r&&r.match("px")&&(r=r.replace(/px/g,"")),n.run(o,r,a)})},eN=function(t,e,i){var o=eB(e),r=i?[i]:t.series,n=0,s=0;return r.forEach(function(t){var i=eB(t.options.animation);n=eL(e)&&eC(e.defer)?o.defer:Math.max(n,i.duration+i.defer),s=Math.min(o.duration,i.duration)}),t.renderer.forExport&&(n=0),{defer:Math.max(0,n-s),duration:Math.min(n,s)}},eR=function(t,e){e.renderer.globalAnimation=eD(t,e.options.chart.animation,!0)},eW=tb.SVG_NS,eX=tb.win,e_=tV.attr,eF=tV.createElement,eY=tV.css,eG=tV.error,eH=tV.isFunction,eV=tV.isString,eU=tV.objectEach,eZ=tV.splat,eq=eX.trustedTypes,eK=eq&&eH(eq.createPolicy)&&eq.createPolicy("highcharts",{createHTML:function(t){return t}}),e$=eK?eK.createHTML(""):"",eJ=function(){function t(t){this.nodes="string"==typeof t?this.parseMarkup(t):t}return t.filterUserAttributes=function(e){return eU(e,function(i,o){var r=!0;-1===t.allowedAttributes.indexOf(o)&&(r=!1),-1!==["background","dynsrc","href","lowsrc","src"].indexOf(o)&&(r=eV(i)&&t.allowedReferences.some(function(t){return 0===i.indexOf(t)})),r||(eG(33,!1,void 0,{"Invalid attribute in config":"".concat(o)}),delete e[o]),eV(i)&&e[o]&&(e[o]=i.replace(/</g,"&lt;"))}),e},t.parseStyle=function(t){return t.split(";").reduce(function(t,e){var i=e.split(":").map(function(t){return t.trim()}),o=i.shift();return o&&i.length&&(t[o.replace(/-([a-z])/g,function(t){return t[1].toUpperCase()})]=i.join(":")),t},{})},t.setElementHTML=function(e,i){e.innerHTML=t.emptyHTML,i&&new t(i).addToDOM(e)},t.prototype.addToDOM=function(e){return function e(i,o){var r;return eZ(i).forEach(function(i){var n,s=i.tagName,a=i.textContent?tb.doc.createTextNode(i.textContent):void 0,l=t.bypassHTMLFiltering;if(s){if("#text"===s)n=a;else if(-1!==t.allowedTags.indexOf(s)||l){var h="svg"===s?eW:o.namespaceURI||eW,c=tb.doc.createElementNS(h,s),d=i.attributes||{};eU(i,function(t,e){"tagName"!==e&&"attributes"!==e&&"children"!==e&&"style"!==e&&"textContent"!==e&&(d[e]=t)}),e_(c,l?d:t.filterUserAttributes(d)),i.style&&eY(c,i.style),a&&c.appendChild(a),e(i.children||[],c),n=c}else eG(33,!1,void 0,{"Invalid tagName in config":s})}n&&o.appendChild(n),r=n}),r}(this.nodes,e)},t.prototype.parseMarkup=function(e){var i,o=[];e=e.trim().replace(/ style=(["'])/g," data-style=$1");try{i=new DOMParser().parseFromString(eK?eK.createHTML(e):e,"text/html")}catch(t){}if(!i){var r=eF("div");r.innerHTML=e,i={body:r}}var n=function(e,i){var o=e.nodeName.toLowerCase(),r={tagName:o};"#text"===o&&(r.textContent=e.textContent||"");var s=e.attributes;if(s){var a={};[].forEach.call(s,function(e){"data-style"===e.name?r.style=t.parseStyle(e.value):a[e.name]=e.value}),r.attributes=a}if(e.childNodes.length){var l=[];[].forEach.call(e.childNodes,function(t){n(t,l)}),l.length&&(r.children=l)}i.push(r)};return[].forEach.call(i.body.childNodes,function(t){return n(t,o)}),o},t.allowedAttributes=["alt","aria-controls","aria-describedby","aria-expanded","aria-haspopup","aria-hidden","aria-label","aria-labelledby","aria-live","aria-pressed","aria-readonly","aria-roledescription","aria-selected","class","clip-path","color","colspan","cx","cy","d","dx","dy","disabled","fill","filterUnits","flood-color","flood-opacity","height","href","id","in","in2","markerHeight","markerWidth","offset","opacity","operator","orient","padding","paddingLeft","paddingRight","patternUnits","r","radius","refX","refY","role","scope","slope","src","startOffset","stdDeviation","stroke","stroke-linecap","stroke-width","style","tableValues","result","rowspan","summary","target","tabindex","text-align","text-anchor","textAnchor","textLength","title","type","valign","width","x","x1","x2","xlink:href","y","y1","y2","zIndex"],t.allowedReferences=["https://","http://","mailto:","/","../","./","#"],t.allowedTags=["a","abbr","b","br","button","caption","circle","clipPath","code","dd","defs","div","dl","dt","em","feComponentTransfer","feComposite","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feMorphology","feOffset","feMerge","feMergeNode","filter","h1","h2","h3","h4","h5","h6","hr","i","img","li","linearGradient","marker","ol","p","path","pattern","pre","rect","small","span","stop","strong","style","sub","sup","svg","table","text","textPath","thead","title","tbody","tspan","td","th","tr","u","ul","#text"],t.emptyHTML=e$,t.bypassHTMLFiltering=!1,t}(),eQ=tb.pageLang,e0=tV.extend,e1=tV.getNestedProperty,e2=tV.isArray,e3=tV.isNumber,e6=tV.isObject,e5=tV.isString,e9=tV.pick,e8={add:function(t,e){return t+e},divide:function(t,e){return 0!==e?t/e:""},eq:function(t,e){return t==e},each:function(t){var e=arguments[arguments.length-1];return!!e2(t)&&t.map(function(i,o){return it(e.body,e0(e6(i)?i:{"@this":i},{"@index":o,"@first":0===o,"@last":o===t.length-1}))}).join("")},ge:function(t,e){return t>=e},gt:function(t,e){return t>e},if:function(t){return!!t},le:function(t,e){return t<=e},lt:function(t,e){return t<e},multiply:function(t,e){return t*e},ne:function(t,e){return t!=e},subtract:function(t,e){return t-e},ucfirst:tV.ucfirst,unless:function(t){return!t}},e4={},e7=function(t){return/^["'].+["']$/.test(t)};function it(t,e,i){void 0===t&&(t="");for(var o,r,n,s,a=/\{([a-zA-Z\u00C0-\u017F\d:\.,;\-\/<>\[\]%_@+"'’= #\(\)]+)\}/g,l=/\(([a-zA-Z\u00C0-\u017F\d:\.,;\-\/<>\[\]%_@+"'= ]+)\)/g,h=[],c=/f$/,d=/\.(\d)/,p=(null===(o=null==i?void 0:i.options)||void 0===o?void 0:o.lang)||el.lang,u=(null==i?void 0:i.time)||eh,f=(null==i?void 0:i.numberFormatter)||ie,g=function(t){var i;return void 0===t&&(t=""),"true"===t||"false"!==t&&((i=Number(t)).toString()===t?i:e7(t)?t.slice(1,-1):e1(t,e))},v=0;null!==(r=a.exec(t));){var m=r,y=l.exec(r[1]);y&&(r=y,s=!0),(null==n?void 0:n.isBlock)||(n={ctx:e,expression:r[1],find:r[0],isBlock:"#"===r[1].charAt(0),start:r.index,startInner:r.index+r[0].length,length:r[0].length});var x=(n.isBlock?m:r)[1].split(" ")[0].replace("#","");e8[x]&&(n.isBlock&&x===n.fn&&v++,n.fn||(n.fn=x));var b="else"===r[1];if(n.isBlock&&n.fn&&(r[1]==="/".concat(n.fn)||b)){if(v)!b&&v--;else{var w=n.startInner,M=t.substr(w,r.index-w);void 0===n.body?(n.body=M,n.startInner=r.index+r[0].length):n.elseBody=M,n.find+=M+r[0],b||(h.push(n),n=void 0)}}else n.isBlock||h.push(n);if(y&&!(null==n?void 0:n.isBlock))break}return h.forEach(function(o){var r,n,s=o.body,a=o.elseBody,h=o.expression,v=o.fn;if(v){var m=[o],y=[],x=h.length,b=0,w=void 0;for(n=0;n<=x;n++){var M=h.charAt(n);w||'"'!==M&&"'"!==M?w===M&&(w=""):w=M,w||" "!==M&&n!==x||(y.push(h.substr(b,n-b)),b=n+1)}for(n=e8[v].length;n--;)m.unshift(g(y[n+1]));r=e8[v].apply(e,m),o.isBlock&&"boolean"==typeof r&&(r=it(r?s:a,e,i))}else{var k=e7(h)?[h]:h.split(":");if(r=g(k.shift()||""),k.length&&"number"==typeof r){var S=k.join(":");if(c.test(S)){var T=parseInt((S.match(d)||["","-1"])[1],10);null!==r&&(r=f(r,T,p.decimalPoint,S.indexOf(",")>-1?p.thousandsSep:""))}else r=u.dateFormat(S,r)}l.lastIndex=0,l.test(o.find)&&e5(r)&&(r='"'.concat(r,'"'))}t=t.replace(o.find,e9(r,""))}),s?it(t,e,i):t}function ie(t,e,i,o){e*=1;var r,n,s,a,l=(t=+t||0).toString().split("e").map(Number),h=l[0],c=l[1],d=(null===(r=this===null||void 0===this?void 0:this.options)||void 0===r?void 0:r.lang)||el.lang,p=(t.toString().split(".")[1]||"").split("e")[0].length,u=e,f={};null!=i||(i=d.decimalPoint),null!=o||(o=d.thousandsSep),-1===e?e=Math.min(p,20):e3(e)?e&&c<0&&((a=e+c)>=0?(h=+h.toExponential(a).split("e")[0],e=a):(h=Math.floor(h),t=e<20?+(h*Math.pow(10,c)).toFixed(e):0,c=0)):e=2,c&&(null!=e||(e=2),t=h),e3(e)&&e>=0&&(f.minimumFractionDigits=e,f.maximumFractionDigits=e),""===o&&(f.useGrouping=!1);var g=o||i,v=g?"en":(this===null||void 0===this?void 0:this.locale)||d.locale||eQ,m=JSON.stringify(f)+v;return s=(null!==(n=e4[m])&&void 0!==n?n:e4[m]=new Intl.NumberFormat(v,f)).format(t),g&&(s=s.replace(/([,\.])/g,"_$1").replace(/_\,/g,null!=o?o:",").replace("_.",null!=i?i:".")),(e||0!=+s)&&(!(c<0)||u)||(s="0"),c&&0!=+s&&(s+="e"+(c<0?"":"+")+c),s}var ii={dateFormat:function(t,e,i){return eh.dateFormat(t,e,i)},format:it,helpers:e8,numberFormat:ie};(h=Z||(Z={})).rendererTypes={},h.getRendererType=function(t){return void 0===t&&(t=c),h.rendererTypes[t]||h.rendererTypes[c]},h.registerRendererType=function(t,e,i){h.rendererTypes[t]=e,(!c||i)&&(c=t,tb.Renderer=e)};var io=Z,ir=tV.clamp,is=tV.pick,ia=tV.pushUnique,il=tV.stableSort;(q||(q={})).distribute=function t(e,i,o){var r,n,s,a,l,h,c=e,d=c.reducedLen||i,p=function(t,e){return t.target-e.target},u=[],f=e.length,g=[],v=u.push,m=!0,y=0;for(r=f;r--;)y+=e[r].size;if(y>d){for(il(e,function(t,e){return(e.rank||0)-(t.rank||0)}),s=(h=e[0].rank===e[e.length-1].rank)?f/2:-1,n=h?s:f-1;s&&y>d;)a=e[r=Math.floor(n)],ia(g,r)&&(y-=a.size),n+=s,h&&n>=e.length&&(s/=2,n=s);g.sort(function(t,e){return e-t}).forEach(function(t){return v.apply(u,e.splice(t,1))})}for(il(e,p),e=e.map(function(t){return{size:t.size,targets:[t.target],align:is(t.align,.5)}});m;){for(r=e.length;r--;)a=e[r],l=(Math.min.apply(0,a.targets)+Math.max.apply(0,a.targets))/2,a.pos=ir(l-a.size*a.align,0,i-a.size);for(r=e.length,m=!1;r--;)r>0&&e[r-1].pos+e[r-1].size>e[r].pos&&(e[r-1].size+=e[r].size,e[r-1].targets=e[r-1].targets.concat(e[r].targets),e[r-1].align=.5,e[r-1].pos+e[r-1].size>i&&(e[r-1].pos=i-e[r-1].size),e.splice(r,1),m=!0)}return v.apply(c,u),r=0,e.some(function(e){var n=0;return(e.targets||[]).some(function(){return(c[r].pos=e.pos+n,void 0!==o&&Math.abs(c[r].pos-c[r].target)>o)?(c.slice(0,r+1).forEach(function(t){return delete t.pos}),c.reducedLen=(c.reducedLen||i)-.1*i,c.reducedLen>.1*i&&t(c,i,o),!0):(n+=c[r].size,r++,!1)})}),il(c,p),c};var ih=q,ic=tb.deg2rad,id=tb.doc,ip=tb.svg,iu=tb.SVG_NS,ig=tb.win,iv=tb.isFirefox,im=tV.addEvent,iy=tV.attr,ix=tV.createElement,ib=tV.crisp,iw=tV.css,iM=tV.defined,ik=tV.erase,iS=tV.extend,iT=tV.fireEvent,iC=tV.getAlignFactor,iA=tV.isArray,iP=tV.isFunction,iO=tV.isNumber,iL=tV.isObject,iE=tV.isString,iI=tV.merge,iD=tV.objectEach,iB=tV.pick,iz=tV.pInt,ij=tV.pushUnique,iN=tV.replaceNested,iR=tV.syncTimeout,iW=tV.uniqueKey,iX=function(){function t(t,e){this.onEvents={},this.opacity=1,this.SVG_NS=iu,this.element="span"===e||"body"===e?ix(e):id.createElementNS(this.SVG_NS,e),this.renderer=t,this.styles={},iT(this,"afterInit")}return t.prototype._defaultGetter=function(t){var e=iB(this[t+"Value"],this[t],this.element?this.element.getAttribute(t):null,0);return/^-?[\d\.]+$/.test(e)&&(e=parseFloat(e)),e},t.prototype._defaultSetter=function(t,e,i){i.setAttribute(e,t)},t.prototype.add=function(t){var e,i=this.renderer,o=this.element;return t&&(this.parentGroup=t),void 0!==this.textStr&&"text"===this.element.nodeName&&i.buildText(this),this.added=!0,(!t||t.handleZ||this.zIndex)&&(e=this.zIndexSetter()),e||(t?t.element:i.box).appendChild(o),this.onAdd&&this.onAdd(),this},t.prototype.addClass=function(t,e){var i=e?"":this.attr("class")||"";return(t=(t||"").split(/ /g).reduce(function(t,e){return -1===i.indexOf(e)&&t.push(e),t},i?[i]:[]).join(" "))!==i&&this.attr("class",t),this},t.prototype.afterSetters=function(){this.doTransform&&(this.updateTransform(),this.doTransform=!1)},t.prototype.align=function(t,e,i,o){void 0===o&&(o=!0);var r=this.renderer,n=r.alignedObjects,s=!!t;t?(this.alignOptions=t,this.alignByTranslate=e,this.alignTo=i):(t=this.alignOptions||{},e=this.alignByTranslate,i=this.alignTo);var a=!i||iE(i)?i||"renderer":void 0;a&&(s&&ij(n,this),i=void 0);var l=iB(i,r[a],r),h=(l.x||0)+(t.x||0)+((l.width||0)-(t.width||0))*iC(t.align),c=(l.y||0)+(t.y||0)+((l.height||0)-(t.height||0))*iC(t.verticalAlign),d={"text-align":null==t?void 0:t.align};return d[e?"translateX":"x"]=Math.round(h),d[e?"translateY":"y"]=Math.round(c),o&&(this[this.placed?"animate":"attr"](d),this.placed=!0),this.alignAttr=d,this},t.prototype.alignSetter=function(t){var e={left:"start",center:"middle",right:"end"};e[t]&&(this.alignValue=t,this.element.setAttribute("text-anchor",e[t]))},t.prototype.animate=function(t,e,i){var o=this,r=eB(iB(e,this.renderer.globalAnimation,!0)),n=r.defer;return id.hidden&&(r.duration=0),0!==r.duration?(i&&(r.complete=i),iR(function(){o.element&&ej(o,t,r)},n)):(this.attr(t,void 0,i||r.complete),iD(t,function(t,e){r.step&&r.step.call(this,t,{prop:e,pos:1,elem:this})},this)),this},t.prototype.applyTextOutline=function(t){var e=this.element;-1!==t.indexOf("contrast")&&(t=t.replace(/contrast/g,this.renderer.getContrast(e.style.fill)));var i=t.indexOf(" "),o=t.substring(i+1),r=t.substring(0,i);if(r&&"none"!==r&&tb.svg){this.fakeTS=!0,r=r.replace(/(^[\d\.]+)(.*?)$/g,function(t,e,i){return 2*Number(e)+i}),this.removeTextOutline();var n=id.createElementNS(iu,"tspan");iy(n,{class:"highcharts-text-outline",fill:o,stroke:o,"stroke-width":r,"stroke-linejoin":"round"});var s=e.querySelector("textPath")||e;[].forEach.call(s.childNodes,function(t){var e=t.cloneNode(!0);e.removeAttribute&&["fill","stroke","stroke-width","stroke"].forEach(function(t){return e.removeAttribute(t)}),n.appendChild(e)});var a=0;[].forEach.call(s.querySelectorAll("text tspan"),function(t){a+=Number(t.getAttribute("dy"))});var l=id.createElementNS(iu,"tspan");l.textContent="​",iy(l,{x:Number(e.getAttribute("x")),dy:-a}),n.appendChild(l),s.insertBefore(n,s.firstChild)}},t.prototype.attr=function(e,i,o,r){var n,s,a,l=this.element,h=t.symbolCustomAttribs,c=this;return"string"==typeof e&&void 0!==i&&(n=e,(e={})[n]=i),"string"==typeof e?c=(this[e+"Getter"]||this._defaultGetter).call(this,e,l):(iD(e,function(t,i){a=!1,r||ez(this,i),this.symbolName&&-1!==h.indexOf(i)&&(s||(this.symbolAttr(e),s=!0),a=!0),this.rotation&&("x"===i||"y"===i)&&(this.doTransform=!0),a||(this[i+"Setter"]||this._defaultSetter).call(this,t,i,l)},this),this.afterSetters()),o&&o.call(this),c},t.prototype.clip=function(t){if(t&&!t.clipPath){var e=iW()+"-",i=this.renderer.createElement("clipPath").attr({id:e}).add(this.renderer.defs);iS(t,{clipPath:i,id:e,count:0}),t.add(i)}return this.attr("clip-path",t?"url(".concat(this.renderer.url,"#").concat(t.id,")"):"none")},t.prototype.crisp=function(t,e){e=Math.round(e||t.strokeWidth||0);var i=t.x||this.x||0,o=t.y||this.y||0,r=(t.width||this.width||0)+i,n=(t.height||this.height||0)+o,s=ib(i,e),a=ib(o,e);return iS(t,{x:s,y:a,width:ib(r,e)-s,height:ib(n,e)-a}),iM(t.strokeWidth)&&(t.strokeWidth=e),t},t.prototype.complexColor=function(t,e,i){var o,r,n,s,a,l,h,c,d,p,u,f=this.renderer,g=[];iT(this.renderer,"complexColor",{args:arguments},function(){if(t.radialGradient?r="radialGradient":t.linearGradient&&(r="linearGradient"),r){if(n=t[r],a=f.gradients,l=t.stops,d=i.radialReference,iA(n)&&(t[r]=n={x1:n[0],y1:n[1],x2:n[2],y2:n[3],gradientUnits:"userSpaceOnUse"}),"radialGradient"===r&&d&&!iM(n.gradientUnits)&&(s=n,n=iI(n,f.getRadialAttr(d,s),{gradientUnits:"userSpaceOnUse"})),iD(n,function(t,e){"id"!==e&&g.push(e,t)}),iD(l,function(t){g.push(t)}),a[g=g.join(",")])p=a[g].attr("id");else{n.id=p=iW();var v=a[g]=f.createElement(r).attr(n).add(f.defs);v.radAttr=s,v.stops=[],l.forEach(function(t){0===t[1].indexOf("rgba")?(h=(o=eb.parse(t[1])).get("rgb"),c=o.get("a")):(h=t[1],c=1);var e=f.createElement("stop").attr({offset:t[0],"stop-color":h,"stop-opacity":c}).add(v);v.stops.push(e)})}u="url("+f.url+"#"+p+")",i.setAttribute(e,u),i.gradient=g,t.toString=function(){return u}}})},t.prototype.css=function(t){var e,i=this.styles,o={},r=this.element,n=!i;if(i&&iD(t,function(t,e){i&&i[e]!==t&&(o[e]=t,n=!0)}),n){i&&(t=iS(i,o)),null===t.width||"auto"===t.width?delete this.textWidth:"text"===r.nodeName.toLowerCase()&&t.width&&(e=this.textWidth=iz(t.width)),iS(this.styles,t),e&&!ip&&this.renderer.forExport&&delete t.width;var s=iv&&t.fontSize||null;s&&(iO(s)||/^\d+$/.test(s))&&(t.fontSize+="px");var a=iI(t);r.namespaceURI===this.SVG_NS&&(["textOutline","textOverflow","whiteSpace","width"].forEach(function(t){return a&&delete a[t]}),a.color&&(a.fill=a.color,delete a.color)),iw(r,a)}return this.added&&("text"===this.element.nodeName&&this.renderer.buildText(this),t.textOutline&&this.applyTextOutline(t.textOutline)),this},t.prototype.dashstyleSetter=function(t){var e,i=this["stroke-width"];if("inherit"===i&&(i=1),t){var o=(t=t.toLowerCase()).replace("shortdashdotdot","3,1,1,1,1,1,").replace("shortdashdot","3,1,1,1").replace("shortdot","1,1,").replace("shortdash","3,1,").replace("longdash","8,3,").replace(/dot/g,"1,3,").replace("dash","4,3,").replace(/,$/,"").split(",");for(e=o.length;e--;)o[e]=""+iz(o[e])*iB(i,NaN);t=o.join(",").replace(/NaN/g,"none"),this.element.setAttribute("stroke-dasharray",t)}},t.prototype.destroy=function(){var t,e,i=this,o=i.element||{},r=i.renderer,n=o.ownerSVGElement,s="SPAN"===o.nodeName&&i.parentGroup||void 0;if(o.onclick=o.onmouseout=o.onmouseover=o.onmousemove=o.point=null,ez(i),i.clipPath&&n){var a=i.clipPath;[].forEach.call(n.querySelectorAll("[clip-path],[CLIP-PATH]"),function(t){t.getAttribute("clip-path").indexOf(a.element.id)>-1&&t.removeAttribute("clip-path")}),i.clipPath=a.destroy()}if(i.stops){for(e=0;e<i.stops.length;e++)i.stops[e].destroy();i.stops.length=0,i.stops=void 0}for(i.safeRemoveChild(o);(null==s?void 0:s.div)&&0===s.div.childNodes.length;)t=s.parentGroup,i.safeRemoveChild(s.div),delete s.div,s=t;i.alignOptions&&ik(r.alignedObjects,i),iD(i,function(t,e){var o,r,n;((null===(o=i[e])||void 0===o?void 0:o.parentGroup)===i||-1!==["connector","foreignObject"].indexOf(e))&&(null===(n=null===(r=i[e])||void 0===r?void 0:r.destroy)||void 0===n||n.call(r)),delete i[e]})},t.prototype.dSetter=function(t,e,i){iA(t)&&("string"==typeof t[0]&&(t=this.renderer.pathToSegments(t)),this.pathArray=t,t=t.reduce(function(t,e,i){return(null==e?void 0:e.join)?(i?t+" ":"")+e.join(" "):(e||"").toString()},"")),/(NaN| {2}|^$)/.test(t)&&(t="M 0 0"),this[e]!==t&&(i.setAttribute(e,t),this[e]=t)},t.prototype.fillSetter=function(t,e,i){"string"==typeof t?i.setAttribute(e,t):t&&this.complexColor(t,e,i)},t.prototype.hrefSetter=function(t,e,i){i.setAttributeNS("http://www.w3.org/1999/xlink",e,t)},t.prototype.getBBox=function(e,i){var o,r,n,s,a=this.alignValue,l=this.element,h=this.renderer,c=this.styles,d=this.textStr,p=h.cache,u=h.cacheKeys,f=l.namespaceURI===this.SVG_NS,g=iB(i,this.rotation,0),v=h.styledMode?l&&t.prototype.getStyle.call(l,"font-size"):c.fontSize;if(iM(d)&&(-1===(s=d.toString()).indexOf("<")&&(s=s.replace(/\d/g,"0")),s+=["",h.rootFontSize,v,g,this.textWidth,a,c.lineClamp,c.textOverflow,c.fontWeight].join(",")),s&&!e&&(o=p[s]),!o||o.polygon){if(f||h.forExport){try{n=this.fakeTS&&function(t){var e=l.querySelector(".highcharts-text-outline");e&&iw(e,{display:t})},iP(n)&&n("none"),o=l.getBBox?iS({},l.getBBox()):{width:l.offsetWidth,height:l.offsetHeight,x:0,y:0},iP(n)&&n("")}catch(t){}(!o||o.width<0)&&(o={x:0,y:0,width:0,height:0})}else o=this.htmlGetBBox();r=o.height,f&&(o.height=r=({"11px,17":14,"13px,20":16})[""+(v||"")+",".concat(Math.round(r))]||r),g&&(o=this.getRotatedBox(o,g));var m={bBox:o};iT(this,"afterGetBBox",m),o=m.bBox}if(s&&(""===d||o.height>0)){for(;u.length>250;)delete p[u.shift()];p[s]||u.push(s),p[s]=o}return o},t.prototype.getRotatedBox=function(t,e){var i=t.x,o=t.y,r=t.width,n=t.height,s=this.alignValue,a=this.translateY,l=this.rotationOriginX,h=this.rotationOriginY,c=iC(s),d=Number(this.element.getAttribute("y")||0)-(a?0:o),p=e*ic,u=(e-90)*ic,f=Math.cos(p),g=Math.sin(p),v=r*f,m=r*g,y=Math.cos(u),x=Math.sin(u),b=[void 0===l?0:l,void 0===h?0:h].map(function(t){return[t-t*f,t*g]}),w=b[0],M=w[0],k=w[1],S=b[1],T=S[0],C=i+c*(r-v)+M+S[1]+d*y,A=C+v,P=A-n*y,O=P-v,L=o+d-c*m-k+T+d*x,E=L+m,I=E-n*x,D=I-m,B=Math.min(C,A,P,O),z=Math.min(L,E,I,D),j=Math.max(C,A,P,O)-B,N=Math.max(L,E,I,D)-z;return{x:B,y:z,width:j,height:N,polygon:[[C,L],[A,E],[P,I],[O,D]]}},t.prototype.getStyle=function(t){return ig.getComputedStyle(this.element||this,"").getPropertyValue(t)},t.prototype.hasClass=function(t){return -1!==(""+this.attr("class")).split(" ").indexOf(t)},t.prototype.hide=function(){return this.attr({visibility:"hidden"})},t.prototype.htmlGetBBox=function(){return{height:0,width:0,x:0,y:0}},t.prototype.on=function(t,e){var i=this.onEvents;return i[t]&&i[t](),i[t]=im(this.element,t,e),this},t.prototype.opacitySetter=function(t,e,i){var o=Number(Number(t).toFixed(3));this.opacity=o,i.setAttribute(e,o)},t.prototype.reAlign=function(){var t;(null===(t=this.alignOptions)||void 0===t?void 0:t.width)&&"left"!==this.alignOptions.align&&(this.alignOptions.width=this.getBBox().width,this.placed=!1,this.align())},t.prototype.removeClass=function(t){return this.attr("class",(""+this.attr("class")).replace(iE(t)?new RegExp("(^| )".concat(t,"( |$)")):t," ").replace(/ +/g," ").trim())},t.prototype.removeTextOutline=function(){var t=this.element.querySelector("tspan.highcharts-text-outline");t&&this.safeRemoveChild(t)},t.prototype.safeRemoveChild=function(t){var e=t.parentNode;e&&e.removeChild(t)},t.prototype.setRadialReference=function(t){var e=this.element.gradient&&this.renderer.gradients[this.element.gradient]||void 0;return this.element.radialReference=t,(null==e?void 0:e.radAttr)&&e.animate(this.renderer.getRadialAttr(t,e.radAttr)),this},t.prototype.shadow=function(t){var e,i=this.renderer,o=iI((null===(e=this.parentGroup)||void 0===e?void 0:e.rotation)===90?{offsetX:-1,offsetY:-1}:{},iL(t)?t:{}),r=i.shadowDefinition(o);return this.attr({filter:t?"url(".concat(i.url,"#").concat(r,")"):"none"})},t.prototype.show=function(t){return void 0===t&&(t=!0),this.attr({visibility:t?"inherit":"visible"})},t.prototype["stroke-widthSetter"]=function(t,e,i){this[e]=t,i.setAttribute(e,t)},t.prototype.strokeWidth=function(){if(!this.renderer.styledMode)return this["stroke-width"]||0;var t,e=this.getStyle("stroke-width"),i=0;return/px$/.test(e)?i=iz(e):""!==e&&(iy(t=id.createElementNS(iu,"rect"),{width:e,"stroke-width":0}),this.element.parentNode.appendChild(t),i=t.getBBox().width,t.parentNode.removeChild(t)),i},t.prototype.symbolAttr=function(e){var i=this;t.symbolCustomAttribs.forEach(function(t){i[t]=iB(e[t],i[t])}),i.attr({d:i.renderer.symbols[i.symbolName](i.x,i.y,i.width,i.height,i)})},t.prototype.textSetter=function(t){t!==this.textStr&&(delete this.textPxLength,this.textStr=t,this.added&&this.renderer.buildText(this),this.reAlign())},t.prototype.titleSetter=function(t){var e=this.element,i=e.getElementsByTagName("title")[0]||id.createElementNS(this.SVG_NS,"title");e.insertBefore?e.insertBefore(i,e.firstChild):e.appendChild(i),i.textContent=iN(iB(t,""),[/<[^>]*>/g,""]).replace(/&lt;/g,"<").replace(/&gt;/g,">")},t.prototype.toFront=function(){var t=this.element;return t.parentNode.appendChild(t),this},t.prototype.translate=function(t,e){return this.attr({translateX:t,translateY:e})},t.prototype.updateTransform=function(t){void 0===t&&(t="transform");var e,i,o,r,n=this.element,s=this.foreignObject,a=this.matrix,l=this.padding,h=this.rotation,c=void 0===h?0:h,d=this.rotationOriginX,p=this.rotationOriginY,u=this.scaleX,f=this.scaleY,g=this.text,v=this.translateX,m=this.translateY,y=["translate("+(void 0===v?0:v)+","+(void 0===m?0:m)+")"];iM(a)&&y.push("matrix("+a.join(",")+")"),!c||(y.push("rotate("+c+" "+(null!==(i=null!==(e=null!=d?d:n.getAttribute("x"))&&void 0!==e?e:this.x)&&void 0!==i?i:0)+" "+(null!==(r=null!==(o=null!=p?p:n.getAttribute("y"))&&void 0!==o?o:this.y)&&void 0!==r?r:0)+")"),(null==g?void 0:g.element.tagName)!=="SPAN"||(null==g?void 0:g.foreignObject)||g.attr({rotation:c,rotationOriginX:(d||0)-l,rotationOriginY:(p||0)-l})),(iM(u)||iM(f))&&y.push("scale("+iB(u,1)+" "+iB(f,1)+")"),y.length&&!(g||this).textPath&&((null==s?void 0:s.element)||n).setAttribute(t,y.join(" "))},t.prototype.visibilitySetter=function(t,e,i){"inherit"===t?i.removeAttribute(e):this[e]!==t&&i.setAttribute(e,t),this[e]=t},t.prototype.xGetter=function(t){return"circle"===this.element.nodeName&&("x"===t?t="cx":"y"===t&&(t="cy")),this._defaultGetter(t)},t.prototype.zIndexSetter=function(t,e){var i,o,r,n,s,a=this.renderer,l=this.parentGroup,h=(l||a).element||a.box,c=this.element,d=h===a.box,p=!1,u=this.added;if(iM(t)?(c.setAttribute("data-z-index",t),t*=1,this[e]===t&&(u=!1)):iM(this[e])&&c.removeAttribute("data-z-index"),this[e]=t,u){for((t=this.zIndex)&&l&&(l.handleZ=!0),s=(i=h.childNodes).length-1;s>=0&&!p;s--)n=!iM(r=(o=i[s]).getAttribute("data-z-index")),o!==c&&(t<0&&n&&!d&&!s?(h.insertBefore(c,i[s]),p=!0):(iz(r)<=t||n&&(!iM(t)||t>=0))&&(h.insertBefore(c,i[s+1]),p=!0));p||(h.insertBefore(c,i[3*!!d]),p=!0)}return p},t.symbolCustomAttribs=["anchorX","anchorY","clockwise","end","height","innerR","r","start","width","x","y"],t}();iX.prototype.strokeSetter=iX.prototype.fillSetter,iX.prototype.yGetter=iX.prototype.xGetter,iX.prototype.matrixSetter=iX.prototype.rotationOriginXSetter=iX.prototype.rotationOriginYSetter=iX.prototype.rotationSetter=iX.prototype.scaleXSetter=iX.prototype.scaleYSetter=iX.prototype.translateXSetter=iX.prototype.translateYSetter=iX.prototype.verticalAlignSetter=function(t,e){this[e]=t,this.doTransform=!0};var i_=(d=function(t,e){return(d=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}d(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),iF=tV.defined,iY=tV.extend,iG=tV.getAlignFactor,iH=tV.isNumber,iV=tV.merge,iU=tV.pick,iZ=tV.removeEvent,iq=function(t){function e(i,o,r,n,s,a,l,h,c,d){var p,u=t.call(this,i,"g")||this;return u.paddingLeftSetter=u.paddingSetter,u.paddingRightSetter=u.paddingSetter,u.doUpdate=!1,u.textStr=o,u.x=r,u.y=n,u.anchorX=a,u.anchorY=l,u.baseline=c,u.className=d,u.addClass("button"===d?"highcharts-no-tooltip":"highcharts-label"),d&&u.addClass("highcharts-"+d),u.text=i.text(void 0,0,0,h).attr({zIndex:1}),"string"==typeof s&&((p=/^url\((.*?)\)$/.test(s))||u.renderer.symbols[s])&&(u.symbolKey=s),u.bBox=e.emptyBBox,u.padding=3,u.baselineOffset=0,u.needsBox=i.styledMode||p,u.deferredAttr={},u.alignFactor=0,u}return i_(e,t),e.prototype.alignSetter=function(t){var e=iG(t);this.textAlign=t,e!==this.alignFactor&&(this.alignFactor=e,this.bBox&&iH(this.xSetting)&&this.attr({x:this.xSetting}))},e.prototype.anchorXSetter=function(t,e){this.anchorX=t,this.boxAttr(e,Math.round(t)-this.getCrispAdjust()-this.xSetting)},e.prototype.anchorYSetter=function(t,e){this.anchorY=t,this.boxAttr(e,t-this.ySetting)},e.prototype.boxAttr=function(t,e){this.box?this.box.attr(t,e):this.deferredAttr[t]=e},e.prototype.css=function(t){if(t){var i={};t=iV(t),e.textProps.forEach(function(e){void 0!==t[e]&&(i[e]=t[e],delete t[e])}),this.text.css(i),"fontSize"in i||"fontWeight"in i?this.updateTextPadding():("width"in i||"textOverflow"in i)&&this.updateBoxSize()}return iX.prototype.css.call(this,t)},e.prototype.destroy=function(){iZ(this.element,"mouseenter"),iZ(this.element,"mouseleave"),this.text&&this.text.destroy(),this.box&&(this.box=this.box.destroy()),iX.prototype.destroy.call(this)},e.prototype.fillSetter=function(t,e){t&&(this.needsBox=!0),this.fill=t,this.boxAttr(e,t)},e.prototype.getBBox=function(t,e){this.textStr&&0===this.bBox.width&&0===this.bBox.height&&this.updateBoxSize();var i=this.padding,o=this.height,r=this.translateX,n=this.translateY,s=this.width,a=iU(this.paddingLeft,i),l=null!=e?e:this.rotation||0,h={width:void 0===s?0:s,height:void 0===o?0:o,x:(void 0===r?0:r)+this.bBox.x-a,y:(void 0===n?0:n)+this.bBox.y-i+this.baselineOffset};return l&&(h=this.getRotatedBox(h,l)),h},e.prototype.getCrispAdjust=function(){return(this.renderer.styledMode&&this.box?this.box.strokeWidth():this["stroke-width"]?parseInt(this["stroke-width"],10):0)%2/2},e.prototype.heightSetter=function(t){this.heightSetting=t,this.doUpdate=!0},e.prototype.afterSetters=function(){t.prototype.afterSetters.call(this),this.doUpdate&&(this.updateBoxSize(),this.doUpdate=!1)},e.prototype.onAdd=function(){this.text.add(this),this.attr({text:iU(this.textStr,""),x:this.x||0,y:this.y||0}),this.box&&iF(this.anchorX)&&this.attr({anchorX:this.anchorX,anchorY:this.anchorY})},e.prototype.paddingSetter=function(t,e){iH(t)?t!==this[e]&&(this[e]=t,this.updateTextPadding()):this[e]=void 0},e.prototype.rSetter=function(t,e){this.boxAttr(e,t)},e.prototype.strokeSetter=function(t,e){this.stroke=t,this.boxAttr(e,t)},e.prototype["stroke-widthSetter"]=function(t,e){t&&(this.needsBox=!0),this["stroke-width"]=t,this.boxAttr(e,t)},e.prototype["text-alignSetter"]=function(t){this.textAlign=this["text-align"]=t,this.updateTextPadding()},e.prototype.textSetter=function(t){void 0!==t&&this.text.attr({text:t}),this.updateTextPadding(),this.reAlign()},e.prototype.updateBoxSize=function(){var t,i=this.text,o={},r=this.padding,n=this.bBox=(!iH(this.widthSetting)||!iH(this.heightSetting)||this.textAlign)&&iF(i.textStr)?i.getBBox(void 0,0):e.emptyBBox;this.width=this.getPaddedWidth(),this.height=(this.heightSetting||n.height||0)+2*r;var s=this.renderer.fontMetrics(i);if(this.baselineOffset=r+Math.min((this.text.firstLineMetrics||s).b,n.height||1/0),this.heightSetting&&(this.baselineOffset+=(this.heightSetting-s.h)/2),this.needsBox&&!i.textPath){if(!this.box){var a=this.box=this.symbolKey?this.renderer.symbol(this.symbolKey):this.renderer.rect();a.addClass(("button"===this.className?"":"highcharts-label-box")+(this.className?" highcharts-"+this.className+"-box":"")),a.add(this)}o.x=t=this.getCrispAdjust(),o.y=(this.baseline?-this.baselineOffset:0)+t,o.width=Math.round(this.width),o.height=Math.round(this.height),this.box.attr(iY(o,this.deferredAttr)),this.deferredAttr={}}},e.prototype.updateTextPadding=function(){var t,e,i=this.text,o=i.styles.textAlign||this.textAlign;if(!i.textPath){this.updateBoxSize();var r=this.baseline?0:this.baselineOffset,n=(null!==(t=this.paddingLeft)&&void 0!==t?t:this.padding)+iG(o)*(null!==(e=this.widthSetting)&&void 0!==e?e:this.bBox.width);(n!==i.x||r!==i.y)&&(i.attr({align:o,x:n}),void 0!==r&&i.attr("y",r)),i.x=n,i.y=r}},e.prototype.widthSetter=function(t){this.widthSetting=iH(t)?t:void 0,this.doUpdate=!0},e.prototype.getPaddedWidth=function(){var t=this.padding,e=iU(this.paddingLeft,t),i=iU(this.paddingRight,t);return(this.widthSetting||this.bBox.width||0)+e+i},e.prototype.xSetter=function(t){this.x=t,this.alignFactor&&(t-=this.alignFactor*this.getPaddedWidth(),this["forceAnimate:x"]=!0),this.xSetting=Math.round(t),this.attr("translateX",this.xSetting)},e.prototype.ySetter=function(t){this.ySetting=this.y=Math.round(t),this.attr("translateY",this.ySetting)},e.emptyBBox={width:0,height:0,x:0,y:0},e.textProps=["color","direction","fontFamily","fontSize","fontStyle","fontWeight","lineClamp","lineHeight","textAlign","textDecoration","textOutline","textOverflow","whiteSpace","width"],e}(iX),iK=tV.defined,i$=tV.isNumber,iJ=tV.pick;function iQ(t,e,i,o,r){var n=[];if(r){var s=r.start||0,a=r.end||0,l=iJ(r.r,i),h=iJ(r.r,o||i),c=2e-4/(r.borderRadius?1:Math.max(l,1)),d=Math.abs(a-s-2*Math.PI)<c;d&&(s=Math.PI/2,a=2.5*Math.PI-c);var p=r.innerR,u=iJ(r.open,d),f=Math.cos(s),g=Math.sin(s),v=Math.cos(a),m=Math.sin(a),y=iJ(r.longArc,a-s-Math.PI<c?0:1),x=["A",l,h,0,y,iJ(r.clockwise,1),t+l*v,e+h*m];x.params={start:s,end:a,cx:t,cy:e},n.push(["M",t+l*f,e+h*g],x),iK(p)&&((x=["A",p,p,0,y,iK(r.clockwise)?1-r.clockwise:0,t+p*f,e+p*g]).params={start:a,end:s,cx:t,cy:e},n.push(u?["M",t+p*v,e+p*m]:["L",t+p*v,e+p*m],x)),u||n.push(["Z"])}return n}function i0(t,e,i,o,r){return(null==r?void 0:r.r)?i1(t,e,i,o,r):[["M",t,e],["L",t+i,e],["L",t+i,e+o],["L",t,e+o],["Z"]]}function i1(t,e,i,o,r){var n=(null==r?void 0:r.r)||0;return[["M",t+n,e],["L",t+i-n,e],["A",n,n,0,0,1,t+i,e+n],["L",t+i,e+o-n],["A",n,n,0,0,1,t+i-n,e+o],["L",t+n,e+o],["A",n,n,0,0,1,t,e+o-n],["L",t,e+n],["A",n,n,0,0,1,t+n,e],["Z"]]}var i2={arc:iQ,callout:function(t,e,i,o,r){var n=Math.min((null==r?void 0:r.r)||0,i,o),s=n+6,a=null==r?void 0:r.anchorX,l=(null==r?void 0:r.anchorY)||0,h=i1(t,e,i,o,{r:n});if(!i$(a)||a<i&&a>0&&l<o&&l>0)return h;if(t+a>i-s){if(l>e+s&&l<e+o-s)h.splice(3,1,["L",t+i,l-6],["L",t+i+6,l],["L",t+i,l+6],["L",t+i,e+o-n]);else if(a<i){var c=l<e+s,d=c?e:e+o,p=c?2:5;h.splice(p,0,["L",a,l],["L",t+i-n,d])}else h.splice(3,1,["L",t+i,o/2],["L",a,l],["L",t+i,o/2],["L",t+i,e+o-n])}else if(t+a<s){if(l>e+s&&l<e+o-s)h.splice(7,1,["L",t,l+6],["L",t-6,l],["L",t,l-6],["L",t,e+n]);else if(a>0){var c=l<e+s,d=c?e:e+o,p=c?1:6;h.splice(p,0,["L",a,l],["L",t+n,d])}else h.splice(7,1,["L",t,o/2],["L",a,l],["L",t,o/2],["L",t,e+n])}else l>o&&a<i-s?h.splice(5,1,["L",a+6,e+o],["L",a,e+o+6],["L",a-6,e+o],["L",t+n,e+o]):l<0&&a>s&&h.splice(1,1,["L",a-6,e],["L",a,e-6],["L",a+6,e],["L",i-n,e]);return h},circle:function(t,e,i,o){return iQ(t+i/2,e+o/2,i/2,o/2,{start:.5*Math.PI,end:2.5*Math.PI,open:!1})},diamond:function(t,e,i,o){return[["M",t+i/2,e],["L",t+i,e+o/2],["L",t+i/2,e+o],["L",t,e+o/2],["Z"]]},rect:i0,roundedRect:i1,square:i0,triangle:function(t,e,i,o){return[["M",t+i/2,e],["L",t+i,e+o],["L",t,e+o],["Z"]]},"triangle-down":function(t,e,i,o){return[["M",t,e],["L",t+i,e],["L",t+i/2,e+o],["Z"]]}},i3=tb.doc,i6=tb.SVG_NS,i5=tb.win,i9=tV.attr,i8=tV.extend,i4=tV.fireEvent,i7=tV.isString,ot=tV.objectEach,oe=tV.pick,oi=function(t,e){return t.substring(0,e)+"…"},oo=function(){function t(t){var e=t.styles;this.renderer=t.renderer,this.svgElement=t,this.width=t.textWidth,this.textLineHeight=null==e?void 0:e.lineHeight,this.textOutline=null==e?void 0:e.textOutline,this.ellipsis=(null==e?void 0:e.textOverflow)==="ellipsis",this.lineClamp=null==e?void 0:e.lineClamp,this.noWrap=(null==e?void 0:e.whiteSpace)==="nowrap"}return t.prototype.buildSVG=function(){var t=this.svgElement,e=t.element,i=t.renderer,o=oe(t.textStr,"").toString(),r=-1!==o.indexOf("<"),n=e.childNodes,s=!t.added&&i.box,a=[o,this.ellipsis,this.noWrap,this.textLineHeight,this.textOutline,t.getStyle("font-size"),t.styles.lineClamp,this.width].join(",");if(a!==t.textCache){t.textCache=a,delete t.actualWidth;for(var l=n.length;l--;)e.removeChild(n[l]);if(r||this.ellipsis||this.width||t.textPath||-1!==o.indexOf(" ")&&(!this.noWrap||/<br.*?>/g.test(o))){if(""!==o){s&&s.appendChild(e);var h=new eJ(o);this.modifyTree(h.nodes),h.addToDOM(e),this.modifyDOM(),this.ellipsis&&-1!==(e.textContent||"").indexOf("…")&&t.attr("title",this.unescapeEntities(t.textStr||"",["&lt;","&gt;"])),s&&s.removeChild(e)}}else e.appendChild(i3.createTextNode(this.unescapeEntities(o)));i7(this.textOutline)&&t.applyTextOutline&&t.applyTextOutline(this.textOutline)}},t.prototype.modifyDOM=function(){var t,e=this,i=this.svgElement,o=i9(i.element,"x");for(i.firstLineMetrics=void 0;t=i.element.firstChild;)if(/^[\s\u200B]*$/.test(t.textContent||" "))i.element.removeChild(t);else break;[].forEach.call(i.element.querySelectorAll("tspan.highcharts-br"),function(t,r){t.nextSibling&&t.previousSibling&&(0===r&&1===t.previousSibling.nodeType&&(i.firstLineMetrics=i.renderer.fontMetrics(t.previousSibling)),i9(t,{dy:e.getLineHeight(t.nextSibling),x:o}))});var r=this.width||0;if(r){var n=function(t,n){var s,a=t.textContent||"",l=a.replace(/([^\^])-/g,"$1- ").split(" "),h=!e.noWrap&&(l.length>1||i.element.childNodes.length>1),c=e.getLineHeight(n),d=Math.max(0,r-.8*c),p=0,u=i.actualWidth;if(h){for(var f=[],g=[];n.firstChild&&n.firstChild!==t;)g.push(n.firstChild),n.removeChild(n.firstChild);for(;l.length;)if(l.length&&!e.noWrap&&p>0&&(f.push(t.textContent||""),t.textContent=l.join(" ").replace(/- /g,"-")),e.truncate(t,void 0,l,0===p&&u||0,r,d,function(t,e){return l.slice(0,e).join(" ").replace(/- /g,"-")}),u=i.actualWidth,p++,e.lineClamp&&p>=e.lineClamp){l.length&&(e.truncate(t,t.textContent||"",void 0,0,r,d,oi),t.textContent=(null===(s=t.textContent)||void 0===s?void 0:s.replace("…",""))+"…");break}g.forEach(function(e){n.insertBefore(e,t)}),f.forEach(function(e){n.insertBefore(i3.createTextNode(e),t);var i=i3.createElementNS(i6,"tspan");i.textContent="​",i9(i,{dy:c,x:o}),n.insertBefore(i,t)})}else e.ellipsis&&a&&e.truncate(t,a,void 0,0,r,d,oi)},s=function(t){[].slice.call(t.childNodes).forEach(function(e){e.nodeType===i5.Node.TEXT_NODE?n(e,t):(-1!==e.className.baseVal.indexOf("highcharts-br")&&(i.actualWidth=0),s(e))})};s(i.element)}},t.prototype.getLineHeight=function(t){var e=t.nodeType===i5.Node.TEXT_NODE?t.parentElement:t;return this.textLineHeight?parseInt(this.textLineHeight.toString(),10):this.renderer.fontMetrics(e||this.svgElement.element).h},t.prototype.modifyTree=function(t){var e=this,i=function(o,r){var n=o.attributes,s=void 0===n?{}:n,a=o.children,l=o.style,h=void 0===l?{}:l,c=o.tagName,d=e.renderer.styledMode;if("b"===c||"strong"===c?d?s.class="highcharts-strong":h.fontWeight="bold":("i"===c||"em"===c)&&(d?s.class="highcharts-emphasized":h.fontStyle="italic"),(null==h?void 0:h.color)&&(h.fill=h.color),"br"===c){s.class="highcharts-br",o.textContent="​";var p=t[r+1];(null==p?void 0:p.textContent)&&(p.textContent=p.textContent.replace(/^ +/gm,""))}else"a"===c&&a&&a.some(function(t){return"#text"===t.tagName})&&(o.children=[{children:a,tagName:"tspan"}]);"#text"!==c&&"a"!==c&&(o.tagName="tspan"),i8(o,{attributes:s,style:h}),a&&a.filter(function(t){return"#text"!==t.tagName}).forEach(i)};t.forEach(i),i4(this.svgElement,"afterModifyTree",{nodes:t})},t.prototype.truncate=function(t,e,i,o,r,n,s){var a,l,h=this.svgElement,c=h.rotation,d=[],p=i&&!o?1:0,u=(e||i||"").length,f=u;i||(r=n);var g=function(e,r){var n=r||e,s=t.parentNode;if(s&&void 0===d[n]&&s.getSubStringLength)try{d[n]=o+s.getSubStringLength(0,i?n+1:n)}catch(t){}return d[n]};if(h.rotation=0,o+(l=g(t.textContent.length))>r){for(;p<=u;)f=Math.ceil((p+u)/2),i&&(a=s(i,f)),l=g(f,a&&a.length-1),p===u?p=u+1:l>r?u=f-1:p=f;0===u?t.textContent="":e&&u===e.length-1||(t.textContent=a||s(e||i,f)),this.ellipsis&&l>r&&this.truncate(t,t.textContent||"",void 0,0,r,n,oi)}i&&i.splice(0,f),h.actualWidth=l,h.rotation=c},t.prototype.unescapeEntities=function(t,e){return ot(this.renderer.escapes,function(i,o){e&&-1!==e.indexOf(i)||(t=t.toString().replace(RegExp(i,"g"),o))}),t},t}(),or=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},on=tb.charts,os=tb.deg2rad,oa=tb.doc,ol=tb.isFirefox,oh=tb.isMS,oc=tb.isWebKit,od=tb.noop,op=tb.SVG_NS,ou=tb.symbolSizes,of=tb.win,og=tV.addEvent,ov=tV.attr,om=tV.createElement,oy=tV.crisp,ox=tV.css,ob=tV.defined,ow=tV.destroyObjectProperties,oM=tV.extend,ok=tV.isArray,oS=tV.isNumber,oT=tV.isObject,oC=tV.isString,oA=tV.merge,oP=tV.pick,oO=tV.pInt,oL=tV.replaceNested,oE=tV.uniqueKey,oI=function(){function t(t,e,i,o,r,n,s){this.x=0,this.y=0;var a,l,h=this.createElement("svg").attr({version:"1.1",class:"highcharts-root"}),c=h.element;s||h.css(this.getStyle(o||{})),t.appendChild(c),ov(t,"dir","ltr"),-1===t.innerHTML.indexOf("xmlns")&&ov(c,"xmlns",this.SVG_NS),this.box=c,this.boxWrapper=h,this.alignedObjects=[],this.url=this.getReferenceURL(),this.createElement("desc").add().element.appendChild(oa.createTextNode("Created with Highcharts 12.2.0")),this.defs=this.createElement("defs").add(),this.allowHTML=n,this.forExport=r,this.styledMode=s,this.gradients={},this.cache={},this.cacheKeys=[],this.imgCount=0,this.rootFontSize=h.getStyle("font-size"),this.setSize(e,i,!1),ol&&t.getBoundingClientRect&&((a=function(){ox(t,{left:0,top:0}),l=t.getBoundingClientRect(),ox(t,{left:Math.ceil(l.left)-l.left+"px",top:Math.ceil(l.top)-l.top+"px"})})(),this.unSubPixelFix=og(of,"resize",a))}return t.prototype.definition=function(t){return new eJ([t]).addToDOM(this.defs.element)},t.prototype.getReferenceURL=function(){if((ol||oc)&&oa.getElementsByTagName("base").length){if(!ob(K)){var t=oE(),e=new eJ([{tagName:"svg",attributes:{width:8,height:8},children:[{tagName:"defs",children:[{tagName:"clipPath",attributes:{id:t},children:[{tagName:"rect",attributes:{width:4,height:4}}]}]},{tagName:"rect",attributes:{id:"hitme",width:8,height:8,"clip-path":"url(#".concat(t,")"),fill:"rgba(0,0,0,0.001)"}}]}]).addToDOM(oa.body);ox(e,{position:"fixed",top:0,left:0,zIndex:9e5});var i=oa.elementFromPoint(6,6);K=(null==i?void 0:i.id)==="hitme",oa.body.removeChild(e)}if(K)return oL(of.location.href.split("#")[0],[/<[^>]*>/g,""],[/([\('\)])/g,"\\$1"],[/ /g,"%20"])}return""},t.prototype.getStyle=function(t){return this.style=oM({fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", sans-serif',fontSize:"1rem"},t),this.style},t.prototype.setStyle=function(t){this.boxWrapper.css(this.getStyle(t))},t.prototype.isHidden=function(){return!this.boxWrapper.getBBox().width},t.prototype.destroy=function(){var t=this.defs;return this.box=null,this.boxWrapper=this.boxWrapper.destroy(),ow(this.gradients||{}),this.gradients=null,this.defs=t.destroy(),this.unSubPixelFix&&this.unSubPixelFix(),this.alignedObjects=null,null},t.prototype.createElement=function(t){return new this.Element(this,t)},t.prototype.getRadialAttr=function(t,e){return{cx:t[0]-t[2]/2+(e.cx||0)*t[2],cy:t[1]-t[2]/2+(e.cy||0)*t[2],r:(e.r||0)*t[2]}},t.prototype.shadowDefinition=function(t){var e=or(["highcharts-drop-shadow-".concat(this.chartIndex)],Object.keys(t).map(function(e){return""+e+"-".concat(t[e])}),!0).join("-").toLowerCase().replace(/[^a-z\d\-]/g,""),i=oA({color:"#000000",offsetX:1,offsetY:1,opacity:.15,width:5},t);return this.defs.element.querySelector("#".concat(e))||this.definition({tagName:"filter",attributes:{id:e,filterUnits:i.filterUnits},children:this.getShadowFilterContent(i)}),e},t.prototype.getShadowFilterContent=function(t){return[{tagName:"feDropShadow",attributes:{dx:t.offsetX,dy:t.offsetY,"flood-color":t.color,"flood-opacity":Math.min(5*t.opacity,1),stdDeviation:t.width/2}}]},t.prototype.buildText=function(t){new oo(t).buildSVG()},t.prototype.getContrast=function(t){var e=eb.parse(t).rgba,i=" clamp(0,calc(9e9*(0.5 - (0.2126*r + 0.7152*g + 0.0722*b))),1)";if(oS(e[0])||!eb.useColorMix){var o=e.map(function(t){var e=t/255;return e<=.04?e/12.92:Math.pow((e+.055)/1.055,2.4)}),r=.2126*o[0]+.7152*o[1]+.0722*o[2];return 1.05/(r+.05)>(r+.05)/.05?"#FFFFFF":"#000000"}return"color(from "+t+" srgb"+i+i+i+")"},t.prototype.button=function(t,e,i,o,r,n,s,a,l,h){void 0===r&&(r={});var c=this.label(t,e,i,l,void 0,void 0,h,void 0,"button"),d=this.styledMode,p=arguments,u=0;r=oA(el.global.buttonTheme,r),d&&(delete r.fill,delete r.stroke,delete r["stroke-width"]);var f=r.states||{},g=r.style||{};delete r.states,delete r.style;var v=[eJ.filterUserAttributes(r)],m=[g];return d||["hover","select","disabled"].forEach(function(t,e){v.push(oA(v[0],eJ.filterUserAttributes(p[e+5]||f[t]||{}))),m.push(v[e+1].style),delete v[e+1].style}),og(c.element,oh?"mouseover":"mouseenter",function(){3!==u&&c.setState(1)}),og(c.element,oh?"mouseout":"mouseleave",function(){3!==u&&c.setState(u)}),c.setState=function(t){if(void 0===t&&(t=0),1!==t&&(c.state=u=t),c.removeClass(/highcharts-button-(normal|hover|pressed|disabled)/).addClass("highcharts-button-"+["normal","hover","pressed","disabled"][t]),!d){c.attr(v[t]);var e=m[t];oT(e)&&c.css(e)}},c.attr(v[0]),!d&&(c.css(oM({cursor:"default"},g)),h&&c.text.css({pointerEvents:"none"})),c.on("touchstart",function(t){return t.stopPropagation()}).on("click",function(t){3!==u&&(null==o||o.call(c,t))})},t.prototype.crispLine=function(t,e){var i=t[0],o=t[1];return ob(i[1])&&i[1]===o[1]&&(i[1]=o[1]=oy(i[1],e)),ob(i[2])&&i[2]===o[2]&&(i[2]=o[2]=oy(i[2],e)),t},t.prototype.path=function(t){var e=this.styledMode?{}:{fill:"none"};return ok(t)?e.d=t:oT(t)&&oM(e,t),this.createElement("path").attr(e)},t.prototype.circle=function(t,e,i){var o=oT(t)?t:void 0===t?{}:{x:t,y:e,r:i},r=this.createElement("circle");return r.xSetter=r.ySetter=function(t,e,i){i.setAttribute("c"+e,t)},r.attr(o)},t.prototype.arc=function(t,e,i,o,r,n){oT(t)?(e=(s=t).y,i=s.r,o=s.innerR,r=s.start,n=s.end,t=s.x):s={innerR:o,start:r,end:n};var s,a=this.symbol("arc",t,e,i,i,s);return a.r=i,a},t.prototype.rect=function(t,e,i,o,r,n){var s=oT(t)?t:void 0===t?{}:{x:t,y:e,r:r,width:Math.max(i||0,0),height:Math.max(o||0,0)},a=this.createElement("rect");return this.styledMode||(void 0!==n&&(s["stroke-width"]=n,oM(s,a.crisp(s))),s.fill="none"),a.rSetter=function(t,e,i){a.r=t,ov(i,{rx:t,ry:t})},a.rGetter=function(){return a.r||0},a.attr(s)},t.prototype.roundedRect=function(t){return this.symbol("roundedRect").attr(t)},t.prototype.setSize=function(t,e,i){this.width=t,this.height=e,this.boxWrapper.animate({width:t,height:e},{step:function(){this.attr({viewBox:"0 0 "+this.attr("width")+" "+this.attr("height")})},duration:oP(i,!0)?void 0:0}),this.alignElements()},t.prototype.g=function(t){var e=this.createElement("g");return t?e.attr({class:"highcharts-"+t}):e},t.prototype.image=function(t,e,i,o,r,n){var s={preserveAspectRatio:"none"};oS(e)&&(s.x=e),oS(i)&&(s.y=i),oS(o)&&(s.width=o),oS(r)&&(s.height=r);var a=this.createElement("image").attr(s),l=function(e){a.attr({href:t}),n.call(a,e)};if(n){a.attr({href:"data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="});var h=new of.Image;og(h,"load",l),h.src=t,h.complete&&l({})}else a.attr({href:t});return a},t.prototype.symbol=function(t,e,i,o,r,n){var s,a,l,h,c,d,p=this,u=/^url\((.*?)\)$/,f=u.test(t),g=!f&&(this.symbols[t]?t:"circle"),v=g&&this.symbols[g];if(v)"number"==typeof e&&(h=v.call(this.symbols,e||0,i||0,o||0,r||0,n)),l=this.path(h),p.styledMode||l.attr("fill","none"),oM(l,{symbolName:g||void 0,x:e,y:i,width:o,height:r}),n&&oM(l,n);else if(f){c=t.match(u)[1];var m=l=this.image(c);m.imgwidth=oP(null==n?void 0:n.width,null===(s=ou[c])||void 0===s?void 0:s.width),m.imgheight=oP(null==n?void 0:n.height,null===(a=ou[c])||void 0===a?void 0:a.height),d=function(t){return t.attr({width:t.width,height:t.height})},["width","height"].forEach(function(t){m[""+t+"Setter"]=function(t,e){this[e]=t;var i=this.alignByTranslate,o=this.element,r=this.width,s=this.height,a=this.imgwidth,l=this.imgheight,h="width"===e?a:l,c=1;n&&"within"===n.backgroundSize&&r&&s&&a&&l?(c=Math.min(r/a,s/l),ov(o,{width:Math.round(a*c),height:Math.round(l*c)})):o&&h&&o.setAttribute(e,h),!i&&a&&l&&this.translate(((r||0)-a*c)/2,((s||0)-l*c)/2)}}),ob(e)&&m.attr({x:e,y:i}),m.isImg=!0,m.symbolUrl=t,ob(m.imgwidth)&&ob(m.imgheight)?d(m):(m.attr({width:0,height:0}),om("img",{onload:function(){var t=on[p.chartIndex];0===this.width&&(ox(this,{position:"absolute",top:"-999em"}),oa.body.appendChild(this)),ou[c]={width:this.width,height:this.height},m.imgwidth=this.width,m.imgheight=this.height,m.element&&d(m),this.parentNode&&this.parentNode.removeChild(this),p.imgCount--,p.imgCount||!t||t.hasLoaded||t.onload()},src:c}),this.imgCount++)}return l},t.prototype.clipRect=function(t,e,i,o){return this.rect(t,e,i,o,0)},t.prototype.text=function(t,e,i,o){var r={};if(o&&(this.allowHTML||!this.forExport))return this.html(t,e,i);r.x=Math.round(e||0),i&&(r.y=Math.round(i)),ob(t)&&(r.text=t);var n=this.createElement("text").attr(r);return o&&(!this.forExport||this.allowHTML)||(n.xSetter=function(t,e,i){for(var o=i.getElementsByTagName("tspan"),r=i.getAttribute(e),n=0,s=void 0;n<o.length;n++)(s=o[n]).getAttribute(e)===r&&s.setAttribute(e,t);i.setAttribute(e,t)}),n},t.prototype.fontMetrics=function(t){var e=oO(iX.prototype.getStyle.call(t,"font-size")||0),i=e<24?e+3:Math.round(1.2*e),o=Math.round(.8*i);return{h:i,b:o,f:e}},t.prototype.rotCorr=function(t,e,i){var o=t;return e&&i&&(o=Math.max(o*Math.cos(e*os),4)),{x:-t/3*Math.sin(e*os),y:o}},t.prototype.pathToSegments=function(t){for(var e=[],i=[],o={A:8,C:7,H:2,L:3,M:3,Q:5,S:5,T:3,V:2},r=0;r<t.length;r++)oC(i[0])&&oS(t[r])&&i.length===o[i[0].toUpperCase()]&&t.splice(r,0,i[0].replace("M","L").replace("m","l")),"string"==typeof t[r]&&(i.length&&e.push(i.slice(0)),i.length=0),i.push(t[r]);return e.push(i.slice(0)),e},t.prototype.label=function(t,e,i,o,r,n,s,a,l){return new iq(this,t,e,i,o,r,n,s,a,l)},t.prototype.alignElements=function(){this.alignedObjects.forEach(function(t){return t.align()})},t}();oM(oI.prototype,{Element:iX,SVG_NS:op,escapes:{"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"},symbols:i2,draw:od}),io.registerRendererType("svg",oI,!0);var oD=(p=function(t,e){return(p=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}p(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),oB=function(){return(oB=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},oz=tb.composed,oj=tb.isFirefox,oN=tV.attr,oR=tV.css,oW=tV.createElement,oX=tV.defined,o_=tV.extend,oF=tV.getAlignFactor,oY=tV.isNumber,oG=tV.pInt,oH=tV.pushUnique;function oV(t,e,i){var o,r=(null===(o=this.div)||void 0===o?void 0:o.style)||i.style;iX.prototype[""+e+"Setter"].call(this,t,e,i),r&&(r[e]=t)}var oU=function(t,e){var i;if(!t.div){var o=oN(t.element,"class"),r=t.css,n=oW("div",o?{className:o}:void 0,oB(oB({position:"absolute",left:""+(t.translateX||0)+"px",top:""+(t.translateY||0)+"px"},t.styles),{display:t.display,opacity:t.opacity,visibility:t.visibility}),(null===(i=t.parentGroup)||void 0===i?void 0:i.div)||e);t.classSetter=function(t,e,i){i.setAttribute("class",t),n.className=t},t.translateXSetter=t.translateYSetter=function(e,i){t[i]=e,n.style["translateX"===i?"left":"top"]=""+e+"px",t.doTransform=!0},t.opacitySetter=t.visibilitySetter=oV,t.css=function(e){return r.call(t,e),e.cursor&&(n.style.cursor=e.cursor),e.pointerEvents&&(n.style.pointerEvents=e.pointerEvents),t},t.on=function(){return iX.prototype.on.apply({element:n,onEvents:t.onEvents},arguments),t},t.div=n}return t.div},oZ=function(t){function e(i,o){var r=t.call(this,i,o)||this;return e.useForeignObject?r.foreignObject=i.createElement("foreignObject").attr({zIndex:2}):r.css(oB({position:"absolute"},i.styledMode?{}:{fontFamily:i.style.fontFamily,fontSize:i.style.fontSize})),r.element.style.whiteSpace="nowrap",r}return oD(e,t),e.compose=function(t){oH(oz,this.compose)&&(t.prototype.html=function(t,i,o){return new e(this,"span").attr({text:t,x:Math.round(i),y:Math.round(o)})})},e.prototype.getSpanCorrection=function(t,e,i){this.xCorr=-t*i,this.yCorr=-e},e.prototype.css=function(t){var e,i=this.element,o="SPAN"===i.tagName&&t&&"width"in t,r=o&&t.width;return o&&(delete t.width,this.textWidth=oG(r)||void 0,e=!0),(null==t?void 0:t.textOverflow)==="ellipsis"&&(t.overflow="hidden",t.whiteSpace="nowrap"),(null==t?void 0:t.lineClamp)&&(t.display="-webkit-box",t.WebkitLineClamp=t.lineClamp,t.WebkitBoxOrient="vertical",t.overflow="hidden"),oY(Number(null==t?void 0:t.fontSize))&&(t.fontSize+="px"),o_(this.styles,t),oR(i,t),e&&this.updateTransform(),this},e.prototype.htmlGetBBox=function(){var t=this.element;return{x:t.offsetLeft,y:t.offsetTop,width:t.offsetWidth,height:t.offsetHeight}},e.prototype.updateTransform=function(){if(!this.added){this.alignOnAdd=!0;return}var e,i=this.element,o=this.foreignObject,r=this.oldTextWidth,n=this.renderer,s=this.rotation,a=this.rotationOriginX,l=this.rotationOriginY,h=this.scaleX,c=this.scaleY,d=this.styles,p=d.display,u=void 0===p?"inline-block":p,f=d.whiteSpace,g=this.textAlign,v=void 0===g?"left":g,m=this.textWidth,y=this.translateX,x=this.translateY,b=this.x,w=void 0===b?0:b,M=this.y,k=void 0===M?0:M;if(o||oR(i,{marginLeft:""+(void 0===y?0:y)+"px",marginTop:""+(void 0===x?0:x)+"px"}),"SPAN"===i.tagName){var S=[s,v,i.innerHTML,m,this.textAlign].join(","),T=-((null===(e=this.parentGroup)||void 0===e?void 0:e.padding)*1)||0,C=void 0;if(m!==r){var A=this.textPxLength?this.textPxLength:(oR(i,{width:"",whiteSpace:f||"nowrap"}),i.offsetWidth),P=m||0,O=""===i.style.textOverflow&&i.style.webkitLineClamp;(P>r||A>P||O)&&(/[\-\s\u00AD]/.test(i.textContent||i.innerText)||"ellipsis"===i.style.textOverflow)&&(oR(i,{width:(s||h||A>P||O)&&oY(m)?m+"px":"auto",display:u,whiteSpace:f||"normal"}),this.oldTextWidth=m)}o&&(oR(i,{display:"inline-block",verticalAlign:"top"}),o.attr({width:n.width,height:n.height})),S!==this.cTT&&(C=n.fontMetrics(i).b,oX(s)&&!o&&(s!==(this.oldRotation||0)||v!==this.oldAlign)&&oR(i,{transform:"rotate(".concat(s,"deg)"),transformOrigin:""+T+"% "+T+"px"}),this.getSpanCorrection(!oX(s)&&!this.textWidth&&this.textPxLength||i.offsetWidth,C,oF(v)));var L=this.xCorr,E=void 0===L?0:L,I=this.yCorr,D=void 0===I?0:I,B=(null!=a?a:w)-E-w-T,z=(null!=l?l:k)-D-k-T,j={left:""+(w+E)+"px",top:""+(k+D)+"px",textAlign:v,transformOrigin:""+B+"px "+z+"px"};(h||c)&&(j.transform="scale(".concat(null!=h?h:1,",").concat(null!=c?c:1,")")),o?(t.prototype.updateTransform.call(this),oY(w)&&oY(k)?(o.attr({x:w+E,y:k+D,width:i.offsetWidth+3,height:i.offsetHeight,"transform-origin":i.getAttribute("transform-origin")||"0 0"}),oR(i,{display:u,textAlign:v})):oj&&o.attr({width:0,height:0})):oR(i,j),this.cTT=S,this.oldRotation=s,this.oldAlign=v}},e.prototype.add=function(e){var i=this.foreignObject,o=this.renderer,r=o.box.parentNode,n=[];if(i)i.add(e),t.prototype.add.call(this,o.createElement("body").attr({xmlns:"http://www.w3.org/1999/xhtml"}).css({background:"transparent",margin:"0 3px 0 0"}).add(i));else{var s=void 0;if(this.parentGroup=e,e&&!(s=e.div)){for(var a=e;a;)n.push(a),a=a.parentGroup;for(var l=0,h=n.reverse();l<h.length;l++)s=oU(h[l],r)}(s||r).appendChild(this.element)}return this.added=!0,this.alignOnAdd&&this.updateTransform(),this},e.prototype.textSetter=function(t){t!==this.textStr&&(delete this.bBox,delete this.oldTextWidth,eJ.setElementHTML(this.element,null!=t?t:""),this.textStr=t,this.doTransform=!0)},e.prototype.alignSetter=function(t){this.alignValue=this.textAlign=t,this.doTransform=!0},e.prototype.xSetter=function(t,e){this[e]=t,this.doTransform=!0},e}(iX),oq=oZ.prototype;oq.visibilitySetter=oq.opacitySetter=oV,oq.ySetter=oq.rotationSetter=oq.rotationOriginXSetter=oq.rotationOriginYSetter=oq.xSetter,(u=$||($={})).xAxis={alignTicks:!0,allowDecimals:void 0,panningEnabled:!0,zIndex:2,zoomEnabled:!0,dateTimeLabelFormats:{millisecond:{main:"%[HMSL]",range:!1},second:{main:"%[HMS]",range:!1},minute:{main:"%[HM]",range:!1},hour:{main:"%[HM]",range:!1},day:{main:"%[eb]"},week:{main:"%[eb]"},month:{main:"%[bY]"},year:{main:"%Y"}},endOnTick:!1,gridLineDashStyle:"Solid",gridZIndex:1,labels:{autoRotationLimit:80,distance:15,enabled:!0,indentation:10,overflow:"justify",reserveSpace:void 0,rotation:void 0,staggerLines:0,step:0,useHTML:!1,zIndex:7,style:{color:"#333333",cursor:"default",fontSize:"0.8em",textOverflow:"ellipsis"}},maxPadding:.01,minorGridLineDashStyle:"Solid",minorTickLength:2,minorTickPosition:"outside",minorTicksPerMajor:5,minPadding:.01,offset:void 0,reversed:void 0,reversedStacks:!1,showEmpty:!0,showFirstLabel:!0,showLastLabel:!0,startOfWeek:1,startOnTick:!1,tickLength:10,tickPixelInterval:100,tickmarkPlacement:"between",tickPosition:"outside",title:{align:"middle",useHTML:!1,x:0,y:0,style:{color:"#666666",fontSize:"0.8em"}},visible:!0,minorGridLineColor:"#f2f2f2",minorGridLineWidth:1,minorTickColor:"#999999",lineColor:"#333333",lineWidth:1,gridLineColor:"#e6e6e6",gridLineWidth:void 0,tickColor:"#333333"},u.yAxis={reversedStacks:!0,endOnTick:!0,maxPadding:.05,minPadding:.05,tickPixelInterval:72,showLastLabel:!0,labels:{x:void 0},startOnTick:!0,title:{},stackLabels:{animation:{},allowOverlap:!1,enabled:!1,crop:!0,overflow:"justify",formatter:function(){return(0,this.axis.chart.numberFormatter)(this.total||0,-1)},style:{color:"#000000",fontSize:"0.7em",fontWeight:"bold",textOutline:"1px contrast"}},gridLineWidth:1,lineWidth:0};var oK=$,o$=tV.addEvent,oJ=tV.isFunction,oQ=tV.objectEach,o0=tV.removeEvent;(J||(J={})).registerEventOptions=function(t,e){t.eventOptions=t.eventOptions||{},oQ(e.events,function(e,i){t.eventOptions[i]!==e&&(t.eventOptions[i]&&(o0(t,i,t.eventOptions[i]),delete t.eventOptions[i]),oJ(e)&&(t.eventOptions[i]=e,o$(t,i,e,{order:0})))})};var o1=J,o2=tb.deg2rad,o3=tV.clamp,o6=tV.correctFloat,o5=tV.defined,o9=tV.destroyObjectProperties,o8=tV.extend,o4=tV.fireEvent,o7=tV.getAlignFactor,rt=tV.isNumber,re=tV.merge,ri=tV.objectEach,ro=tV.pick,rr=function(){function t(t,e,i,o,r){this.isNew=!0,this.isNewLabel=!0,this.axis=t,this.pos=e,this.type=i||"",this.parameters=r||{},this.tickmarkOffset=this.parameters.tickmarkOffset,this.options=this.parameters.options,o4(this,"init"),i||o||this.addLabel()}return t.prototype.addLabel=function(){var t,e,i,o,r=this,n=r.axis,s=n.options,a=n.chart,l=n.categories,h=n.logarithmic,c=n.names,d=r.pos,p=ro(null===(t=r.options)||void 0===t?void 0:t.labels,s.labels),u=n.tickPositions,f=d===u[0],g=d===u[u.length-1],v=(!p.step||1===p.step)&&1===n.tickInterval,m=u.info,y=r.label,x=this.parameters.category||(l?ro(l[d],c[d],d):d);h&&rt(x)&&(x=o6(h.lin2log(x))),n.dateTime&&(m?e=(i=a.time.resolveDTLFormat(s.dateTimeLabelFormats[!s.grid&&m.higherRanks[d]||m.unitName])).main:rt(x)&&(e=n.dateTime.getXDateFormat(x,s.dateTimeLabelFormats||{}))),r.isFirst=f,r.isLast=g;var b={axis:n,chart:a,dateTimeLabelFormat:e,isFirst:f,isLast:g,pos:d,tick:r,tickPositionInfo:m,value:x};o4(this,"labelFormat",b);var w=function(t){return p.formatter?p.formatter.call(t,t):p.format?(t.text=n.defaultLabelFormatter.call(t),ii.format(p.format,t,a)):n.defaultLabelFormatter.call(t)},M=w.call(b,b),k=null==i?void 0:i.list;k?r.shortenLabel=function(){for(o=0;o<k.length;o++)if(o8(b,{dateTimeLabelFormat:k[o]}),y.attr({text:w.call(b,b)}),y.getBBox().width<n.getSlotWidth(r)-2*(p.padding||0))return;y.attr({text:""})}:r.shortenLabel=void 0,v&&n._addedPlotLB&&r.moveLabel(M,p),o5(y)||r.movedLabel?y&&y.textStr!==M&&!v&&(!y.textWidth||p.style.width||y.styles.width||y.css({width:null}),y.attr({text:M}),y.textPxLength=y.getBBox().width):(r.label=y=r.createLabel(M,p),r.rotation=0)},t.prototype.createLabel=function(t,e,i){var o=this.axis,r=o.chart,n=r.renderer,s=r.styledMode,a=e.style.whiteSpace,l=o5(t)&&e.enabled?n.text(t,null==i?void 0:i.x,null==i?void 0:i.y,e.useHTML).add(o.labelGroup):void 0;return l&&(s||l.css(re(e.style)),l.textPxLength=l.getBBox().width,!s&&a&&l.css({whiteSpace:a})),l},t.prototype.destroy=function(){o9(this,this.axis)},t.prototype.getPosition=function(t,e,i,o){var r=this.axis,n=r.chart,s=o&&n.oldChartHeight||n.chartHeight,a={x:t?o6(r.translate(e+i,void 0,void 0,o)+r.transB):r.left+r.offset+(r.opposite?(o&&n.oldChartWidth||n.chartWidth)-r.right-r.left:0),y:t?s-r.bottom+r.offset-(r.opposite?r.height:0):o6(s-r.translate(e+i,void 0,void 0,o)-r.transB)};return a.y=o3(a.y,-1e9,1e9),o4(this,"afterGetPosition",{pos:a}),a},t.prototype.getLabelPosition=function(t,e,i,o,r,n,s,a){var l,h,c=this.axis,d=c.transA,p=c.isLinked&&c.linkedParent?c.linkedParent.reversed:c.reversed,u=c.staggerLines,f=c.tickRotCorr||{x:0,y:0},g=o||c.reserveSpaceDefault?0:-c.labelOffset*("center"===c.labelAlign?.5:1),v=r.distance,m={};return l=0===c.side?i.rotation?-v:-i.getBBox().height:2===c.side?f.y+v:Math.cos(i.rotation*o2)*(f.y-i.getBBox(!1,0).height/2),o5(r.y)&&(l=0===c.side&&c.horiz?r.y+l:r.y),t=t+ro(r.x,[0,1,0,-1][c.side]*v)+g+f.x-(n&&o?n*d*(p?-1:1):0),e=e+l-(n&&!o?n*d*(p?1:-1):0),u&&(h=s/(a||1)%u,c.opposite&&(h=u-h-1),e+=h*(c.labelOffset/u)),m.x=t,m.y=Math.round(e),o4(this,"afterGetLabelPosition",{pos:m,tickmarkOffset:n,index:s}),m},t.prototype.getLabelSize=function(){return this.label?this.label.getBBox()[this.axis.horiz?"height":"width"]:0},t.prototype.getMarkPath=function(t,e,i,o,r,n){return void 0===r&&(r=!1),n.crispLine([["M",t,e],["L",t+(r?0:-i),e+(r?i:0)]],o)},t.prototype.handleOverflow=function(t){var e,i,o=this.axis,r=o.options.labels,n=t.x,s=o.chart.chartWidth,a=o.chart.spacing,l=ro(o.labelLeft,Math.min(o.pos,a[3])),h=ro(o.labelRight,Math.max(o.isRadial?0:o.pos+o.len,s-a[1])),c=this.label,d=this.rotation,p=o7(o.labelAlign||c.attr("align")),u=c.getBBox().width,f=o.getSlotWidth(this),g=f,v=1;d||"justify"!==r.overflow?d<0&&n-p*u<l?i=Math.round(n/Math.cos(d*o2)-l):d>0&&n+p*u>h&&(i=Math.round((s-n)/Math.cos(d*o2))):(n-p*u<l?g=t.x+g*(1-p)-l:n+(1-p)*u>h&&(g=h-t.x+g*p,v=-1),(g=Math.min(f,g))<f&&"center"===o.labelAlign&&(t.x+=v*(f-g-p*(f-Math.min(u,g)))),(u>g||o.autoRotation&&(null===(e=null==c?void 0:c.styles)||void 0===e?void 0:e.width))&&(i=g)),i&&c&&(this.shortenLabel?this.shortenLabel():c.css(o8({},{width:Math.floor(i)+"px",lineClamp:+!o.isRadial})))},t.prototype.moveLabel=function(t,e){var i,o=this,r=o.label,n=o.axis,s=!1;r&&r.textStr===t?(o.movedLabel=r,s=!0,delete o.label):ri(n.ticks,function(e){s||e.isNew||e===o||!e.label||e.label.textStr!==t||(o.movedLabel=e.label,s=!0,e.labelPos=o.movedLabel.xy,delete e.label)}),!s&&(o.labelPos||r)&&(i=o.labelPos||r.xy,o.movedLabel=o.createLabel(t,e,i),o.movedLabel&&o.movedLabel.attr({opacity:0}))},t.prototype.render=function(t,e,i){var o,r=this.axis,n=r.horiz,s=this.pos,a=ro(this.tickmarkOffset,r.tickmarkOffset),l=this.getPosition(n,s,a,e),h=l.x,c=l.y,d=r.pos,p=d+r.len,u=n?h:c,f=ro(i,null===(o=this.label)||void 0===o?void 0:o.newOpacity,1);!r.chart.polar&&(o6(u)<d||u>p)&&(i=0),null!=i||(i=1),this.isActive=!0,this.renderGridLine(e,i),this.renderMark(l,i),this.renderLabel(l,e,f,t),this.isNew=!1,o4(this,"afterRender")},t.prototype.renderGridLine=function(t,e){var i,o=this.axis,r=o.options,n={},s=this.pos,a=this.type,l=ro(this.tickmarkOffset,o.tickmarkOffset),h=o.chart.renderer,c=this.gridLine,d=r.gridLineWidth,p=r.gridLineColor,u=r.gridLineDashStyle;"minor"===this.type&&(d=r.minorGridLineWidth,p=r.minorGridLineColor,u=r.minorGridLineDashStyle),c||(o.chart.styledMode||(n.stroke=p,n["stroke-width"]=d||0,n.dashstyle=u),a||(n.zIndex=1),t&&(e=0),this.gridLine=c=h.path().attr(n).addClass("highcharts-"+(a?a+"-":"")+"grid-line").add(o.gridGroup)),c&&(i=o.getPlotLinePath({value:s+l,lineWidth:c.strokeWidth(),force:"pass",old:t,acrossPanes:!1}))&&c[t||this.isNew?"attr":"animate"]({d:i,opacity:e})},t.prototype.renderMark=function(t,e){var i=this.axis,o=i.options,r=i.chart.renderer,n=this.type,s=i.tickSize(n?n+"Tick":"tick"),a=t.x,l=t.y,h=ro(o["minor"!==n?"tickWidth":"minorTickWidth"],!n&&i.isXAxis?1:0),c=o["minor"!==n?"tickColor":"minorTickColor"],d=this.mark,p=!d;s&&(i.opposite&&(s[0]=-s[0]),d||(this.mark=d=r.path().addClass("highcharts-"+(n?n+"-":"")+"tick").add(i.axisGroup),i.chart.styledMode||d.attr({stroke:c,"stroke-width":h})),d[p?"attr":"animate"]({d:this.getMarkPath(a,l,s[0],d.strokeWidth(),i.horiz,r),opacity:e}))},t.prototype.renderLabel=function(t,e,i,o){var r=this.axis,n=r.horiz,s=r.options,a=this.label,l=s.labels,h=l.step,c=ro(this.tickmarkOffset,r.tickmarkOffset),d=t.x,p=t.y,u=!0;a&&rt(d)&&(a.xy=t=this.getLabelPosition(d,p,a,n,l,c,o,h),(!this.isFirst||this.isLast||s.showFirstLabel)&&(!this.isLast||this.isFirst||s.showLastLabel)?!n||l.step||l.rotation||e||0===i||this.handleOverflow(t):u=!1,h&&o%h&&(u=!1),u&&rt(t.y)?(t.opacity=i,a[this.isNewLabel?"attr":"animate"](t).show(!0),this.isNewLabel=!1):(a.hide(),this.isNewLabel=!0))},t.prototype.replaceMovedLabel=function(){var t=this.label,e=this.axis;t&&!this.isNew&&(t.animate({opacity:0},void 0,t.destroy),delete this.label),e.isDirty=!0,this.label=this.movedLabel,delete this.movedLabel},t}(),rn=oK.xAxis,rs=oK.yAxis,ra=o1.registerEventOptions,rl=tb.deg2rad,rh=tV.arrayMax,rc=tV.arrayMin,rd=tV.clamp,rp=tV.correctFloat,ru=tV.defined,rf=tV.destroyObjectProperties,rg=tV.erase,rv=tV.error,rm=tV.extend,ry=tV.fireEvent,rx=tV.getClosestDistance,rb=tV.insertItem,rw=tV.isArray,rM=tV.isNumber,rk=tV.isString,rS=tV.merge,rT=tV.normalizeTickInterval,rC=tV.objectEach,rA=tV.pick,rP=tV.relativeLength,rO=tV.removeEvent,rL=tV.splat,rE=tV.syncTimeout,rI=function(t,e){return rT(e,void 0,void 0,rA(t.options.allowDecimals,e<.5||void 0!==t.tickAmount),!!t.tickAmount)};rm(el,{xAxis:rn,yAxis:rS(rn,rs)});var rD=function(){function t(t,e,i){this.init(t,e,i)}return t.prototype.init=function(t,e,i){void 0===i&&(i=this.coll);var o,r,n,s,a="xAxis"===i,l=this.isZAxis||(t.inverted?!a:a);this.chart=t,this.horiz=l,this.isXAxis=a,this.coll=i,ry(this,"init",{userOptions:e}),this.opposite=rA(e.opposite,this.opposite),this.side=rA(e.side,this.side,l?2*!this.opposite:this.opposite?1:3),this.setOptions(e);var h=this.options,c=h.labels;null!==(o=this.type)&&void 0!==o||(this.type=h.type||"linear"),null!==(r=this.uniqueNames)&&void 0!==r||(this.uniqueNames=null===(n=h.uniqueNames)||void 0===n||n),ry(this,"afterSetType"),this.userOptions=e,this.minPixelPadding=0,this.reversed=rA(h.reversed,this.reversed),this.visible=h.visible,this.zoomEnabled=h.zoomEnabled,this.hasNames="category"===this.type||!0===h.categories,this.categories=rw(h.categories)&&h.categories||(this.hasNames?[]:void 0),this.names||(this.names=[],this.names.keys={}),this.plotLinesAndBandsGroups={},this.positiveValuesOnly=!!this.logarithmic,this.isLinked=ru(h.linkedTo),this.ticks={},this.labelEdge=[],this.minorTicks={},this.plotLinesAndBands=[],this.alternateBands={},null!==(s=this.len)&&void 0!==s||(this.len=0),this.minRange=this.userMinRange=h.minRange||h.maxZoom,this.range=h.range,this.offset=h.offset||0,this.max=void 0,this.min=void 0;var d=rA(h.crosshair,rL(t.options.tooltip.crosshairs)[+!a]);this.crosshair=!0===d?{}:d,-1===t.axes.indexOf(this)&&(a?t.axes.splice(t.xAxis.length,0,this):t.axes.push(this),rb(this,t[this.coll])),t.orderItems(this.coll),this.series=this.series||[],t.inverted&&!this.isZAxis&&a&&!ru(this.reversed)&&(this.reversed=!0),this.labelRotation=rM(c.rotation)?c.rotation:void 0,ra(this,h),ry(this,"afterInit")},t.prototype.setOptions=function(t){var e=this.horiz?{labels:{autoRotation:[-45],padding:3},margin:15}:{labels:{padding:1},title:{rotation:90*this.side}};this.options=rS(e,"yAxis"===this.coll?{title:{text:this.chart.options.lang.yAxisTitle}}:{},el[this.coll],t),ry(this,"afterSetOptions",{userOptions:t})},t.prototype.defaultLabelFormatter=function(){var t,e,i=this.axis,o=this.chart.numberFormatter,r=rM(this.value)?this.value:NaN,n=i.chart.time,s=i.categories,a=this.dateTimeLabelFormat,l=el.lang,h=l.numericSymbols,c=l.numericSymbolMagnitude||1e3,d=i.logarithmic?Math.abs(r):i.tickInterval,p=null==h?void 0:h.length;if(s)e="".concat(this.value);else if(a)e=n.dateFormat(a,r,!0);else if(p&&h&&d>=1e3)for(;p--&&void 0===e;)d>=(t=Math.pow(c,p+1))&&10*r%t==0&&null!==h[p]&&0!==r&&(e=o(r/t,-1)+h[p]);return void 0===e&&(e=Math.abs(r)>=1e4?o(r,-1):o(r,-1,void 0,"")),e},t.prototype.getSeriesExtremes=function(){var t,e=this;ry(this,"getSeriesExtremes",null,function(){e.hasVisibleSeries=!1,e.dataMin=e.dataMax=e.threshold=void 0,e.softThreshold=!e.isXAxis,e.series.forEach(function(i){if(i.reserveSpace()){var o=i.options,r=void 0,n=o.threshold,s=void 0,a=void 0;if(e.hasVisibleSeries=!0,e.positiveValuesOnly&&0>=(n||0)&&(n=void 0),e.isXAxis)(r=i.getColumn("x")).length&&(r=e.logarithmic?r.filter(function(t){return t>0}):r,s=(t=i.getXExtremes(r)).min,a=t.max,rM(s)||s instanceof Date||(r=r.filter(rM),s=(t=i.getXExtremes(r)).min,a=t.max),r.length&&(e.dataMin=Math.min(rA(e.dataMin,s),s),e.dataMax=Math.max(rA(e.dataMax,a),a)));else{var l=i.applyExtremes();rM(l.dataMin)&&(s=l.dataMin,e.dataMin=Math.min(rA(e.dataMin,s),s)),rM(l.dataMax)&&(a=l.dataMax,e.dataMax=Math.max(rA(e.dataMax,a),a)),ru(n)&&(e.threshold=n),(!o.softThreshold||e.positiveValuesOnly)&&(e.softThreshold=!1)}}})}),ry(this,"afterGetSeriesExtremes")},t.prototype.translate=function(t,e,i,o,r,n){var s,a=this.linkedParent||this,l=o&&a.old?a.old.min:a.min;if(!rM(l))return NaN;var h=a.minPixelPadding,c=(a.isOrdinal||(null===(s=a.brokenAxis)||void 0===s?void 0:s.hasBreaks)||a.logarithmic&&r)&&a.lin2val,d=1,p=0,u=o&&a.old?a.old.transA:a.transA,f=0;return u||(u=a.transA),i&&(d*=-1,p=a.len),a.reversed&&(d*=-1,p-=d*(a.sector||a.len)),e?(f=(t=t*d+p-h)/u+l,c&&(f=a.lin2val(f))):(c&&(t=a.val2lin(t)),f=d*(t-l)*u+p+d*h+(rM(n)?u*n:0),a.isRadial||(f=rp(f))),f},t.prototype.toPixels=function(t,e){var i,o;return this.translate(null!==(o=null===(i=this.chart)||void 0===i?void 0:i.time.parse(t))&&void 0!==o?o:NaN,!1,!this.horiz,void 0,!0)+(e?0:this.pos)},t.prototype.toValue=function(t,e){return this.translate(t-(e?0:this.pos),!0,!this.horiz,void 0,!0)},t.prototype.getPlotLinePath=function(t){var e,i,o,r,n,s=this,a=s.chart,l=s.left,h=s.top,c=t.old,d=t.value,p=t.lineWidth,u=c&&a.oldChartHeight||a.chartHeight,f=c&&a.oldChartWidth||a.chartWidth,g=s.transB,v=t.translatedValue,m=t.force;function y(t,e,i){return"pass"!==m&&(t<e||t>i)&&(m?t=rd(t,e,i):n=!0),t}var x={value:d,lineWidth:p,old:c,force:m,acrossPanes:t.acrossPanes,translatedValue:v};return ry(this,"getPlotLinePath",x,function(t){e=o=(v=rd(v=rA(v,s.translate(d,void 0,void 0,c)),-1e9,1e9))+g,i=r=u-v-g,rM(v)?s.horiz?(i=h,r=u-s.bottom+(s.options.isInternal?0:a.scrollablePixelsY||0),e=o=y(e,l,l+s.width)):(e=l,o=f-s.right+(a.scrollablePixelsX||0),i=r=y(i,h,h+s.height)):(n=!0,m=!1),t.path=n&&!m?void 0:a.renderer.crispLine([["M",e,i],["L",o,r]],p||1)}),x.path},t.prototype.getLinearTickPositions=function(t,e,i){var o,r,n,s=rp(Math.floor(e/t)*t),a=rp(Math.ceil(i/t)*t),l=[];if(rp(s+t)===s&&(n=20),this.single)return[e];for(o=s;o<=a&&(l.push(o),(o=rp(o+t,n))!==r);)r=o;return l},t.prototype.getMinorTickInterval=function(){var t=this.options,e=t.minorTicks,i=t.minorTickInterval;return!0===e?rA(i,"auto"):!1!==e?i:void 0},t.prototype.getMinorTickPositions=function(){var t,e,i=this.options,o=this.tickPositions,r=this.minorTickInterval,n=this.pointRangePadding||0,s=(this.min||0)-n,a=(this.max||0)+n,l=(null===(t=this.brokenAxis)||void 0===t?void 0:t.hasBreaks)?this.brokenAxis.unitLength:a-s,h=[];if(l&&l/r<this.len/3){var c=this.logarithmic;if(c)this.paddedTicks.forEach(function(t,e,i){e&&h.push.apply(h,c.getLogTickPositions(r,i[e-1],i[e],!0))});else if(this.dateTime&&"auto"===this.getMinorTickInterval())h=h.concat(this.getTimeTicks(this.dateTime.normalizeTimeTickInterval(r),s,a,i.startOfWeek));else for(e=s+(o[0]-s)%r;e<=a&&e!==h[0];e+=r)h.push(e)}return 0!==h.length&&this.trimTicks(h),h},t.prototype.adjustForMinRange=function(){var t,e,i,o,r,n,s,a=this.options,l=this.logarithmic,h=this.chart.time,c=this.max,d=this.min,p=this.minRange;this.isXAxis&&void 0===p&&!l&&(p=ru(a.min)||ru(a.max)||ru(a.floor)||ru(a.ceiling)?null:Math.min(5*(rx(this.series.map(function(t){var e=t.getColumn("x");return t.xIncrement?e.slice(0,2):e}))||0),this.dataMax-this.dataMin)),rM(c)&&rM(d)&&rM(p)&&c-d<p&&(r=this.dataMax-this.dataMin>=p,o=(p-c+d)/2,n=[d-o,null!==(t=h.parse(a.min))&&void 0!==t?t:d-o],r&&(n[2]=l?l.log2lin(this.dataMin):this.dataMin),s=[(d=rh(n))+p,null!==(e=h.parse(a.max))&&void 0!==e?e:d+p],r&&(s[2]=l?l.log2lin(this.dataMax):this.dataMax),(c=rc(s))-d<p&&(n[0]=c-p,n[1]=null!==(i=h.parse(a.min))&&void 0!==i?i:c-p,d=rh(n))),this.minRange=p,this.min=d,this.max=c},t.prototype.getClosest=function(){var t,e;if(this.categories)e=1;else{var i=[];this.series.forEach(function(t){var o=t.closestPointRange,r=t.getColumn("x");1===r.length?i.push(r[0]):t.sorted&&ru(o)&&t.reserveSpace()&&(e=ru(e)?Math.min(e,o):o)}),i.length&&(i.sort(function(t,e){return t-e}),t=rx([i]))}return t&&e?Math.min(t,e):t||e},t.prototype.nameToX=function(t){var e,i=rw(this.options.categories),o=i?this.categories:this.names,r=t.options.x;return t.series.requireSorting=!1,ru(r)||(r=this.uniqueNames&&o?i?o.indexOf(t.name):rA(o.keys[t.name],-1):t.series.autoIncrement()),-1===r?!i&&o&&(e=o.length):rM(r)&&(e=r),void 0!==e?(this.names[e]=t.name,this.names.keys[t.name]=e):t.x&&(e=t.x),e},t.prototype.updateNames=function(){var t=this,e=this.names;e.length>0&&(Object.keys(e.keys).forEach(function(t){delete e.keys[t]}),e.length=0,this.minRange=this.userMinRange,(this.series||[]).forEach(function(e){e.xIncrement=null,(!e.points||e.isDirtyData)&&(t.max=Math.max(t.max||0,e.dataTable.rowCount-1),e.processData(),e.generatePoints());var i=e.getColumn("x").slice();e.data.forEach(function(e,o){var r=i[o];(null==e?void 0:e.options)&&void 0!==e.name&&void 0!==(r=t.nameToX(e))&&r!==e.x&&(i[o]=e.x=r)}),e.dataTable.setColumn("x",i)}))},t.prototype.setAxisTranslation=function(){var t,e,i,o=this,r=o.max-o.min,n=o.linkedParent,s=!!o.categories,a=o.isXAxis,l=o.axisPointRange||0,h=0,c=0,d=o.transA;(a||s||l)&&(e=o.getClosest(),n?(h=n.minPointOffset,c=n.pointRangePadding):o.series.forEach(function(t){var i=s?1:a?rA(t.options.pointRange,e,0):o.axisPointRange||0,r=t.options.pointPlacement;if(l=Math.max(l,i),!o.single||s){var n=t.is("xrange")?!a:a;h=Math.max(h,n&&rk(r)?0:i/2),c=Math.max(c,n&&"on"===r?0:i)}}),i=(null===(t=o.ordinal)||void 0===t?void 0:t.slope)&&e?o.ordinal.slope/e:1,o.minPointOffset=h*=i,o.pointRangePadding=c*=i,o.pointRange=Math.min(l,o.single&&s?1:r),a&&(o.closestPointRange=e)),o.translationSlope=o.transA=d=o.staticScale||o.len/(r+c||1),o.transB=o.horiz?o.left:o.bottom,o.minPixelPadding=d*h,ry(this,"afterSetAxisTranslation")},t.prototype.minFromRange=function(){var t=this.max,e=this.min;return rM(t)&&rM(e)&&t-e||void 0},t.prototype.setTickInterval=function(t){var e,i,o,r,n,s,a,l,h,c=this.categories,d=this.chart,p=this.dataMax,u=this.dataMin,f=this.dateTime,g=this.isXAxis,v=this.logarithmic,m=this.options,y=this.softThreshold,x=d.time,b=rM(this.threshold)?this.threshold:void 0,w=this.minRange||0,M=m.ceiling,k=m.floor,S=m.linkedTo,T=m.softMax,C=m.softMin,A=rM(S)&&(null===(e=d[this.coll])||void 0===e?void 0:e[S]),P=m.tickPixelInterval,O=m.maxPadding,L=m.minPadding,E=0,I=rM(m.tickInterval)&&m.tickInterval>=0?m.tickInterval:void 0;if(f||c||A||this.getTickAmount(),l=rA(this.userMin,x.parse(m.min)),h=rA(this.userMax,x.parse(m.max)),A?(this.linkedParent=A,n=A.getExtremes(),this.min=rA(n.min,n.dataMin),this.max=rA(n.max,n.dataMax),this.type!==A.type&&rv(11,!0,d)):(y&&ru(b)&&rM(p)&&rM(u)&&(u>=b?(s=b,L=0):p<=b&&(a=b,O=0)),this.min=rA(l,s,u),this.max=rA(h,a,p)),rM(this.max)&&rM(this.min)&&(v&&(this.positiveValuesOnly&&!t&&0>=Math.min(this.min,rA(u,this.min))&&rv(10,!0,d),this.min=rp(v.log2lin(this.min),16),this.max=rp(v.log2lin(this.max),16)),this.range&&rM(u)&&(this.userMin=this.min=l=Math.max(u,this.minFromRange()||0),this.userMax=h=this.max,this.range=void 0)),ry(this,"foundExtremes"),this.adjustForMinRange(),rM(this.min)&&rM(this.max)){if(!rM(this.userMin)&&rM(C)&&C<this.min&&(this.min=l=C),!rM(this.userMax)&&rM(T)&&T>this.max&&(this.max=h=T),c||this.axisPointRange||(null===(i=this.stacking)||void 0===i?void 0:i.usePercentage)||A||!(E=this.max-this.min)||(!ru(l)&&L&&(this.min-=E*L),ru(h)||!O||(this.max+=E*O)),!rM(this.userMin)&&rM(k)&&(this.min=Math.max(this.min,k)),!rM(this.userMax)&&rM(M)&&(this.max=Math.min(this.max,M)),y&&rM(u)&&rM(p)){var D=b||0;!ru(l)&&this.min<D&&u>=D?this.min=m.minRange?Math.min(D,this.max-w):D:!ru(h)&&this.max>D&&p<=D&&(this.max=m.minRange?Math.max(D,this.min+w):D)}!d.polar&&this.min>this.max&&(ru(m.min)?this.max=this.min:ru(m.max)&&(this.min=this.max)),E=this.max-this.min}if(this.min!==this.max&&rM(this.min)&&rM(this.max)?A&&!I&&P===A.options.tickPixelInterval?this.tickInterval=I=A.tickInterval:this.tickInterval=rA(I,this.tickAmount?E/Math.max(this.tickAmount-1,1):void 0,c?1:E*P/Math.max(this.len,P)):this.tickInterval=1,g&&!t){var B=this.min!==(null===(o=this.old)||void 0===o?void 0:o.min)||this.max!==(null===(r=this.old)||void 0===r?void 0:r.max);this.series.forEach(function(t){var e;t.forceCrop=null===(e=t.forceCropping)||void 0===e?void 0:e.call(t),t.processData(B)}),ry(this,"postProcessData",{hasExtremesChanged:B})}this.setAxisTranslation(),ry(this,"initialAxisTranslation"),this.pointRange&&!I&&(this.tickInterval=Math.max(this.pointRange,this.tickInterval));var z=rA(m.minTickInterval,f&&!this.series.some(function(t){return!t.sorted})?this.closestPointRange:0);!I&&z&&this.tickInterval<z&&(this.tickInterval=z),f||v||I||(this.tickInterval=rI(this,this.tickInterval)),this.tickAmount||(this.tickInterval=this.unsquish()),this.setTickPositions()},t.prototype.setTickPositions=function(){var t,e,i,o=this.options,r=o.tickPositions,n=o.tickPositioner,s=this.getMinorTickInterval(),a=!this.isPanning,l=a&&o.startOnTick,h=a&&o.endOnTick,c=[];if(this.tickmarkOffset=this.categories&&"between"===o.tickmarkPlacement&&1===this.tickInterval?.5:0,this.single=this.min===this.max&&ru(this.min)&&!this.tickAmount&&(this.min%1==0||!1!==o.allowDecimals),r)c=r.slice();else if(rM(this.min)&&rM(this.max)){if(!(null===(t=this.ordinal)||void 0===t?void 0:t.positions)&&(this.max-this.min)/this.tickInterval>Math.max(2*this.len,200))c=[this.min,this.max],rv(19,!1,this.chart);else if(this.dateTime)c=this.getTimeTicks(this.dateTime.normalizeTimeTickInterval(this.tickInterval,o.units),this.min,this.max,o.startOfWeek,null===(e=this.ordinal)||void 0===e?void 0:e.positions,this.closestPointRange,!0);else if(this.logarithmic)c=this.logarithmic.getLogTickPositions(this.tickInterval,this.min,this.max);else for(var d=this.tickInterval,p=d;p<=2*d;)if(c=this.getLinearTickPositions(this.tickInterval,this.min,this.max),this.tickAmount&&c.length>this.tickAmount)this.tickInterval=rI(this,p*=1.1);else break;c.length>this.len&&(c=[c[0],c[c.length-1]])[0]===c[1]&&(c.length=1),n&&(this.tickPositions=c,(i=n.apply(this,[this.min,this.max]))&&(c=i))}this.tickPositions=c,this.minorTickInterval="auto"===s&&this.tickInterval?this.tickInterval/o.minorTicksPerMajor:s,this.paddedTicks=c.slice(0),this.trimTicks(c,l,h),!this.isLinked&&rM(this.min)&&rM(this.max)&&(this.single&&c.length<2&&!this.categories&&!this.series.some(function(t){return t.is("heatmap")&&"between"===t.options.pointPlacement})&&(this.min-=.5,this.max+=.5),r||i||this.adjustTickAmount()),ry(this,"afterSetTickPositions")},t.prototype.trimTicks=function(t,e,i){var o=t[0],r=t[t.length-1],n=!this.isOrdinal&&this.minPointOffset||0;if(ry(this,"trimTicks"),!this.isLinked||!this.grid){if(e&&o!==-1/0)this.min=o;else for(;this.min-n>t[0];)t.shift();if(i)this.max=r;else for(;this.max+n<t[t.length-1];)t.pop();0===t.length&&ru(o)&&!this.options.tickPositions&&t.push((r+o)/2)}},t.prototype.alignToOthers=function(){var t,e=this,i=e.chart,o=[this],r=e.options,n=i.options.chart,s="yAxis"===this.coll&&n.alignThresholds,a=[];if(e.thresholdAlignment=void 0,(!1!==n.alignTicks&&r.alignTicks||s)&&!1!==r.startOnTick&&!1!==r.endOnTick&&!e.logarithmic){var l=function(t){var e=t.horiz,i=t.options;return[e?i.left:i.top,i.width,i.height,i.pane].join(",")},h=l(this);i[this.coll].forEach(function(i){var r=i.series;r.length&&r.some(function(t){return t.visible})&&i!==e&&l(i)===h&&(t=!0,o.push(i))})}if(t&&s){o.forEach(function(t){var i=t.getThresholdAlignment(e);rM(i)&&a.push(i)});var c=a.length>1?a.reduce(function(t,e){return t+e},0)/a.length:void 0;o.forEach(function(t){t.thresholdAlignment=c})}return t},t.prototype.getThresholdAlignment=function(t){if((!rM(this.dataMin)||this!==t&&this.series.some(function(t){return t.isDirty||t.isDirtyData}))&&this.getSeriesExtremes(),rM(this.threshold)){var e=rd((this.threshold-(this.dataMin||0))/((this.dataMax||0)-(this.dataMin||0)),0,1);return this.options.reversed&&(e=1-e),e}},t.prototype.getTickAmount=function(){var t=this.options,e=t.tickPixelInterval,i=t.tickAmount;ru(t.tickInterval)||i||!(this.len<e)||this.isRadial||this.logarithmic||!t.startOnTick||!t.endOnTick||(i=2),!i&&this.alignToOthers()&&(i=Math.ceil(this.len/e)+1),i<4&&(this.finalTickAmt=i,i=5),this.tickAmount=i},t.prototype.adjustTickAmount=function(){var t,e,i,o=this,r=o.finalTickAmt,n=o.max,s=o.min,a=o.options,l=o.tickPositions,h=o.tickAmount,c=o.thresholdAlignment,d=null==l?void 0:l.length,p=rA(o.threshold,o.softThreshold?0:null),u=o.tickInterval,f=function(){return l.push(rp(l[l.length-1]+u))},g=function(){return l.unshift(rp(l[0]-u))};if(rM(c)&&(i=c<.5?Math.ceil(c*(h-1)):Math.floor(c*(h-1)),a.reversed&&(i=h-1-i)),o.hasData()&&rM(s)&&rM(n)){var v=function(){o.transA*=(d-1)/(h-1),o.min=a.startOnTick?l[0]:Math.min(s,l[0]),o.max=a.endOnTick?l[l.length-1]:Math.max(n,l[l.length-1])};if(rM(i)&&rM(o.threshold)){for(;l[i]!==p||l.length!==h||l[0]>s||l[l.length-1]<n;){for(l.length=0,l.push(o.threshold);l.length<h;)void 0===l[i]||l[i]>o.threshold?g():f();if(u>8*o.tickInterval)break;u*=2}v()}else if(d<h){for(;l.length<h;)l.length%2||s===p?f():g();v()}if(ru(r)){for(e=t=l.length;e--;)(3===r&&e%2==1||r<=2&&e>0&&e<t-1)&&l.splice(e,1);o.finalTickAmt=void 0}}},t.prototype.setScale=function(){var t,e,i,o,r,n=this.coll,s=this.stacking,a=!1,l=!1;this.series.forEach(function(t){var e;a=a||t.isDirtyData||t.isDirty,l=l||(null===(e=t.xAxis)||void 0===e?void 0:e.isDirty)||!1}),this.setAxisSize();var h=this.len!==(null===(t=this.old)||void 0===t?void 0:t.len);h||a||l||this.isLinked||this.forceRedraw||this.userMin!==(null===(e=this.old)||void 0===e?void 0:e.userMin)||this.userMax!==(null===(i=this.old)||void 0===i?void 0:i.userMax)||this.alignToOthers()?(s&&"yAxis"===n&&s.buildStacks(),this.forceRedraw=!1,this.userMinRange||(this.minRange=void 0),this.getSeriesExtremes(),this.setTickInterval(),s&&"xAxis"===n&&s.buildStacks(),this.isDirty||(this.isDirty=h||this.min!==(null===(o=this.old)||void 0===o?void 0:o.min)||this.max!==(null===(r=this.old)||void 0===r?void 0:r.max))):s&&s.cleanStacks(),a&&delete this.allExtremes,ry(this,"afterSetScale")},t.prototype.setExtremes=function(t,e,i,o,r){var n=this;void 0===i&&(i=!0);var s=this.chart;this.series.forEach(function(t){delete t.kdTree}),ry(this,"setExtremes",r=rm(r,{min:t=s.time.parse(t),max:e=s.time.parse(e)}),function(t){n.userMin=t.min,n.userMax=t.max,n.eventArgs=t,i&&s.redraw(o)})},t.prototype.setAxisSize=function(){var t=this.chart,e=this.options,i=e.offsets||[0,0,0,0],o=this.horiz,r=this.width=Math.round(rP(rA(e.width,t.plotWidth-i[3]+i[1]),t.plotWidth)),n=this.height=Math.round(rP(rA(e.height,t.plotHeight-i[0]+i[2]),t.plotHeight)),s=this.top=Math.round(rP(rA(e.top,t.plotTop+i[0]),t.plotHeight,t.plotTop)),a=this.left=Math.round(rP(rA(e.left,t.plotLeft+i[3]),t.plotWidth,t.plotLeft));this.bottom=t.chartHeight-n-s,this.right=t.chartWidth-r-a,this.len=Math.max(o?r:n,0),this.pos=o?a:s},t.prototype.getExtremes=function(){var t=this.logarithmic;return{min:t?rp(t.lin2log(this.min)):this.min,max:t?rp(t.lin2log(this.max)):this.max,dataMin:this.dataMin,dataMax:this.dataMax,userMin:this.userMin,userMax:this.userMax}},t.prototype.getThreshold=function(t){var e=this.logarithmic,i=e?e.lin2log(this.min):this.min,o=e?e.lin2log(this.max):this.max;return null===t||t===-1/0?t=i:t===1/0?t=o:i>t?t=i:o<t&&(t=o),this.translate(t,0,1,0,1)},t.prototype.autoLabelAlign=function(t){var e=(rA(t,0)-90*this.side+720)%360,i={align:"center"};return ry(this,"autoLabelAlign",i,function(t){e>15&&e<165?t.align="right":e>195&&e<345&&(t.align="left")}),i.align},t.prototype.tickSize=function(t){var e,i=this.options,o=rA(i["tick"===t?"tickWidth":"minorTickWidth"],"tick"===t&&this.isXAxis&&!this.categories?1:0),r=i["tick"===t?"tickLength":"minorTickLength"];o&&r&&("inside"===i[t+"Position"]&&(r=-r),e=[r,o]);var n={tickSize:e};return ry(this,"afterTickSize",n),n.tickSize},t.prototype.labelMetrics=function(){var t=this.chart.renderer,e=this.ticks,i=e[Object.keys(e)[0]]||{};return this.chart.renderer.fontMetrics(i.label||i.movedLabel||t.box)},t.prototype.unsquish=function(){var t,e,i=this.options.labels,o=i.padding||0,r=this.horiz,n=this.tickInterval,s=this.len/((+!!this.categories+this.max-this.min)/n),a=i.rotation,l=rp(.8*this.labelMetrics().h),h=Math.max(this.max-this.min,0),c=function(t){var e=(t+2*o)/(s||1);return(e=e>1?Math.ceil(e):1)*n>h&&t!==1/0&&s!==1/0&&h&&(e=Math.ceil(h/n)),rp(e*n)},d=n,p=Number.MAX_VALUE;if(r){if(!i.staggerLines&&(rM(a)?e=[a]:s<i.autoRotationLimit&&(e=i.autoRotation)),e)for(var u=void 0,f=void 0,g=0,v=e;g<v.length;g++){var m=v[g];(m===a||m&&m>=-90&&m<=90)&&(f=(u=c(Math.abs(l/Math.sin(rl*m))))+Math.abs(m/360))<p&&(p=f,t=m,d=u)}}else d=c(.75*l);return this.autoRotation=e,this.labelRotation=rA(t,rM(a)?a:0),i.step?n:d},t.prototype.getSlotWidth=function(t){var e=this.chart,i=this.horiz,o=this.options.labels,r=Math.max(this.tickPositions.length-+!this.categories,1),n=e.margin[3];if(t&&rM(t.slotWidth))return t.slotWidth;if(i&&o.step<2&&!this.isRadial)return o.rotation?0:(this.staggerLines||1)*this.len/r;if(!i){var s=o.style.width;if(void 0!==s)return parseInt(String(s),10);if(n)return n-e.spacing[3]}return .33*e.chartWidth},t.prototype.renderUnsquish=function(){var t,e=this.chart,i=e.renderer,o=this.tickPositions,r=this.ticks,n=this.options.labels,s=n.style,a=this.horiz,l=this.getSlotWidth(),h=Math.max(1,Math.round(l-(a?2*(n.padding||0):n.distance||0))),c={},d=this.labelMetrics(),p=s.lineClamp,u=null!=p?p:Math.floor(this.len/(o.length*d.h))||1,f=0;rk(n.rotation)||(c.rotation=n.rotation||0),o.forEach(function(t){var e,i=r[t];i.movedLabel&&i.replaceMovedLabel();var o=(null===(e=i.label)||void 0===e?void 0:e.textPxLength)||0;o>f&&(f=o)}),this.maxLabelLength=f,this.autoRotation?f>h&&f>d.h?c.rotation=this.labelRotation:this.labelRotation=0:l&&(t=h),c.rotation&&(t=f>.5*e.chartHeight?.33*e.chartHeight:f,p||(u=1)),this.labelAlign=n.align||this.autoLabelAlign(this.labelRotation),this.labelAlign&&(c.align=this.labelAlign),o.forEach(function(e){var i=r[e],o=null==i?void 0:i.label,n=s.width,a={};o&&(o.attr(c),i.shortenLabel?i.shortenLabel():t&&!n&&"nowrap"!==s.whiteSpace&&(t<(o.textPxLength||0)||"SPAN"===o.element.tagName)?o.css(rm(a,{width:""+t+"px",lineClamp:u})):!o.styles.width||a.width||n||o.css({width:"auto"}),i.rotation=c.rotation)},this),this.tickRotCorr=i.rotCorr(d.b,this.labelRotation||0,0!==this.side)},t.prototype.hasData=function(){return this.series.some(function(t){return t.hasData()})||this.options.showEmpty&&ru(this.min)&&ru(this.max)},t.prototype.addTitle=function(t){var e,i=this.chart.renderer,o=this.horiz,r=this.opposite,n=this.options.title,s=this.chart.styledMode;this.axisTitle||((e=n.textAlign)||(e=(o?{low:"left",middle:"center",high:"right"}:{low:r?"right":"left",middle:"center",high:r?"left":"right"})[n.align]),this.axisTitle=i.text(n.text||"",0,0,n.useHTML).attr({zIndex:7,rotation:n.rotation||0,align:e}).addClass("highcharts-axis-title"),s||this.axisTitle.css(rS(n.style)),this.axisTitle.add(this.axisGroup),this.axisTitle.isNew=!0),s||n.style.width||this.isRadial||this.axisTitle.css({width:this.len+"px"}),this.axisTitle[t?"show":"hide"](t)},t.prototype.generateTick=function(t){var e=this.ticks;e[t]?e[t].addLabel():e[t]=new rr(this,t)},t.prototype.createGroups=function(){var t=this,e=this.axisParent,i=this.chart,o=this.coll,r=this.options,n=i.renderer,s=function(i,s,a){return n.g(i).attr({zIndex:a}).addClass("highcharts-".concat(o.toLowerCase()).concat(s," ")+(t.isRadial?"highcharts-radial-axis".concat(s," "):"")+(r.className||"")).add(e)};this.axisGroup||(this.gridGroup=s("grid","-grid",r.gridZIndex),this.axisGroup=s("axis","",r.zIndex),this.labelGroup=s("axis-labels","-labels",r.labels.zIndex))},t.prototype.getOffset=function(){var t,e,i,o,r=this,n=r.chart,s=r.horiz,a=r.options,l=r.side,h=r.ticks,c=r.tickPositions,d=r.coll,p=n.inverted&&!r.isZAxis?[1,0,3,2][l]:l,u=r.hasData(),f=a.title,g=a.labels,v=rM(a.crossing),m=n.axisOffset,y=n.clipOffset,x=[-1,1,1,-1][l],b=0,w=0,M=0;if(r.showAxis=t=u||a.showEmpty,r.staggerLines=r.horiz&&g.staggerLines||void 0,r.createGroups(),u||r.isLinked?(c.forEach(function(t){r.generateTick(t)}),r.renderUnsquish(),r.reserveSpaceDefault=0===l||2===l||({1:"left",3:"right"})[l]===r.labelAlign,rA(g.reserveSpace,!v&&null,"center"===r.labelAlign||null,r.reserveSpaceDefault)&&c.forEach(function(t){M=Math.max(h[t].getLabelSize(),M)}),r.staggerLines&&(M*=r.staggerLines),r.labelOffset=M*(r.opposite?-1:1)):rC(h,function(t,e){t.destroy(),delete h[e]}),(null==f?void 0:f.text)&&!1!==f.enabled&&(r.addTitle(t),t&&!v&&!1!==f.reserveSpace&&(r.titleOffset=b=r.axisTitle.getBBox()[s?"height":"width"],w=ru(e=f.offset)?0:rA(f.margin,s?5:10))),r.renderLine(),r.offset=x*rA(a.offset,m[l]?m[l]+(a.margin||0):0),r.tickRotCorr=r.tickRotCorr||{x:0,y:0},o=0===l?-r.labelMetrics().h:2===l?r.tickRotCorr.y:0,i=Math.abs(M)+w,M&&(i-=o,i+=x*(s?rA(g.y,r.tickRotCorr.y+x*g.distance):rA(g.x,x*g.distance))),r.axisTitleMargin=rA(e,i),r.getMaxLabelDimensions&&(r.maxLabelDimensions=r.getMaxLabelDimensions(h,c)),"colorAxis"!==d&&y){var k=this.tickSize("tick");m[l]=Math.max(m[l],(r.axisTitleMargin||0)+b+x*r.offset,i,(null==c?void 0:c.length)&&k?k[0]+x*r.offset:0);var S=!r.axisLine||a.offset?0:r.axisLine.strokeWidth()/2;y[p]=Math.max(y[p],S)}ry(this,"afterGetOffset")},t.prototype.getLinePath=function(t){var e=this.chart,i=this.opposite,o=this.offset,r=this.horiz,n=this.left+(i?this.width:0)+o,s=e.chartHeight-this.bottom-(i?this.height:0)+o;return i&&(t*=-1),e.renderer.crispLine([["M",r?this.left:n,r?s:this.top],["L",r?e.chartWidth-this.right:n,r?s:e.chartHeight-this.bottom]],t)},t.prototype.renderLine=function(){this.axisLine||(this.axisLine=this.chart.renderer.path().addClass("highcharts-axis-line").add(this.axisGroup),this.chart.styledMode||this.axisLine.attr({stroke:this.options.lineColor,"stroke-width":this.options.lineWidth,zIndex:7}))},t.prototype.getTitlePosition=function(t){var e=this.horiz,i=this.left,o=this.top,r=this.len,n=this.options.title,s=e?i:o,a=this.opposite,l=this.offset,h=n.x,c=n.y,d=this.chart.renderer.fontMetrics(t),p=t?Math.max(t.getBBox(!1,0).height-d.h-1,0):0,u={low:s+(e?0:r),middle:s+r/2,high:s+(e?r:0)}[n.align],f=(e?o+this.height:i)+(e?1:-1)*(a?-1:1)*(this.axisTitleMargin||0)+[-p,p,d.f,-p][this.side],g={x:e?u+h:f+(a?this.width:0)+l+h,y:e?f+c-(a?this.height:0)+l:u+c};return ry(this,"afterGetTitlePosition",{titlePosition:g}),g},t.prototype.renderMinorTick=function(t,e){var i=this.minorTicks;i[t]||(i[t]=new rr(this,t,"minor")),e&&i[t].isNew&&i[t].render(null,!0),i[t].render(null,!1,1)},t.prototype.renderTick=function(t,e,i){var o,r=this.isLinked,n=this.ticks;(!r||t>=this.min&&t<=this.max||(null===(o=this.grid)||void 0===o?void 0:o.isColumn))&&(n[t]||(n[t]=new rr(this,t)),i&&n[t].isNew&&n[t].render(e,!0,-1),n[t].render(e))},t.prototype.render=function(){var t,e,i=this,o=i.chart,r=i.logarithmic,n=o.renderer,s=i.options,a=i.isLinked,l=i.tickPositions,h=i.axisTitle,c=i.ticks,d=i.minorTicks,p=i.alternateBands,u=s.stackLabels,f=s.alternateGridColor,g=s.crossing,v=i.tickmarkOffset,m=i.axisLine,y=i.showAxis,x=eB(n.globalAnimation);if(i.labelEdge.length=0,i.overlap=!1,[c,d,p].forEach(function(t){rC(t,function(t){t.isActive=!1})}),rM(g)){var b=this.isXAxis?o.yAxis[0]:o.xAxis[0],w=[1,-1,-1,1][this.side];if(b){var M=b.toPixels(g,!0);i.horiz&&(M=b.len-M),i.offset=w*M}}if(i.hasData()||a){var k=i.chart.hasRendered&&i.old&&rM(i.old.min);i.minorTickInterval&&!i.categories&&i.getMinorTickPositions().forEach(function(t){i.renderMinorTick(t,k)}),l.length&&(l.forEach(function(t,e){i.renderTick(t,e,k)}),v&&(0===i.min||i.single)&&(c[-1]||(c[-1]=new rr(i,-1,null,!0)),c[-1].render(-1))),f&&l.forEach(function(n,s){e=void 0!==l[s+1]?l[s+1]+v:i.max-v,s%2==0&&n<i.max&&e<=i.max+(o.polar?-v:v)&&(p[n]||(p[n]=new tb.PlotLineOrBand(i,{})),t=n+v,p[n].options={from:r?r.lin2log(t):t,to:r?r.lin2log(e):e,color:f,className:"highcharts-alternate-grid"},p[n].render(),p[n].isActive=!0)}),i._addedPlotLB||(i._addedPlotLB=!0,(s.plotLines||[]).concat(s.plotBands||[]).forEach(function(t){i.addPlotBandOrLine(t)}))}[c,d,p].forEach(function(t){var e=[],i=x.duration;rC(t,function(t,i){t.isActive||(t.render(i,!1,0),t.isActive=!1,e.push(i))}),rE(function(){for(var i=e.length;i--;)t[e[i]]&&!t[e[i]].isActive&&(t[e[i]].destroy(),delete t[e[i]])},t!==p&&o.hasRendered&&i?i:0)}),m&&(m[m.isPlaced?"animate":"attr"]({d:this.getLinePath(m.strokeWidth())}),m.isPlaced=!0,m[y?"show":"hide"](y)),h&&y&&(h[h.isNew?"attr":"animate"](i.getTitlePosition(h)),h.isNew=!1),(null==u?void 0:u.enabled)&&i.stacking&&i.stacking.renderStackTotals(),i.old={len:i.len,max:i.max,min:i.min,transA:i.transA,userMax:i.userMax,userMin:i.userMin},i.isDirty=!1,ry(this,"afterRender")},t.prototype.redraw=function(){this.visible&&(this.render(),this.plotLinesAndBands.forEach(function(t){t.render()})),this.series.forEach(function(t){t.isDirty=!0})},t.prototype.getKeepProps=function(){return this.keepProps||t.keepProps},t.prototype.destroy=function(t){var e=this,i=e.plotLinesAndBands,o=this.eventOptions;if(ry(this,"destroy",{keepEvents:t}),t||rO(e),[e.ticks,e.minorTicks,e.alternateBands].forEach(function(t){rf(t)}),i)for(var r=i.length;r--;)i[r].destroy();for(var n in["axisLine","axisTitle","axisGroup","gridGroup","labelGroup","cross","scrollbar"].forEach(function(t){e[t]&&(e[t]=e[t].destroy())}),e.plotLinesAndBandsGroups)e.plotLinesAndBandsGroups[n]=e.plotLinesAndBandsGroups[n].destroy();rC(e,function(t,i){-1===e.getKeepProps().indexOf(i)&&delete e[i]}),this.eventOptions=o},t.prototype.drawCrosshair=function(t,e){var i,o,r,n,s,a,l=this.crosshair,h=null===(i=null==l?void 0:l.snap)||void 0===i||i,c=this.chart,d=this.cross;if(ry(this,"drawCrosshair",{e:t,point:e}),t||(t=null===(o=this.cross)||void 0===o?void 0:o.e),l&&!1!==(ru(e)||!h)){if(h?ru(e)&&(n=rA("colorAxis"!==this.coll?e.crosshairPos:null,this.isXAxis?e.plotX:this.len-e.plotY)):n=t&&(this.horiz?t.chartX-this.pos:this.len-t.chartY+this.pos),ru(n)&&(a={value:e&&(this.isXAxis?e.x:rA(e.stackY,e.y)),translatedValue:n},c.polar&&rm(a,{isCrosshair:!0,chartX:null==t?void 0:t.chartX,chartY:null==t?void 0:t.chartY,point:e}),r=this.getPlotLinePath(a)||null),!ru(r)){this.hideCrosshair();return}s=this.categories&&!this.isRadial,d||(this.cross=d=c.renderer.path().addClass("highcharts-crosshair highcharts-crosshair-"+(s?"category ":"thin ")+(l.className||"")).attr({zIndex:rA(l.zIndex,2)}).add(),!c.styledMode&&(d.attr({stroke:l.color||(s?eb.parse("#ccd3ff").setOpacity(.25).get():"#cccccc"),"stroke-width":rA(l.width,1)}).css({"pointer-events":"none"}),l.dashStyle&&d.attr({dashstyle:l.dashStyle}))),d.show().attr({d:r}),s&&!l.width&&d.attr({"stroke-width":this.transA}),this.cross.e=t}else this.hideCrosshair();ry(this,"afterDrawCrosshair",{e:t,point:e})},t.prototype.hideCrosshair=function(){this.cross&&this.cross.hide(),ry(this,"afterHideCrosshair")},t.prototype.update=function(t,e){var i=this.chart;t=rS(this.userOptions,t),this.destroy(!0),this.init(i,t),i.isDirtyBox=!0,rA(e,!0)&&i.redraw()},t.prototype.remove=function(t){for(var e=this.chart,i=this.coll,o=this.series,r=o.length;r--;)o[r]&&o[r].remove(!1);rg(e.axes,this),rg(e[i]||[],this),e.orderItems(i),this.destroy(),e.isDirtyBox=!0,rA(t,!0)&&e.redraw()},t.prototype.setTitle=function(t,e){this.update({title:t},e)},t.prototype.setCategories=function(t,e){this.update({categories:t},e)},t.keepProps=["coll","extKey","hcEvents","len","names","series","userMax","userMin"],t}(),rB=tV.addEvent,rz=tV.getMagnitude,rj=tV.normalizeTickInterval,rN=tV.timeUnits;!function(t){function e(){return this.chart.time.getTimeTicks.apply(this.chart.time,arguments)}function i(){if("datetime"!==this.type){this.dateTime=void 0;return}this.dateTime||(this.dateTime=new o(this))}t.compose=function(t){return t.keepProps.includes("dateTime")||(t.keepProps.push("dateTime"),t.prototype.getTimeTicks=e,rB(t,"afterSetType",i)),t};var o=function(){function t(t){this.axis=t}return t.prototype.normalizeTimeTickInterval=function(t,e){var i,o=e||[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2]],["week",[1,2]],["month",[1,2,3,4,6]],["year",null]],r=o[o.length-1],n=rN[r[0]],s=r[1];for(i=0;i<o.length&&(n=rN[(r=o[i])[0]],s=r[1],!o[i+1]||!(t<=(n*s[s.length-1]+rN[o[i+1][0]])/2));i++);n===rN.year&&t<5*n&&(s=[1,2,5]);var a=rj(t/n,s,"year"===r[0]?Math.max(rz(t/n),1):1);return{unitRange:n,count:a,unitName:r[0]}},t.prototype.getXDateFormat=function(t,e){var i=this.axis,o=i.chart.time;return i.closestPointRange?o.getDateFormat(i.closestPointRange,t,i.options.startOfWeek,e)||o.resolveDTLFormat(e.year).main:o.resolveDTLFormat(e.day).main},t}();t.Additions=o}(Q||(Q={}));var rR=Q,rW=tV.addEvent,rX=tV.normalizeTickInterval,r_=tV.pick;!function(t){function e(){var t;"logarithmic"!==this.type?this.logarithmic=void 0:null!==(t=this.logarithmic)&&void 0!==t||(this.logarithmic=new o(this))}function i(){var t=this.logarithmic;t&&(this.lin2val=function(e){return t.lin2log(e)},this.val2lin=function(e){return t.log2lin(e)})}t.compose=function(t){return t.keepProps.includes("logarithmic")||(t.keepProps.push("logarithmic"),rW(t,"afterSetType",e),rW(t,"afterInit",i)),t};var o=function(){function t(t){this.axis=t}return t.prototype.getLogTickPositions=function(t,e,i,o){var r=this.axis,n=r.len,s=r.options,a=[];if(o||(this.minorAutoInterval=void 0),t>=.5)t=Math.round(t),a=r.getLinearTickPositions(t,e,i);else if(t>=.08){var l=Math.floor(e),h=void 0,c=void 0,d=void 0,p=void 0,u=void 0,f=void 0,g=void 0;for(h=t>.3?[1,2,4]:t>.15?[1,2,4,6,8]:[1,2,3,4,5,6,7,8,9],c=l;c<i+1&&!g;c++)for(d=0,p=h.length;d<p&&!g;d++)(u=this.log2lin(this.lin2log(c)*h[d]))>e&&(!o||f<=i)&&void 0!==f&&a.push(f),f>i&&(g=!0),f=u}else{var v=this.lin2log(e),m=this.lin2log(i),y=o?r.getMinorTickInterval():s.tickInterval,x=s.tickPixelInterval/(o?5:1),b=o?n/r.tickPositions.length:n;t=rX(t=r_("auto"===y?null:y,this.minorAutoInterval,(m-v)*x/(b||1))),a=r.getLinearTickPositions(t,v,m).map(this.log2lin),o||(this.minorAutoInterval=t/5)}return o||(r.tickInterval=t),a},t.prototype.lin2log=function(t){return Math.pow(10,t)},t.prototype.log2lin=function(t){return Math.log(t)/Math.LN10},t}();t.Additions=o}(tt||(tt={}));var rF=tt,rY=tV.erase,rG=tV.extend,rH=tV.isNumber;!function(t){var e;function i(t){return this.addPlotBandOrLine(t,"plotBands")}function o(t,i){var o=this,r=this.userOptions,n=new e(this,t);if(this.visible&&(n=n.render()),n){if(this._addedPlotLB||(this._addedPlotLB=!0,(r.plotLines||[]).concat(r.plotBands||[]).forEach(function(t){o.addPlotBandOrLine(t)})),i){var s=r[i]||[];s.push(t),r[i]=s}this.plotLinesAndBands.push(n)}return n}function r(t){return this.addPlotBandOrLine(t,"plotLines")}function n(t,e,i){i=i||this.options;var o,r,n=this.getPlotLinePath({value:e,force:!0,acrossPanes:i.acrossPanes}),s=[],a=this.horiz,l=!rH(this.min)||!rH(this.max)||t<this.min&&e<this.min||t>this.max&&e>this.max,h=this.getPlotLinePath({value:t,force:!0,acrossPanes:i.acrossPanes}),c=1;if(h&&n)for(l&&(r=h.toString()===n.toString(),c=0),o=0;o<h.length;o+=2){var d=h[o],p=h[o+1],u=n[o],f=n[o+1];("M"===d[0]||"L"===d[0])&&("M"===p[0]||"L"===p[0])&&("M"===u[0]||"L"===u[0])&&("M"===f[0]||"L"===f[0])&&(a&&u[1]===d[1]?(u[1]+=c,f[1]+=c):a||u[2]!==d[2]||(u[2]+=c,f[2]+=c),s.push(["M",d[1],d[2]],["L",p[1],p[2]],["L",f[1],f[2]],["L",u[1],u[2]],["Z"])),s.isFlat=r}return s}function s(t){this.removePlotBandOrLine(t)}function a(t){var e=this.plotLinesAndBands,i=this.options,o=this.userOptions;if(e){for(var r=e.length;r--;)e[r].id===t&&e[r].destroy();[i.plotLines||[],o.plotLines||[],i.plotBands||[],o.plotBands||[]].forEach(function(e){var i;for(r=e.length;r--;)(null===(i=e[r])||void 0===i?void 0:i.id)===t&&rY(e,e[r])})}}function l(t){this.removePlotBandOrLine(t)}t.compose=function(t,h){var c=h.prototype;return c.addPlotBand||(e=t,rG(c,{addPlotBand:i,addPlotLine:r,addPlotBandOrLine:o,getPlotBandPath:n,removePlotBand:s,removePlotLine:l,removePlotBandOrLine:a})),h}}(te||(te={}));var rV=te,rU=function(){return(rU=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},rZ=tV.addEvent,rq=tV.arrayMax,rK=tV.arrayMin,r$=tV.defined,rJ=tV.destroyObjectProperties,rQ=tV.erase,r0=tV.fireEvent,r1=tV.merge,r2=tV.objectEach,r3=tV.pick,r6=function(){function t(t,e){this.axis=t,this.options=e,this.id=e.id}return t.compose=function(e,i){return rZ(e,"afterInit",function(){var t=this;this.labelCollectors.push(function(){for(var e,i=[],o=0,r=t.axes;o<r.length;o++)for(var n=r[o],s=0,a=n.plotLinesAndBands;s<a.length;s++){var l=a[s],h=l.label,c=l.options;!h||(null===(e=null==c?void 0:c.label)||void 0===e?void 0:e.allowOverlap)||i.push(h)}return i})}),rV.compose(t,i)},t.prototype.render=function(){var t=this;r0(this,"render");var e,i,o,r,n=this.axis,s=this.options,a=n.horiz,l=n.logarithmic,h=s.color,c=s.events,d=s.zIndex,p=void 0===d?0:d,u=n.chart,f=u.renderer,g=u.time,v={},m=g.parse(s.to),y=g.parse(s.from),x=g.parse(s.value),b=s.borderWidth,w=s.label,M=this.label,k=this.svgElem,S=[],T=r$(y)&&r$(m),C=r$(x),A=!k,P={class:"highcharts-plot-"+(T?"band ":"line ")+(s.className||"")},O=T?"bands":"lines";if(!n.chart.styledMode&&(C?(P.stroke=h||"#999999",P["stroke-width"]=r3(s.width,1),s.dashStyle&&(P.dashstyle=s.dashStyle)):T&&(P.fill=h||"#e6e9ff",b&&(P.stroke=s.borderColor,P["stroke-width"]=b))),v.zIndex=p,O+="-"+p,(r=n.plotLinesAndBandsGroups[O])||(n.plotLinesAndBandsGroups[O]=r=f.g("plot-"+O).attr(v).add()),k||(this.svgElem=k=f.path().attr(P).add(r)),r$(x))S=n.getPlotLinePath({value:null!==(e=null==l?void 0:l.log2lin(x))&&void 0!==e?e:x,lineWidth:k.strokeWidth(),acrossPanes:s.acrossPanes});else{if(!(r$(y)&&r$(m)))return;S=n.getPlotBandPath(null!==(i=null==l?void 0:l.log2lin(y))&&void 0!==i?i:y,null!==(o=null==l?void 0:l.log2lin(m))&&void 0!==o?o:m,s)}return!this.eventsAdded&&c&&(r2(c,function(e,i){null==k||k.on(i,function(e){c[i].apply(t,[e])})}),this.eventsAdded=!0),(A||!k.d)&&(null==S?void 0:S.length)?k.attr({d:S}):k&&(S?(k.show(),k.animate({d:S})):k.d&&(k.hide(),M&&(this.label=M=M.destroy()))),w&&(r$(w.text)||r$(w.formatter))&&(null==S?void 0:S.length)&&n.width>0&&n.height>0&&!S.isFlat?(w=r1(rU({align:a&&T?"center":void 0,x:a?!T&&4:10,verticalAlign:!a&&T?"middle":void 0,y:a?T?16:10:T?6:-4,rotation:a&&!T?90:0},T?{inside:!0}:{}),w),this.renderLabel(w,S,T,p)):M&&M.hide(),this},t.prototype.renderLabel=function(t,e,i,o){var r,n=this.axis,s=n.chart.renderer,a=t.inside,l=this.label;l||(this.label=l=s.text(this.getLabelText(t),0,0,t.useHTML).attr({align:t.textAlign||t.align,rotation:t.rotation,class:"highcharts-plot-"+(i?"band":"line")+"-label "+(t.className||""),zIndex:o}),n.chart.styledMode||l.css(r1({fontSize:"0.8em",textOverflow:i&&!a?"":"ellipsis"},t.style)),l.add());var h=e.xBounds||[e[0][1],e[1][1],i?e[2][1]:e[0][1]],c=e.yBounds||[e[0][2],e[1][2],i?e[2][2]:e[0][2]],d=rK(h),p=rK(c),u=rq(h)-d;l.align(t,!1,{x:d,y:p,width:u,height:rq(c)-p}),l.alignAttr.y-=s.fontMetrics(l).b,(!l.alignValue||"left"===l.alignValue||r$(a))&&l.css({width:((null===(r=t.style)||void 0===r?void 0:r.width)||(i&&a?u:90===l.rotation?n.height-(l.alignAttr.y-n.top):(t.clip?n.width:n.chart.chartWidth)-(l.alignAttr.x-n.left)))+"px"}),l.show(!0)},t.prototype.getLabelText=function(t){return r$(t.formatter)?t.formatter.call(this):t.text},t.prototype.destroy=function(){rQ(this.axis.plotLinesAndBands,this),delete this.axis,rJ(this)},t}(),r5=ii.format,r9=tb.composed,r8=tb.dateFormats,r4=tb.doc,r7=tb.isSafari,nt=ih.distribute,ne=tV.addEvent,ni=tV.clamp,no=tV.css,nr=tV.discardElement,nn=tV.extend,ns=tV.fireEvent,na=tV.getAlignFactor,nl=tV.isArray,nh=tV.isNumber,nc=tV.isObject,nd=tV.isString,np=tV.merge,nu=tV.pick,nf=tV.pushUnique,ng=tV.splat,nv=tV.syncTimeout,nm=function(){function t(t,e,i){this.allowShared=!0,this.crosshairs=[],this.distance=0,this.isHidden=!0,this.isSticky=!1,this.options={},this.outside=!1,this.chart=t,this.init(t,e),this.pointer=i}return t.prototype.bodyFormatter=function(t){return t.map(function(t){var e=t.series.tooltipOptions,i=t.formatPrefix||"point";return(e[i+"Formatter"]||t.tooltipFormatter).call(t,e[i+"Format"]||"")})},t.prototype.cleanSplit=function(t){this.chart.series.forEach(function(e){var i=null==e?void 0:e.tt;i&&(!i.isActive||t?e.tt=i.destroy():i.isActive=!1)})},t.prototype.defaultFormatter=function(t){var e,i=this.points||ng(this);return(e=(e=[t.headerFooterFormatter(i[0])]).concat(t.bodyFormatter(i))).push(t.headerFooterFormatter(i[0],!0)),e},t.prototype.destroy=function(){this.label&&(this.label=this.label.destroy()),this.split&&(this.cleanSplit(!0),this.tt&&(this.tt=this.tt.destroy())),this.renderer&&(this.renderer=this.renderer.destroy(),nr(this.container)),tV.clearTimeout(this.hideTimer)},t.prototype.getAnchor=function(t,e){var i,o,r=this.chart,n=this.pointer,s=r.inverted,a=r.plotTop,l=r.plotLeft;if((null===(i=(t=ng(t))[0].series)||void 0===i?void 0:i.yAxis)&&!t[0].series.yAxis.options.reversedStacks&&(t=t.slice().reverse()),this.followPointer&&e)void 0===e.chartX&&(e=n.normalize(e)),o=[e.chartX-l,e.chartY-a];else if(t[0].tooltipPos)o=t[0].tooltipPos;else{var h=0,c=0;t.forEach(function(t){var e=t.pos(!0);e&&(h+=e[0],c+=e[1])}),h/=t.length,c/=t.length,this.shared&&t.length>1&&e&&(s?h=e.chartX:c=e.chartY),o=[h-l,c-a]}return o.map(Math.round)},t.prototype.getClassName=function(t,e,i){var o=this.options,r=t.series,n=r.options;return[o.className,"highcharts-label",i&&"highcharts-tooltip-header",e?"highcharts-tooltip-box":"highcharts-tooltip",!i&&"highcharts-color-"+nu(t.colorIndex,r.colorIndex),null==n?void 0:n.className].filter(nd).join(" ")},t.prototype.getLabel=function(t){var e,i=void 0===t?{anchorX:0,anchorY:0}:t,o=i.anchorX,r=i.anchorY,n=this,s=this.chart.styledMode,a=this.options,l=this.split&&this.allowShared,h=this.container,c=this.chart.renderer;if(this.label){var d=!this.label.hasClass("highcharts-label");(!l&&d||l&&!d)&&this.destroy()}if(!this.label){if(this.outside){var p=this.chart,u=p.options.chart.style,f=io.getRendererType();this.container=h=tb.doc.createElement("div"),h.className="highcharts-tooltip-container "+(p.renderTo.className.match(/(highcharts[a-zA-Z0-9-]+)\s?/gm)||""),no(h,{position:"absolute",top:"1px",pointerEvents:"none",zIndex:Math.max(this.options.style.zIndex||0,((null==u?void 0:u.zIndex)||0)+3)}),this.renderer=c=new f(h,0,0,u,void 0,void 0,c.styledMode)}if(l?this.label=c.g("tooltip"):(this.label=c.label("",o,r,a.shape||"callout",void 0,void 0,a.useHTML,void 0,"tooltip").attr({padding:a.padding,r:a.borderRadius}),s||this.label.attr({fill:a.backgroundColor,"stroke-width":a.borderWidth||0}).css(a.style).css({pointerEvents:a.style.pointerEvents||(this.shouldStickOnContact()?"auto":"none")})),n.outside){var g=this.label;[g.xSetter,g.ySetter].forEach(function(t,e){g[e?"ySetter":"xSetter"]=function(i){t.call(g,n.distance),g[e?"y":"x"]=i,h&&(h.style[e?"top":"left"]=""+i+"px")}})}this.label.attr({zIndex:8}).shadow(null!==(e=a.shadow)&&void 0!==e?e:!a.fixed).add()}return h&&!h.parentElement&&tb.doc.body.appendChild(h),this.label},t.prototype.getPlayingField=function(){var t=r4.body,e=r4.documentElement,i=this.chart,o=this.distance,r=this.outside;return{width:r?Math.max(t.scrollWidth,e.scrollWidth,t.offsetWidth,e.offsetWidth,e.clientWidth)-2*o-2:i.chartWidth,height:r?Math.max(t.scrollHeight,e.scrollHeight,t.offsetHeight,e.offsetHeight,e.clientHeight):i.chartHeight}},t.prototype.getPosition=function(t,e,i){var o,r,n,s=this.distance,a=this.chart,l=this.outside,h=this.pointer,c=a.inverted,d=a.plotLeft,p=a.plotTop,u=a.polar,f=i.plotX,g=void 0===f?0:f,v=i.plotY,m=void 0===v?0:v,y={},x=c&&i.h||0,b=this.getPlayingField(),w=b.height,M=b.width,k=h.getChartPosition(),S=function(t){return t*k.scaleX},T=function(t){return t*k.scaleY},C=function(i){var o="x"===i;return[i,o?M:w,o?t:e].concat(l?[o?S(t):T(e),o?k.left-s+S(g+d):k.top-s+T(m+p),0,o?M:w]:[o?t:e,o?g+d:m+p,o?d:p,o?d+a.plotWidth:p+a.plotHeight])},A=C("y"),P=C("x"),O=!!i.negative;!u&&(null===(r=null===(o=a.hoverSeries)||void 0===o?void 0:o.yAxis)||void 0===r?void 0:r.reversed)&&(O=!O);var L=!this.followPointer&&nu(i.ttBelow,!u&&!c===O),E=function(t,e,i,o,r,n,a){var h=l?"y"===t?T(s):S(s):s,c=(i-o)/2,d=o<r-s,p=r+s+o<e,u=r-h-i+c,f=r+h-c;if(L&&p)y[t]=f;else if(!L&&d)y[t]=u;else if(d)y[t]=Math.min(a-o,u-x<0?u:u-x);else{if(!p)return y[t]=0,!1;y[t]=Math.max(n,f+x+i>e?f:f+x)}},I=function(t,e,i,o,r){if(r<s||r>e-s)return!1;r<i/2?y[t]=1:r>e-o/2?y[t]=e-o-2:y[t]=r-i/2},D=function(t){var e;A=(e=[P,A])[0],P=e[1],n=t},B=function(){!1!==E.apply(0,A)?!1!==I.apply(0,P)||n||(D(!0),B()):n?y.x=y.y=0:(D(!0),B())};return(c&&!u||this.len>1)&&D(),B(),y},t.prototype.getFixedPosition=function(t,e,i){var o,r=i.series,n=this.chart,s=this.options,a=this.split,l=s.position,h=l.relativeTo,c=s.shared||(null===(o=null==r?void 0:r.yAxis)||void 0===o?void 0:o.isRadial)&&("pane"===h||!h)?"plotBox":h,d="chart"===c?n.renderer:n[c]||n.getClipBox(r,!0);return{x:d.x+(d.width-t)*na(l.align)+l.x,y:d.y+(d.height-e)*na(l.verticalAlign)+(!a&&l.y||0)}},t.prototype.hide=function(t){var e=this;tV.clearTimeout(this.hideTimer),t=nu(t,this.options.hideDelay),this.isHidden||(this.hideTimer=nv(function(){var i=e.getLabel();e.getLabel().animate({opacity:0},{duration:t?150:t,complete:function(){i.hide(),e.container&&e.container.remove()}}),e.isHidden=!0},t))},t.prototype.init=function(t,e){this.chart=t,this.options=e,this.crosshairs=[],this.isHidden=!0,this.split=e.split&&!t.inverted&&!t.polar,this.shared=e.shared||this.split,this.outside=nu(e.outside,!!(t.scrollablePixelsX||t.scrollablePixelsY))},t.prototype.shouldStickOnContact=function(t){return!!(!this.followPointer&&this.options.stickOnContact&&(!t||this.pointer.inClass(t.target,"highcharts-tooltip")))},t.prototype.move=function(t,e,i,o){var r=this,n=this.followPointer,s=this.options,a=eB(!n&&!this.isHidden&&!s.fixed&&s.animation),l=n||(this.len||0)>1,h={x:t,y:e};l?h.anchorX=h.anchorY=NaN:(h.anchorX=i,h.anchorY=o),a.step=function(){return r.drawTracker()},this.getLabel().animate(h,a)},t.prototype.refresh=function(t,e){var i=this.chart,o=this.options,r=this.pointer,n=this.shared,s=ng(t),a=s[0],l=o.format,h=o.formatter||this.defaultFormatter,c=i.styledMode,d=this.allowShared;if(o.enabled&&a.series){tV.clearTimeout(this.hideTimer),this.allowShared=!(!nl(t)&&t.series&&t.series.noSharedTooltip),d=d&&!this.allowShared,this.followPointer=!this.split&&a.series.tooltipOptions.followPointer;var p=this.getAnchor(t,e),u=p[0],f=p[1];n&&this.allowShared&&(r.applyInactiveState(s),s.forEach(function(t){return t.setState("hover")}),a.points=s),this.len=s.length;var g=nd(l)?r5(l,a,i):h.call(a,this);a.points=void 0;var v=a.series;if(this.distance=nu(v.tooltipOptions.distance,16),!1===g)this.hide();else{if(this.split&&this.allowShared)this.renderSplit(g,s);else{var m=u,y=f;if(e&&r.isDirectTouch&&(m=e.chartX-i.plotLeft,y=e.chartY-i.plotTop),i.polar||!1===v.options.clip||s.some(function(t){return r.isDirectTouch||t.series.shouldShowTooltip(m,y)})){var x=this.getLabel(d&&this.tt||{});(!o.style.width||c)&&x.css({width:(this.outside?this.getPlayingField():i.spacingBox).width+"px"}),x.attr({class:this.getClassName(a),text:g&&g.join?g.join(""):g}),this.outside&&x.attr({x:ni(x.x||0,0,this.getPlayingField().width-(x.width||0)-1)}),c||x.attr({stroke:o.borderColor||a.color||v.color||"#666666"}),this.updatePosition({plotX:u,plotY:f,negative:a.negative,ttBelow:a.ttBelow,series:v,h:p[2]||0})}else{this.hide();return}}this.isHidden&&this.label&&this.label.attr({opacity:1}).show(),this.isHidden=!1}ns(this,"refresh")}},t.prototype.renderSplit=function(t,e){var i,o,r=this,n=this,s=n.chart,a=n.chart,l=a.chartWidth,h=a.chartHeight,c=a.plotHeight,d=a.plotLeft,p=a.plotTop,u=a.scrollablePixelsY,f=a.scrollablePixelsX,g=a.styledMode,v=n.distance,m=n.options,y=n.options,x=y.fixed,b=y.position,w=y.positioner,M=n.pointer,k=(null===(i=s.scrollablePlotArea)||void 0===i?void 0:i.scrollingContainer)||{},S=k.scrollLeft,T=void 0===S?0:S,C=k.scrollTop,A=void 0===C?0:C,P=n.outside&&"number"!=typeof f?r4.documentElement.getBoundingClientRect():{left:T,right:T+l,top:A,bottom:A+h},O=n.getLabel(),L=this.renderer||s.renderer,E=!!(null===(o=s.xAxis[0])||void 0===o?void 0:o.opposite),I=M.getChartPosition(),D=I.left,B=I.top,z=w||x,j=p+A,N=0,R=c-(void 0===u?0:u),W=function(t,e,i,o,r){if(void 0===o&&(o=[0,0]),void 0===r&&(r=!0),i.isHeader)a=E?0:R,s=ni(o[0]-t/2,P.left,P.right-t-(n.outside?D:0));else if(x&&i){var s,a,l=n.getFixedPosition(t,e,i);s=l.x,a=l.y-j}else a=o[1]-j,s=ni(s=r?o[0]-t-v:o[0]+v,r?s:P.left,P.right);return{x:s,y:a}};nd(t)&&(t=[!1,t]);var X=t.slice(0,e.length+1).reduce(function(t,i,o){if(!1!==i&&""!==i){var r=e[o-1]||{isHeader:!0,plotX:e[0].plotX,plotY:c,series:{}},s=r.isHeader,a=s?n:r.series,l=a.tt=function(t,e,i){var o,r=t,s=e.isHeader,a=e.series,l=a.tooltipOptions||m;if(!r){var h={padding:l.padding,r:l.borderRadius};g||(h.fill=l.backgroundColor,h["stroke-width"]=null!==(o=l.borderWidth)&&void 0!==o?o:x&&!s?0:1),r=L.label("",0,0,l[s?"headerShape":"shape"]||(x&&!s?"rect":"callout"),void 0,void 0,l.useHTML).addClass(n.getClassName(e,!0,s)).attr(h).add(O)}return r.isActive=!0,r.attr({text:i}),g||r.css(l.style).attr({stroke:l.borderColor||e.color||a.color||"#333333"}),r}(a.tt,r,i.toString()),h=l.getBBox(),u=h.width+l.strokeWidth();s&&(N=h.height,R+=N,E&&(j-=N));var f=function(t){var e,i,o=t.isHeader,r=t.plotX,n=void 0===r?0:r,s=t.plotY,a=void 0===s?0:s,l=t.series;if(o)e=Math.max(d+n,d),i=p+c/2;else{var h=l.xAxis,u=l.yAxis;e=h.pos+ni(n,-v,h.len+v),l.shouldShowTooltip(0,u.pos-p+a,{ignoreX:!0})&&(i=u.pos+a)}return{anchorX:e=ni(e,P.left-v,P.right+v),anchorY:i}}(r),y=f.anchorX,b=f.anchorY;if("number"==typeof b){var M=h.height+1,k=(w||W).call(n,u,M,r,[y,b]);t.push({align:z?0:void 0,anchorX:y,anchorY:b,boxWidth:u,point:r,rank:nu(k.rank,+!!s),size:M,target:k.y,tt:l,x:k.x})}else l.isActive=!1}return t},[]);!z&&X.some(function(t){var e=(n.outside?D:0)+t.anchorX;return e<P.left&&e+t.boxWidth<P.right||e<D-P.left+t.boxWidth&&P.right-e>e})&&(X=X.map(function(t){var e=W.call(r,t.boxWidth,t.size,t.point,[t.anchorX,t.anchorY],!1),i=e.x;return nn(t,{target:e.y,x:i})})),n.cleanSplit(),nt(X,R);var _={left:D,right:D};X.forEach(function(t){var e=t.x,i=t.boxWidth,o=t.isHeader;!o&&(n.outside&&D+e<_.left&&(_.left=D+e),!o&&n.outside&&_.left+i>_.right&&(_.right=D+e))}),X.forEach(function(t){var e=t.x,i=t.anchorX,o=t.anchorY,r=t.pos,s=t.point.isHeader,a={visibility:void 0===r?"hidden":"inherit",x:e,y:(r||0)+j+(x&&b.y||0),anchorX:i,anchorY:o};if(n.outside&&e<i){var l=D-_.left;l>0&&(s||(a.x=e+l,a.anchorX=i+l),s&&(a.x=(_.right-_.left)/2,a.anchorX=i+l))}t.tt.attr(a)});var F=n.container,Y=n.outside,G=n.renderer;if(Y&&F&&G){var H=O.getBBox(),V=H.width,U=H.height,Z=H.x,q=H.y;G.setSize(V+Z,U+q,!1),F.style.left=_.left+"px",F.style.top=B+"px"}r7&&O.attr({opacity:1===O.opacity?.999:1})},t.prototype.drawTracker=function(){if(!this.shouldStickOnContact()){this.tracker&&(this.tracker=this.tracker.destroy());return}var t=this.chart,e=this.label,i=this.shared?t.hoverPoints:t.hoverPoint;if(e&&i){var o={x:0,y:0,width:0,height:0},r=this.getAnchor(i),n=e.getBBox();r[0]+=t.plotLeft-(e.translateX||0),r[1]+=t.plotTop-(e.translateY||0),o.x=Math.min(0,r[0]),o.y=Math.min(0,r[1]),o.width=r[0]<0?Math.max(Math.abs(r[0]),n.width-r[0]):Math.max(Math.abs(r[0]),n.width),o.height=r[1]<0?Math.max(Math.abs(r[1]),n.height-Math.abs(r[1])):Math.max(Math.abs(r[1]),n.height),this.tracker?this.tracker.attr(o):(this.tracker=e.renderer.rect(o).addClass("highcharts-tracker").add(e),t.styledMode||this.tracker.attr({fill:"rgba(0,0,0,0)"}))}},t.prototype.styledModeFormat=function(t){return t.replace('style="font-size: 0.8em"','class="highcharts-header"').replace(/style="color:{(point|series)\.color}"/g,'class="highcharts-color-{$1.colorIndex} {series.options.className} {point.options.className}"')},t.prototype.headerFooterFormatter=function(t,e){var i=t.series,o=i.tooltipOptions,r=i.xAxis,n=null==r?void 0:r.dateTime,s={isFooter:e,point:t},a=o.xDateFormat||"",l=o[e?"footerFormat":"headerFormat"];return ns(this,"headerFormatter",s,function(e){if(n&&!a&&nh(t.key)&&(a=n.getXDateFormat(t.key,o.dateTimeLabelFormats)),n&&a){if(nc(a)){var r=a;r8[0]=function(t){return i.chart.time.dateFormat(r,t)},a="%0"}(t.tooltipDateKeys||["key"]).forEach(function(t){l=l.replace(RegExp("point\\."+t+"([ \\)}])"),"(point.".concat(t,":").concat(a,")$1"))})}i.chart.styledMode&&(l=this.styledModeFormat(l)),e.text=r5(l,t,this.chart)}),s.text||""},t.prototype.update=function(t){this.destroy(),this.init(this.chart,np(!0,this.options,t))},t.prototype.updatePosition=function(t){var e,i,o=this.chart,r=this.container,n=this.distance,s=this.options,a=this.pointer,l=this.renderer,h=this.getLabel(),c=h.height,d=void 0===c?0:c,p=h.width,u=void 0===p?0:p,f=s.fixed,g=s.positioner,v=a.getChartPosition(),m=v.left,y=v.top,x=v.scaleX,b=v.scaleY,w=(g||f&&this.getFixedPosition||this.getPosition).call(this,u,d,t),M=tb.doc,k=(t.plotX||0)+o.plotLeft,S=(t.plotY||0)+o.plotTop;if(l&&r){if(g||f){var T=(null===(e=o.scrollablePlotArea)||void 0===e?void 0:e.scrollingContainer)||{},C=T.scrollLeft,A=T.scrollTop;w.x+=(void 0===C?0:C)+m-n,w.y+=(void 0===A?0:A)+y-n}i=(s.borderWidth||0)+2*n+2,l.setSize(ni(u+i,0,M.documentElement.clientWidth)-1,d+i,!1),(1!==x||1!==b)&&(no(r,{transform:"scale(".concat(x,", ").concat(b,")")}),k*=x,S*=b),k+=m-w.x,S+=y-w.y}this.move(Math.round(w.x),Math.round(w.y||0),k,S)},t}();(f=nm||(nm={})).compose=function(t){nf(r9,"Core.Tooltip")&&ne(t,"afterInit",function(){var t=this.chart;t.options.tooltip&&(t.tooltip=new f(t,t.options.tooltip,this))})};var ny=nm,nx=ii.format,nb=tV.addEvent,nw=tV.crisp,nM=tV.erase,nk=tV.extend,nS=tV.fireEvent,nT=tV.getNestedProperty,nC=tV.isArray,nA=tV.isFunction,nP=tV.isNumber,nO=tV.isObject,nL=tV.merge,nE=tV.pick,nI=tV.syncTimeout,nD=tV.removeEvent,nB=tV.uniqueKey,nz=function(){function t(t,e,i){var o,r;this.formatPrefix="point",this.visible=!0,this.point=this,this.series=t,this.applyOptions(e,i),null!==(o=this.id)&&void 0!==o||(this.id=nB()),this.resolveColor(),null!==(r=this.dataLabelOnNull)&&void 0!==r||(this.dataLabelOnNull=t.options.nullInteraction),t.chart.pointCount++,nS(this,"afterInit")}return t.prototype.animateBeforeDestroy=function(){var t=this,e={x:t.startXPos,opacity:0},i=t.getGraphicalProps();i.singular.forEach(function(i){t[i]=t[i].animate("dataLabel"===i?{x:t[i].startXPos,y:t[i].startYPos,opacity:0}:e)}),i.plural.forEach(function(e){t[e].forEach(function(e){e.element&&e.animate(nk({x:t.startXPos},e.startYPos?{x:e.startXPos,y:e.startYPos}:{}))})})},t.prototype.applyOptions=function(e,i){var o=this.series,r=o.options.pointValKey||o.pointValKey;return nk(this,e=t.prototype.optionsToObject.call(this,e)),this.options=this.options?nk(this.options,e):e,e.group&&delete this.group,e.dataLabels&&delete this.dataLabels,r&&(this.y=t.prototype.getNestedProperty.call(this,r)),this.selected&&(this.state="select"),"name"in this&&void 0===i&&o.xAxis&&o.xAxis.hasNames&&(this.x=o.xAxis.nameToX(this)),void 0===this.x&&o?this.x=null!=i?i:o.autoIncrement():nP(e.x)&&o.options.relativeXValue?this.x=o.autoIncrement(e.x):"string"==typeof this.x&&(null!=i||(i=o.chart.time.parse(this.x)),nP(i)&&(this.x=i)),this.isNull=this.isValid&&!this.isValid(),this.formatPrefix=this.isNull?"null":"point",this},t.prototype.destroy=function(){if(!this.destroyed){var t=this,e=t.series,i=e.chart,o=e.options.dataSorting,r=i.hoverPoints,n=eB(t.series.chart.renderer.globalAnimation),s=function(){for(var e in(t.graphic||t.graphics||t.dataLabel||t.dataLabels)&&(nD(t),t.destroyElements()),t)delete t[e]};t.legendItem&&i.legend.destroyItem(t),r&&(t.setState(),nM(r,t),r.length||(i.hoverPoints=null)),t===i.hoverPoint&&t.onMouseOut(),(null==o?void 0:o.enabled)?(this.animateBeforeDestroy(),nI(s,n.duration)):s(),i.pointCount--}this.destroyed=!0},t.prototype.destroyElements=function(t){var e=this,i=e.getGraphicalProps(t);i.singular.forEach(function(t){e[t]=e[t].destroy()}),i.plural.forEach(function(t){e[t].forEach(function(t){(null==t?void 0:t.element)&&t.destroy()}),delete e[t]})},t.prototype.firePointEvent=function(t,e,i){var o=this,r=this.series.options;o.manageEvent(t),"click"===t&&r.allowPointSelect&&(i=function(t){!o.destroyed&&o.select&&o.select(null,t.ctrlKey||t.metaKey||t.shiftKey)}),nS(o,t,e,i)},t.prototype.getClassName=function(){var t;return"highcharts-point"+(this.selected?" highcharts-point-select":"")+(this.negative?" highcharts-negative":"")+(this.isNull?" highcharts-null-point":"")+(void 0!==this.colorIndex?" highcharts-color-"+this.colorIndex:"")+(this.options.className?" "+this.options.className:"")+((null===(t=this.zone)||void 0===t?void 0:t.className)?" "+this.zone.className.replace("highcharts-negative",""):"")},t.prototype.getGraphicalProps=function(t){var e,i,o=this,r=[],n={singular:[],plural:[]};for((t=t||{graphic:1,dataLabel:1}).graphic&&r.push("graphic","connector"),t.dataLabel&&r.push("dataLabel","dataLabelPath","dataLabelUpper"),i=r.length;i--;)o[e=r[i]]&&n.singular.push(e);return["graphic","dataLabel"].forEach(function(e){var i=e+"s";t[e]&&o[i]&&n.plural.push(i)}),n},t.prototype.getNestedProperty=function(t){return t?0===t.indexOf("custom.")?nT(t,this.options):this[t]:void 0},t.prototype.getZone=function(){var t,e=this.series,i=e.zones,o=e.zoneAxis||"y",r=0;for(t=i[0];this[o]>=t.value;)t=i[++r];return this.nonZonedColor||(this.nonZonedColor=this.color),(null==t?void 0:t.color)&&!this.options.color?this.color=t.color:this.color=this.nonZonedColor,t},t.prototype.hasNewShapeType=function(){return(this.graphic&&(this.graphic.symbolName||this.graphic.element.nodeName))!==this.shapeType},t.prototype.isValid=function(){return(nP(this.x)||this.x instanceof Date)&&nP(this.y)},t.prototype.optionsToObject=function(e){var i,o,r=this.series,n=r.options.keys,s=n||r.pointArrayMap||["y"],a=s.length,l={},h=0,c=0;if(nP(e)||null===e)l[s[0]]=e;else if(nC(e))for(!n&&e.length>a&&("string"==(o=typeof e[0])?(null===(i=r.xAxis)||void 0===i?void 0:i.dateTime)?l.x=r.chart.time.parse(e[0]):l.name=e[0]:"number"===o&&(l.x=e[0]),h++);c<a;)n&&void 0===e[h]||(s[c].indexOf(".")>0?t.prototype.setNestedProperty(l,e[h],s[c]):l[s[c]]=e[h]),h++,c++;else"object"==typeof e&&(l=e,e.dataLabels&&(r.hasDataLabels=function(){return!0}),e.marker&&(r._hasPointMarkers=!0));return l},t.prototype.pos=function(t,e){if(void 0===e&&(e=this.plotY),!this.destroyed){var i=this.plotX,o=this.series,r=o.chart,n=o.xAxis,s=o.yAxis,a=0,l=0;if(nP(i)&&nP(e))return t&&(a=n?n.pos:r.plotLeft,l=s?s.pos:r.plotTop),r.inverted&&n&&s?[s.len-e+l,n.len-i+a]:[i+a,e+l]}},t.prototype.resolveColor=function(){var t,e,i,o=this.series,r=o.chart.options.chart,n=o.chart.styledMode,s=r.colorCount;delete this.nonZonedColor,o.options.colorByPoint?(n||(t=(e=o.options.colors||o.chart.options.colors)[o.colorCounter],s=e.length),i=o.colorCounter,o.colorCounter++,o.colorCounter===s&&(o.colorCounter=0)):(n||(t=o.color),i=o.colorIndex),this.colorIndex=nE(this.options.colorIndex,i),this.color=nE(this.options.color,t)},t.prototype.setNestedProperty=function(t,e,i){return i.split(".").reduce(function(t,i,o,r){var n=r.length-1===o;return t[i]=n?e:nO(t[i],!0)?t[i]:{},t[i]},t),t},t.prototype.shouldDraw=function(){return!this.isNull},t.prototype.tooltipFormatter=function(t){var e,i=this.series,o=i.chart,r=i.pointArrayMap,n=i.tooltipOptions,s=n.valueDecimals,a=void 0===s?"":s,l=n.valuePrefix,h=void 0===l?"":l,c=n.valueSuffix,d=void 0===c?"":c;return o.styledMode&&(t=(null===(e=o.tooltip)||void 0===e?void 0:e.styledModeFormat(t))||t),(void 0===r?["y"]:r).forEach(function(e){e="{point."+e,(h||d)&&(t=t.replace(RegExp(e+"}","g"),h+e+"}"+d)),t=t.replace(RegExp(e+"}","g"),e+":,."+a+"f}")}),nx(t,this,o)},t.prototype.update=function(t,e,i,o){var r,n=this,s=n.series,a=n.graphic,l=s.chart,h=s.options;function c(){n.applyOptions(t);var o=a&&n.hasMockGraphic,c=null===n.y?!o:o;a&&c&&(n.graphic=a.destroy(),delete n.hasMockGraphic),nO(t,!0)&&((null==a?void 0:a.element)&&t&&t.marker&&void 0!==t.marker.symbol&&(n.graphic=a.destroy()),(null==t?void 0:t.dataLabels)&&n.dataLabel&&(n.dataLabel=n.dataLabel.destroy())),r=n.index;for(var d={},p=0,u=s.dataColumnKeys();p<u.length;p++){var f=u[p];d[f]=n[f]}s.dataTable.setRow(d,r),h.data[r]=nO(h.data[r],!0)||nO(t,!0)?n.options:nE(t,h.data[r]),s.isDirty=s.isDirtyData=!0,!s.fixedBox&&s.hasCartesianSeries&&(l.isDirtyBox=!0),"point"===h.legendType&&(l.isDirtyLegend=!0),e&&l.redraw(i)}e=nE(e,!0),!1===o?c():n.firePointEvent("update",{options:t},c)},t.prototype.remove=function(t,e){this.series.removePoint(this.series.data.indexOf(this),t,e)},t.prototype.select=function(t,e){var i=this,o=i.series,r=o.chart;t=nE(t,!i.selected),this.selectedStaging=t,i.firePointEvent(t?"select":"unselect",{accumulate:e},function(){i.selected=i.options.selected=t,o.options.data[o.data.indexOf(i)]=i.options,i.setState(t&&"select"),e||r.getSelectedPoints().forEach(function(t){var e=t.series;t.selected&&t!==i&&(t.selected=t.options.selected=!1,e.options.data[e.data.indexOf(t)]=t.options,t.setState(r.hoverPoints&&e.options.inactiveOtherPoints?"inactive":""),t.firePointEvent("unselect"))})}),delete this.selectedStaging},t.prototype.onMouseOver=function(t){var e=this.series.chart,i=e.inverted,o=e.pointer;o&&(t=t?o.normalize(t):o.getChartCoordinatesFromPoint(this,i),o.runPointActions(t,this))},t.prototype.onMouseOut=function(){var t=this.series.chart;this.firePointEvent("mouseOut"),this.series.options.inactiveOtherPoints||(t.hoverPoints||[]).forEach(function(t){t.setState()}),t.hoverPoints=t.hoverPoint=null},t.prototype.manageEvent=function(t){var e,i,o,r,n,s,a,l=null===(e=nL(this.series.options.point,this.options).events)||void 0===e?void 0:e[t];!nA(l)||(null===(i=this.hcEvents)||void 0===i?void 0:i[t])&&(null===(r=null===(o=this.hcEvents)||void 0===o?void 0:o[t])||void 0===r?void 0:r.map(function(t){return t.fn}).indexOf(l))!==-1?this.importedUserEvent&&!l&&(null===(s=this.hcEvents)||void 0===s?void 0:s[t])&&(null===(a=this.hcEvents)||void 0===a?void 0:a[t].userEvent)&&(nD(this,t),delete this.hcEvents[t],Object.keys(this.hcEvents)||delete this.importedUserEvent):(null===(n=this.importedUserEvent)||void 0===n||n.call(this),this.importedUserEvent=nb(this,t,l),this.hcEvents&&(this.hcEvents[t].userEvent=!0))},t.prototype.setState=function(t,e){var i,o,r,n,s,a,l=this.series,h=this.state,c=l.options.states[t||"normal"]||{},d=el.plotOptions[l.type].marker&&l.options.marker,p=d&&!1===d.enabled,u=(null===(i=null==d?void 0:d.states)||void 0===i?void 0:i[t||"normal"])||{},f=!1===u.enabled,g=this.marker||{},v=l.chart,m=d&&l.markerAttribs,y=l.halo,x=l.stateMarkerGraphic;if(((t=t||"")!==this.state||e)&&(!this.selected||"select"===t)&&!1!==c.enabled&&(!t||!f&&(!p||!1!==u.enabled))&&(!t||!g.states||!g.states[t]||!1!==g.states[t].enabled)){if(this.state=t,m&&(r=l.markerAttribs(this,t)),this.graphic&&!this.hasMockGraphic){if(h&&this.graphic.removeClass("highcharts-point-"+h),t&&this.graphic.addClass("highcharts-point-"+t),!v.styledMode){n=l.pointAttribs(this,t),s=nE(v.options.chart.animation,c.animation);var b=n.opacity;l.options.inactiveOtherPoints&&nP(b)&&(this.dataLabels||[]).forEach(function(t){t&&!t.hasClass("highcharts-data-label-hidden")&&(t.animate({opacity:b},s),t.connector&&t.connector.animate({opacity:b},s))}),this.graphic.animate(n,s)}r&&this.graphic.animate(r,nE(v.options.chart.animation,u.animation,d.animation)),x&&x.hide()}else t&&u&&(a=g.symbol||l.symbol,x&&x.currentSymbol!==a&&(x=x.destroy()),r&&(x?x[e?"animate":"attr"]({x:r.x,y:r.y}):a&&(l.stateMarkerGraphic=x=v.renderer.symbol(a,r.x,r.y,r.width,r.height,nL(d,u)).add(l.markerGroup),x.currentSymbol=a)),!v.styledMode&&x&&"inactive"!==this.state&&x.attr(l.pointAttribs(this,t))),x&&(x[t&&this.isInside?"show":"hide"](),x.element.point=this,x.addClass(this.getClassName(),!0));var w=c.halo,M=this.graphic||x,k=(null==M?void 0:M.visibility)||"inherit";(null==w?void 0:w.size)&&M&&"hidden"!==k&&!this.isCluster?(y||(l.halo=y=v.renderer.path().add(M.parentGroup)),y.show()[e?"animate":"attr"]({d:this.haloPath(w.size)}),y.attr({class:"highcharts-halo highcharts-color-"+nE(this.colorIndex,l.colorIndex)+(this.className?" "+this.className:""),visibility:k,zIndex:-1}),y.point=this,v.styledMode||y.attr(nk({fill:this.color||l.color,"fill-opacity":w.opacity},eJ.filterUserAttributes(w.attributes||{})))):(null===(o=null==y?void 0:y.point)||void 0===o?void 0:o.haloPath)&&!y.point.destroyed&&y.animate({d:y.point.haloPath(0)},null,y.hide),nS(this,"afterSetState",{state:t})}},t.prototype.haloPath=function(t){var e=this.pos();return e?this.series.chart.renderer.symbols.circle(nw(e[0],1)-t,e[1]-t,2*t,2*t):[]},t}(),nj=function(){return(nj=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},nN=eb.parse,nR=tb.charts,nW=tb.composed,nX=tb.isTouchDevice,n_=tV.addEvent,nF=tV.attr,nY=tV.css,nG=tV.extend,nH=tV.find,nV=tV.fireEvent,nU=tV.isNumber,nZ=tV.isObject,nq=tV.objectEach,nK=tV.offset,n$=tV.pick,nJ=tV.pushUnique,nQ=tV.splat,n0=function(){function t(t,e){var i;this.hasDragged=0,this.pointerCaptureEventsToUnbind=[],this.eventsToUnbind=[],this.options=e,this.chart=t,this.runChartClick=!!(null===(i=e.chart.events)||void 0===i?void 0:i.click),this.pinchDown=[],this.setDOMEvents(),nV(this,"afterInit")}return t.prototype.applyInactiveState=function(t){var e=this;void 0===t&&(t=[]);var i=[];t.forEach(function(t){var o=t.series;i.push(o),o.linkedParent&&i.push(o.linkedParent),o.linkedSeries&&i.push.apply(i,o.linkedSeries),o.navigatorSeries&&i.push(o.navigatorSeries),o.boosted&&o.markerGroup&&i.push.apply(i,e.chart.series.filter(function(t){return t.markerGroup===o.markerGroup}))}),this.chart.series.forEach(function(t){-1===i.indexOf(t)?t.setState("inactive",!0):t.options.inactiveOtherPoints&&t.setAllPointsToState("inactive")})},t.prototype.destroy=function(){var e=this;this.eventsToUnbind.forEach(function(t){return t()}),this.eventsToUnbind=[],!tb.chartCount&&(t.unbindDocumentMouseUp.forEach(function(t){return t.unbind()}),t.unbindDocumentMouseUp.length=0,t.unbindDocumentTouchEnd&&(t.unbindDocumentTouchEnd=t.unbindDocumentTouchEnd())),clearInterval(e.tooltipTimeout),nq(e,function(t,i){e[i]=void 0})},t.prototype.getSelectionMarkerAttrs=function(t,e){var i=this,o={args:{chartX:t,chartY:e},attrs:{},shapeType:"rect"};return nV(this,"getSelectionMarkerAttrs",o,function(o){var r,n=i.chart,s=i.zoomHor,a=i.zoomVert,l=n.mouseDownX,h=void 0===l?0:l,c=n.mouseDownY,d=void 0===c?0:c,p=o.attrs;p.x=n.plotLeft,p.y=n.plotTop,p.width=s?1:n.plotWidth,p.height=a?1:n.plotHeight,s&&(p.width=Math.max(1,Math.abs(r=t-h)),p.x=(r>0?0:r)+h),a&&(p.height=Math.max(1,Math.abs(r=e-d)),p.y=(r>0?0:r)+d)}),o},t.prototype.drag=function(t){var e,i=this.chart,o=i.mouseDownX,r=void 0===o?0:o,n=i.mouseDownY,s=void 0===n?0:n,a=i.options.chart,l=a.panning,h=a.panKey,c=a.selectionMarkerFill,d=i.plotLeft,p=i.plotTop,u=i.plotWidth,f=i.plotHeight,g=nZ(l)?l.enabled:l,v=h&&t[""+h+"Key"],m=t.chartX,y=t.chartY,x=this.selectionMarker;if((!x||!x.touch)&&(m<d?m=d:m>d+u&&(m=d+u),y<p?y=p:y>p+f&&(y=p+f),this.hasDragged=Math.sqrt(Math.pow(r-m,2)+Math.pow(s-y,2)),this.hasDragged>10)){e=i.isInsidePlot(r-d,s-p,{visiblePlotOnly:!0});var b=this.getSelectionMarkerAttrs(m,y),w=b.shapeType,M=b.attrs;(i.hasCartesianSeries||i.mapView)&&this.hasZoom&&e&&!v&&!x&&(this.selectionMarker=x=i.renderer[w](),x.attr({class:"highcharts-selection-marker",zIndex:7}).add(),i.styledMode||x.attr({fill:c||nN("#334eff").setOpacity(.25).get()})),x&&x.attr(M),e&&!x&&g&&i.pan(t,l)}},t.prototype.dragStart=function(t){var e=this.chart;e.mouseIsDown=t.type,e.cancelClick=!1,e.mouseDownX=t.chartX,e.mouseDownY=t.chartY},t.prototype.getSelectionBox=function(t){var e={args:{marker:t},result:t.getBBox()};return nV(this,"getSelectionBox",e),e.result},t.prototype.drop=function(t){for(var e,i=this,o=this.chart,r=this.selectionMarker,n=0,s=o.axes;n<s.length;n++){var a=s[n];a.isPanning&&(a.isPanning=!1,(a.options.startOnTick||a.options.endOnTick||a.series.some(function(t){return t.boosted}))&&(a.forceRedraw=!0,a.setExtremes(a.userMin,a.userMax,!1),e=!0))}if(e&&o.redraw(),r&&t){if(this.hasDragged){var l=this.getSelectionBox(r);o.transform({axes:o.axes.filter(function(t){return t.zoomEnabled&&("xAxis"===t.coll&&i.zoomX||"yAxis"===t.coll&&i.zoomY)}),selection:nj({originalEvent:t,xAxis:[],yAxis:[]},l),from:l})}nU(o.index)&&(this.selectionMarker=r.destroy())}o&&nU(o.index)&&(nY(o.container,{cursor:o._cursor}),o.cancelClick=this.hasDragged>10,o.mouseIsDown=!1,this.hasDragged=0,this.pinchDown=[])},t.prototype.findNearestKDPoint=function(t,e,i){var o;return t.forEach(function(t){var r,n,s,a,l,h,c,d=!(t.noSharedTooltip&&e)&&0>t.options.findNearestPointBy.indexOf("y"),p=t.searchPoint(i,d);nZ(p,!0)&&p.series&&(!nZ(o,!0)||(l=(r=o).distX-p.distX,h=r.dist-p.dist,c=(null===(n=p.series.group)||void 0===n?void 0:n.zIndex)-(null===(s=r.series.group)||void 0===s?void 0:s.zIndex),(0!==l&&e?l:0!==h?h:0!==c?c:r.series.index>p.series.index?-1:1)>0))&&(o=p)}),o},t.prototype.getChartCoordinatesFromPoint=function(t,e){var i,o,r=t.series,n=r.xAxis,s=r.yAxis,a=t.shapeArgs;if(n&&s){var l=null!==(o=null!==(i=t.clientX)&&void 0!==i?i:t.plotX)&&void 0!==o?o:0,h=t.plotY||0;return t.isNode&&a&&nU(a.x)&&nU(a.y)&&(l=a.x,h=a.y),e?{chartX:s.len+s.pos-h,chartY:n.len+n.pos-l}:{chartX:l+n.pos,chartY:h+s.pos}}if((null==a?void 0:a.x)&&a.y)return{chartX:a.x,chartY:a.y}},t.prototype.getChartPosition=function(){if(this.chartPosition)return this.chartPosition;var t=this.chart.container,e=nK(t);this.chartPosition={left:e.left,top:e.top,scaleX:1,scaleY:1};var i=t.offsetHeight,o=t.offsetWidth;return o>2&&i>2&&(this.chartPosition.scaleX=e.width/o,this.chartPosition.scaleY=e.height/i),this.chartPosition},t.prototype.getCoordinates=function(t){for(var e={xAxis:[],yAxis:[]},i=0,o=this.chart.axes;i<o.length;i++){var r=o[i];e[r.isXAxis?"xAxis":"yAxis"].push({axis:r,value:r.toValue(t[r.horiz?"chartX":"chartY"])})}return e},t.prototype.getHoverData=function(t,e,i,o,r,n){var s,a=[],l=function(t){return t.visible&&!(!r&&t.directTouch)&&n$(t.options.enableMouseTracking,!0)},h=e,c={chartX:n?n.chartX:void 0,chartY:n?n.chartY:void 0,shared:r};nV(this,"beforeGetHoverData",c),s=h&&!h.stickyTracking?[h]:i.filter(function(t){return t.stickyTracking&&(c.filter||l)(t)});var d=o&&t||!n?t:this.findNearestKDPoint(s,r,n);return h=null==d?void 0:d.series,d&&(r&&!h.noSharedTooltip?(s=i.filter(function(t){return c.filter?c.filter(t):l(t)&&!t.noSharedTooltip})).forEach(function(t){var e,i=null===(e=t.options)||void 0===e?void 0:e.nullInteraction,o=nH(t.points,function(t){return t.x===d.x&&(!t.isNull||!!i)});nZ(o)&&(t.boosted&&t.boost&&(o=t.boost.getPoint(o)),a.push(o))}):a.push(d)),nV(this,"afterGetHoverData",c={hoverPoint:d}),{hoverPoint:c.hoverPoint,hoverSeries:h,hoverPoints:a}},t.prototype.getPointFromEvent=function(t){for(var e,i=t.target;i&&!e;)e=i.point,i=i.parentNode;return e},t.prototype.onTrackerMouseOut=function(t){var e=this.chart,i=t.relatedTarget,o=e.hoverSeries;this.isDirectTouch=!1,!o||!i||o.stickyTracking||this.inClass(i,"highcharts-tooltip")||this.inClass(i,"highcharts-series-"+o.index)&&this.inClass(i,"highcharts-tracker")||o.onMouseOut()},t.prototype.inClass=function(t,e){for(var i,o=t;o;){if(i=nF(o,"class")){if(-1!==i.indexOf(e))return!0;if(-1!==i.indexOf("highcharts-container"))return!1}o=o.parentElement}},t.prototype.normalize=function(t,e){var i=t.touches,o=i?i.length?i.item(0):n$(i.changedTouches,t.changedTouches)[0]:t;e||(e=this.getChartPosition());var r=o.pageX-e.left,n=o.pageY-e.top;return nG(t,{chartX:Math.round(r/=e.scaleX),chartY:Math.round(n/=e.scaleY)})},t.prototype.onContainerClick=function(t){var e=this.chart,i=e.hoverPoint,o=this.normalize(t),r=e.plotLeft,n=e.plotTop;!e.cancelClick&&(i&&this.inClass(o.target,"highcharts-tracker")?(nV(i.series,"click",nG(o,{point:i})),e.hoverPoint&&i.firePointEvent("click",o)):(nG(o,this.getCoordinates(o)),e.isInsidePlot(o.chartX-r,o.chartY-n,{visiblePlotOnly:!0})&&nV(e,"click",o)))},t.prototype.onContainerMouseDown=function(t){var e,i=(1&(t.buttons||t.button))==1;t=this.normalize(t),tb.isFirefox&&0!==t.button&&this.onContainerMouseMove(t),(void 0===t.button||i)&&(this.zoomOption(t),i&&(null===(e=t.preventDefault)||void 0===e||e.call(t)),this.dragStart(t))},t.prototype.onContainerMouseLeave=function(e){var i=(nR[n$(t.hoverChartIndex,-1)]||{}).pointer;e=this.normalize(e),this.onContainerMouseMove(e),i&&!this.inClass(e.relatedTarget,"highcharts-tooltip")&&(i.reset(),i.chartPosition=void 0)},t.prototype.onContainerMouseEnter=function(){delete this.chartPosition},t.prototype.onContainerMouseMove=function(t){var e=this.chart,i=e.tooltip,o=this.normalize(t);this.setHoverChartIndex(t),("mousedown"===e.mouseIsDown||this.touchSelect(o))&&this.drag(o),!e.openMenu&&(this.inClass(o.target,"highcharts-tracker")||e.isInsidePlot(o.chartX-e.plotLeft,o.chartY-e.plotTop,{visiblePlotOnly:!0}))&&!(null==i?void 0:i.shouldStickOnContact(o))&&(this.inClass(o.target,"highcharts-no-tooltip")?this.reset(!1,0):this.runPointActions(o))},t.prototype.onDocumentTouchEnd=function(t){this.onDocumentMouseUp(t)},t.prototype.onContainerTouchMove=function(t){this.touchSelect(t)?this.onContainerMouseMove(t):this.touch(t)},t.prototype.onContainerTouchStart=function(t){this.touchSelect(t)?this.onContainerMouseDown(t):(this.zoomOption(t),this.touch(t,!0))},t.prototype.onDocumentMouseMove=function(t){var e=this.chart,i=e.tooltip,o=this.chartPosition,r=this.normalize(t,o);!o||e.isInsidePlot(r.chartX-e.plotLeft,r.chartY-e.plotTop,{visiblePlotOnly:!0})||(null==i?void 0:i.shouldStickOnContact(r))||r.target!==e.container.ownerDocument&&this.inClass(r.target,"highcharts-tracker")||this.reset()},t.prototype.onDocumentMouseUp=function(e){var i,o;null===(o=null===(i=nR[n$(t.hoverChartIndex,-1)])||void 0===i?void 0:i.pointer)||void 0===o||o.drop(e)},t.prototype.pinch=function(t){var e=this,i=this,o=i.chart,r=i.hasZoom,n=i.lastTouches,s=[].map.call(t.touches||[],function(t){return i.normalize(t)}),a=s.length,l=1===a&&(i.inClass(t.target,"highcharts-tracker")&&o.runTrackerClick||i.runChartClick),h=o.tooltip,c=1===a&&n$(null==h?void 0:h.options.followTouchMove,!0);a>1?i.initiated=!0:c&&(i.initiated=!1),r&&i.initiated&&!l&&!1!==t.cancelable&&t.preventDefault(),"touchstart"===t.type?(i.pinchDown=s,i.res=!0,o.mouseDownX=t.chartX):c?this.runPointActions(i.normalize(t)):n&&(nV(o,"touchpan",{originalEvent:t,touches:s},function(){var i=function(t){var e=t[0],i=t[1]||e;return{x:e.chartX,y:e.chartY,width:i.chartX-e.chartX,height:i.chartY-e.chartY}};o.transform({axes:o.axes.filter(function(t){return t.zoomEnabled&&(e.zoomHor&&t.horiz||e.zoomVert&&!t.horiz)}),to:i(s),from:i(n),trigger:t.type})}),i.res&&(i.res=!1,this.reset(!1,0))),i.lastTouches=s},t.prototype.reset=function(t,e){var i=this.chart,o=i.hoverSeries,r=i.hoverPoint,n=i.hoverPoints,s=i.tooltip,a=(null==s?void 0:s.shared)?n:r;t&&a&&nQ(a).forEach(function(e){e.series.isCartesian&&void 0===e.plotX&&(t=!1)}),t?s&&a&&nQ(a).length&&(s.refresh(a),s.shared&&n?n.forEach(function(t){t.setState(t.state,!0),t.series.isCartesian&&(t.series.xAxis.crosshair&&t.series.xAxis.drawCrosshair(null,t),t.series.yAxis.crosshair&&t.series.yAxis.drawCrosshair(null,t))}):r&&(r.setState(r.state,!0),i.axes.forEach(function(t){t.crosshair&&r.series[t.coll]===t&&t.drawCrosshair(null,r)}))):(r&&r.onMouseOut(),n&&n.forEach(function(t){t.setState()}),o&&o.onMouseOut(),s&&s.hide(e),this.unDocMouseMove&&(this.unDocMouseMove=this.unDocMouseMove()),i.axes.forEach(function(t){t.hideCrosshair()}),i.hoverPoints=i.hoverPoint=void 0)},t.prototype.runPointActions=function(e,i,o){var r,n=this.chart,s=n.series,a=(null===(r=n.tooltip)||void 0===r?void 0:r.options.enabled)?n.tooltip:void 0,l=!!a&&a.shared,h=i||n.hoverPoint,c=(null==h?void 0:h.series)||n.hoverSeries,d=(!e||"touchmove"!==e.type)&&(!!i||(null==c?void 0:c.directTouch)&&this.isDirectTouch),p=this.getHoverData(h,c,s,d,l,e);h=p.hoverPoint,c=p.hoverSeries;var u=p.hoverPoints,f=(null==c?void 0:c.tooltipOptions.followPointer)&&!c.tooltipOptions.split,g=l&&c&&!c.noSharedTooltip;if(h&&(o||h!==n.hoverPoint||(null==a?void 0:a.isHidden))){if((n.hoverPoints||[]).forEach(function(t){-1===u.indexOf(t)&&t.setState()}),n.hoverSeries!==c&&c.onMouseOver(),this.applyInactiveState(u),(u||[]).forEach(function(t){t.setState("hover")}),n.hoverPoint&&n.hoverPoint.firePointEvent("mouseOut"),!h.series)return;n.hoverPoints=u,n.hoverPoint=h,h.firePointEvent("mouseOver",void 0,function(){a&&h&&a.refresh(g?u:h,e)})}else if(f&&a&&!a.isHidden){var v=a.getAnchor([{}],e);n.isInsidePlot(v[0],v[1],{visiblePlotOnly:!0})&&a.updatePosition({plotX:v[0],plotY:v[1]})}this.unDocMouseMove||(this.unDocMouseMove=n_(n.container.ownerDocument,"mousemove",function(e){var i,o,r;return null===(r=null===(o=nR[null!==(i=t.hoverChartIndex)&&void 0!==i?i:-1])||void 0===o?void 0:o.pointer)||void 0===r?void 0:r.onDocumentMouseMove(e)}),this.eventsToUnbind.push(this.unDocMouseMove)),n.axes.forEach(function(t){var i,o,r,s=null===(o=null===(i=t.crosshair)||void 0===i?void 0:i.snap)||void 0===o||o;!s||(r=n.hoverPoint)&&r.series[t.coll]===t||(r=nH(u,function(e){var i;return(null===(i=e.series)||void 0===i?void 0:i[t.coll])===t})),r||!s?t.drawCrosshair(e,r):t.hideCrosshair()})},t.prototype.setDOMEvents=function(){var e=this,i=this.chart.container,o=i.ownerDocument;i.onmousedown=this.onContainerMouseDown.bind(this),i.onmousemove=this.onContainerMouseMove.bind(this),i.onclick=this.onContainerClick.bind(this),this.eventsToUnbind.push(n_(i,"mouseenter",this.onContainerMouseEnter.bind(this)),n_(i,"mouseleave",this.onContainerMouseLeave.bind(this))),t.unbindDocumentMouseUp.some(function(t){return t.doc===o})||t.unbindDocumentMouseUp.push({doc:o,unbind:n_(o,"mouseup",this.onDocumentMouseUp.bind(this))});for(var r=this.chart.renderTo.parentElement;r&&"BODY"!==r.tagName;)this.eventsToUnbind.push(n_(r,"scroll",function(){delete e.chartPosition})),r=r.parentElement;this.eventsToUnbind.push(n_(i,"touchstart",this.onContainerTouchStart.bind(this),{passive:!1}),n_(i,"touchmove",this.onContainerTouchMove.bind(this),{passive:!1})),t.unbindDocumentTouchEnd||(t.unbindDocumentTouchEnd=n_(o,"touchend",this.onDocumentTouchEnd.bind(this),{passive:!1})),this.setPointerCapture(),n_(this.chart,"redraw",this.setPointerCapture.bind(this))},t.prototype.setPointerCapture=function(){if(nX){var t,e,i=this.pointerCaptureEventsToUnbind,o=this.chart,r=o.container,n=n$(null===(t=o.options.tooltip)||void 0===t?void 0:t.followTouchMove,!0)&&o.series.some(function(t){return t.options.findNearestPointBy.indexOf("y")>-1});!this.hasPointerCapture&&n?(i.push(n_(r,"pointerdown",function(t){var e,i;(null===(e=t.target)||void 0===e?void 0:e.hasPointerCapture(t.pointerId))&&(null===(i=t.target)||void 0===i||i.releasePointerCapture(t.pointerId))}),n_(r,"pointermove",function(t){var e,i;null===(i=null===(e=o.pointer)||void 0===e?void 0:e.getPointFromEvent(t))||void 0===i||i.onMouseOver(t)})),o.styledMode||nY(r,{"touch-action":"none"}),r.className+=" highcharts-no-touch-action",this.hasPointerCapture=!0):this.hasPointerCapture&&!n&&(i.forEach(function(t){return t()}),i.length=0,o.styledMode||nY(r,{"touch-action":n$(null===(e=o.options.chart.style)||void 0===e?void 0:e["touch-action"],"manipulation")}),r.className=r.className.replace(" highcharts-no-touch-action",""),this.hasPointerCapture=!1)}},t.prototype.setHoverChartIndex=function(e){var i,o=this.chart,r=tb.charts[n$(t.hoverChartIndex,-1)];if(r&&r!==o){var n={relatedTarget:o.container};!e||(null==e?void 0:e.relatedTarget)||Object.assign({},e,n),null===(i=r.pointer)||void 0===i||i.onContainerMouseLeave(e||n)}(null==r?void 0:r.mouseIsDown)||(t.hoverChartIndex=o.index)},t.prototype.touch=function(t,e){var i,o=this.chart,r=this.pinchDown,n=void 0===r?[]:r;this.setHoverChartIndex(),1===(t=this.normalize(t)).touches.length?o.isInsidePlot(t.chartX-o.plotLeft,t.chartY-o.plotTop,{visiblePlotOnly:!0})&&!o.openMenu?(e&&this.runPointActions(t),"touchmove"===t.type&&(i=!!n[0]&&Math.pow(n[0].chartX-t.chartX,2)+Math.pow(n[0].chartY-t.chartY,2)>=16),n$(i,!0)&&this.pinch(t)):e&&this.reset():2===t.touches.length&&this.pinch(t)},t.prototype.touchSelect=function(t){return!!(this.chart.zooming.singleTouch&&t.touches&&1===t.touches.length)},t.prototype.zoomOption=function(t){var e,i,o=this.chart,r=o.inverted,n=o.zooming.type||"";/touch/.test(t.type)&&(n=n$(o.zooming.pinchType,n)),this.zoomX=e=/x/.test(n),this.zoomY=i=/y/.test(n),this.zoomHor=e&&!r||i&&r,this.zoomVert=i&&!r||e&&r,this.hasZoom=e||i},t.unbindDocumentMouseUp=[],t}();(g=n0||(n0={})).compose=function(t){nJ(nW,"Core.Pointer")&&n_(t,"beforeRender",function(){this.pointer=new g(this,this.options)})};var n1=n0,n2=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))};(v=ti||(ti={})).setLength=function(t,e,i){return Array.isArray(t)?(t.length=e,t):t[i?"subarray":"slice"](0,e)},v.splice=function(t,e,i,o,r){if(void 0===r&&(r=[]),Array.isArray(t))return Array.isArray(r)||(r=Array.from(r)),{removed:t.splice.apply(t,n2([e,i],r,!1)),array:t};var n=Object.getPrototypeOf(t).constructor,s=t[o?"subarray":"slice"](e,e+i),a=new n(t.length-i+r.length);return a.set(t.subarray(0,e),0),a.set(r,e),a.set(t.subarray(e+i),e+r.length),{removed:s,array:a}};var n3=ti,n6=n3.setLength,n5=n3.splice,n9=tV.fireEvent,n8=tV.objectEach,n4=tV.uniqueKey,n7=function(){function t(t){void 0===t&&(t={});var e=this;this.autoId=!t.id,this.columns={},this.id=t.id||n4(),this.modified=this,this.rowCount=0,this.versionTag=n4();var i=0;n8(t.columns||{},function(t,o){e.columns[o]=t.slice(),i=Math.max(i,t.length)}),this.applyRowCount(i)}return t.prototype.applyRowCount=function(t){var e=this;this.rowCount=t,n8(this.columns,function(i,o){i.length!==t&&(e.columns[o]=n6(i,t))})},t.prototype.deleteRows=function(t,e){var i=this;if(void 0===e&&(e=1),e>0&&t<this.rowCount){var o=0;n8(this.columns,function(r,n){i.columns[n]=n5(r,t,e).array,o=r.length}),this.rowCount=o}n9(this,"afterDeleteRows",{rowIndex:t,rowCount:e}),this.versionTag=n4()},t.prototype.getColumn=function(t,e){return this.columns[t]},t.prototype.getColumns=function(t,e){var i=this;return(t||Object.keys(this.columns)).reduce(function(t,e){return t[e]=i.columns[e],t},{})},t.prototype.getRow=function(t,e){var i=this;return(e||Object.keys(this.columns)).map(function(e){var o;return null===(o=i.columns[e])||void 0===o?void 0:o[t]})},t.prototype.setColumn=function(t,e,i,o){var r;void 0===e&&(e=[]),void 0===i&&(i=0),this.setColumns(((r={})[t]=e,r),i,o)},t.prototype.setColumns=function(t,e,i){var o=this,r=this.rowCount;n8(t,function(t,e){o.columns[e]=t.slice(),r=t.length}),this.applyRowCount(r),(null==i?void 0:i.silent)||(n9(this,"afterSetColumns"),this.versionTag=n4())},t.prototype.setRow=function(t,e,i,o){void 0===e&&(e=this.rowCount);var r=this.columns,n=i?this.rowCount+1:e+1;n8(t,function(t,s){var a=r[s]||(null==o?void 0:o.addColumns)!==!1&&Array(n);a&&(i?a=n5(a,e,0,!0,[t]).array:a[e]=t,r[s]=a)}),n>this.rowCount&&this.applyRowCount(n),(null==o?void 0:o.silent)||(n9(this,"afterSetRows"),this.versionTag=n4())},t}(),st=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},se=tV.extend,si=tV.merge,so=tV.pick;!function(t){function e(t,e,i){var o,r,n,s=this.legendItem=this.legendItem||{},a=this.chart,l=this.options,h=t.baseline,c=void 0===h?0:h,d=t.symbolWidth,p=t.symbolHeight,u=this.symbol||"circle",f=p/2,g=a.renderer,v=s.group,m=c-Math.round(((null===(o=t.fontMetrics)||void 0===o?void 0:o.b)||p)*(i?.4:.3)),y={},x=l.marker,b=0;if(a.styledMode||(y["stroke-width"]=Math.min(l.lineWidth||0,24),l.dashStyle?y.dashstyle=l.dashStyle:"square"===l.linecap||(y["stroke-linecap"]="round")),s.line=g.path().addClass("highcharts-graph").attr(y).add(v),i&&(s.area=g.path().addClass("highcharts-area").add(v)),y["stroke-linecap"]&&(b=Math.min(s.line.strokeWidth(),d)/2),d){var w=[["M",b,m],["L",d-b,m]];s.line.attr({d:w}),null===(r=s.area)||void 0===r||r.attr({d:st(st([],w,!0),[["L",d-b,c],["L",b,c]],!1)})}if(x&&!1!==x.enabled&&d){var M=Math.min(so(x.radius,f),f);0===u.indexOf("url")&&(x=si(x,{width:p,height:p}),M=0),s.symbol=n=g.symbol(u,d/2-M,m-M,2*M,2*M,se({context:"legend"},x)).addClass("highcharts-point").add(v),n.isMarker=!0}}t.areaMarker=function(t,i){e.call(this,t,i,!0)},t.lineMarker=e,t.rectangle=function(t,e){var i=e.legendItem||{},o=t.options,r=t.symbolHeight,n=o.squareSymbol,s=n?r:t.symbolWidth;i.symbol=this.chart.renderer.rect(n?(t.symbolWidth-r)/2:0,t.baseline-r+1,s,r,so(t.options.symbolRadius,r/2)).addClass("highcharts-point").attr({zIndex:3}).add(i.group)}}(to||(to={}));var sr=to,sn={lineWidth:2,allowPointSelect:!1,crisp:!0,showCheckbox:!1,animation:{duration:1e3},enableMouseTracking:!0,events:{},marker:{enabledThreshold:2,lineColor:"#ffffff",lineWidth:0,radius:4,states:{normal:{animation:!0},hover:{animation:{duration:150},enabled:!0,radiusPlus:2,lineWidthPlus:1},select:{fillColor:"#cccccc",lineColor:"#000000",lineWidth:2}}},point:{events:{}},dataLabels:{animation:{},align:"center",borderWidth:0,defer:!0,formatter:function(){var t=this.series.chart.numberFormatter;return"number"!=typeof this.y?"":t(this.y,-1)},padding:5,style:{fontSize:"0.7em",fontWeight:"bold",color:"contrast",textOutline:"1px contrast"},verticalAlign:"bottom",x:0,y:0},cropThreshold:300,opacity:1,pointRange:0,softThreshold:!0,states:{normal:{animation:!0},hover:{animation:{duration:150},lineWidthPlus:1,marker:{},halo:{size:10,opacity:.25}},select:{animation:{duration:0}},inactive:{animation:{duration:150},opacity:.2}},stickyTracking:!0,turboThreshold:1e3,findNearestPointBy:"x"},ss=(m=function(t,e){return(m=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}m(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),sa=tV.extend,sl=tV.extendClass,sh=tV.merge;!function(t){function e(e,i){var o=el.plotOptions||{},r=i.defaultOptions,n=i.prototype;return n.type=e,n.pointClass||(n.pointClass=nz),!t.seriesTypes[e]&&(r&&(o[e]=r),t.seriesTypes[e]=i,!0)}t.seriesTypes=tb.seriesTypes,t.registerSeriesType=e,t.seriesType=function(i,o,r,n,s){var a=el.plotOptions||{};if(o=o||"",a[i]=sh(a[o],r),delete t.seriesTypes[i],e(i,sl(t.seriesTypes[o]||function(){},n)),t.seriesTypes[i].prototype.type=i,s){var l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ss(e,t),e}(nz);sa(l.prototype,s),t.seriesTypes[i].prototype.pointClass=l}return t.seriesTypes[i]}}(tr||(tr={}));var sc=tr,sd=function(){return(sd=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},sp=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},su=o1.registerEventOptions,sf=tb.svg,sg=tb.win,sv=sc.seriesTypes,sm=ii.format,sy=tV.arrayMax,sx=tV.arrayMin,sb=tV.clamp,sw=tV.correctFloat,sM=tV.crisp,sk=tV.defined,sS=tV.destroyObjectProperties,sT=tV.diffObjects,sC=tV.erase,sA=tV.error,sP=tV.extend,sO=tV.find,sL=tV.fireEvent,sE=tV.getClosestDistance,sI=tV.getNestedProperty,sD=tV.insertItem,sB=tV.isArray,sz=tV.isNumber,sj=tV.isString,sN=tV.merge,sR=tV.objectEach,sW=tV.pick,sX=tV.removeEvent,s_=tV.syncTimeout,sF=function(){function t(){this.zoneAxis="y"}return t.prototype.init=function(t,e){sL(this,"init",{options:e}),null!==(i=this.dataTable)&&void 0!==i||(this.dataTable=new n7);var i,o,r,n,s,a=t.series;this.eventsToUnbind=[],this.chart=t,this.options=this.setOptions(e);var l=this.options,h=!1!==l.visible;this.linkedSeries=[],this.bindAxes(),sP(this,{name:l.name,state:"",visible:h,selected:!0===l.selected}),su(this,l);var c=l.events;((null==c?void 0:c.click)||(null===(r=null===(o=l.point)||void 0===o?void 0:o.events)||void 0===r?void 0:r.click)||l.allowPointSelect)&&(t.runTrackerClick=!0),this.getColor(),this.getSymbol(),this.isCartesian&&(t.hasCartesianSeries=!0),a.length&&(s=a[a.length-1]),this._i=sW(null==s?void 0:s._i,-1)+1,this.opacity=this.options.opacity,t.orderItems("series",sD(this,a)),(null===(n=l.dataSorting)||void 0===n?void 0:n.enabled)?this.setDataSortingOptions():this.points||this.data||this.setData(l.data,!1),sL(this,"afterInit")},t.prototype.is=function(t){return sv[t]&&this instanceof sv[t]},t.prototype.bindAxes=function(){var t,e=this,i=e.options,o=e.chart;sL(this,"bindAxes",null,function(){(e.axisTypes||[]).forEach(function(r){(o[r]||[]).forEach(function(o){t=o.options,(sW(i[r],0)===o.index||void 0!==i[r]&&i[r]===t.id)&&(sD(e,o.series),e[r]=o,o.isDirty=!0)}),e[r]||e.optionalAxis===r||sA(18,!0,o)})}),sL(this,"afterBindAxes")},t.prototype.hasData=function(){return this.visible&&void 0!==this.dataMax&&void 0!==this.dataMin||this.visible&&this.dataTable.rowCount>0},t.prototype.hasMarkerChanged=function(t,e){var i=t.marker,o=e.marker||{};return i&&(o.enabled&&!i.enabled||o.symbol!==i.symbol||o.height!==i.height||o.width!==i.width)},t.prototype.autoIncrement=function(t){var e,i,o,r=this.options,n=this.options,s=n.pointIntervalUnit,a=n.relativeXValue,l=this.chart.time,h=null!==(i=null!==(e=this.xIncrement)&&void 0!==e?e:l.parse(r.pointStart))&&void 0!==i?i:0;if(this.pointInterval=o=sW(this.pointInterval,r.pointInterval,1),a&&sz(t)&&(o*=t),s){var c=l.toParts(h);"day"===s?c[2]+=o:"month"===s?c[1]+=o:"year"===s&&(c[0]+=o),o=l.makeTime.apply(l,c)-h}return a&&sz(t)?h+o:(this.xIncrement=h+o,h)},t.prototype.setDataSortingOptions=function(){var t=this.options;sP(this,{requireSorting:!1,sorted:!1,enabledDataSorting:!0,allowDG:!1}),sk(t.pointRange)||(t.pointRange=1)},t.prototype.setOptions=function(t){var e,i,o,r=this.chart,n=r.options.plotOptions,s=r.userOptions||{},a=sN(t),l=r.styledMode,h={plotOptions:n,userOptions:a};sL(this,"setOptions",h);var c=h.plotOptions[this.type],d=s.plotOptions||{},p=d.series||{},u=el.plotOptions[this.type]||{},f=d[this.type]||{};c.dataLabels=this.mergeArrays(u.dataLabels,c.dataLabels),this.userOptions=h.userOptions;var g=sN(c,n.series,f,a);this.tooltipOptions=sN(el.tooltip,null===(e=el.plotOptions.series)||void 0===e?void 0:e.tooltip,null==u?void 0:u.tooltip,r.userOptions.tooltip,null===(i=d.series)||void 0===i?void 0:i.tooltip,f.tooltip,a.tooltip),this.stickyTracking=sW(a.stickyTracking,f.stickyTracking,p.stickyTracking,!!this.tooltipOptions.shared&&!this.noSharedTooltip||g.stickyTracking),null===c.marker&&delete g.marker,this.zoneAxis=g.zoneAxis||"y";var v=this.zones=(g.zones||[]).map(function(t){return sd({},t)});return(g.negativeColor||g.negativeFillColor)&&!g.zones&&(o={value:g[this.zoneAxis+"Threshold"]||g.threshold||0,className:"highcharts-negative"},l||(o.color=g.negativeColor,o.fillColor=g.negativeFillColor),v.push(o)),v.length&&sk(v[v.length-1].value)&&v.push(l?{}:{color:this.color,fillColor:this.fillColor}),sL(this,"afterSetOptions",{options:g}),g},t.prototype.getName=function(){var t;return null!==(t=this.options.name)&&void 0!==t?t:sm(this.chart.options.lang.seriesName,this,this.chart)},t.prototype.getCyclic=function(t,e,i){var o,r,n=this.chart,s=""+t+"Index",a=""+t+"Counter",l=(null==i?void 0:i.length)||n.options.chart.colorCount;!e&&(sk(r=sW("color"===t?this.options.colorIndex:void 0,this[s]))?o=r:(n.series.length||(n[a]=0),o=n[a]%l,n[a]+=1),i&&(e=i[o])),void 0!==o&&(this[s]=o),this[t]=e},t.prototype.getColor=function(){this.chart.styledMode?this.getCyclic("color"):this.options.colorByPoint?this.color="#cccccc":this.getCyclic("color",this.options.color||el.plotOptions[this.type].color,this.chart.options.colors)},t.prototype.getPointsCollection=function(){return(this.hasGroupedData?this.points:this.data)||[]},t.prototype.getSymbol=function(){var t=this.options.marker;this.getCyclic("symbol",t.symbol,this.chart.options.symbols)},t.prototype.getColumn=function(t,e){return(e?this.dataTable.modified:this.dataTable).getColumn(t,!0)||[]},t.prototype.findPointIndex=function(t,e){var i,o,r,n,s=t.id,a=t.x,l=this.points,h=this.options.dataSorting,c=this.cropStart||0;if(s){var d=this.chart.get(s);d instanceof nz&&(o=d)}else if(this.linkedParent||this.enabledDataSorting||this.options.relativeXValue){var p=function(e){return!e.touched&&e.index===t.index};if((null==h?void 0:h.matchByName)?p=function(e){return!e.touched&&e.name===t.name}:this.options.relativeXValue&&(p=function(e){return!e.touched&&e.options.x===t.x}),!(o=sO(l,p)))return}return o&&void 0!==(n=null==o?void 0:o.index)&&(r=!0),void 0===n&&sz(a)&&(n=this.getColumn("x").indexOf(a,e)),-1!==n&&void 0!==n&&this.cropped&&(n=n>=c?n-c:n),!r&&sz(n)&&(null===(i=l[n])||void 0===i?void 0:i.touched)&&(n=void 0),n},t.prototype.updateData=function(t,e){var i,o,r,n,s,a=this,l=this.options,h=this.requireSorting,c=l.dataSorting,d=this.points,p=[],u=t.length===d.length,f=!0;if(this.xIncrement=null,t.forEach(function(t,e){var i,r,n=sk(t)&&a.pointClass.prototype.optionsToObject.call({series:a},t)||{},f=n.id,g=n.x;f||sz(g)?(-1===(r=a.findPointIndex(n,s))||void 0===r?p.push(t):d[r]&&t!==(null===(i=l.data)||void 0===i?void 0:i[r])?(d[r].update(t,!1,void 0,!1),d[r].touched=!0,h&&(s=r+1)):d[r]&&(d[r].touched=!0),(!u||e!==r||(null==c?void 0:c.enabled)||a.hasDerivedData)&&(o=!0)):p.push(t)},this),o)for(r=d.length;r--;)(n=d[r])&&!n.touched&&(null===(i=n.remove)||void 0===i||i.call(n,!1,e));else!u||(null==c?void 0:c.enabled)?f=!1:(t.forEach(function(t,e){t===d[e].y||d[e].destroyed||d[e].update(t,!1,void 0,!1)}),p.length=0);if(d.forEach(function(t){t&&(t.touched=!1)}),!f)return!1;p.forEach(function(t){a.addPoint(t,!1,void 0,void 0,!1)},this);var g=this.getColumn("x");return null===this.xIncrement&&g.length&&(this.xIncrement=sy(g),this.autoIncrement()),!0},t.prototype.dataColumnKeys=function(){return sp(["x"],this.pointArrayMap||["y"],!0)},t.prototype.setData=function(t,e,i,o){void 0===e&&(e=!0);var r,n,s,a,l,h,c,d=this.points,p=(null==d?void 0:d.length)||0,u=this.options,f=this.chart,g=u.dataSorting,v=this.xAxis,m=u.turboThreshold,y=this.dataTable,x=this.dataColumnKeys(),b=this.pointValKey||"y",w=(this.pointArrayMap||[]).length,M=u.keys,k=0,S=1;f.options.chart.allowMutatingData||(u.data&&delete this.options.data,this.userOptions.data&&delete this.userOptions.data,c=sN(!0,t));var T=(t=c||t||[]).length;if((null==g?void 0:g.enabled)&&(t=this.sortData(t)),f.options.chart.allowMutatingData&&!1!==o&&T&&p&&!this.cropped&&!this.hasGroupedData&&this.visible&&!this.boosted&&(h=this.updateData(t,i)),!h){this.xIncrement=null,this.colorCounter=0;var C=m&&T>m;if(C){var A=this.getFirstValidPoint(t),P=this.getFirstValidPoint(t,T-1,-1),O=function(t){return!!(sB(t)&&(M||sz(t[0])))};if(sz(A)&&sz(P)){for(var L=[],E=[],I=0,D=t;I<D.length;I++){var B=D[I];L.push(this.autoIncrement()),E.push(B)}y.setColumns(((r={x:L})[b]=E,r))}else if(O(A)&&O(P)){if(w){for(var z=+(A.length===w),j=Array(x.length).fill(0).map(function(){return[]}),N=0,R=t;N<R.length;N++){var W=R[N];z&&j[0].push(this.autoIncrement());for(var X=z;X<=w;X++)null===(s=j[X])||void 0===s||s.push(W[X-z])}y.setColumns(x.reduce(function(t,e,i){return t[e]=j[i],t},{}))}else{M&&(k=M.indexOf("x"),S=M.indexOf("y"),k=k>=0?k:0,S=S>=0?S:1),1===A.length&&(S=0);var _=[],E=[];if(k===S)for(var F=0,Y=t;F<Y.length;F++){var W=Y[F];_.push(this.autoIncrement()),E.push(W[S])}else for(var G=0,H=t;G<H.length;G++){var W=H[G];_.push(W[k]),E.push(W[S])}y.setColumns(((n={x:_})[b]=E,n))}}else C=!1}if(!C){var V=x.reduce(function(t,e){return t[e]=[],t},{});for(l=0;l<T;l++)for(var W=this.pointClass.prototype.applyOptions.apply({series:this},[t[l]]),U=0;U<x.length;U++){var Z=x[U];V[Z][l]=W[Z]}y.setColumns(V)}for(sj(this.getColumn("y")[0])&&sA(14,!0,f),this.data=[],this.options.data=this.userOptions.data=t,l=p;l--;)null===(a=d[l])||void 0===a||a.destroy();v&&(v.minRange=v.userMinRange),this.isDirty=f.isDirtyBox=!0,this.isDirtyData=!!d,i=!1}"point"===u.legendType&&(this.processData(),this.generatePoints()),e&&f.redraw(i)},t.prototype.sortData=function(t){var e=this,i=e.options.dataSorting.sortKey||"y",o=function(t,e){return sk(e)&&t.pointClass.prototype.optionsToObject.call({series:t},e)||{}};return t.forEach(function(i,r){t[r]=o(e,i),t[r].index=r},this),t.concat().sort(function(t,e){var o=sI(i,t),r=sI(i,e);return r<o?-1:+(r>o)}).forEach(function(t,e){t.x=e},this),e.linkedSeries&&e.linkedSeries.forEach(function(e){var i,r=e.options,n=r.data;(null===(i=r.dataSorting)||void 0===i?void 0:i.enabled)||!n||(n.forEach(function(i,r){n[r]=o(e,i),t[r]&&(n[r].x=t[r].x,n[r].index=r)}),e.setData(n,!1))}),t},t.prototype.getProcessedData=function(t){var e,i,o,r,n,s=this,a=s.dataTable,l=s.isCartesian,h=s.options,c=s.xAxis,d=h.cropThreshold,p=t||s.getExtremesFromAll,u=null==c?void 0:c.logarithmic,f=a.rowCount,g=0,v=s.getColumn("x"),m=a,y=!1;return c&&(r=(o=c.getExtremes()).min,n=o.max,y=!!(c.categories&&!c.names.length),l&&s.sorted&&!p&&(!d||f>d||s.forceCrop)&&(v[f-1]<r||v[0]>n?m=new n7:s.getColumn(s.pointValKey||"y").length&&(v[0]<r||v[f-1]>n)&&(m=(e=this.cropData(a,r,n)).modified,g=e.start,i=!0))),v=m.getColumn("x")||[],{modified:m,cropped:i,cropStart:g,closestPointRange:sE([u?v.map(u.log2lin):v],function(){return s.requireSorting&&!y&&sA(15,!1,s.chart)})}},t.prototype.processData=function(t){var e=this.xAxis,i=this.dataTable;if(this.isCartesian&&!this.isDirty&&!e.isDirty&&!this.yAxis.isDirty&&!t)return!1;var o=this.getProcessedData();i.modified=o.modified,this.cropped=o.cropped,this.cropStart=o.cropStart,this.closestPointRange=this.basePointRange=o.closestPointRange,sL(this,"afterProcessData")},t.prototype.cropData=function(t,e,i){var o,r,n=t.getColumn("x",!0)||[],s=n.length,a={},l=0,h=s;for(o=0;o<s;o++)if(n[o]>=e){l=Math.max(0,o-1);break}for(r=o;r<s;r++)if(n[r]>i){h=r+1;break}for(var c=0,d=this.dataColumnKeys();c<d.length;c++){var p=d[c],u=t.getColumn(p,!0);u&&(a[p]=u.slice(l,h))}return{modified:new n7({columns:a}),start:l,end:h}},t.prototype.generatePoints=function(){var t,e,i,o,r,n,s,a,l,h,c=this.options,d=this.processedData||c.data,p=this.dataTable.modified,u=this.getColumn("x",!0),f=this.pointClass,g=p.rowCount,v=this.cropStart||0,m=this.hasGroupedData,y=c.keys,x=[],b=(null===(t=c.dataGrouping)||void 0===t?void 0:t.groupAll)?v:0,w=null===(e=this.xAxis)||void 0===e?void 0:e.categories,M=this.pointArrayMap||["y"],k=this.dataColumnKeys(),S=this.data;if(!S&&!m){var T=[];T.length=(null==d?void 0:d.length)||0,S=this.data=T}for(y&&m&&(this.options.keys=!1),l=0;l<g;l++)s=v+l,m?((a=new f(this,p.getRow(l,k)||[])).dataGroup=this.groupMap[b+l],(null===(i=a.dataGroup)||void 0===i?void 0:i.options)&&(a.options=a.dataGroup.options,sP(a,a.dataGroup.options),delete a.dataLabels)):(a=S[s],h=d?d[s]:p.getRow(l,M),a||void 0===h||(S[s]=a=new f(this,h,u[l]))),a&&(a.index=m?b+l:s,x[l]=a,a.category=null!==(o=null==w?void 0:w[a.x])&&void 0!==o?o:a.x,a.key=null!==(r=a.name)&&void 0!==r?r:a.category);if(this.options.keys=y,S&&(g!==(n=S.length)||m))for(l=0;l<n;l++)l!==v||m||(l+=g),S[l]&&(S[l].destroyElements(),S[l].plotX=void 0);this.data=S,this.points=x,sL(this,"afterGeneratePoints")},t.prototype.getXExtremes=function(t){return{min:sx(t),max:sy(t)}},t.prototype.getExtremes=function(t,e){var i,o,r,n,s=this.xAxis,a=this.yAxis,l=e||this.getExtremesFromAll||this.options.getExtremesFromAll,h=l&&this.cropped?this.dataTable:this.dataTable.modified,c=h.rowCount,d=t||this.stackedYData,p=d?[d]:(null===(i=this.keysAffectYAxis||this.pointArrayMap||["y"])||void 0===i?void 0:i.map(function(t){return h.getColumn(t,!0)||[]}))||[],u=this.getColumn("x",!0),f=[],g=this.requireSorting&&!this.is("column")?1:0,v=!!a&&a.positiveValuesOnly,m=l||this.cropped||!s,y=0,x=0;for(s&&(y=(o=s.getExtremes()).min,x=o.max),n=0;n<c;n++)if(r=u[n],m||(u[n+g]||r)>=y&&(u[n-g]||r)<=x)for(var b=0;b<p.length;b++){var w=p[b][n];sz(w)&&(w>0||!v)&&f.push(w)}var M={activeYData:f,dataMin:sx(f),dataMax:sy(f)};return sL(this,"afterGetExtremes",{dataExtremes:M}),M},t.prototype.applyExtremes=function(){var t=this.getExtremes();return this.dataMin=t.dataMin,this.dataMax=t.dataMax,t},t.prototype.getFirstValidPoint=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=1);for(var o=t.length,r=e;r>=0&&r<o;){if(sk(t[r]))return t[r];r+=i}},t.prototype.translate=function(){this.generatePoints();var t,e,i,o,r,n=this.options,s=n.stacking,a=this.xAxis,l=this.enabledDataSorting,h=this.yAxis,c=this.points,d=c.length,p=this.pointPlacementToXValue(),u=!!p,f=n.threshold,g=n.startFromThreshold?f:0,v=(null==n?void 0:n.nullInteraction)&&h.len,m=Number.MAX_VALUE;function y(t){return sb(t,-1e9,1e9)}for(e=0;e<d;e++){var x=c[e],b=x.x,w=void 0,M=void 0,k=x.y,S=x.low,T=s&&(null===(t=h.stacking)||void 0===t?void 0:t.stacks[(this.negStacks&&k<(g?0:f)?"-":"")+this.stackKey]);x.plotX=sz(i=a.translate(b,!1,!1,!1,!0,p))?sw(y(i)):void 0,s&&this.visible&&T&&T[b]&&(r=this.getStackIndicator(r,b,this.index),!x.isNull&&r.key&&(M=(w=T[b]).points[r.key]),w&&sB(M)&&(S=M[0],k=M[1],S===g&&r.key===T[b].base&&(S=sW(sz(f)?f:h.min)),h.positiveValuesOnly&&sk(S)&&S<=0&&(S=void 0),x.total=x.stackTotal=sW(w.total),x.percentage=sk(x.y)&&w.total?x.y/w.total*100:void 0,x.stackY=k,this.irregularWidths||w.setOffset(this.pointXOffset||0,this.barW||0,void 0,void 0,void 0,this.xAxis))),x.yBottom=sk(S)?y(h.translate(S,!1,!0,!1,!0)):void 0,this.dataModify&&(k=this.dataModify.modifyValue(k,e));var C=void 0;sz(k)&&void 0!==x.plotX?C=sz(C=h.translate(k,!1,!0,!1,!0))?y(C):void 0:!sz(k)&&v&&(C=v),x.plotY=C,x.isInside=this.isPointInside(x),x.clientX=u?sw(a.translate(b,!1,!1,!1,!0,p)):i,x.negative=(x.y||0)<(f||0),x.isNull||!1===x.visible||(void 0!==o&&(m=Math.min(m,Math.abs(i-o))),o=i),x.zone=this.zones.length?x.getZone():void 0,!x.graphic&&this.group&&l&&(x.isNew=!0)}this.closestPointRangePx=m,sL(this,"afterTranslate")},t.prototype.getValidPoints=function(t,e,i){var o=this.chart;return(t||this.points||[]).filter(function(t){var r=t.plotX,n=t.plotY;return!!((i||!t.isNull&&sz(n))&&(!e||o.isInsidePlot(r,n,{inverted:o.inverted})))&&!1!==t.visible})},t.prototype.getSharedClipKey=function(){return this.sharedClipKey=(this.options.xAxis||0)+","+(this.options.yAxis||0),this.sharedClipKey},t.prototype.setClip=function(){var t=this.chart,e=this.group,i=this.markerGroup,o=t.sharedClips,r=t.renderer,n=t.getClipBox(this),s=this.getSharedClipKey(),a=o[s];a?a.animate(n):o[s]=a=r.clipRect(n),e&&e.clip(!1===this.options.clip?void 0:a),i&&i.clip()},t.prototype.animate=function(t){var e=this.chart,i=this.group,o=this.markerGroup,r=e.inverted,n=eB(this.options.animation),s=[this.getSharedClipKey(),n.duration,n.easing,n.defer].join(","),a=e.sharedClips[s],l=e.sharedClips[s+"m"];if(t&&i){var h=e.getClipBox(this);if(a)a.attr("height",h.height);else{h.width=0,r&&(h.x=e.plotHeight),a=e.renderer.clipRect(h),e.sharedClips[s]=a;var c={x:-99,y:-99,width:r?e.plotWidth+199:99,height:r?99:e.plotHeight+199};l=e.renderer.clipRect(c),e.sharedClips[s+"m"]=l}i.clip(a),null==o||o.clip(l)}else if(a&&!a.hasClass("highcharts-animating")){var d=e.getClipBox(this),p=n.step;((null==o?void 0:o.element.childNodes.length)||e.series.length>1)&&(n.step=function(t,e){p&&p.apply(e,arguments),"width"===e.prop&&(null==l?void 0:l.element)&&l.attr(r?"height":"width",t+99)}),a.addClass("highcharts-animating").animate(d,n)}},t.prototype.afterAnimate=function(){var t=this;this.setClip(),sR(this.chart.sharedClips,function(e,i,o){e&&!t.chart.container.querySelector('[clip-path="url(#'.concat(e.id,')"]'))&&(e.destroy(),delete o[i])}),this.finishedAnimating=!0,sL(this,"afterAnimate")},t.prototype.drawPoints=function(t){void 0===t&&(t=this.points);var e,i,o,r,n,s,a,l=this.chart,h=l.styledMode,c=this.colorAxis,d=this.options,p=d.marker,u=d.nullInteraction,f=this[this.specialGroup||"markerGroup"],g=this.xAxis,v=sW(p.enabled,!g||!!g.isRadial||null,this.closestPointRangePx>=p.enabledThreshold*p.radius);if(!1!==p.enabled||this._hasPointMarkers)for(e=0;e<t.length;e++){r=(o=(i=t[e]).graphic)?"animate":"attr",n=i.marker||{},s=!!i.marker;var m=i.isNull;if((v&&!sk(n.enabled)||n.enabled)&&(!m||u)&&!1!==i.visible){var y=sW(n.symbol,this.symbol,"rect");a=this.markerAttribs(i,i.selected&&"select"),this.enabledDataSorting&&(i.startXPos=g.reversed?-(a.width||0):g.width);var x=!1!==i.isInside;if(!o&&x&&((a.width||0)>0||i.hasImage)&&(i.graphic=o=l.renderer.symbol(y,a.x,a.y,a.width,a.height,s?n:p).add(f),this.enabledDataSorting&&l.hasRendered&&(o.attr({x:i.startXPos}),r="animate")),o&&"animate"===r&&o[x?"show":"hide"](x).animate(a),o){var b=this.pointAttribs(i,h||!i.selected?void 0:"select");h?c&&o.css({fill:b.fill}):o[r](b)}o&&o.addClass(i.getClassName(),!0)}else o&&(i.graphic=o.destroy())}},t.prototype.markerAttribs=function(t,e){var i,o,r=this.options,n=r.marker,s=t.marker||{},a=s.symbol||n.symbol,l={},h=sW(s.radius,null==n?void 0:n.radius);e&&(i=n.states[e],h=sW(null==(o=s.states&&s.states[e])?void 0:o.radius,null==i?void 0:i.radius,h&&h+((null==i?void 0:i.radiusPlus)||0))),t.hasImage=a&&0===a.indexOf("url"),t.hasImage&&(h=0);var c=t.pos();return sz(h)&&c&&(r.crisp&&(c[0]=sM(c[0],t.hasImage?0:"rect"===a?(null==n?void 0:n.lineWidth)||0:1)),l.x=c[0]-h,l.y=c[1]-h),h&&(l.width=l.height=2*h),l},t.prototype.pointAttribs=function(t,e){var i,o,r,n,s,a=this.options,l=a.marker,h=null==t?void 0:t.options,c=(null==h?void 0:h.marker)||{},d=null==h?void 0:h.color,p=null==t?void 0:t.color,u=null===(i=null==t?void 0:t.zone)||void 0===i?void 0:i.color,f=this.color,g=sW(c.lineWidth,l.lineWidth),v=(null==t?void 0:t.isNull)&&a.nullInteraction?0:1;return f=d||u||p||f,n=c.fillColor||l.fillColor||f,s=c.lineColor||l.lineColor||f,e=e||"normal",o=l.states[e]||{},g=sW((r=c.states&&c.states[e]||{}).lineWidth,o.lineWidth,g+sW(r.lineWidthPlus,o.lineWidthPlus,0)),n=r.fillColor||o.fillColor||n,s=r.lineColor||o.lineColor||s,{stroke:s,"stroke-width":g,fill:n,opacity:v=sW(r.opacity,o.opacity,v)}},t.prototype.destroy=function(t){var e,i,o,r,n=this,s=n.chart,a=/AppleWebKit\/533/.test(sg.navigator.userAgent),l=n.data||[];for(sL(n,"destroy",{keepEventsForUpdate:t}),this.removeEvents(t),(n.axisTypes||[]).forEach(function(t){(null==(r=n[t])?void 0:r.series)&&(sC(r.series,n),r.isDirty=r.forceRedraw=!0)}),n.legendItem&&n.chart.legend.destroyItem(n),o=l.length;o--;)null===(i=null===(e=l[o])||void 0===e?void 0:e.destroy)||void 0===i||i.call(e);for(var h=0,c=n.zones;h<c.length;h++)sS(c[h],void 0,!0);tV.clearTimeout(n.animationTimeout),sR(n,function(t,e){t instanceof iX&&!t.survive&&t[a&&"group"===e?"hide":"destroy"]()}),s.hoverSeries===n&&(s.hoverSeries=void 0),sC(s.series,n),s.orderItems("series"),sR(n,function(e,i){t&&"hcEvents"===i||delete n[i]})},t.prototype.applyZones=function(){var t=this.area,e=this.chart,i=this.graph,o=this.zones,r=this.points,n=this.xAxis,s=this.yAxis,a=this.zoneAxis,l=e.inverted,h=e.renderer,c=this[""+a+"Axis"],d=c||{},p=d.isXAxis,u=d.len,f=void 0===u?0:u,g=d.minPointOffset,v=void 0===g?0:g,m=((null==i?void 0:i.strokeWidth())||0)/2+1,y=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=0),l&&(i=f-i);var o=t.translated,r=void 0===o?0:o,n=t.lineClip,s=i-r;null==n||n.push(["L",e,Math.abs(s)<m?i-m*(s<=0?-1:1):r])};if(o.length&&(i||t)&&c&&sz(c.min)){var x=c.getExtremes().max+v,b=function(t){t.forEach(function(e,i){("M"===e[0]||"L"===e[0])&&(t[i]=[e[0],p?f-e[1]:e[1],p?e[2]:f-e[2]])})};if(o.forEach(function(t){t.lineClip=[],t.translated=sb(c.toPixels(sW(t.value,x),!0)||0,0,f)}),i&&!this.showLine&&i.hide(),t&&t.hide(),"y"===a&&r.length<n.len)for(var w=0;w<r.length;w++){var M=r[w],k=M.plotX,S=M.plotY,T=M.zone,C=T&&o[o.indexOf(T)-1];T&&y(T,k,S),C&&y(C,k,S)}var A=[],P=c.toPixels(c.getExtremes().min-v,!0);o.forEach(function(e){var o,r,a=e.lineClip||[],c=Math.round(e.translated||0);n.reversed&&a.reverse();var d=e.clip,u=e.simpleClip,f=0,g=0,v=n.len,m=s.len;p?(f=c,v=P):(g=c,m=P);var y=[["M",f,g],["L",v,g],["L",v,m],["L",f,m],["Z"]],x=sp(sp(sp(sp([y[0]],a,!0),[y[1],y[2]],!1),A,!0),[y[3],y[4]],!1);A=a.reverse(),P=c,l&&(b(x),t&&b(y)),d?(d.animate({d:x}),null==u||u.animate({d:y})):(d=e.clip=h.path(x),t&&(u=e.simpleClip=h.path(y))),i&&(null===(o=e.graph)||void 0===o||o.clip(d)),t&&(null===(r=e.area)||void 0===r||r.clip(u))})}else this.visible&&(i&&i.show(),t&&t.show())},t.prototype.plotGroup=function(t,e,i,o,r){var n=this[t],s=!n,a={visibility:i,zIndex:o||.1};return sk(this.opacity)&&!this.chart.styledMode&&"inactive"!==this.state&&(a.opacity=this.opacity),n||(this[t]=n=this.chart.renderer.g().add(r)),n.addClass("highcharts-"+e+" highcharts-series-"+this.index+" highcharts-"+this.type+"-series "+(sk(this.colorIndex)?"highcharts-color-"+this.colorIndex+" ":"")+(this.options.className||"")+(n.hasClass("highcharts-tracker")?" highcharts-tracker":""),!0),n.attr(a)[s?"attr":"animate"](this.getPlotBox(e)),n},t.prototype.getPlotBox=function(t){var e=this.xAxis,i=this.yAxis,o=this.chart,r=o.inverted&&!o.polar&&e&&this.invertible&&"series"===t;return o.inverted&&(e=i,i=this.xAxis),{translateX:e?e.left:o.plotLeft,translateY:i?i.top:o.plotTop,rotation:90*!!r,rotationOriginX:r?(e.len-i.len)/2:0,rotationOriginY:r?(e.len+i.len)/2:0,scaleX:r?-1:1,scaleY:1}},t.prototype.removeEvents=function(t){var e=this.eventsToUnbind;t||sX(this),e.length&&(e.forEach(function(t){t()}),e.length=0)},t.prototype.render=function(){var t,e,i,o,r,n=this,s=n.chart,a=n.options,l=n.hasRendered,h=eB(a.animation),c=n.visible?"inherit":"hidden",d=a.zIndex,p=s.seriesGroup,u=n.finishedAnimating?0:h.duration;sL(this,"render"),n.plotGroup("group","series",c,d,p),n.markerGroup=n.plotGroup("markerGroup","markers",c,d,p),!1!==a.clip&&n.setClip(),u&&(null===(t=n.animate)||void 0===t||t.call(n,!0)),n.drawGraph&&(n.drawGraph(),n.applyZones()),n.visible&&n.drawPoints(),null===(e=n.drawDataLabels)||void 0===e||e.call(n),null===(i=n.redrawPoints)||void 0===i||i.call(n),a.enableMouseTracking&&(null===(o=n.drawTracker)||void 0===o||o.call(n)),u&&(null===(r=n.animate)||void 0===r||r.call(n)),l||(u&&h.defer&&(u+=h.defer),n.animationTimeout=s_(function(){n.afterAnimate()},u||0)),n.isDirty=!1,n.hasRendered=!0,sL(n,"afterRender")},t.prototype.redraw=function(){var t=this.isDirty||this.isDirtyData;this.translate(),this.render(),t&&delete this.kdTree},t.prototype.reserveSpace=function(){return this.visible||!this.chart.options.chart.ignoreHiddenSeries},t.prototype.searchPoint=function(t,e){var i=this.xAxis,o=this.yAxis,r=this.chart.inverted;return this.searchKDTree({clientX:r?i.len-t.chartY+i.pos:t.chartX-i.pos,plotY:r?o.len-t.chartX+o.pos:t.chartY-o.pos},e,t)},t.prototype.buildKDTree=function(t){this.buildingKdTree=!0;var e=this,i=e.options,o=i.findNearestPointBy.indexOf("y")>-1?2:1;delete e.kdTree,s_(function(){e.kdTree=function t(i,o,r){var n,s,a=null==i?void 0:i.length;if(a)return n=e.kdAxisArray[o%r],i.sort(function(t,e){return(t[n]||0)-(e[n]||0)}),{point:i[s=Math.floor(a/2)],left:t(i.slice(0,s),o+1,r),right:t(i.slice(s+1),o+1,r)}}(e.getValidPoints(void 0,!e.directTouch,null==i?void 0:i.nullInteraction),o,o),e.buildingKdTree=!1},i.kdNow||(null==t?void 0:t.type)==="touchstart"?0:1)},t.prototype.searchKDTree=function(t,e,i,o,r){var n=this,s=this.kdAxisArray,a=s[0],l=s[1],h=e?"distX":"dist",c=(n.options.findNearestPointBy||"").indexOf("y")>-1?2:1,d=!!n.isBubble,p=o||function(t,e,i){var o=t[i]||0,r=e[i]||0;return[o===r&&t.index>e.index||o<r?t:e,!1]},u=r||function(t,e){return t<e};if(this.kdTree||this.buildingKdTree||this.buildKDTree(i),this.kdTree)return function t(e,i,o,r){var s,c,f,g,v,m,y,x,b,w,M=i.point,k=n.kdAxisArray[o%r],S=M,T=!1;c=e[a],f=M[a],g=sk(c)&&sk(f)?c-f:null,v=e[l],m=M[l],y=sk(v)&&sk(m)?v-m:0,x=d&&(null===(s=M.marker)||void 0===s?void 0:s.radius)||0,M.dist=Math.sqrt((g&&g*g||0)+y*y)-x,M.distX=sk(g)?Math.abs(g)-x:Number.MAX_VALUE;var C=(e[k]||0)-(M[k]||0)+(d&&(null===(w=M.marker)||void 0===w?void 0:w.radius)||0),A=C<0?"left":"right",P=C<0?"right":"left";return i[A]&&(S=(b=p(M,t(e,i[A],o+1,r),h))[0],T=b[1]),i[P]&&u(Math.sqrt(C*C),S[h],T)&&(S=p(S,t(e,i[P],o+1,r),h)[0]),S}(t,this.kdTree,c,c)},t.prototype.pointPlacementToXValue=function(){var t=this.options,e=this.xAxis,i=t.pointPlacement;return"between"===i&&(i=e.reversed?-.5:.5),sz(i)?i*(t.pointRange||e.pointRange):0},t.prototype.isPointInside=function(t){var e=this.chart,i=this.xAxis,o=this.yAxis,r=t.plotX,n=void 0===r?-1:r,s=t.plotY,a=void 0===s?-1:s;return a>=0&&a<=(o?o.len:e.plotHeight)&&n>=0&&n<=(i?i.len:e.plotWidth)},t.prototype.drawTracker=function(){var t,e=this,i=e.options,o=i.trackByArea,r=[].concat((o?e.areaPath:e.graphPath)||[]),n=e.chart,s=n.pointer,a=n.renderer,l=(null===(t=n.options.tooltip)||void 0===t?void 0:t.snap)||0,h=function(){i.enableMouseTracking&&n.hoverSeries!==e&&e.onMouseOver()},c="rgba(192,192,192,"+(sf?1e-4:.002)+")",d=e.tracker;d?d.attr({d:r}):e.graph&&(e.tracker=d=a.path(r).attr({visibility:e.visible?"inherit":"hidden",zIndex:2}).addClass(o?"highcharts-tracker-area":"highcharts-tracker-line").add(e.group),n.styledMode||d.attr({"stroke-linecap":"round","stroke-linejoin":"round",stroke:c,fill:o?c:"none","stroke-width":e.graph.strokeWidth()+(o?0:2*l)}),[e.tracker,e.markerGroup,e.dataLabelsGroup].forEach(function(t){t&&(t.addClass("highcharts-tracker").on("mouseover",h).on("mouseout",function(t){null==s||s.onTrackerMouseOut(t)}),i.cursor&&!n.styledMode&&t.css({cursor:i.cursor}),t.on("touchstart",h))})),sL(this,"afterDrawTracker")},t.prototype.addPoint=function(t,e,i,o,r){var n,s,a=this.options,l=this.chart,h=this.data,c=this.dataTable,d=this.xAxis,p=(null==d?void 0:d.hasNames)&&d.names,u=a.data,f=this.getColumn("x");e=sW(e,!0);var g={series:this};this.pointClass.prototype.applyOptions.apply(g,[t]);var v=g.x;if(s=f.length,this.requireSorting&&v<f[s-1])for(n=!0;s&&f[s-1]>v;)s--;c.setRow(g,s,!0,{addColumns:!1}),p&&g.name&&(p[v]=g.name),null==u||u.splice(s,0,t),(n||this.processedData)&&(this.data.splice(s,0,null),this.processData()),"point"===a.legendType&&this.generatePoints(),i&&(h[0]&&h[0].remove?h[0].remove(!1):([h,u].filter(sk).forEach(function(t){t.shift()}),c.deleteRows(0))),!1!==r&&sL(this,"addPoint",{point:g}),this.isDirty=!0,this.isDirtyData=!0,e&&l.redraw(o)},t.prototype.removePoint=function(t,e,i){var o=this,r=o.chart,n=o.data,s=o.points,a=o.dataTable,l=n[t],h=function(){[(null==s?void 0:s.length)===n.length?s:void 0,n,o.options.data].filter(sk).forEach(function(e){e.splice(t,1)}),a.deleteRows(t),null==l||l.destroy(),o.isDirty=!0,o.isDirtyData=!0,e&&r.redraw()};eR(i,r),e=sW(e,!0),l?l.firePointEvent("remove",null,h):h()},t.prototype.remove=function(t,e,i,o){var r=this,n=r.chart;function s(){r.destroy(o),n.isDirtyLegend=n.isDirtyBox=!0,n.linkSeries(o),sW(t,!0)&&n.redraw(e)}!1!==i?sL(r,"remove",null,s):s()},t.prototype.update=function(e,i){sL(this,"update",{options:e=sT(e,this.userOptions)});var o,r,n,s,a,l,h=this,c=h.chart,d=h.userOptions,p=h.initialType||h.type,u=c.options.plotOptions,f=sv[p].prototype,g=h.finishedAnimating&&{animation:!1},v={},m=t.keepProps.slice(),y=e.type||d.type||c.options.chart.type,x=!(this.hasDerivedData||y&&y!==this.type||void 0!==e.keys||void 0!==e.pointStart||void 0!==e.pointInterval||void 0!==e.relativeXValue||e.joinBy||e.mapData||["dataGrouping","pointStart","pointInterval","pointIntervalUnit","keys"].some(function(t){return h.hasOptionChanged(t)}));y=y||p,x?(m.push.apply(m,t.keepPropsForPoints),!1!==e.visible&&m.push("area","graph"),h.parallelArrays.forEach(function(t){m.push(t+"Data")}),e.data&&(e.dataSorting&&sP(h.options.dataSorting,e.dataSorting),this.setData(e.data,!1))):this.dataTable.modified=this.dataTable,e=sN(d,{index:void 0===d.index?h.index:d.index,pointStart:null!==(n=null!==(r=null===(o=null==u?void 0:u.series)||void 0===o?void 0:o.pointStart)&&void 0!==r?r:d.pointStart)&&void 0!==n?n:h.getColumn("x")[0]},!x&&{data:h.options.data},e,g),x&&e.data&&(e.data=h.options.data),(m=["group","markerGroup","dataLabelsGroup","transformGroup"].concat(m)).forEach(function(t){m[t]=h[t],delete h[t]});var b=!1;if(sv[y]){if(b=y!==h.type,h.remove(!1,!1,!1,!0),b){if(c.propFromSeries(),Object.setPrototypeOf)Object.setPrototypeOf(h,sv[y].prototype);else{var w=Object.hasOwnProperty.call(h,"hcEvents")&&h.hcEvents;for(l in f)h[l]=void 0;sP(h,sv[y].prototype),w?h.hcEvents=w:delete h.hcEvents}}}else sA(17,!0,c,{missingModuleFor:y});if(m.forEach(function(t){h[t]=m[t]}),h.init(c,e),x&&this.points){!1===(a=h.options).visible?(v.graphic=1,v.dataLabel=1):(this.hasMarkerChanged(a,d)&&(v.graphic=1),(null===(s=h.hasDataLabels)||void 0===s?void 0:s.call(h))||(v.dataLabel=1));for(var M=0,k=this.points;M<k.length;M++){var S=k[M];(null==S?void 0:S.series)&&(S.resolveColor(),Object.keys(v).length&&S.destroyElements(v),!1===a.showInLegend&&S.legendItem&&c.legend.destroyItem(S))}}h.initialType=p,c.linkSeries(),c.setSortedData(),b&&h.linkedSeries.length&&(h.isDirtyData=!0),sL(this,"afterUpdate"),sW(i,!0)&&c.redraw(!!x&&void 0)},t.prototype.setName=function(t){this.name=this.options.name=this.userOptions.name=t,this.chart.isDirtyLegend=!0},t.prototype.hasOptionChanged=function(t){var e,i,o=this.chart,r=this.options[t],n=o.options.plotOptions,s=this.userOptions[t],a=sW(null===(e=null==n?void 0:n[this.type])||void 0===e?void 0:e[t],null===(i=null==n?void 0:n.series)||void 0===i?void 0:i[t]);return s&&!sk(a)?r!==s:r!==sW(a,r)},t.prototype.onMouseOver=function(){var t=this.chart,e=t.hoverSeries,i=t.pointer;null==i||i.setHoverChartIndex(),e&&e!==this&&e.onMouseOut(),this.options.events.mouseOver&&sL(this,"mouseOver"),this.setState("hover"),t.hoverSeries=this},t.prototype.onMouseOut=function(){var t=this.options,e=this.chart,i=e.tooltip,o=e.hoverPoint;e.hoverSeries=null,o&&o.onMouseOut(),this&&t.events.mouseOut&&sL(this,"mouseOut"),i&&!this.stickyTracking&&(!i.shared||this.noSharedTooltip)&&i.hide(),e.series.forEach(function(t){t.setState("",!0)})},t.prototype.setState=function(t,e){var i=this,o=i.options,r=i.graph,n=o.inactiveOtherPoints,s=o.states,a=sW(s[t||"normal"]&&s[t||"normal"].animation,i.chart.options.chart.animation),l=o.lineWidth,h=o.opacity;if(t=t||"",i.state!==t&&([i.group,i.markerGroup,i.dataLabelsGroup].forEach(function(e){e&&(i.state&&e.removeClass("highcharts-series-"+i.state),t&&e.addClass("highcharts-series-"+t))}),i.state=t,!i.chart.styledMode)){if(s[t]&&!1===s[t].enabled)return;if(t&&(l=s[t].lineWidth||l+(s[t].lineWidthPlus||0),h=sW(s[t].opacity,h)),r&&!r.dashstyle&&sz(l))for(var c=0,d=sp([r],this.zones.map(function(t){return t.graph}),!0);c<d.length;c++){var p=d[c];null==p||p.animate({"stroke-width":l},a)}n||[i.group,i.markerGroup,i.dataLabelsGroup,i.labelBySeries].forEach(function(t){t&&t.animate({opacity:h},a)})}e&&n&&i.points&&i.setAllPointsToState(t||void 0)},t.prototype.setAllPointsToState=function(t){this.points.forEach(function(e){e.setState&&e.setState(t)})},t.prototype.setVisible=function(t,e){var i,o=this,r=o.chart,n=r.options.chart.ignoreHiddenSeries,s=o.visible;o.visible=t=o.options.visible=o.userOptions.visible=void 0===t?!s:t;var a=t?"show":"hide";["group","dataLabelsGroup","markerGroup","tracker","tt"].forEach(function(t){var e;null===(e=o[t])||void 0===e||e[a]()}),(r.hoverSeries===o||(null===(i=r.hoverPoint)||void 0===i?void 0:i.series)===o)&&o.onMouseOut(),o.legendItem&&r.legend.colorizeItem(o,t),o.isDirty=!0,o.options.stacking&&r.series.forEach(function(t){t.options.stacking&&t.visible&&(t.isDirty=!0)}),o.linkedSeries.forEach(function(e){e.setVisible(t,!1)}),n&&(r.isDirtyBox=!0),sL(o,a),!1!==e&&r.redraw()},t.prototype.show=function(){this.setVisible(!0)},t.prototype.hide=function(){this.setVisible(!1)},t.prototype.select=function(t){this.selected=t=this.options.selected=void 0===t?!this.selected:t,this.checkbox&&(this.checkbox.checked=t),sL(this,t?"select":"unselect")},t.prototype.shouldShowTooltip=function(t,e,i){return void 0===i&&(i={}),i.series=this,i.visiblePlotOnly=!0,this.chart.isInsidePlot(t,e,i)},t.prototype.drawLegendSymbol=function(t,e){var i;null===(i=sr[this.options.legendSymbol||"rectangle"])||void 0===i||i.call(this,t,e)},t.defaultOptions=sn,t.types=sc.seriesTypes,t.registerType=sc.registerSeriesType,t.keepProps=["colorIndex","eventOptions","navigatorSeries","symbolIndex","baseSeries"],t.keepPropsForPoints=["data","isDirtyData","isDirtyCanvas","points","dataTable","processedData","xIncrement","cropped","_hasPointMarkers","hasDataLabels","nodes","layout","level","mapMap","mapData","minY","maxY","minX","maxX","transformGroups"],t}();sP(sF.prototype,{axisTypes:["xAxis","yAxis"],coll:"series",colorCounter:0,directTouch:!1,invertible:!0,isCartesian:!0,kdAxisArray:["clientX","plotY"],parallelArrays:["x","y"],pointClass:nz,requireSorting:!0,sorted:!0}),sc.series=sF;var sY=o1.registerEventOptions,sG=tb.composed,sH=tb.marginNames,sV=ih.distribute,sU=ii.format,sZ=tV.addEvent,sq=tV.createElement,sK=tV.css,s$=tV.defined,sJ=tV.discardElement,sQ=tV.find,s0=tV.fireEvent,s1=tV.isNumber,s2=tV.merge,s3=tV.pick,s6=tV.pushUnique,s5=tV.relativeLength,s9=tV.stableSort,s8=tV.syncTimeout,s4=function(){function t(t,e){var i=this;this.allItems=[],this.initialItemY=0,this.itemHeight=0,this.itemMarginBottom=0,this.itemMarginTop=0,this.itemX=0,this.itemY=0,this.lastItemY=0,this.lastLineHeight=0,this.legendHeight=0,this.legendWidth=0,this.maxItemWidth=0,this.maxLegendWidth=0,this.offsetWidth=0,this.padding=0,this.pages=[],this.symbolHeight=0,this.symbolWidth=0,this.titleHeight=0,this.totalItemWidth=0,this.widthOption=0,this.chart=t,this.setOptions(e),e.enabled&&(this.render(),sY(this,e),sZ(this.chart,"endResize",function(){this.legend.positionCheckboxes()})),sZ(this.chart,"render",function(){i.options.enabled&&i.proximate&&(i.proximatePositions(),i.positionItems())})}return t.prototype.setOptions=function(t){var e=s3(t.padding,8);this.options=t,this.chart.styledMode||(this.itemStyle=t.itemStyle,this.itemHiddenStyle=s2(this.itemStyle,t.itemHiddenStyle)),this.itemMarginTop=t.itemMarginTop,this.itemMarginBottom=t.itemMarginBottom,this.padding=e,this.initialItemY=e-5,this.symbolWidth=s3(t.symbolWidth,16),this.pages=[],this.proximate="proximate"===t.layout&&!this.chart.inverted,this.baseline=void 0},t.prototype.update=function(t,e){var i=this.chart;this.setOptions(s2(!0,this.options,t)),"events"in this.options&&sY(this,this.options),this.destroy(),i.isDirtyLegend=i.isDirtyBox=!0,s3(e,!0)&&i.redraw(),s0(this,"afterUpdate",{redraw:e})},t.prototype.colorizeItem=function(t,e){var i,o=t.color,r=t.legendItem||{},n=r.area,s=r.group,a=r.label,l=r.line,h=r.symbol;if((t instanceof sF||t instanceof nz)&&(t.color=(null===(i=t.options)||void 0===i?void 0:i.legendSymbolColor)||o),null==s||s[e?"removeClass":"addClass"]("highcharts-legend-item-hidden"),!this.chart.styledMode){var c=this.itemHiddenStyle,d=void 0===c?{}:c,p=d.color,u=t.options,f=u.fillColor,g=u.fillOpacity,v=u.lineColor,m=u.marker,y=function(t){return!e&&(t.fill&&(t.fill=p),t.stroke&&(t.stroke=p)),t};null==a||a.css(s2(e?this.itemStyle:d)),null==l||l.attr(y({stroke:v||t.color})),h&&h.attr(y(m&&h.isMarker?t.pointAttribs():{fill:t.color})),null==n||n.attr(y({fill:f||t.color,"fill-opacity":f?1:null!=g?g:.75}))}t.color=o,s0(this,"afterColorizeItem",{item:t,visible:e})},t.prototype.positionItems=function(){this.allItems.forEach(this.positionItem,this),this.chart.isResizing||this.positionCheckboxes()},t.prototype.positionItem=function(t){var e=this,i=t.legendItem||{},o=i.group,r=i.x,n=void 0===r?0:r,s=i.y,a=void 0===s?0:s,l=this.options,h=l.symbolPadding,c=!l.rtl,d=t.checkbox;if(null==o?void 0:o.element){var p={translateX:c?n:this.legendWidth-n-2*h-4,translateY:a};o[s$(o.translateY)?"animate":"attr"](p,void 0,function(){s0(e,"afterPositionItem",{item:t})})}d&&(d.x=n,d.y=a)},t.prototype.destroyItem=function(t){for(var e=t.checkbox,i=t.legendItem||{},o=0,r=["group","label","line","symbol"];o<r.length;o++){var n=r[o];i[n]&&(i[n]=i[n].destroy())}e&&sJ(e),t.legendItem=void 0},t.prototype.destroy=function(){for(var t=0,e=this.getAllItems();t<e.length;t++){var i=e[t];this.destroyItem(i)}for(var o=0,r=["clipRect","up","down","pager","nav","box","title","group"];o<r.length;o++){var n=r[o];this[n]&&(this[n]=this[n].destroy())}this.display=null},t.prototype.positionCheckboxes=function(){var t,e,i=null===(t=this.group)||void 0===t?void 0:t.alignAttr,o=this.clipHeight||this.legendHeight,r=this.titleHeight;i&&(e=i.translateY,this.allItems.forEach(function(t){var n,s=t.checkbox;s&&(n=e+r+s.y+(this.scrollOffset||0)+3,sK(s,{left:i.translateX+t.checkboxOffset+s.x-20+"px",top:n+"px",display:this.proximate||n>e-6&&n<e+o-6?"":"none"}))},this))},t.prototype.renderTitle=function(){var t,e=this.options,i=this.padding,o=e.title,r=0;o.text&&(this.title||(this.title=this.chart.renderer.label(o.text,i-3,i-4,void 0,void 0,void 0,e.useHTML,void 0,"legend-title").attr({zIndex:1}),this.chart.styledMode||this.title.css(o.style),this.title.add(this.group)),o.width||this.title.css({width:this.maxLegendWidth+"px"}),r=(t=this.title.getBBox()).height,this.offsetWidth=t.width,this.contentGroup.attr({translateY:r})),this.titleHeight=r},t.prototype.setText=function(t){var e=this.options;t.legendItem.label.attr({text:e.labelFormat?sU(e.labelFormat,t,this.chart):e.labelFormatter.call(t)})},t.prototype.renderItem=function(t){var e,i=t.legendItem=t.legendItem||{},o=this.chart,r=o.renderer,n=this.options,s="horizontal"===n.layout,a=this.symbolWidth,l=n.symbolPadding||0,h=this.itemStyle,c=this.itemHiddenStyle,d=s?s3(n.itemDistance,20):0,p=!n.rtl,u=!t.series,f=!u&&t.series.drawLegendSymbol?t.series:t,g=f.options,v=!!this.createCheckboxForItem&&g&&g.showCheckbox,m=n.useHTML,y=t.options.className,x=i.label,b=a+l+d+20*!!v;!x&&(i.group=r.g("legend-item").addClass("highcharts-"+f.type+"-series highcharts-color-"+t.colorIndex+(y?" "+y:"")+(u?" highcharts-series-"+t.index:"")).attr({zIndex:1}).add(this.scrollGroup),i.label=x=r.text("",p?a+l:-l,this.baseline||0,m),o.styledMode||x.css(s2(t.visible?h:c)),x.attr({align:p?"left":"right",zIndex:2}).add(i.group),!this.baseline&&(this.fontMetrics=r.fontMetrics(x),this.baseline=this.fontMetrics.f+3+this.itemMarginTop,x.attr("y",this.baseline),this.symbolHeight=s3(n.symbolHeight,this.fontMetrics.f),n.squareSymbol&&(this.symbolWidth=s3(n.symbolWidth,Math.max(this.symbolHeight,16)),b=this.symbolWidth+l+d+20*!!v,p&&x.attr("x",this.symbolWidth+l))),f.drawLegendSymbol(this,t),this.setItemEvents&&this.setItemEvents(t,x,m)),v&&!t.checkbox&&this.createCheckboxForItem&&this.createCheckboxForItem(t),this.colorizeItem(t,t.visible),(o.styledMode||!h.width)&&x.css({width:(n.itemWidth||this.widthOption||o.spacingBox.width)-b+"px"}),this.setText(t);var w=x.getBBox(),M=(null===(e=this.fontMetrics)||void 0===e?void 0:e.h)||0;t.itemWidth=t.checkboxOffset=n.itemWidth||i.labelWidth||w.width+b,this.maxItemWidth=Math.max(this.maxItemWidth,t.itemWidth),this.totalItemWidth+=t.itemWidth,this.itemHeight=t.itemHeight=Math.round(i.labelHeight||(w.height>1.5*M?w.height:M))},t.prototype.layoutItem=function(t){var e=this.options,i=this.padding,o="horizontal"===e.layout,r=t.itemHeight,n=this.itemMarginBottom,s=this.itemMarginTop,a=o?s3(e.itemDistance,20):0,l=this.maxLegendWidth,h=e.alignColumns&&this.totalItemWidth>l?this.maxItemWidth:t.itemWidth,c=t.legendItem||{};o&&this.itemX-i+h>l&&(this.itemX=i,this.lastLineHeight&&(this.itemY+=s+this.lastLineHeight+n),this.lastLineHeight=0),this.lastItemY=s+this.itemY+n,this.lastLineHeight=Math.max(r,this.lastLineHeight),c.x=this.itemX,c.y=this.itemY,o?this.itemX+=h:(this.itemY+=s+r+n,this.lastLineHeight=r),this.offsetWidth=this.widthOption||Math.max((o?this.itemX-i-(t.checkbox?0:a):h)+i,this.offsetWidth)},t.prototype.getAllItems=function(){var t=[];return this.chart.series.forEach(function(e){var i,o=null==e?void 0:e.options;e&&s3(o.showInLegend,!s$(o.linkedTo)&&void 0,!0)&&(t=t.concat((null===(i=e.legendItem)||void 0===i?void 0:i.labels)||("point"===o.legendType?e.data:e)))}),s0(this,"afterGetAllItems",{allItems:t}),t},t.prototype.getAlignment=function(){var t=this.options;return this.proximate?t.align.charAt(0)+"tv":t.floating?"":t.align.charAt(0)+t.verticalAlign.charAt(0)+t.layout.charAt(0)},t.prototype.adjustMargins=function(t,e){var i=this.chart,o=this.options,r=this.getAlignment();r&&[/(lth|ct|rth)/,/(rtv|rm|rbv)/,/(rbh|cb|lbh)/,/(lbv|lm|ltv)/].forEach(function(n,s){n.test(r)&&!s$(t[s])&&(i[sH[s]]=Math.max(i[sH[s]],i.legend[(s+1)%2?"legendHeight":"legendWidth"]+[1,-1,-1,1][s]*o[s%2?"x":"y"]+s3(o.margin,12)+e[s]+(i.titleOffset[s]||0)))})},t.prototype.proximatePositions=function(){var t,e=this.chart,i=[],o="left"===this.options.align;this.allItems.forEach(function(t){var r,n,s,a,l=o;t.yAxis&&(t.xAxis.options.reversed&&(l=!l),t.points&&(r=sQ(l?t.points:t.points.slice(0).reverse(),function(t){return s1(t.plotY)})),n=this.itemMarginTop+t.legendItem.label.getBBox().height+this.itemMarginBottom,a=t.yAxis.top-e.plotTop,s=t.visible?(r?r.plotY:t.yAxis.height)+(a-.3*n):a+t.yAxis.height,i.push({target:s,size:n,item:t}))},this);for(var r=0,n=sV(i,e.plotHeight);r<n.length;r++){var s=n[r];t=s.item.legendItem||{},s1(s.pos)&&(t.y=e.plotTop-e.spacing[0]+s.pos)}},t.prototype.render=function(){var t,e,i,o,r=this.chart,n=r.renderer,s=this.options,a=this.padding,l=this.getAllItems(),h=this.group,c=this.box;this.itemX=a,this.itemY=this.initialItemY,this.offsetWidth=0,this.lastItemY=0,this.widthOption=s5(s.width,r.spacingBox.width-a),o=r.spacingBox.width-2*a-s.x,["rm","lm"].indexOf(this.getAlignment().substring(0,2))>-1&&(o/=2),this.maxLegendWidth=this.widthOption||o,h||(this.group=h=n.g("legend").addClass(s.className||"").attr({zIndex:7}).add(),this.contentGroup=n.g().attr({zIndex:1}).add(h),this.scrollGroup=n.g().add(this.contentGroup)),this.renderTitle(),s9(l,function(t,e){var i,o;return((null===(i=t.options)||void 0===i?void 0:i.legendIndex)||0)-((null===(o=e.options)||void 0===o?void 0:o.legendIndex)||0)}),s.reversed&&l.reverse(),this.allItems=l,this.display=t=!!l.length,this.lastLineHeight=0,this.maxItemWidth=0,this.totalItemWidth=0,this.itemHeight=0,l.forEach(this.renderItem,this),l.forEach(this.layoutItem,this),e=(this.widthOption||this.offsetWidth)+a,i=this.lastItemY+this.lastLineHeight+this.titleHeight,i=this.handleOverflow(i)+a,c||(this.box=c=n.rect().addClass("highcharts-legend-box").attr({r:s.borderRadius}).add(h)),r.styledMode||c.attr({stroke:s.borderColor,"stroke-width":s.borderWidth||0,fill:s.backgroundColor||"none"}).shadow(s.shadow),e>0&&i>0&&c[c.placed?"animate":"attr"](c.crisp.call({},{x:0,y:0,width:e,height:i},c.strokeWidth())),h[t?"show":"hide"](),r.styledMode&&"none"===h.getStyle("display")&&(e=i=0),this.legendWidth=e,this.legendHeight=i,t&&this.align(),this.proximate||this.positionItems(),s0(this,"afterRender")},t.prototype.align=function(t){void 0===t&&(t=this.chart.spacingBox);var e=this.chart,i=this.options,o=t.y;/(lth|ct|rth)/.test(this.getAlignment())&&e.titleOffset[0]>0?o+=e.titleOffset[0]:/(lbh|cb|rbh)/.test(this.getAlignment())&&e.titleOffset[2]>0&&(o-=e.titleOffset[2]),o!==t.y&&(t=s2(t,{y:o})),e.hasRendered||(this.group.placed=!1),this.group.align(s2(i,{width:this.legendWidth,height:this.legendHeight,verticalAlign:this.proximate?"top":i.verticalAlign}),!0,t)},t.prototype.handleOverflow=function(t){var e,i,o,r,n=this,s=this.chart,a=s.renderer,l=this.options,h=l.y,c="top"===l.verticalAlign,d=this.padding,p=l.maxHeight,u=l.navigation,f=s3(u.animation,!0),g=u.arrowSize||12,v=this.pages,m=this.allItems,y=function(t){"number"==typeof t?M.attr({height:t}):M&&(n.clipRect=M.destroy(),n.contentGroup.clip()),n.contentGroup.div&&(n.contentGroup.div.style.clip=t?"rect("+d+"px,9999px,"+(d+t)+"px,0)":"auto")},x=function(t){return n[t]=a.circle(0,0,1.3*g).translate(g/2,g/2).add(w),s.styledMode||n[t].attr("fill","rgba(0,0,0,0.0001)"),n[t]},b=s.spacingBox.height+(c?-h:h)-d,w=this.nav,M=this.clipRect;return"horizontal"!==l.layout||"middle"===l.verticalAlign||l.floating||(b/=2),p&&(b=Math.min(b,p)),v.length=0,t&&b>0&&t>b&&!1!==u.enabled?(this.clipHeight=e=Math.max(b-20-this.titleHeight-d,0),this.currentPage=s3(this.currentPage,1),this.fullHeight=t,m.forEach(function(t,n){var s=(o=t.legendItem||{}).y||0,a=Math.round(o.label.getBBox().height),l=v.length;(!l||s-v[l-1]>e&&(i||s)!==v[l-1])&&(v.push(i||s),l++),o.pageIx=l-1,i&&r&&(r.pageIx=l-1),n===m.length-1&&s+a-v[l-1]>e&&s>v[l-1]&&(v.push(s),o.pageIx=l),s!==i&&(i=s),r=o}),M||(M=n.clipRect=a.clipRect(0,d-2,9999,0),n.contentGroup.clip(M)),y(e),w||(this.nav=w=a.g().attr({zIndex:1}).add(this.group),this.up=a.symbol("triangle",0,0,g,g).add(w),x("upTracker").on("click",function(){n.scroll(-1,f)}),this.pager=a.text("",15,10).addClass("highcharts-legend-navigation"),!s.styledMode&&u.style&&this.pager.css(u.style),this.pager.add(w),this.down=a.symbol("triangle-down",0,0,g,g).add(w),x("downTracker").on("click",function(){n.scroll(1,f)})),n.scroll(0),t=b):w&&(y(),this.nav=w.destroy(),this.scrollGroup.attr({translateY:1}),this.clipHeight=0),t},t.prototype.scroll=function(t,e){var i=this,o=this.chart,r=this.pages,n=r.length,s=this.clipHeight,a=this.options.navigation,l=this.pager,h=this.padding,c=this.currentPage+t;c>n&&(c=n),c>0&&(void 0!==e&&eR(e,o),this.nav.attr({translateX:h,translateY:s+this.padding+7+this.titleHeight,visibility:"inherit"}),[this.up,this.upTracker].forEach(function(t){t.attr({class:1===c?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"})}),l.attr({text:c+"/"+n}),[this.down,this.downTracker].forEach(function(t){t.attr({x:18+this.pager.getBBox().width,class:c===n?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"})},this),o.styledMode||(this.up.attr({fill:1===c?a.inactiveColor:a.activeColor}),this.upTracker.css({cursor:1===c?"default":"pointer"}),this.down.attr({fill:c===n?a.inactiveColor:a.activeColor}),this.downTracker.css({cursor:c===n?"default":"pointer"})),this.scrollOffset=-r[c-1]+this.initialItemY,this.scrollGroup.animate({translateY:this.scrollOffset}),this.currentPage=c,this.positionCheckboxes(),s8(function(){s0(i,"afterScroll",{currentPage:c})},eB(s3(e,o.renderer.globalAnimation,!0)).duration))},t.prototype.setItemEvents=function(t,e,i){for(var o=this,r=t.legendItem||{},n=o.chart.renderer.boxWrapper,s=t instanceof nz,a=t instanceof sF,l="highcharts-legend-"+(s?"point":"series")+"-active",h=o.chart.styledMode,c=i?[e,r.symbol]:[r.group],d=function(e){o.allItems.forEach(function(i){t!==i&&[i].concat(i.linkedSeries||[]).forEach(function(t){t.setState(e,!s)})})},p=0;p<c.length;p++){var u=c[p];u&&u.on("mouseover",function(){t.visible&&d("inactive"),t.setState("hover"),t.visible&&n.addClass(l),h||e.css(o.options.itemHoverStyle)}).on("mouseout",function(){o.chart.styledMode||e.css(s2(t.visible?o.itemStyle:o.itemHiddenStyle)),d(""),n.removeClass(l),t.setState()}).on("click",function(e){var i=function(){t.setVisible&&t.setVisible(),d(t.visible?"inactive":"")};n.removeClass(l),s0(o,"itemClick",{browserEvent:e,legendItem:t},i),s?t.firePointEvent("legendItemClick",{browserEvent:e}):a&&s0(t,"legendItemClick",{browserEvent:e})})}},t.prototype.createCheckboxForItem=function(t){t.checkbox=sq("input",{type:"checkbox",className:"highcharts-legend-checkbox",checked:t.selected,defaultChecked:t.selected},this.options.itemCheckboxStyle,this.chart.container),sZ(t.checkbox,"click",function(e){var i=e.target;s0(t.series||t,"checkboxClick",{checked:i.checked,item:t},function(){t.select()})})},t}();(y=s4||(s4={})).compose=function(t){s6(sG,"Core.Legend")&&sZ(t,"beforeMargins",function(){this.legend=new y(this,this.options.legend)})};var s7=s4,at=function(){return(at=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},ae=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},ai=ii.numberFormat,ao=o1.registerEventOptions,ar=tb.charts,an=tb.doc,as=tb.marginNames,aa=tb.svg,al=tb.win,ah=sc.seriesTypes,ac=tV.addEvent,ad=tV.attr,ap=tV.createElement,au=tV.css,af=tV.defined,ag=tV.diffObjects,av=tV.discardElement,am=tV.erase,ay=tV.error,ax=tV.extend,ab=tV.find,aw=tV.fireEvent,aM=tV.getAlignFactor,ak=tV.getStyle,aS=tV.isArray,aT=tV.isNumber,aC=tV.isObject,aA=tV.isString,aP=tV.merge,aO=tV.objectEach,aL=tV.pick,aE=tV.pInt,aI=tV.relativeLength,aD=tV.removeEvent,aB=tV.splat,az=tV.syncTimeout,aj=tV.uniqueKey,aN=function(){function t(t,e,i){this.sharedClips={};var o=ae([],arguments,!0);(aA(t)||t.nodeName)&&(this.renderTo=o.shift()),this.init(o[0],o[1])}return t.chart=function(e,i,o){return new t(e,i,o)},t.prototype.setZoomOptions=function(){var t=this.options.chart,e=t.zooming;this.zooming=at(at({},e),{type:aL(t.zoomType,e.type),key:aL(t.zoomKey,e.key),pinchType:aL(t.pinchType,e.pinchType),singleTouch:aL(t.zoomBySingleTouch,e.singleTouch,!1),resetButton:aP(e.resetButton,t.resetZoomButton)})},t.prototype.init=function(t,e){aw(this,"init",{args:arguments},function(){var i,o,r=aP(el,t),n=r.chart,s=this.renderTo||n.renderTo;this.userOptions=ax({},t),(this.renderTo=aA(s)?an.getElementById(s):s)||ay(13,!0,this),this.margin=[],this.spacing=[],this.labelCollectors=[],this.callback=e,this.isResizing=0,this.options=r,this.axes=[],this.series=[],this.locale=null!==(i=r.lang.locale)&&void 0!==i?i:null===(o=this.renderTo.closest("[lang]"))||void 0===o?void 0:o.lang,this.time=new eo(ax(r.time||{},{locale:this.locale}),r.lang),r.time=this.time.options,this.numberFormatter=(n.numberFormatter||ai).bind(this),this.styledMode=n.styledMode,this.hasCartesianSeries=n.showAxes,this.index=ar.length,ar.push(this),tb.chartCount++,ao(this,n),this.xAxis=[],this.yAxis=[],this.pointCount=this.colorCounter=this.symbolCounter=0,this.setZoomOptions(),aw(this,"afterInit"),this.firstRender()})},t.prototype.initSeries=function(t){var e=this.options.chart,i=t.type||e.type,o=ah[i];o||ay(17,!0,this,{missingModuleFor:i});var r=new o;return"function"==typeof r.init&&r.init(this,t),r},t.prototype.setSortedData=function(){this.getSeriesOrderByLinks().forEach(function(t){t.points||t.data||!t.enabledDataSorting||t.setData(t.options.data,!1)})},t.prototype.getSeriesOrderByLinks=function(){return this.series.concat().sort(function(t,e){return t.linkedSeries.length||e.linkedSeries.length?e.linkedSeries.length-t.linkedSeries.length:0})},t.prototype.orderItems=function(t,e){void 0===e&&(e=0);var i=this[t],o=this.options[t]=aB(this.options[t]).slice(),r=this.userOptions[t]=this.userOptions[t]?aB(this.userOptions[t]).slice():[];if(this.hasRendered&&(o.splice(e),r.splice(e)),i)for(var n=e,s=i.length;n<s;++n){var a=i[n];a&&(a.index=n,a instanceof sF&&(a.name=a.getName()),a.options.isInternal||(o[n]=a.options,r[n]=a.userOptions))}},t.prototype.getClipBox=function(t,e){var i,o,r,n,s,a=this.inverted,l=t||{},h=l.xAxis,c=l.yAxis,d=aP(this.clipBox),p=d.x,u=d.y,f=d.width,g=d.height;return t&&(h&&h.len!==this.plotSizeX&&(f=h.len),c&&c.len!==this.plotSizeY&&(g=c.len),a&&!t.invertible&&(f=(i=[g,f])[0],g=i[1])),e&&(p+=null!==(r=null===(o=a?c:h)||void 0===o?void 0:o.pos)&&void 0!==r?r:this.plotLeft,u+=null!==(s=null===(n=a?h:c)||void 0===n?void 0:n.pos)&&void 0!==s?s:this.plotTop),{x:p,y:u,width:f,height:g}},t.prototype.isInsidePlot=function(t,e,i){void 0===i&&(i={});var o,r=this.inverted,n=this.plotBox,s=this.plotLeft,a=this.plotTop,l=this.scrollablePlotBox,h=i.visiblePlotOnly&&(null===(o=this.scrollablePlotArea)||void 0===o?void 0:o.scrollingContainer)||{},c=h.scrollLeft,d=void 0===c?0:c,p=h.scrollTop,u=void 0===p?0:p,f=i.series,g=i.visiblePlotOnly&&l||n,v=i.inverted?e:t,m=i.inverted?t:e,y={x:v,y:m,isInsidePlot:!0,options:i};if(!i.ignoreX){var x=f&&(r&&!this.polar?f.yAxis:f.xAxis)||{pos:s,len:1/0},b=i.paneCoordinates?x.pos+v:s+v;b>=Math.max(d+s,x.pos)&&b<=Math.min(d+s+g.width,x.pos+x.len)||(y.isInsidePlot=!1)}if(!i.ignoreY&&y.isInsidePlot){var w=!r&&i.axis&&!i.axis.isXAxis&&i.axis||f&&(r?f.xAxis:f.yAxis)||{pos:a,len:1/0},M=i.paneCoordinates?w.pos+m:a+m;M>=Math.max(u+a,w.pos)&&M<=Math.min(u+a+g.height,w.pos+w.len)||(y.isInsidePlot=!1)}return aw(this,"afterIsInsidePlot",y),y.isInsidePlot},t.prototype.redraw=function(t){aw(this,"beforeRedraw");var e,i,o,r,n=this.hasCartesianSeries?this.axes:this.colorAxis||[],s=this.series,a=this.pointer,l=this.legend,h=this.userOptions.legend,c=this.renderer,d=c.isHidden(),p=[],u=this.isDirtyBox,f=this.isDirtyLegend;for(c.rootFontSize=c.boxWrapper.getStyle("font-size"),this.setResponsive&&this.setResponsive(!1),eR(!!this.hasRendered&&t,this),d&&this.temporaryDisplay(),this.layOutTitles(!1),o=s.length;o--;)if(((r=s[o]).options.stacking||r.options.centerInCategory)&&(i=!0,r.isDirty)){e=!0;break}if(e)for(o=s.length;o--;)(r=s[o]).options.stacking&&(r.isDirty=!0);s.forEach(function(t){t.isDirty&&("point"===t.options.legendType?("function"==typeof t.updateTotals&&t.updateTotals(),f=!0):h&&(h.labelFormatter||h.labelFormat)&&(f=!0)),t.isDirtyData&&aw(t,"updatedData")}),f&&l&&l.options.enabled&&(l.render(),this.isDirtyLegend=!1),i&&this.getStacks(),n.forEach(function(t){t.updateNames(),t.setScale()}),this.getMargins(),n.forEach(function(t){t.isDirty&&(u=!0)}),n.forEach(function(t){var e=t.min+","+t.max;t.extKey!==e&&(t.extKey=e,p.push(function(){aw(t,"afterSetExtremes",ax(t.eventArgs,t.getExtremes())),delete t.eventArgs})),(u||i)&&t.redraw()}),u&&this.drawChartBox(),aw(this,"predraw"),s.forEach(function(t){(u||t.isDirty)&&t.visible&&t.redraw(),t.isDirtyData=!1}),a&&a.reset(!0),c.draw(),aw(this,"redraw"),aw(this,"render"),d&&this.temporaryDisplay(!0),p.forEach(function(t){t.call()})},t.prototype.get=function(t){var e=this.series;function i(e){return e.id===t||e.options&&e.options.id===t}for(var o=ab(this.axes,i)||ab(this.series,i),r=0;!o&&r<e.length;r++)o=ab(e[r].points||[],i);return o},t.prototype.createAxes=function(){var t=this.userOptions;aw(this,"createAxes");for(var e=0,i=["xAxis","yAxis"];e<i.length;e++)for(var o=i[e],r=t[o]=aB(t[o]||{}),n=0;n<r.length;n++)new rD(this,r[n],o);aw(this,"afterCreateAxes")},t.prototype.getSelectedPoints=function(){return this.series.reduce(function(t,e){return e.getPointsCollection().forEach(function(e){aL(e.selectedStaging,e.selected)&&t.push(e)}),t},[])},t.prototype.getSelectedSeries=function(){return this.series.filter(function(t){return t.selected})},t.prototype.setTitle=function(t,e,i){this.applyDescription("title",t),this.applyDescription("subtitle",e),this.applyDescription("caption",void 0),this.layOutTitles(i)},t.prototype.applyDescription=function(t,e){var i,o=this,r=this.options[t]=aP(this.options[t],e),n=this[t];n&&e&&(this[t]=n=n.destroy()),r&&!n&&((n=this.renderer.text(r.text,0,0,r.useHTML).attr({align:r.align,class:"highcharts-"+t,zIndex:r.zIndex||4}).css({textOverflow:"ellipsis",whiteSpace:"nowrap"}).add()).update=function(e,i){o.applyDescription(t,e),o.layOutTitles(i)},this.styledMode||n.css(ax("title"===t?{fontSize:this.options.isStock?"1em":"1.2em"}:{},r.style)),n.textPxLength=n.getBBox().width,n.css({whiteSpace:null===(i=r.style)||void 0===i?void 0:i.whiteSpace}),this[t]=n)},t.prototype.layOutTitles=function(t){var e,i,o,r,n=this;void 0===t&&(t=!0);var s=[0,0,0],a=this.options,l=this.renderer,h=this.spacingBox;["title","subtitle","caption"].forEach(function(t){var e,i=n[t],o=n.options[t],r=aP(h),a=(null==i?void 0:i.textPxLength)||0;if(i&&o){aw(n,"layOutTitle",{alignTo:r,key:t,textPxLength:a});var c=l.fontMetrics(i),d=c.b,p=c.h,u=o.verticalAlign||"top",f="top"===u,g=f&&o.minScale||1,v="title"===t?f?-3:0:f?s[0]+2:0,m=Math.min(r.width/a,1),y=Math.max(g,m),x=aP({y:"bottom"===u?d:v+d},{align:"title"===t?m<g?"left":"center":null===(e=n.title)||void 0===e?void 0:e.alignValue},o),b=(o.width||(m>g?n.chartWidth:r.width)/y)+"px";i.alignValue!==x.align&&(i.placed=!1);var w=Math.round(i.css({width:b}).getBBox(o.useHTML).height);if(x.height=w,i.align(x,!1,r).attr({align:x.align,scaleX:y,scaleY:y,"transform-origin":""+(r.x+a*y*aM(x.align))+" ".concat(p)}),!o.floating){var M=w*(w<1.2*p?1:y);"top"===u?s[0]=Math.ceil(s[0]+M):"bottom"===u&&(s[2]=Math.ceil(s[2]+M))}}},this),s[0]&&"top"===((null===(e=a.title)||void 0===e?void 0:e.verticalAlign)||"top")&&(s[0]+=(null===(i=a.title)||void 0===i?void 0:i.margin)||0),s[2]&&(null===(o=a.caption)||void 0===o?void 0:o.verticalAlign)==="bottom"&&(s[2]+=(null===(r=a.caption)||void 0===r?void 0:r.margin)||0);var c=!this.titleOffset||this.titleOffset.join(",")!==s.join(",");this.titleOffset=s,aw(this,"afterLayOutTitles"),!this.isDirtyBox&&c&&(this.isDirtyBox=this.isDirtyLegend=c,this.hasRendered&&t&&this.isDirtyBox&&this.redraw())},t.prototype.getContainerBox=function(){var t=this,e=[].map.call(this.renderTo.children,function(e){if(e!==t.container){var i=e.style.display;return e.style.display="none",[e,i]}}),i={width:ak(this.renderTo,"width",!0)||0,height:ak(this.renderTo,"height",!0)||0};return e.filter(Boolean).forEach(function(t){var e=t[0],i=t[1];e.style.display=i}),i},t.prototype.getChartSize=function(){var t,e=this.options.chart,i=e.width,o=e.height,r=this.getContainerBox(),n=r.height<=1||!(null===(t=this.renderTo.parentElement)||void 0===t?void 0:t.style.height)&&"100%"===this.renderTo.style.height;this.chartWidth=Math.max(0,i||r.width||600),this.chartHeight=Math.max(0,aI(o,this.chartWidth)||(n?400:r.height)),this.containerBox=r},t.prototype.temporaryDisplay=function(t){var e,i=this.renderTo;if(t)for(;null==i?void 0:i.style;)i.hcOrigStyle&&(au(i,i.hcOrigStyle),delete i.hcOrigStyle),i.hcOrigDetached&&(an.body.removeChild(i),i.hcOrigDetached=!1),i=i.parentNode;else for(;(null==i?void 0:i.style)&&(an.body.contains(i)||i.parentNode||(i.hcOrigDetached=!0,an.body.appendChild(i)),("none"===ak(i,"display",!1)||i.hcOricDetached)&&(i.hcOrigStyle={display:i.style.display,height:i.style.height,overflow:i.style.overflow},e={display:"block",overflow:"hidden"},i!==this.renderTo&&(e.height=0),au(i,e),i.offsetWidth||i.style.setProperty("display","block","important")),(i=i.parentNode)!==an.body););},t.prototype.setClassName=function(t){this.container.className="highcharts-container "+(t||"")},t.prototype.getContainer=function(){var t,e,i,o=this.options,r=o.chart,n="data-highcharts-chart",s=aj(),a=this.renderTo,l=aE(ad(a,n));aT(l)&&ar[l]&&ar[l].hasRendered&&ar[l].destroy(),ad(a,n,this.index),a.innerHTML=eJ.emptyHTML,r.skipClone||a.offsetWidth||this.temporaryDisplay(),this.getChartSize();var h=this.chartHeight,c=this.chartWidth;au(a,{overflow:"hidden"}),this.styledMode||(i=ax({position:"relative",overflow:"hidden",width:c+"px",height:h+"px",textAlign:"left",lineHeight:"normal",zIndex:0,"-webkit-tap-highlight-color":"rgba(0,0,0,0)",userSelect:"none","touch-action":"manipulation",outline:"none",padding:"0px"},r.style||{}));var d=ap("div",{id:s},i,a);this.container=d,this.getChartSize(),c===this.chartWidth||(c=this.chartWidth,this.styledMode||au(d,{width:aL(null===(t=r.style)||void 0===t?void 0:t.width,c+"px")})),this.containerBox=this.getContainerBox(),this._cursor=d.style.cursor;var p=r.renderer||!aa?io.getRendererType(r.renderer):oI;if(this.renderer=new p(d,c,h,void 0,r.forExport,null===(e=o.exporting)||void 0===e?void 0:e.allowHTML,this.styledMode),eR(void 0,this),this.setClassName(r.className),this.styledMode)for(var u in o.defs)this.renderer.definition(o.defs[u]);else this.renderer.setStyle(r.style);this.renderer.chartIndex=this.index,aw(this,"afterGetContainer")},t.prototype.getMargins=function(t){var e,i=this.spacing,o=this.margin,r=this.titleOffset;this.resetMargins(),r[0]&&!af(o[0])&&(this.plotTop=Math.max(this.plotTop,r[0]+i[0])),r[2]&&!af(o[2])&&(this.marginBottom=Math.max(this.marginBottom,r[2]+i[2])),(null===(e=this.legend)||void 0===e?void 0:e.display)&&this.legend.adjustMargins(o,i),aw(this,"getMargins"),t||this.getAxisMargins()},t.prototype.getAxisMargins=function(){var t=this,e=t.axisOffset=[0,0,0,0],i=t.colorAxis,o=t.margin,r=function(t){t.forEach(function(t){t.visible&&t.getOffset()})};t.hasCartesianSeries?r(t.axes):(null==i?void 0:i.length)&&r(i),as.forEach(function(i,r){af(o[r])||(t[i]+=e[r])}),t.setChartSize()},t.prototype.getOptions=function(){return ag(this.userOptions,el)},t.prototype.reflow=function(t){var e,i=this,o=i.containerBox,r=i.getContainerBox();null===(e=i.pointer)||void 0===e||delete e.chartPosition,!i.isPrinting&&!i.isResizing&&o&&r.width&&((r.width!==o.width||r.height!==o.height)&&(tV.clearTimeout(i.reflowTimeout),i.reflowTimeout=az(function(){i.container&&i.setSize(void 0,void 0,!1)},100*!!t)),i.containerBox=r)},t.prototype.setReflow=function(){var t=this,e=function(e){var i;(null===(i=t.options)||void 0===i?void 0:i.chart.reflow)&&t.hasLoaded&&t.reflow(e)};if("function"==typeof ResizeObserver)new ResizeObserver(e).observe(t.renderTo);else{var i=ac(al,"resize",e);ac(this,"destroy",i)}},t.prototype.setSize=function(t,e,i){var o=this,r=o.renderer;o.isResizing+=1,eR(i,o);var n=r.globalAnimation;o.oldChartHeight=o.chartHeight,o.oldChartWidth=o.chartWidth,void 0!==t&&(o.options.chart.width=t),void 0!==e&&(o.options.chart.height=e),o.getChartSize();var s=o.chartWidth,a=o.chartHeight,l=o.scrollablePixelsX,h=o.scrollablePixelsY;(o.isDirtyBox||s!==o.oldChartWidth||a!==o.oldChartHeight)&&(o.styledMode||(n?ej:au)(o.container,{width:""+(s+(void 0===l?0:l))+"px",height:""+(a+(void 0===h?0:h))+"px"},n),o.setChartSize(!0),r.setSize(s,a,n),o.axes.forEach(function(t){t.isDirty=!0,t.setScale()}),o.isDirtyLegend=!0,o.isDirtyBox=!0,o.layOutTitles(),o.getMargins(),o.redraw(n),o.oldChartHeight=void 0,aw(o,"resize"),setTimeout(function(){o&&aw(o,"endResize")},eB(n).duration)),o.isResizing-=1},t.prototype.setChartSize=function(t){var e,i,o,r,n,s,a=this.chartHeight,l=this.chartWidth,h=this.inverted,c=this.spacing,d=this.renderer,p=this.clipOffset,u=Math[h?"floor":"round"];this.plotLeft=o=Math.round(this.plotLeft),this.plotTop=r=Math.round(this.plotTop),this.plotWidth=n=Math.max(0,Math.round(l-o-(null!==(e=this.marginRight)&&void 0!==e?e:0))),this.plotHeight=s=Math.max(0,Math.round(a-r-(null!==(i=this.marginBottom)&&void 0!==i?i:0))),this.plotSizeX=h?s:n,this.plotSizeY=h?n:s,this.spacingBox=d.spacingBox={x:c[3],y:c[0],width:l-c[3]-c[1],height:a-c[0]-c[2]},this.plotBox=d.plotBox={x:o,y:r,width:n,height:s},p&&(this.clipBox={x:u(p[3]),y:u(p[0]),width:u(this.plotSizeX-p[1]-p[3]),height:u(this.plotSizeY-p[0]-p[2])}),t||(this.axes.forEach(function(t){t.setAxisSize(),t.setAxisTranslation()}),d.alignElements()),aw(this,"afterSetChartSize",{skipAxes:t})},t.prototype.resetMargins=function(){aw(this,"resetMargins");var t=this,e=t.options.chart,i=e.plotBorderWidth||0,o=Math.round(i)/2;["margin","spacing"].forEach(function(i){var o=e[i],r=aC(o)?o:[o,o,o,o];["Top","Right","Bottom","Left"].forEach(function(o,n){t[i][n]=aL(e[i+o],r[n])})}),as.forEach(function(e,i){t[e]=aL(t.margin[i],t.spacing[i])}),t.axisOffset=[0,0,0,0],t.clipOffset=[o,o,o,o],t.plotBorderWidth=i},t.prototype.drawChartBox=function(){var t,e,i,o=this.options.chart,r=this.renderer,n=this.chartWidth,s=this.chartHeight,a=this.styledMode,l=this.plotBGImage,h=o.backgroundColor,c=o.plotBackgroundColor,d=o.plotBackgroundImage,p=this.plotLeft,u=this.plotTop,f=this.plotWidth,g=this.plotHeight,v=this.plotBox,m=this.clipRect,y=this.clipBox,x=this.chartBackground,b=this.plotBackground,w=this.plotBorder,M="animate";x||(this.chartBackground=x=r.rect().addClass("highcharts-background").add(),M="attr"),a?t=e=x.strokeWidth():(e=(t=o.borderWidth||0)+8*!!o.shadow,i={fill:h||"none"},(t||x["stroke-width"])&&(i.stroke=o.borderColor,i["stroke-width"]=t),x.attr(i).shadow(o.shadow)),x[M]({x:e/2,y:e/2,width:n-e-t%2,height:s-e-t%2,r:o.borderRadius}),M="animate",b||(M="attr",this.plotBackground=b=r.rect().addClass("highcharts-plot-background").add()),b[M](v),!a&&(b.attr({fill:c||"none"}).shadow(o.plotShadow),d&&(l?(d!==l.attr("href")&&l.attr("href",d),l.animate(v)):this.plotBGImage=r.image(d,p,u,f,g).add())),m?m.animate({width:y.width,height:y.height}):this.clipRect=r.clipRect(y),M="animate",w||(M="attr",this.plotBorder=w=r.rect().addClass("highcharts-plot-border").attr({zIndex:1}).add()),a||w.attr({stroke:o.plotBorderColor,"stroke-width":o.plotBorderWidth||0,fill:"none"}),w[M](w.crisp(v,-w.strokeWidth())),this.isDirtyBox=!1,aw(this,"afterDrawChartBox")},t.prototype.propFromSeries=function(){var t,e,i,o=this,r=o.options.chart,n=o.options.series;["inverted","angular","polar"].forEach(function(s){for(e=ah[r.type],i=r[s]||e&&e.prototype[s],t=null==n?void 0:n.length;!i&&t--;)(e=ah[n[t].type])&&e.prototype[s]&&(i=!0);o[s]=i})},t.prototype.linkSeries=function(t){var e=this,i=e.series;i.forEach(function(t){t.linkedSeries.length=0}),i.forEach(function(t){var i=t.options.linkedTo;if(aA(i)){var o=void 0;(o=":previous"===i?e.series[t.index-1]:e.get(i))&&o.linkedParent!==t&&(o.linkedSeries.push(t),t.linkedParent=o,o.enabledDataSorting&&t.setDataSortingOptions(),t.visible=aL(t.options.visible,o.options.visible,t.visible))}}),aw(this,"afterLinkSeries",{isUpdating:t})},t.prototype.renderSeries=function(){this.series.forEach(function(t){t.translate(),t.render()})},t.prototype.render=function(){var t,e,i=this.axes,o=this.colorAxis,r=this.renderer,n=this.options.chart.axisLayoutRuns||2,s=function(t){t.forEach(function(t){t.visible&&t.render()})},a=0,l=!0,h=0;this.setTitle(),aw(this,"beforeMargins"),null===(t=this.getStacks)||void 0===t||t.call(this),this.getMargins(!0),this.setChartSize();for(var c=0;c<i.length;c++){var d=i[c],p=d.options,u=p.labels;if(this.hasCartesianSeries&&d.horiz&&d.visible&&u.enabled&&d.series.length&&"colorAxis"!==d.coll&&!this.polar){a=p.tickLength,d.createGroups();var f=new rr(d,0,"",!0),g=f.createLabel("x",u);if(f.destroy(),g&&aL(u.reserveSpace,!aT(p.crossing))&&(a=g.getBBox().height+u.distance+Math.max(p.offset||0,0)),a){null==g||g.destroy();break}}}for(this.plotHeight=Math.max(this.plotHeight-a,0);(l||e||n>1)&&h<n;){for(var v=this.plotWidth,m=this.plotHeight,y=0;y<i.length;y++){var d=i[y];0===h?d.setScale():(d.horiz&&l||!d.horiz&&e)&&d.setTickInterval(!0)}0===h?this.getAxisMargins():this.getMargins(),l=v/this.plotWidth>(h?1:1.1),e=m/this.plotHeight>(h?1:1.05),h++}this.drawChartBox(),this.hasCartesianSeries?s(i):(null==o?void 0:o.length)&&s(o),this.seriesGroup||(this.seriesGroup=r.g("series-group").attr({zIndex:3}).shadow(this.options.chart.seriesGroupShadow).add()),this.renderSeries(),this.addCredits(),this.setResponsive&&this.setResponsive(),this.hasRendered=!0},t.prototype.addCredits=function(t){var e=this,i=aP(!0,this.options.credits,t);i.enabled&&!this.credits&&(this.credits=this.renderer.text(i.text+(this.mapCredits||""),0,0).addClass("highcharts-credits").on("click",function(){i.href&&(al.location.href=i.href)}).attr({align:i.position.align,zIndex:8}),e.styledMode||this.credits.css(i.style),this.credits.add().align(i.position),this.credits.update=function(t){e.credits=e.credits.destroy(),e.addCredits(t)})},t.prototype.destroy=function(){var t,e,i,o=this,r=o.axes,n=o.series,s=o.container,a=null==s?void 0:s.parentNode;for(aw(o,"destroy"),o.renderer.forExport?am(ar,o):ar[o.index]=void 0,tb.chartCount--,o.renderTo.removeAttribute("data-highcharts-chart"),aD(o),i=r.length;i--;)r[i]=r[i].destroy();for(null===(e=null===(t=this.scroller)||void 0===t?void 0:t.destroy)||void 0===e||e.call(t),i=n.length;i--;)n[i]=n[i].destroy();["title","subtitle","chartBackground","plotBackground","plotBGImage","plotBorder","seriesGroup","clipRect","credits","pointer","rangeSelector","legend","resetZoomButton","tooltip","renderer"].forEach(function(t){var e,i;o[t]=null===(i=null===(e=o[t])||void 0===e?void 0:e.destroy)||void 0===i?void 0:i.call(e)}),s&&(s.innerHTML=eJ.emptyHTML,aD(s),a&&av(s)),aO(o,function(t,e){delete o[e]})},t.prototype.firstRender=function(){var t,e=this,i=e.options;e.getContainer(),e.resetMargins(),e.setChartSize(),e.propFromSeries(),e.createAxes();var o=aS(i.series)?i.series:[];i.series=[],o.forEach(function(t){e.initSeries(t)}),e.linkSeries(),e.setSortedData(),aw(e,"beforeRender"),e.render(),null===(t=e.pointer)||void 0===t||t.getChartPosition(),e.renderer.imgCount||e.hasLoaded||e.onload(),e.temporaryDisplay(!0)},t.prototype.onload=function(){this.callbacks.concat([this.callback]).forEach(function(t){t&&void 0!==this.index&&t.apply(this,[this])},this),aw(this,"load"),aw(this,"render"),af(this.index)&&this.setReflow(),this.warnIfA11yModuleNotLoaded(),this.hasLoaded=!0},t.prototype.warnIfA11yModuleNotLoaded=function(){var t=this.options,e=this.title;!t||this.accessibility||(this.renderer.boxWrapper.attr({role:"img","aria-label":((null==e?void 0:e.element.textContent)||"").replace(/</g,"&lt;")}),t.accessibility&&!1===t.accessibility.enabled||ay('Highcharts warning: Consider including the "accessibility.js" module to make your chart more usable for people with disabilities. Set the "accessibility.enabled" option to false to remove this warning. See https://www.highcharts.com/docs/accessibility/accessibility-module.',!1,this))},t.prototype.addSeries=function(t,e,i){var o,r=this;return t&&(e=aL(e,!0),aw(r,"addSeries",{options:t},function(){o=r.initSeries(t),r.isDirtyLegend=!0,r.linkSeries(),o.enabledDataSorting&&o.setData(t.data,!1),aw(r,"afterAddSeries",{series:o}),e&&r.redraw(i)})),o},t.prototype.addAxis=function(t,e,i,o){return this.createAxis(e?"xAxis":"yAxis",{axis:t,redraw:i,animation:o})},t.prototype.addColorAxis=function(t,e,i){return this.createAxis("colorAxis",{axis:t,redraw:e,animation:i})},t.prototype.createAxis=function(t,e){var i=new rD(this,e.axis,t);return aL(e.redraw,!0)&&this.redraw(e.animation),i},t.prototype.showLoading=function(t){var e=this,i=e.options,o=i.loading,r=function(){n&&au(n,{left:e.plotLeft+"px",top:e.plotTop+"px",width:e.plotWidth+"px",height:e.plotHeight+"px"})},n=e.loadingDiv,s=e.loadingSpan;n||(e.loadingDiv=n=ap("div",{className:"highcharts-loading highcharts-loading-hidden"},null,e.container)),s||(e.loadingSpan=s=ap("span",{className:"highcharts-loading-inner"},null,n),ac(e,"redraw",r)),n.className="highcharts-loading",eJ.setElementHTML(s,aL(t,i.lang.loading,"")),e.styledMode||(au(n,ax(o.style,{zIndex:10})),au(s,o.labelStyle),e.loadingShown||(au(n,{opacity:0,display:""}),ej(n,{opacity:o.style.opacity||.5},{duration:o.showDuration||0}))),e.loadingShown=!0,r()},t.prototype.hideLoading=function(){var t=this.options,e=this.loadingDiv;e&&(e.className="highcharts-loading highcharts-loading-hidden",this.styledMode||ej(e,{opacity:0},{duration:t.loading.hideDuration||100,complete:function(){au(e,{display:"none"})}})),this.loadingShown=!1},t.prototype.update=function(t,e,i,o){var r,n,s,a=this,l={credits:"addCredits",title:"setTitle",subtitle:"setSubtitle",caption:"setCaption"},h=t.isResponsiveOptions,c=[];aw(a,"update",{options:t}),h||a.setResponsive(!1,!0),t=ag(t,a.options),a.userOptions=aP(a.userOptions,t);var d=t.chart;d&&(aP(!0,a.options.chart,d),this.setZoomOptions(),"className"in d&&a.setClassName(d.className),("inverted"in d||"polar"in d||"type"in d)&&(a.propFromSeries(),r=!0),"alignTicks"in d&&(r=!0),"events"in d&&ao(this,d),aO(d,function(t,e){-1!==a.propsRequireUpdateSeries.indexOf("chart."+e)&&(n=!0),-1!==a.propsRequireDirtyBox.indexOf(e)&&(a.isDirtyBox=!0),-1===a.propsRequireReflow.indexOf(e)||(a.isDirtyBox=!0,h||(s=!0))}),!a.styledMode&&d.style&&a.renderer.setStyle(a.options.chart.style||{})),!a.styledMode&&t.colors&&(this.options.colors=t.colors),aO(t,function(e,i){a[i]&&"function"==typeof a[i].update?a[i].update(e,!1):"function"==typeof a[l[i]]?a[l[i]](e):"colors"!==i&&-1===a.collectionsWithUpdate.indexOf(i)&&aP(!0,a.options[i],t[i]),"chart"!==i&&-1!==a.propsRequireUpdateSeries.indexOf(i)&&(n=!0)}),this.collectionsWithUpdate.forEach(function(e){t[e]&&(aB(t[e]).forEach(function(t,o){var r,n=af(t.id);n&&(r=a.get(t.id)),!r&&a[e]&&(r=a[e][aL(t.index,o)])&&(n&&af(r.options.id)||r.options.isInternal)&&(r=void 0),r&&r.coll===e&&(r.update(t,!1),i&&(r.touched=!0)),!r&&i&&a.collectionsWithInit[e]&&(a.collectionsWithInit[e][0].apply(a,[t].concat(a.collectionsWithInit[e][1]||[]).concat([!1])).touched=!0)}),i&&a[e].forEach(function(t){t.touched||t.options.isInternal?delete t.touched:c.push(t)}))}),c.forEach(function(t){t.chart&&t.remove&&t.remove(!1)}),r&&a.axes.forEach(function(t){t.update({},!1)}),n&&a.getSeriesOrderByLinks().forEach(function(t){t.chart&&t.update({},!1)},this);var p=null==d?void 0:d.width,u=d&&(aA(d.height)?aI(d.height,p||a.chartWidth):d.height);s||aT(p)&&p!==a.chartWidth||aT(u)&&u!==a.chartHeight?a.setSize(p,u,o):aL(e,!0)&&a.redraw(o),aw(a,"afterUpdate",{options:t,redraw:e,animation:o})},t.prototype.setSubtitle=function(t,e){this.applyDescription("subtitle",t),this.layOutTitles(e)},t.prototype.setCaption=function(t,e){this.applyDescription("caption",t),this.layOutTitles(e)},t.prototype.showResetZoom=function(){var t=this,e=el.lang,i=t.zooming.resetButton,o=i.theme,r="chart"===i.relativeTo||"spacingBox"===i.relativeTo?null:"plotBox";function n(){t.zoomOut()}aw(this,"beforeShowResetZoom",null,function(){t.resetZoomButton=t.renderer.button(e.resetZoom,null,null,n,o).attr({align:i.position.align,title:e.resetZoomTitle}).addClass("highcharts-reset-zoom").add().align(i.position,!1,r)}),aw(this,"afterShowResetZoom")},t.prototype.zoomOut=function(){var t=this;aw(this,"selection",{resetSelection:!0},function(){return t.transform({reset:!0,trigger:"zoom"})})},t.prototype.pan=function(t,e){var i=this,o="object"==typeof e?e:{enabled:e,type:"x"},r=o.type,n=r&&i[({x:"xAxis",xy:"axes",y:"yAxis"})[r]].filter(function(t){return t.options.panningEnabled&&!t.options.isInternal}),s=i.options.chart;(null==s?void 0:s.panning)&&(s.panning=o),aw(this,"pan",{originalEvent:t},function(){i.transform({axes:n,event:t,to:{x:t.chartX-(i.mouseDownX||0),y:t.chartY-(i.mouseDownY||0)},trigger:"pan"}),au(i.container,{cursor:"move"})})},t.prototype.transform=function(t){var e,i,o,r,n,s,a=this,l=t.axes,h=void 0===l?this.axes:l,c=t.event,d=t.from,p=void 0===d?{}:d,u=t.reset,f=t.selection,g=t.to,v=void 0===g?{}:g,m=t.trigger,y=this.inverted,x=this.time,b=!1;null===(i=this.hoverPoints)||void 0===i||i.forEach(function(t){return t.setState()});for(var w=0;w<h.length;w++){var M=h[w],k=M.horiz,S=M.len,T=M.minPointOffset,C=void 0===T?0:T,A=M.options,P=M.reversed,O=k?"width":"height",L=k?"x":"y",E=aL(v[O],M.len),I=aL(p[O],M.len),D=10>Math.abs(E)?1:E/I,B=(p[L]||0)+I/2-M.pos,z=B-((null!==(o=v[L])&&void 0!==o?o:M.pos)+E/2-M.pos)/D,j=P&&!y||!P&&y?-1:1;if(u||!(B<0)&&!(B>M.len)){var N=M.toValue(z,!0)+(f||M.isOrdinal?0:C*j),R=M.toValue(z+S/D,!0)-(f||M.isOrdinal?0:C*j||0),W=M.allExtremes;if(N>R&&(N=(e=[R,N])[0],R=e[1]),1===D&&!u&&"yAxis"===M.coll&&!W){for(var X=0,_=M.series;X<_.length;X++){var F=_[X],Y=F.getExtremes(F.getProcessedData(!0).modified.getColumn("y")||[],!0);null!=W||(W={dataMin:Number.MAX_VALUE,dataMax:-Number.MAX_VALUE}),aT(Y.dataMin)&&aT(Y.dataMax)&&(W.dataMin=Math.min(Y.dataMin,W.dataMin),W.dataMax=Math.max(Y.dataMax,W.dataMax))}M.allExtremes=W}var G=ax(M.getExtremes(),W||{}),H=G.dataMin,V=G.dataMax,U=G.min,Z=G.max,q=x.parse(A.min),K=x.parse(A.max),$=null!=H?H:q,J=null!=V?V:K,Q=R-N,tt=M.categories?0:Math.min(Q,J-$),te=$-tt*(af(q)?0:A.minPadding),ti=J+tt*(af(K)?0:A.maxPadding),to=M.allowZoomOutside||1===D||"zoom"!==m&&D>1,tr=Math.min(null!=q?q:te,te,to?U:te),tn=Math.max(null!=K?K:ti,ti,to?Z:ti);(!M.isOrdinal||M.options.overscroll||1!==D||u)&&(N<tr&&(N=tr,D>=1&&(R=N+Q)),R>tn&&(R=tn,D>=1&&(N=R-Q)),(u||M.series.length&&(N!==U||R!==Z)&&N>=tr&&R<=tn)&&(f?f[M.coll].push({axis:M,min:N,max:R}):(M.isPanning="zoom"!==m,M.isPanning&&(s=!0),M.setExtremes(u?void 0:N,u?void 0:R,!1,!1,{move:z,trigger:m,scale:D}),!u&&(N>tr||R<tn)&&"mousewheel"!==m&&(n=!0)),b=!0),c&&(this[k?"mouseDownX":"mouseDownY"]=c[k?"chartX":"chartY"]))}}return b&&(f?aw(this,"selection",f,function(){delete t.selection,t.trigger="zoom",a.transform(t)}):(!n||s||this.resetZoomButton?!n&&this.resetZoomButton&&(this.resetZoomButton=this.resetZoomButton.destroy()):this.showResetZoom(),this.redraw("zoom"===m&&(null!==(r=this.options.chart.animation)&&void 0!==r?r:this.pointCount<100)))),b},t}();ax(aN.prototype,{callbacks:[],collectionsWithInit:{xAxis:[aN.prototype.addAxis,[!0]],yAxis:[aN.prototype.addAxis,[!1]],series:[aN.prototype.addSeries]},collectionsWithUpdate:["xAxis","yAxis","series"],propsRequireDirtyBox:["backgroundColor","borderColor","borderWidth","borderRadius","plotBackgroundColor","plotBackgroundImage","plotBorderColor","plotBorderWidth","plotShadow","shadow"],propsRequireReflow:["margin","marginTop","marginRight","marginBottom","marginLeft","spacing","spacingTop","spacingRight","spacingBottom","spacingLeft"],propsRequireUpdateSeries:["chart.inverted","chart.polar","chart.ignoreHiddenSeries","chart.type","colors","plotOptions","time","tooltip"]});var aR=tb.composed,aW=tV.addEvent,aX=tV.createElement,a_=tV.css,aF=tV.defined,aY=tV.erase,aG=tV.merge,aH=tV.pushUnique;function aV(){var t=this.scrollablePlotArea;(this.scrollablePixelsX||this.scrollablePixelsY)&&!t&&(this.scrollablePlotArea=t=new aZ(this)),null==t||t.applyFixed()}function aU(){this.chart.scrollablePlotArea&&(this.chart.scrollablePlotArea.isDirty=!0)}var aZ=function(){function t(t){var e,i,o,r=t.options.chart,n=io.getRendererType(),s=r.scrollablePlotArea||{},a=this.moveFixedElements.bind(this),l={WebkitOverflowScrolling:"touch",overflowX:"hidden",overflowY:"hidden"};t.scrollablePixelsX&&(l.overflowX="auto"),t.scrollablePixelsY&&(l.overflowY="auto"),this.chart=t;var h=this.parentDiv=aX("div",{className:"highcharts-scrolling-parent"},{position:"relative"},t.renderTo),c=this.scrollingContainer=aX("div",{className:"highcharts-scrolling"},l,h),d=this.innerContainer=aX("div",{className:"highcharts-inner-container"},void 0,c),p=this.fixedDiv=aX("div",{className:"highcharts-fixed"},{position:"absolute",overflow:"hidden",pointerEvents:"none",zIndex:((null===(e=r.style)||void 0===e?void 0:e.zIndex)||0)+2,top:0},void 0,!0),u=this.fixedRenderer=new n(p,t.chartWidth,t.chartHeight,r.style);this.mask=u.path().attr({fill:r.backgroundColor||"#fff","fill-opacity":null!==(i=s.opacity)&&void 0!==i?i:.85,zIndex:-1}).addClass("highcharts-scrollable-mask").add(),c.parentNode.insertBefore(p,c),a_(t.renderTo,{overflow:"visible"}),aW(t,"afterShowResetZoom",a),aW(t,"afterApplyDrilldown",a),aW(t,"afterLayOutTitles",a),aW(c,"scroll",function(){var e=t.pointer,i=t.hoverPoint;e&&(delete e.chartPosition,i&&(o=i),e.runPointActions(void 0,o,!0))}),d.appendChild(t.container)}return t.compose=function(t,e,i){var o=this;aH(aR,this.compose)&&(aW(t,"afterInit",aU),aW(e,"afterSetChartSize",function(t){return o.afterSetSize(t.target,t)}),aW(e,"render",aV),aW(i,"show",aU))},t.afterSetSize=function(t,e){var i,o,r,n=t.options.chart.scrollablePlotArea||{},s=n.minWidth,a=n.minHeight,l=t.clipBox,h=t.plotBox,c=t.inverted;if(!t.renderer.forExport&&(s?(t.scrollablePixelsX=i=Math.max(0,s-t.chartWidth),i&&(t.scrollablePlotBox=aG(t.plotBox),h.width=t.plotWidth+=i,l[c?"height":"width"]+=i,r=!0)):a&&(t.scrollablePixelsY=o=Math.max(0,a-t.chartHeight),aF(o)&&(t.scrollablePlotBox=aG(t.plotBox),h.height=t.plotHeight+=o,l[c?"width":"height"]+=o,r=!1)),aF(r)&&!e.skipAxes))for(var d=0,p=t.axes;d<p.length;d++){var u=p[d];(u.horiz===r||t.hasParallelCoordinates&&"yAxis"===u.coll)&&(u.setAxisSize(),u.setAxisTranslation())}},t.prototype.applyFixed=function(){var t,e=this.chart,i=this.fixedRenderer,o=this.isDirty,r=this.scrollingContainer,n=e.axisOffset,s=e.chartWidth,a=e.chartHeight,l=e.container,h=e.plotHeight,c=e.plotLeft,d=e.plotTop,p=e.plotWidth,u=e.scrollablePixelsX,f=void 0===u?0:u,g=e.scrollablePixelsY,v=void 0===g?0:g,m=e.options.chart.scrollablePlotArea||{},y=m.scrollPositionX,x=m.scrollPositionY,b=s+f,w=a+v;i.setSize(s,a),(null==o||o)&&(this.isDirty=!1,this.moveFixedElements()),ez(e.container),a_(l,{width:""+b+"px",height:""+w+"px"}),e.renderer.boxWrapper.attr({width:b,height:w,viewBox:[0,0,b,w].join(" ")}),null===(t=e.chartBackground)||void 0===t||t.attr({width:b,height:w}),a_(r,{width:""+s+"px",height:""+a+"px"}),aF(o)||(r.scrollLeft=f*(void 0===y?0:y),r.scrollTop=v*(void 0===x?0:x));var M=d-n[0]-1,k=c-n[3]-1,S=d+h+n[2]+1,T=c+p+n[1]+1,C=c+p-f,A=d+h-v,P=[["M",0,0]];f?P=[["M",0,M],["L",c-1,M],["L",c-1,S],["L",0,S],["Z"],["M",C,M],["L",s,M],["L",s,S],["L",C,S],["Z"]]:v&&(P=[["M",k,0],["L",k,d-1],["L",T,d-1],["L",T,0],["Z"],["M",k,A],["L",k,a],["L",T,a],["L",T,A],["Z"]]),"adjustHeight"!==e.redrawTrigger&&this.mask.attr({d:P})},t.prototype.moveFixedElements=function(){var e,i=this.chart,o=i.container,r=i.inverted,n=i.scrollablePixelsX,s=i.scrollablePixelsY,a=this.fixedRenderer,l=t.fixedSelectors;if(n&&!r?e=".highcharts-yaxis":n&&r?e=".highcharts-xaxis":s&&!r?e=".highcharts-xaxis":s&&r&&(e=".highcharts-yaxis"),e&&!(this.chart.hasParallelCoordinates&&".highcharts-yaxis"===e))for(var h=0,c=[""+e+":not(.highcharts-radial-axis)",""+e+"-labels:not(.highcharts-radial-axis-labels)"];h<c.length;h++){var d=c[h];aH(l,d)}else for(var p=0,u=[".highcharts-xaxis",".highcharts-yaxis"];p<u.length;p++)for(var f=u[p],g=0,v=[""+f+":not(.highcharts-radial-axis)",""+f+"-labels:not(.highcharts-radial-axis-labels)"];g<v.length;g++){var d=v[g];aY(l,d)}for(var m=0;m<l.length;m++){var d=l[m];[].forEach.call(o.querySelectorAll(d),function(t){(t.namespaceURI===a.SVG_NS?a.box:a.box.parentNode).appendChild(t),t.style.pointerEvents="auto"})}},t.fixedSelectors=[".highcharts-breadcrumbs-group",".highcharts-contextbutton",".highcharts-caption",".highcharts-credits",".highcharts-drillup-button",".highcharts-legend",".highcharts-legend-checkbox",".highcharts-navigator-series",".highcharts-navigator-xaxis",".highcharts-navigator-yaxis",".highcharts-navigator",".highcharts-range-selector-group",".highcharts-reset-zoom",".highcharts-scrollbar",".highcharts-subtitle",".highcharts-title"],t}(),aq=ii.format,aK=sc.series,a$=tV.destroyObjectProperties,aJ=tV.fireEvent,aQ=tV.getAlignFactor,a0=tV.isNumber,a1=tV.pick,a2=function(){function t(t,e,i,o,r){var n=t.chart.inverted,s=t.reversed;this.axis=t;var a=this.isNegative=!!i!=!!s;this.options=e=e||{},this.x=o,this.total=null,this.cumulative=null,this.points={},this.hasValidPoints=!1,this.stack=r,this.leftCliff=0,this.rightCliff=0,this.alignOptions={align:e.align||(n?a?"left":"right":"center"),verticalAlign:e.verticalAlign||(n?"middle":a?"bottom":"top"),y:e.y,x:e.x},this.textAlign=e.textAlign||(n?a?"right":"left":"center")}return t.prototype.destroy=function(){a$(this,this.axis)},t.prototype.render=function(t){var e=this.axis.chart,i=this.options,o=i.format,r=o?aq(o,this,e):i.formatter.call(this);if(this.label)this.label.attr({text:r,visibility:"hidden"});else{this.label=e.renderer.label(r,null,void 0,i.shape,void 0,void 0,i.useHTML,!1,"stack-labels");var n={r:i.borderRadius||0,text:r,padding:a1(i.padding,5),visibility:"hidden"};e.styledMode||(n.fill=i.backgroundColor,n.stroke=i.borderColor,n["stroke-width"]=i.borderWidth,this.label.css(i.style||{})),this.label.attr(n),this.label.added||this.label.add(t)}this.label.labelrank=e.plotSizeY,aJ(this,"afterRender")},t.prototype.setOffset=function(t,e,i,o,r,n){var s=this.alignOptions,a=this.axis,l=this.label,h=this.options,c=this.textAlign,d=a.chart,p=this.getStackBox({xOffset:t,width:e,boxBottom:i,boxTop:o,defaultX:r,xAxis:n}),u=s.verticalAlign;if(l&&p){var f=l.getBBox(void 0,0),g=l.padding,v="justify"===a1(h.overflow,"justify"),m=void 0;s.x=h.x||0,s.y=h.y||0;var y=this.adjustStackPosition({labelBox:f,verticalAlign:u,textAlign:c}),x=y.x,b=y.y;p.x-=x,p.y-=b,l.align(s,!1,p),(m=d.isInsidePlot(l.alignAttr.x+s.x+x,l.alignAttr.y+s.y+b))||(v=!1),v&&aK.prototype.justifyDataLabel.call(a,l,s,l.alignAttr,f,p),l.attr({x:l.alignAttr.x,y:l.alignAttr.y,rotation:h.rotation,rotationOriginX:f.width*aQ(h.textAlign||"center"),rotationOriginY:f.height/2}),a1(!v&&h.crop,!0)&&(m=a0(l.x)&&a0(l.y)&&d.isInsidePlot(l.x-g+(l.width||0),l.y)&&d.isInsidePlot(l.x+g,l.y)),l[m?"show":"hide"]()}aJ(this,"afterSetOffset",{xOffset:t,width:e})},t.prototype.adjustStackPosition=function(t){var e=t.labelBox,i=t.verticalAlign,o=t.textAlign;return{x:e.width/2+e.width/2*(2*aQ(o)-1),y:e.height/2*2*(1-aQ(i))}},t.prototype.getStackBox=function(t){var e=this.axis,i=e.chart,o=t.boxTop,r=t.defaultX,n=t.xOffset,s=t.width,a=t.boxBottom,l=e.stacking.usePercentage?100:a1(o,this.total,0),h=e.toPixels(l),c=t.xAxis||i.xAxis[0],d=a1(r,c.translate(this.x))+n,p=Math.abs(h-e.toPixels(a||a0(e.min)&&e.logarithmic&&e.logarithmic.lin2log(e.min)||0)),u=i.inverted,f=this.isNegative;return u?{x:(f?h:h-p)-i.plotLeft,y:c.height-d-s+c.top-i.plotTop,width:p,height:s}:{x:d+c.transB-i.plotLeft,y:(f?h-p:h)-i.plotTop,width:s,height:p}},t}(),a3=sc.series.prototype,a6=tV.addEvent,a5=tV.correctFloat,a9=tV.defined,a8=tV.destroyObjectProperties,a4=tV.fireEvent,a7=tV.isNumber,lt=tV.objectEach,le=tV.pick;function li(){var t=this.inverted;this.axes.forEach(function(t){var e;(null===(e=t.stacking)||void 0===e?void 0:e.stacks)&&t.hasVisibleSeries&&(t.stacking.oldStacks=t.stacking.stacks)}),this.series.forEach(function(e){var i,o=(null===(i=e.xAxis)||void 0===i?void 0:i.options)||{};e.options.stacking&&e.reserveSpace()&&(e.stackKey=[e.type,le(e.options.stack,""),t?o.top:o.left,t?o.height:o.width].join(","))})}function lo(){var t,e=this.stacking;if(e){var i=e.stacks;lt(i,function(t,e){a8(t),delete i[e]}),null===(t=e.stackTotalGroup)||void 0===t||t.destroy()}}function lr(){this.stacking||(this.stacking=new lc(this))}function ln(t,e,i,o){return!a9(t)||t.x!==e||o&&t.stackKey!==o?t={x:e,index:0,key:o,stackKey:o}:t.index++,t.key=[i,e,t.index].join(","),t}function ls(){var t,e=this,i=e.yAxis,o=e.stackKey||"",r=i.stacking.stacks,n=e.getColumn("x",!0),s=e.options.stacking,a=e[s+"Stacker"];a&&[o,"-"+o].forEach(function(i){for(var o,s,l,h,c=n.length;c--;)s=n[c],t=e.getStackIndicator(t,s,e.index,i),(h=null==(l=null===(o=r[i])||void 0===o?void 0:o[s])?void 0:l.points[t.key||""])&&a.call(e,h,l,c)})}function la(t,e,i){var o=e.total?100/e.total:0;t[0]=a5(t[0]*o),t[1]=a5(t[1]*o),this.stackedYData[i]=t[1]}function ll(t){(this.is("column")||this.is("columnrange"))&&(this.options.centerInCategory&&this.chart.series.length>1?a3.setStackedPoints.call(this,t,"group"):t.stacking.resetStacks())}function lh(t,e){var i,o,r,n,s,a,l,h,c,d=e||this.options.stacking;if(d&&this.reserveSpace()&&(({group:"xAxis"})[d]||"yAxis")===t.coll){var p=this.getColumn("x",!0),u=this.getColumn(this.pointValKey||"y",!0),f=[],g=u.length,v=this.options,m=v.threshold||0,y=v.startFromThreshold?m:0,x=v.stack,b=e?""+this.type+",".concat(d):this.stackKey||"",w="-"+b,M=this.negStacks,k=t.stacking,S=k.stacks,T=k.oldStacks;for(k.stacksTouched+=1,c=0;c<g;c++){var C=p[c]||0,A=u[c],P=a7(A)&&A||0;h=(r=this.getStackIndicator(r,C,this.index)).key||"",S[l=(n=M&&P<(y?0:m))?w:b]||(S[l]={}),S[l][C]||((null===(i=T[l])||void 0===i?void 0:i[C])?(S[l][C]=T[l][C],S[l][C].total=null):S[l][C]=new a2(t,t.options.stackLabels,!!n,C,x)),s=S[l][C],null!==A?(s.points[h]=s.points[this.index]=[le(s.cumulative,y)],a9(s.cumulative)||(s.base=h),s.touched=k.stacksTouched,r.index>0&&!1===this.singleStacks&&(s.points[h][0]=s.points[this.index+","+C+",0"][0])):(delete s.points[h],delete s.points[this.index]);var O=s.total||0;"percent"===d?(a=n?b:w,O=M&&(null===(o=S[a])||void 0===o?void 0:o[C])?(a=S[a][C]).total=Math.max(a.total||0,O)+Math.abs(P):a5(O+Math.abs(P))):"group"===d?a7(A)&&O++:O=a5(O+P),"group"===d?s.cumulative=(O||1)-1:s.cumulative=a5(le(s.cumulative,y)+P),s.total=O,null!==A&&(s.points[h].push(s.cumulative),f[c]=s.cumulative,s.hasValidPoints=!0)}"percent"===d&&(k.usePercentage=!0),"group"!==d&&(this.stackedYData=f),k.oldStacks={}}}var lc=function(){function t(t){this.oldStacks={},this.stacks={},this.stacksTouched=0,this.axis=t}return t.prototype.buildStacks=function(){var t,e,i=this.axis,o=i.series,r="xAxis"===i.coll,n=i.options.reversedStacks,s=o.length;for(this.resetStacks(),this.usePercentage=!1,e=s;e--;)t=o[n?e:s-e-1],r&&t.setGroupedPoints(i),t.setStackedPoints(i);if(!r)for(e=0;e<s;e++)o[e].modifyStacks();a4(i,"afterBuildStacks")},t.prototype.cleanStacks=function(){this.oldStacks&&(this.stacks=this.oldStacks,lt(this.stacks,function(t){lt(t,function(t){t.cumulative=t.total})}))},t.prototype.resetStacks=function(){var t=this;lt(this.stacks,function(e){lt(e,function(i,o){a7(i.touched)&&i.touched<t.stacksTouched?(i.destroy(),delete e[o]):(i.total=null,i.cumulative=null)})})},t.prototype.renderStackTotals=function(){var t,e=this.axis,i=e.chart,o=i.renderer,r=this.stacks,n=eN(i,(null===(t=e.options.stackLabels)||void 0===t?void 0:t.animation)||!1),s=this.stackTotalGroup=this.stackTotalGroup||o.g("stack-labels").attr({zIndex:6,opacity:0}).add();s.translate(i.plotLeft,i.plotTop),lt(r,function(t){lt(t,function(t){t.render(s)})}),s.animate({opacity:1},n)},t}();(tn||(tn={})).compose=function(t,e,i){var o=e.prototype,r=i.prototype;o.getStacks||(a6(t,"init",lr),a6(t,"destroy",lo),o.getStacks=li,r.getStackIndicator=ln,r.modifyStacks=ls,r.percentStacker=la,r.setGroupedPoints=ll,r.setStackedPoints=lh)};var ld=tn,lp=(x=function(t,e){return(x=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}x(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),lu=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},lf=tV.defined,lg=tV.merge,lv=tV.isObject,lm=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return lp(e,t),e.prototype.drawGraph=function(){var t=this,e=this.options,i=(this.gappedPath||this.getGraphPath).call(this),o=this.chart.styledMode;lu([this],this.zones,!0).forEach(function(r,n){var s,a=r.graph,l=a?"animate":"attr",h=r.dashStyle||e.dashStyle;a?(a.endX=t.preventGraphAnimation?null:i.xMap,a.animate({d:i})):i.length&&(r.graph=a=t.chart.renderer.path(i).addClass("highcharts-graph"+(n?" highcharts-zone-graph-".concat(n-1," "):" ")+(n&&r.className||"")).attr({zIndex:1}).add(t.group)),a&&!o&&(s={stroke:!n&&e.lineColor||r.color||t.color||"#cccccc","stroke-width":e.lineWidth||0,fill:t.fillGraph&&t.color||"none"},h?s.dashstyle=h:"square"!==e.linecap&&(s["stroke-linecap"]=s["stroke-linejoin"]="round"),a[l](s).shadow(e.shadow&&lg({filterUnits:"userSpaceOnUse"},lv(e.shadow)?e.shadow:{}))),a&&(a.startX=i.xMap,a.isArea=i.isArea)})},e.prototype.getGraphPath=function(t,e,i){var o,r=this,n=r.options,s=[],a=[],l=n.step,h=(t=t||r.points).reversed;return h&&t.reverse(),(l=({right:1,center:2})[l]||l&&3)&&h&&(l=4-l),(t=this.getValidPoints(t,!1,n.nullInteraction||!(n.connectNulls&&!e&&!i))).forEach(function(h,c){var d,p=h.plotX,u=h.plotY,f=t[c-1],g=h.isNull||"number"!=typeof u;(h.leftCliff||(null==f?void 0:f.rightCliff))&&!i&&(o=!0),g&&!lf(e)&&c>0?o=!n.connectNulls:g&&!e?o=!0:(0===c||o?d=[["M",h.plotX,h.plotY]]:r.getPointSpline?d=[r.getPointSpline(t,h,c)]:l?(d=1===l?[["L",f.plotX,u]]:2===l?[["L",(f.plotX+p)/2,f.plotY],["L",(f.plotX+p)/2,u]]:[["L",p,f.plotY]]).push(["L",p,u]):d=[["L",p,u]],a.push(h.x),l&&(a.push(h.x),2===l&&a.push(h.x)),s.push.apply(s,d),o=!1)}),s.xMap=a,r.graphPath=s,s},e.defaultOptions=lg(sF.defaultOptions,{legendSymbol:"lineMarker"}),e}(sF);sc.registerSeriesType("line",lm);var ly={threshold:0,legendSymbol:"areaMarker"},lx=(b=function(t,e){return(b=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}b(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),lb=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},lw=sc.seriesTypes.line,lM=tV.extend,lk=tV.merge,lS=tV.objectEach,lT=tV.pick,lC=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return lx(e,t),e.prototype.drawGraph=function(){var e=this;this.areaPath=[],t.prototype.drawGraph.apply(this);var i=this.areaPath,o=this.options;lb([this],this.zones,!0).forEach(function(t,r){var n,s={},a=t.fillColor||o.fillColor,l=t.area,h=l?"animate":"attr";l?(l.endX=e.preventGraphAnimation?null:i.xMap,l.animate({d:i})):(s.zIndex=0,(l=t.area=e.chart.renderer.path(i).addClass("highcharts-area"+(r?" highcharts-zone-area-".concat(r-1," "):" ")+(r&&t.className||"")).add(e.group)).isArea=!0),e.chart.styledMode||(s.fill=a||t.color||e.color,s["fill-opacity"]=a?1:null!==(n=o.fillOpacity)&&void 0!==n?n:.75,l.css({pointerEvents:e.stickyTracking?"none":"auto"})),l[h](s),l.startX=i.xMap,l.shiftUnit=o.step?2:1})},e.prototype.getGraphPath=function(t){var e,i,o,r=lw.prototype.getGraphPath,n=this.options,s=n.stacking,a=this.yAxis,l=[],h=[],c=this.index,d=a.stacking.stacks[this.stackKey],p=n.threshold,u=Math.round(a.getThreshold(n.threshold)),f=lT(n.connectNulls,"percent"===s),g=function(i,o,r){var n,f,g=t[i],v=s&&d[g.x].points[c],m=g[r+"Null"]||0,y=g[r+"Cliff"]||0,x=!0;y||m?(n=(m?v[0]:v[1])+y,f=v[0]+y,x=!!m):!s&&t[o]&&t[o].isNull&&(n=f=p),void 0!==n&&(h.push({plotX:e,plotY:null===n?u:a.getThreshold(n),isNull:x,isCliff:!0}),l.push({plotX:e,plotY:null===f?u:a.getThreshold(f),doCurve:!1}))};t=t||this.points,s&&(t=this.getStackPoints(t));for(var v=0,m=t.length;v<m;++v)s||(t[v].leftCliff=t[v].rightCliff=t[v].leftNull=t[v].rightNull=void 0),i=t[v].isNull,e=lT(t[v].rectPlotX,t[v].plotX),o=s?lT(t[v].yBottom,u):u,i&&!f||(f||g(v,v-1,"left"),i&&!s&&f||(h.push(t[v]),l.push({x:v,plotX:e,plotY:o})),f||g(v,v+1,"right"));var y=r.call(this,h,!0,!0);l.reversed=!0;var x=r.call(this,l,!0,!0),b=x[0];b&&"M"===b[0]&&(x[0]=["L",b[1],b[2]]);var w=y.concat(x);w.length&&w.push(["Z"]);var M=r.call(this,h,!1,f);return this.chart.series.length>1&&s&&h.some(function(t){return t.isCliff})&&(w.hasStackedCliffs=M.hasStackedCliffs=!0),w.xMap=y.xMap,this.areaPath=w,M},e.prototype.getStackPoints=function(t){var e=this,i=[],o=[],r=this.xAxis,n=this.yAxis,s=n.stacking.stacks[this.stackKey],a={},l=n.series,h=l.length,c=n.options.reversedStacks?1:-1,d=l.indexOf(e);if(t=t||this.points,this.options.stacking){for(var p=0;p<t.length;p++)t[p].leftNull=t[p].rightNull=void 0,a[t[p].x]=t[p];lS(s,function(t,e){null!==t.total&&o.push(e)}),o.sort(function(t,e){return t-e});var u=l.map(function(t){return t.visible});o.forEach(function(t,p){var f,g,v=0;if(a[t]&&!a[t].isNull)i.push(a[t]),[-1,1].forEach(function(i){var r=1===i?"rightNull":"leftNull",n=s[o[p+i]],v=0;if(n)for(var m=d;m>=0&&m<h;){var y=l[m].index;!(f=n.points[y])&&(y===e.index?a[t][r]=!0:u[m]&&(g=s[t].points[y])&&(v-=g[1]-g[0])),m+=c}a[t][1===i?"rightCliff":"leftCliff"]=v});else{for(var m=d;m>=0&&m<h;){var y=l[m].index;if(f=s[t].points[y]){v=f[1];break}m+=c}v=lT(v,0),v=n.translate(v,0,1,0,1),i.push({isNull:!0,plotX:r.translate(t,0,0,0,1),x:t,plotY:v,yBottom:v})}})}return i},e.defaultOptions=lk(lw.defaultOptions,ly),e}(lw);lM(lC.prototype,{singleStacks:!1}),sc.registerSeriesType("area",lC);var lA=(w=function(t,e){return(w=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}w(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),lP=sc.seriesTypes.line,lO=tV.merge,lL=tV.pick,lE=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return lA(e,t),e.prototype.getPointSpline=function(t,e,i){var o,r,n,s,a=e.plotX||0,l=e.plotY||0,h=t[i-1],c=t[i+1];function d(t){return t&&!t.isNull&&!1!==t.doCurve&&!e.isCliff}if(d(h)&&d(c)){var p=h.plotX||0,u=h.plotY||0,f=c.plotX||0,g=c.plotY||0,v=0;o=(1.5*a+p)/2.5,r=(1.5*l+u)/2.5,n=(1.5*a+f)/2.5,s=(1.5*l+g)/2.5,n!==o&&(v=(s-r)*(n-a)/(n-o)+l-s),r+=v,s+=v,r>u&&r>l?(r=Math.max(u,l),s=2*l-r):r<u&&r<l&&(r=Math.min(u,l),s=2*l-r),s>g&&s>l?(s=Math.max(g,l),r=2*l-s):s<g&&s<l&&(s=Math.min(g,l),r=2*l-s),e.rightContX=n,e.rightContY=s,e.controlPoints={low:[o,r],high:[n,s]}}var m=["C",lL(h.rightContX,h.plotX,0),lL(h.rightContY,h.plotY,0),lL(o,a,0),lL(r,l,0),a,l];return h.rightContX=h.rightContY=void 0,m},e.defaultOptions=lO(lP.defaultOptions),e}(lP);sc.registerSeriesType("spline",lE);var lI=(M=function(t,e){return(M=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}M(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),lD=sc.seriesTypes,lB=lD.area,lz=lD.area.prototype,lj=tV.extend,lN=tV.merge,lR=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return lI(e,t),e.defaultOptions=lN(lE.defaultOptions,lB.defaultOptions),e}(lE);lj(lR.prototype,{getGraphPath:lz.getGraphPath,getStackPoints:lz.getStackPoints,drawGraph:lz.drawGraph}),sc.registerSeriesType("areaspline",lR);var lW={borderRadius:3,centerInCategory:!1,groupPadding:.2,marker:null,pointPadding:.1,minPointLength:0,cropThreshold:50,pointRange:null,states:{hover:{halo:!1,brightness:.1},select:{color:"#cccccc",borderColor:"#000000"}},dataLabels:{align:void 0,verticalAlign:void 0,y:void 0},startFromThreshold:!0,stickyTracking:!1,tooltip:{distance:6},threshold:0,borderColor:"#ffffff"},lX=(k=function(t,e){return(k=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}k(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),l_=eb.parse,lF=tb.noop,lY=tV.clamp,lG=tV.crisp,lH=tV.defined,lV=tV.extend,lU=tV.fireEvent,lZ=tV.isArray,lq=tV.isNumber,lK=tV.merge,l$=tV.pick,lJ=tV.objectEach,lQ=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return lX(e,t),e.prototype.animate=function(t){var e,i,o=this,r=this.yAxis,n=r.pos,s=r.reversed,a=o.options,l=this.chart,h=l.clipOffset,c=l.inverted,d={},p=c?"translateX":"translateY";t&&h?(d.scaleY=.001,i=lY(r.toPixels(a.threshold||0),n,n+r.len),c?d.translateX=(i+=s?-Math.floor(h[0]):Math.ceil(h[2]))-r.len:d.translateY=i+=s?Math.ceil(h[0]):-Math.floor(h[2]),o.clipBox&&o.setClip(),o.group.attr(d)):(e=Number(o.group.attr(p)),o.group.animate({scaleY:1},lV(eB(o.options.animation),{step:function(t,i){o.group&&(d[p]=e+i.pos*(n-e),o.group.attr(d))}})))},e.prototype.init=function(e,i){t.prototype.init.apply(this,arguments);var o=this;(e=o.chart).hasRendered&&e.series.forEach(function(t){t.type===o.type&&(t.isDirty=!0)})},e.prototype.getColumnMetrics=function(){var t,e,i,o=this,r=o.options,n=o.xAxis,s=o.yAxis,a=n.options.reversedStacks,l=n.reversed&&!a||!n.reversed&&a,h={},c=0;!1===r.grouping?c=1:o.chart.series.forEach(function(t){var e,r=t.yAxis,n=t.options;t.type===o.type&&t.reserveSpace()&&s.len===r.len&&s.pos===r.pos&&(n.stacking&&"group"!==n.stacking?(void 0===h[i=t.stackKey]&&(h[i]=c++),e=h[i]):!1!==n.grouping&&(e=c++),t.columnIndex=e)});var d=Math.min(Math.abs(n.transA)*(!(null===(t=n.brokenAxis)||void 0===t?void 0:t.hasBreaks)&&(null===(e=n.ordinal)||void 0===e?void 0:e.slope)||r.pointRange||n.closestPointRange||n.tickInterval||1),n.len),p=d*r.groupPadding,u=(d-2*p)/(c||1),f=Math.min(r.maxPointWidth||n.len,l$(r.pointWidth,u*(1-2*r.pointPadding))),g=(o.columnIndex||0)+ +!!l;return o.columnMetrics={width:f,offset:(u-f)/2+(p+g*u-d/2)*(l?-1:1),paddedWidth:u,columnCount:c},o.columnMetrics},e.prototype.crispCol=function(t,e,i,o){var r=this.borderWidth,n=this.chart.inverted;return o=lG(e+o,r,n)-(e=lG(e,r,n)),this.options.crisp&&(i=lG(t+i,r)-(t=lG(t,r))),{x:t,y:e,width:i,height:o}},e.prototype.adjustForMissingColumns=function(t,e,i,o){var r,n=this;if(!i.isNull&&o.columnCount>1){var s=this.xAxis.series.filter(function(t){return t.visible}).map(function(t){return t.index}),a=0,l=0;lJ(null===(r=this.xAxis.stacking)||void 0===r?void 0:r.stacks,function(t){var e,o="number"==typeof i.x?null===(e=t[i.x.toString()])||void 0===e?void 0:e.points:void 0,r=null==o?void 0:o[n.index],h={};if(o&&lZ(r)){var c=n.index,d=Object.keys(o).filter(function(t){return!t.match(",")&&o[t]&&o[t].length>1}).map(parseFloat).filter(function(t){return -1!==s.indexOf(t)}).filter(function(t){var e=n.chart.series[t].options,i=e.stacking&&e.stack;if(lH(i)){if(lq(h[i]))return c===t&&(c=h[i]),!1;h[i]=t}return!0}).sort(function(t,e){return e-t});a=d.indexOf(c),l=d.length}}),a=this.xAxis.reversed?l-1-a:a;var h=(l-1)*o.paddedWidth+e;t=(i.plotX||0)+h/2-e-a*o.paddedWidth}return t},e.prototype.translate=function(){var t=this,e=t.chart,i=t.options,o=t.dense=t.closestPointRange*t.xAxis.transA<2,r=t.borderWidth=l$(i.borderWidth,+!o),n=t.xAxis,s=t.yAxis,a=i.threshold,l=l$(i.minPointLength,5),h=t.getColumnMetrics(),c=h.width,d=t.pointXOffset=h.offset,p=t.dataMin,u=t.dataMax,f=t.translatedThreshold=s.getThreshold(a),g=t.barW=Math.max(c,1+2*r);i.pointPadding&&i.crisp&&(g=Math.ceil(g)),sF.prototype.translate.apply(t),t.points.forEach(function(o){var r,v=l$(o.yBottom,f),m=999+Math.abs(v),y=o.plotX||0,x=lY(o.plotY,-m,s.len+m),b=Math.min(x,v),w=Math.max(x,v)-b,M=c,k=y+d,S=g;l&&Math.abs(w)<l&&(w=l,r=!s.reversed&&!o.negative||s.reversed&&o.negative,lq(a)&&lq(u)&&o.y===a&&u<=a&&(s.min||0)<a&&(p!==u||(s.max||0)<=a)&&(r=!r,o.negative=!o.negative),b=Math.abs(b-f)>l?v-l:f-(r?l:0)),lH(o.options.pointWidth)&&(k-=Math.round(((M=S=Math.ceil(o.options.pointWidth))-c)/2)),i.centerInCategory&&(k=t.adjustForMissingColumns(k,M,o,h)),o.barX=k,o.pointWidth=M,o.tooltipPos=e.inverted?[lY(s.len+s.pos-e.plotLeft-x,s.pos-e.plotLeft,s.len+s.pos-e.plotLeft),n.len+n.pos-e.plotTop-k-S/2,w]:[n.left-e.plotLeft+k+S/2,lY(x+s.pos-e.plotTop,s.pos-e.plotTop,s.len+s.pos-e.plotTop),w],o.shapeType=t.pointClass.prototype.shapeType||"roundedRect",o.shapeArgs=t.crispCol(k,b,S,o.isNull?0:w)}),lU(this,"afterColumnTranslate")},e.prototype.drawGraph=function(){this.group[this.dense?"addClass":"removeClass"]("highcharts-dense-data")},e.prototype.pointAttribs=function(t,e){var i,o,r,n,s,a=this.options,l=this.pointAttrToOptions||{},h=l.stroke||"borderColor",c=l["stroke-width"]||"borderWidth",d=t&&t.color||this.color,p=t&&t[h]||a[h]||d,u=t&&t.options.dashStyle||a.dashStyle,f=t&&t[c]||a[c]||this[c]||0,g=(null==t?void 0:t.isNull)&&a.nullInteraction?0:null!==(o=null!==(i=null==t?void 0:t.opacity)&&void 0!==i?i:a.opacity)&&void 0!==o?o:1;t&&this.zones.length&&(n=t.getZone(),d=t.options.color||n&&(n.color||t.nonZonedColor)||this.color,n&&(p=n.borderColor||p,u=n.dashStyle||u,f=n.borderWidth||f)),e&&t&&(s=(r=lK(a.states[e],t.options.states&&t.options.states[e]||{})).brightness,d=r.color||void 0!==s&&l_(d).brighten(r.brightness).get()||d,p=r[h]||p,f=r[c]||f,u=r.dashStyle||u,g=l$(r.opacity,g));var v={fill:d,stroke:p,"stroke-width":f,opacity:g};return u&&(v.dashstyle=u),v},e.prototype.drawPoints=function(t){void 0===t&&(t=this.points);var e,i=this,o=this.chart,r=i.options,n=r.nullInteraction,s=o.renderer,a=r.animationLimit||250;t.forEach(function(t){var l=t.plotY,h=t.graphic,c=!!h,d=h&&o.pointCount<a?"animate":"attr";lq(l)&&(null!==t.y||n)?(e=t.shapeArgs,h&&t.hasNewShapeType()&&(h=h.destroy()),i.enabledDataSorting&&(t.startXPos=i.xAxis.reversed?-(e&&e.width||0):i.xAxis.width),!h&&(t.graphic=h=s[t.shapeType](e).add(t.group||i.group),h&&i.enabledDataSorting&&o.hasRendered&&o.pointCount<a&&(h.attr({x:t.startXPos}),c=!0,d="animate")),h&&c&&h[d](lK(e)),o.styledMode||h[d](i.pointAttribs(t,t.selected&&"select")).shadow(!1!==t.allowShadow&&r.shadow),h&&(h.addClass(t.getClassName(),!0),h.attr({visibility:t.visible?"inherit":"hidden"}))):h&&(t.graphic=h.destroy())})},e.prototype.drawTracker=function(t){void 0===t&&(t=this.points);var e,i=this,o=i.chart,r=o.pointer,n=function(t){null==r||r.normalize(t);var e=null==r?void 0:r.getPointFromEvent(t);r&&e&&i.options.enableMouseTracking&&(o.isInsidePlot(t.chartX-o.plotLeft,t.chartY-o.plotTop,{visiblePlotOnly:!0})||(null==r?void 0:r.inClass(t.target,"highcharts-data-label")))&&(r.isDirectTouch=!0,e.onMouseOver(t))};t.forEach(function(t){e=lZ(t.dataLabels)?t.dataLabels:t.dataLabel?[t.dataLabel]:[],t.graphic&&(t.graphic.element.point=t),e.forEach(function(e){(e.div||e.element).point=t})}),i._hasTracking||(i.trackerGroups.forEach(function(t){i[t]&&(i[t].addClass("highcharts-tracker").on("mouseover",n).on("mouseout",function(t){null==r||r.onTrackerMouseOut(t)}).on("touchstart",n),!o.styledMode&&i.options.cursor&&i[t].css({cursor:i.options.cursor}))}),i._hasTracking=!0),lU(this,"afterDrawTracker")},e.prototype.remove=function(){var t=this,e=t.chart;e.hasRendered&&e.series.forEach(function(e){e.type===t.type&&(e.isDirty=!0)}),sF.prototype.remove.apply(t,arguments)},e.defaultOptions=lK(sF.defaultOptions,lW),e}(sF);lV(lQ.prototype,{directTouch:!0,getSymbol:lF,negStacks:!0,trackerGroups:["group","dataLabelsGroup"]}),sc.registerSeriesType("column",lQ);var l0=ii.format,l1=tV.defined,l2=tV.extend,l3=tV.fireEvent,l6=tV.getAlignFactor,l5=tV.isArray,l9=tV.isString,l8=tV.merge,l4=tV.objectEach,l7=tV.pick,ht=tV.pInt,he=tV.splat;!function(t){function e(){return l(this).some(function(t){return null==t?void 0:t.enabled})}function i(t,e,i,o,r){var n,s=this.chart,a=this.enabledDataSorting,l=this.isCartesian&&s.inverted,h=t.plotX,c=t.plotY,d=i.rotation||0,p=l1(h)&&l1(c)&&s.isInsidePlot(h,Math.round(c),{inverted:l,paneCoordinates:!0,series:this}),u=0===d&&"justify"===l7(i.overflow,a?"none":"justify"),f=this.visible&&!1!==t.visible&&l1(h)&&(t.series.forceDL||a&&!u||p||l7(i.inside,!!this.options.stacking)&&o&&s.isInsidePlot(h,l?o.x+1:o.y+o.height-1,{inverted:l,paneCoordinates:!0,series:this})),g=t.pos();if(f&&g){var v,m=e.getBBox(),y=e.getBBox(void 0,0);if(o=l2({x:g[0],y:Math.round(g[1]),width:0,height:0},o||{}),"plotEdges"===i.alignTo&&this.isCartesian&&(o[l?"x":"y"]=0,o[l?"width":"height"]=(null===(n=this.yAxis)||void 0===n?void 0:n.len)||0),l2(i,{width:m.width,height:m.height}),v=o,a&&this.xAxis&&!u&&this.setDataLabelStartPos(t,e,r,p,v),e.align(l8(i,{width:y.width,height:y.height}),!1,o,!1),e.alignAttr.x+=l6(i.align)*(y.width-m.width),e.alignAttr.y+=l6(i.verticalAlign)*(y.height-m.height),e[e.placed?"animate":"attr"]({"text-align":e.alignAttr["text-align"]||"center",x:e.alignAttr.x+(m.width-y.width)/2,y:e.alignAttr.y+(m.height-y.height)/2,rotationOriginX:(e.width||0)/2,rotationOriginY:(e.height||0)/2}),u&&o.height>=0)this.justifyDataLabel(e,i,e.alignAttr,m,o,r);else if(l7(i.crop,!0)){var x=e.alignAttr,b=x.x,w=x.y;f=s.isInsidePlot(b,w,{paneCoordinates:!0,series:this})&&s.isInsidePlot(b+m.width-1,w+m.height-1,{paneCoordinates:!0,series:this})}i.shape&&!d&&e[r?"attr":"animate"]({anchorX:g[0],anchorY:g[1]})}r&&a&&(e.placed=!1),f||a&&!u?(e.show(),e.placed=!0):(e.hide(),e.placed=!1)}function o(){return this.plotGroup("dataLabelsGroup","data-labels",this.hasRendered?"inherit":"hidden",this.options.dataLabels.zIndex||6)}function r(t){var e=this.hasRendered||0,i=this.initDataLabelsGroup().attr({opacity:+e});return!e&&i&&(this.visible&&i.show(),this.options.animation?i.animate({opacity:1},t):i.attr({opacity:1})),i}function n(t){t=t||this.points;var e,i,o=this,r=o.chart,n=o.options,s=r.renderer,h=r.options.chart,c=h.backgroundColor,d=h.plotBackgroundColor,p=s.getContrast(l9(d)&&d||l9(c)&&c||"#000000"),u=l(o),f=u[0],g=f.animation,v=f.defer?eN(r,g,o):{defer:0,duration:0};l3(this,"drawDataLabels"),(null===(e=o.hasDataLabels)||void 0===e?void 0:e.call(o))&&(i=this.initDataLabels(v),t.forEach(function(t){var e,l,h,c=t.dataLabels||[],d=t.color||o.color;he(a(u,t.dlOptions||(null===(e=t.options)||void 0===e?void 0:e.dataLabels))).forEach(function(e,a){var l,h,u,f,g,v=e.enabled&&(t.visible||t.dataLabelOnHidden)&&(!t.isNull||t.dataLabelOnNull)&&function(t,e){var i=e.filter;if(i){var o=i.operator,r=t[i.property],n=i.value;return">"===o&&r>n||"<"===o&&r<n||">="===o&&r>=n||"<="===o&&r<=n||"=="===o&&r==n||"==="===o&&r===n||"!="===o&&r!=n||"!=="===o&&r!==n||!1}return!0}(t,e),m=e.backgroundColor,y=e.borderColor,x=e.distance,b=e.style,w=void 0===b?{}:b,M={},k=c[a],S=!k;v&&(u=l1(h=l7(e[t.formatPrefix+"Format"],e.format))?l0(h,t,r):(e[t.formatPrefix+"Formatter"]||e.formatter).call(t,e),f=e.rotation,!r.styledMode&&(w.color=l7(e.color,w.color,l9(o.color)?o.color:void 0,"#000000"),"contrast"===w.color?("none"!==m&&(g=m),t.contrastColor=s.getContrast("auto"!==g&&l9(g)&&g||(l9(d)?d:"")),w.color=g||!l1(x)&&e.inside||0>ht(x||0)||n.stacking?t.contrastColor:p):delete t.contrastColor,n.cursor&&(w.cursor=n.cursor)),M={r:e.borderRadius||0,rotation:f,padding:e.padding,zIndex:1},r.styledMode||(M.fill="auto"===m?t.color:m,M.stroke="auto"===y?t.color:y,M["stroke-width"]=e.borderWidth),l4(M,function(t,e){void 0===t&&delete M[e]})),!k||v&&l1(u)&&!!(k.div||(null===(l=k.text)||void 0===l?void 0:l.foreignObject))==!!e.useHTML&&(k.rotation&&e.rotation||k.rotation===e.rotation)||(k=void 0,S=!0),v&&l1(u)&&(k?M.text=u:(k=s.label(u,0,0,e.shape,void 0,void 0,e.useHTML,void 0,"data-label")).addClass(" highcharts-data-label-color-"+t.colorIndex+" "+(e.className||"")+(e.useHTML?" highcharts-tracker":"")),k&&(k.options=e,k.attr(M),r.styledMode?w.width&&k.css({width:w.width,textOverflow:w.textOverflow,whiteSpace:w.whiteSpace}):k.css(w).shadow(e.shadow),l3(k,"beforeAddingDataLabel",{labelOptions:e,point:t}),k.added||k.add(i),o.alignDataLabel(t,k,e,void 0,S),k.isActive=!0,c[a]&&c[a]!==k&&c[a].destroy(),c[a]=k))});for(var f=c.length;f--;)(null===(l=c[f])||void 0===l?void 0:l.isActive)?c[f].isActive=!1:(null===(h=c[f])||void 0===h||h.destroy(),c.splice(f,1));t.dataLabel=c[0],t.dataLabels=c})),l3(this,"afterDrawDataLabels")}function s(t,e,i,o,r,n){var s,a,l=this.chart,h=e.align,c=e.verticalAlign,d=t.box?0:t.padding||0,p=l.inverted?this.yAxis:this.xAxis,u=p?p.left-l.plotLeft:0,f=l.inverted?this.xAxis:this.yAxis,g=f?f.top-l.plotTop:0,v=e.x,m=void 0===v?0:v,y=e.y,x=void 0===y?0:y;return(s=(i.x||0)+d+u)<0&&("right"===h&&m>=0?(e.align="left",e.inside=!0):m-=s,a=!0),(s=(i.x||0)+o.width-d+u)>l.plotWidth&&("left"===h&&m<=0?(e.align="right",e.inside=!0):m+=l.plotWidth-s,a=!0),(s=i.y+d+g)<0&&("bottom"===c&&x>=0?(e.verticalAlign="top",e.inside=!0):x-=s,a=!0),(s=(i.y||0)+o.height-d+g)>l.plotHeight&&("top"===c&&x<=0?(e.verticalAlign="bottom",e.inside=!0):x+=l.plotHeight-s,a=!0),a&&(e.x=m,e.y=x,t.placed=!n,t.align(e,void 0,r)),a}function a(t,e){var i,o=[];if(l5(t)&&!l5(e))o=t.map(function(t){return l8(t,e)});else if(l5(e)&&!l5(t))o=e.map(function(e){return l8(t,e)});else if(l5(t)||l5(e)){if(l5(t)&&l5(e))for(i=Math.max(t.length,e.length);i--;)o[i]=l8(t[i],e[i])}else o=l8(t,e);return o}function l(t){var e,i,o=t.chart.options.plotOptions;return he(a(a(null===(e=null==o?void 0:o.series)||void 0===e?void 0:e.dataLabels,null===(i=null==o?void 0:o[t.type])||void 0===i?void 0:i.dataLabels),t.options.dataLabels))}function h(t,e,i,o,r){var n=this.chart,s=n.inverted,a=this.xAxis,l=a.reversed,h=((s?e.height:e.width)||0)/2,c=t.pointWidth,d=c?c/2:0;e.startXPos=s?r.x:l?-h-d:a.width-h+d,e.startYPos=s?l?this.yAxis.height-h+d:-h-d:r.y,o?"hidden"===e.visibility&&(e.show(),e.attr({opacity:0}).animate({opacity:1})):e.attr({opacity:1}).animate({opacity:0},void 0,e.hide),n.hasRendered&&(i&&e.attr({x:e.startXPos,y:e.startYPos}),e.placed=!0)}t.compose=function(t){var l=t.prototype;l.initDataLabels||(l.initDataLabels=r,l.initDataLabelsGroup=o,l.alignDataLabel=i,l.drawDataLabels=n,l.justifyDataLabel=s,l.mergeArrays=a,l.setDataLabelStartPos=h,l.hasDataLabels=e)}}(ts||(ts={}));var hi=ts,ho=tb.composed,hr=sc.series,hn=tV.merge,hs=tV.pushUnique;!function(t){function e(t,e,i,o,r){var n,s,a,l,h,c,d,p=this.chart,u=this.options,f=p.inverted,g=(null===(n=this.xAxis)||void 0===n?void 0:n.len)||p.plotSizeX||0,v=(null===(s=this.yAxis)||void 0===s?void 0:s.len)||p.plotSizeY||0,m=t.dlBox||t.shapeArgs,y=null!==(a=t.below)&&void 0!==a?a:(t.plotY||0)>(null!==(l=this.translatedThreshold)&&void 0!==l?l:v),x=null!==(h=i.inside)&&void 0!==h?h:!!u.stacking;if(m){if(o=hn(m),"allow"!==i.overflow||!1!==i.crop||!1!==u.clip){o.y<0&&(o.height+=o.y,o.y=0);var b=o.y+o.height-v;b>0&&b<o.height-1&&(o.height-=b)}f&&(o={x:v-o.y-o.height,y:g-o.x-o.width,width:o.height,height:o.width}),x||(f?(o.x+=y?0:o.width,o.width=0):(o.y+=y?o.height:0,o.height=0))}null!==(c=i.align)&&void 0!==c||(i.align=!f||x?"center":y?"right":"left"),null!==(d=i.verticalAlign)&&void 0!==d||(i.verticalAlign=f||x?"middle":y?"top":"bottom"),hr.prototype.alignDataLabel.call(this,t,e,i,o,r),i.inside&&t.contrastColor&&e.css({color:t.contrastColor})}t.compose=function(t){hi.compose(hr),hs(ho,"ColumnDataLabel")&&(t.prototype.alignDataLabel=e)}}(ta||(ta={}));var ha=ta,hl=(S=function(t,e){return(S=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}S(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hh=tV.extend,hc=tV.merge,hd=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hl(e,t),e.defaultOptions=hc(lQ.defaultOptions,{}),e}(lQ);hh(hd.prototype,{inverted:!0}),sc.registerSeriesType("bar",hd);var hp={lineWidth:0,findNearestPointBy:"xy",jitter:{x:0,y:0},marker:{enabled:!0},tooltip:{headerFormat:'<span style="color:{point.color}">●</span> <span style="font-size: 0.8em"> {series.name}</span><br/>',pointFormat:"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>"}},hu=(T=function(t,e){return(T=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}T(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hf=sc.seriesTypes,hg=hf.column,hv=hf.line,hm=tV.addEvent,hy=tV.extend,hx=tV.merge,hb=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hu(e,t),e.prototype.applyJitter=function(){var t=this,e=this.options.jitter,i=this.points.length;e&&this.points.forEach(function(o,r){["x","y"].forEach(function(n,s){if(e[n]&&!o.isNull){var a="plot".concat(n.toUpperCase()),l=t[""+n+"Axis"],h=e[n]*l.transA;if(l&&!l.logarithmic){var c,d=Math.max(0,(o[a]||0)-h),p=Math.min(l.len,(o[a]||0)+h);o[a]=d+(p-d)*((c=1e4*Math.sin(r+s*i))-Math.floor(c)),"x"===n&&(o.clientX=o.plotX)}}})})},e.prototype.drawGraph=function(){this.options.lineWidth?t.prototype.drawGraph.call(this):this.graph&&(this.graph=this.graph.destroy())},e.defaultOptions=hx(hv.defaultOptions,hp),e}(hv);hy(hb.prototype,{drawTracker:hg.prototype.drawTracker,sorted:!1,requireSorting:!1,noSharedTooltip:!0,trackerGroups:["group","markerGroup","dataLabelsGroup"]}),hm(hb,"afterTranslate",function(){this.applyJitter()}),sc.registerSeriesType("scatter",hb);var hw=tb.deg2rad,hM=tV.fireEvent,hk=tV.isNumber,hS=tV.pick,hT=tV.relativeLength;(C=tl||(tl={})).getCenter=function(){var t,e,i,o=this.options,r=this.chart,n=2*(o.slicedOffset||0),s=r.plotWidth-2*n,a=r.plotHeight-2*n,l=o.center,h=Math.min(s,a),c=o.thickness,d=o.size,p=o.innerSize||0;"string"==typeof d&&(d=parseFloat(d)),"string"==typeof p&&(p=parseFloat(p));var u=[hS(null==l?void 0:l[0],"50%"),hS(null==l?void 0:l[1],"50%"),hS(d&&d<0?void 0:o.size,"100%"),hS(p&&p<0?void 0:o.innerSize||0,"0%")];for(!r.angular||this instanceof sF||(u[3]=0),e=0;e<4;++e)i=u[e],t=e<2||2===e&&/%$/.test(i),u[e]=hT(i,[s,a,h,u[2]][e])+(t?n:0);return u[3]>u[2]&&(u[3]=u[2]),hk(c)&&2*c<u[2]&&c>0&&(u[3]=u[2]-2*c),hM(this,"afterGetCenter",{positions:u}),u},C.getStartAndEndRadians=function(t,e){var i=hk(t)?t:0,o=hk(e)&&e>i&&e-i<360?e:i+360;return{start:hw*(i+-90),end:hw*(o+-90)}};var hC=tl,hA=(A=function(t,e){return(A=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}A(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hP=function(){return(hP=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},hO=tV.addEvent,hL=tV.defined,hE=tV.extend,hI=tV.isNumber,hD=tV.pick,hB=tV.relativeLength,hz=function(t){function e(e,i,o){var r,n=t.call(this,e,i,o)||this;n.half=0,null!==(r=n.name)&&void 0!==r||(n.name=e.chart.options.lang.pieSliceName);var s=function(t){n.slice("select"===t.type)};return hO(n,"select",s),hO(n,"unselect",s),n}return hA(e,t),e.prototype.getConnectorPath=function(t){var e=t.dataLabelPosition,i=t.options||{},o=i.connectorShape,r=this.connectorShapes[o]||o;return e&&r.call(this,hP(hP({},e.computed),{alignment:e.alignment}),e.connectorPosition,i)||[]},e.prototype.getTranslate=function(){return this.sliced&&this.slicedTranslation||{translateX:0,translateY:0}},e.prototype.haloPath=function(t){var e=this.shapeArgs;return this.sliced||!this.visible?[]:this.series.chart.renderer.symbols.arc(e.x,e.y,e.r+t,e.r+t,{innerR:e.r-1,start:e.start,end:e.end,borderRadius:e.borderRadius})},e.prototype.isValid=function(){return hI(this.y)&&this.y>=0},e.prototype.setVisible=function(t,e){void 0===e&&(e=!0),t!==this.visible&&this.update({visible:null!=t?t:!this.visible},e,void 0,!1)},e.prototype.slice=function(t,e,i){var o=this.series;eR(i,o.chart),e=hD(e,!0),this.sliced=this.options.sliced=t=hL(t)?t:!this.sliced,o.options.data[o.data.indexOf(this)]=this.options,this.graphic&&this.graphic.animate(this.getTranslate())},e}(nz);hE(hz.prototype,{connectorShapes:{fixedOffset:function(t,e,i){var o=e.breakAt,r=e.touchingSliceAt,n=i.softConnector?["C",t.x+("left"===t.alignment?-5:5),t.y,2*o.x-r.x,2*o.y-r.y,o.x,o.y]:["L",o.x,o.y];return[["M",t.x,t.y],n,["L",r.x,r.y]]},straight:function(t,e){var i=e.touchingSliceAt;return[["M",t.x,t.y],["L",i.x,i.y]]},crookedLine:function(t,e,i){var o=e.angle,r=void 0===o?this.angle||0:o,n=e.breakAt,s=e.touchingSliceAt,a=this.series,l=a.center,h=l[0],c=l[1],d=l[2]/2,p=a.chart,u=p.plotLeft,f=p.plotWidth,g="left"===t.alignment,v=t.x,m=t.y,y=n.x;if(i.crookDistance){var x=hB(i.crookDistance,1);y=g?h+d+(f+u-h-d)*(1-x):u+(h-d)*x}else y=h+(c-m)*Math.tan(r-Math.PI/2);var b=[["M",v,m]];return(g?y<=v&&y>=n.x:y>=v&&y<=n.x)&&b.push(["L",y,m]),b.push(["L",n.x,n.y],["L",s.x,s.y]),b}}});var hj={borderRadius:3,center:[null,null],clip:!1,colorByPoint:!0,dataLabels:{connectorPadding:5,connectorShape:"crookedLine",crookDistance:void 0,distance:30,enabled:!0,formatter:function(){return this.isNull?void 0:this.name},softConnector:!0,x:0},fillColor:void 0,ignoreHiddenPoint:!0,inactiveOtherPoints:!0,legendType:"point",marker:null,size:null,showInLegend:!1,slicedOffset:10,stickyTracking:!1,tooltip:{followPointer:!0},borderColor:"#ffffff",borderWidth:1,lineWidth:void 0,states:{hover:{brightness:.1}}},hN=(P=function(t,e){return(P=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}P(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hR=hC.getStartAndEndRadians,hW=tb.noop,hX=tV.clamp,h_=tV.extend,hF=tV.fireEvent,hY=tV.merge,hG=tV.pick,hH=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hN(e,t),e.prototype.animate=function(t){var e=this,i=e.points,o=e.startAngleRad;t||i.forEach(function(t){var i=t.graphic,r=t.shapeArgs;i&&r&&(i.attr({r:hG(t.startR,e.center&&e.center[3]/2),start:o,end:o}),i.animate({r:r.r,start:r.start,end:r.end},e.options.animation))})},e.prototype.drawEmpty=function(){var t,e,i=this.startAngleRad,o=this.endAngleRad,r=this.options;0===this.total&&this.center?(t=this.center[0],e=this.center[1],this.graph||(this.graph=this.chart.renderer.arc(t,e,this.center[1]/2,0,i,o).addClass("highcharts-empty-series").add(this.group)),this.graph.attr({d:i2.arc(t,e,this.center[2]/2,0,{start:i,end:o,innerR:this.center[3]/2})}),this.chart.styledMode||this.graph.attr({"stroke-width":r.borderWidth,fill:r.fillColor||"none",stroke:r.color||"#cccccc"})):this.graph&&(this.graph=this.graph.destroy())},e.prototype.drawPoints=function(){var t=this.chart.renderer;this.points.forEach(function(e){e.graphic&&e.hasNewShapeType()&&(e.graphic=e.graphic.destroy()),e.graphic||(e.graphic=t[e.shapeType](e.shapeArgs).add(e.series.group),e.delayedRendering=!0)})},e.prototype.generatePoints=function(){t.prototype.generatePoints.call(this),this.updateTotals()},e.prototype.getX=function(t,e,i,o){var r=this.center,n=this.radii?this.radii[i.index]||0:r[2]/2,s=o.dataLabelPosition,a=(null==s?void 0:s.distance)||0,l=Math.asin(hX((t-r[1])/(n+a),-1,1));return r[0]+Math.cos(l)*(n+a)*(e?-1:1)+(a>0?(e?-1:1)*(o.padding||0):0)},e.prototype.hasData=function(){return!!this.dataTable.rowCount},e.prototype.redrawPoints=function(){var t,e,i,o,r=this,n=r.chart;this.drawEmpty(),r.group&&!n.styledMode&&r.group.shadow(r.options.shadow),r.points.forEach(function(s){var a={};e=s.graphic,!s.isNull&&e?(o=s.shapeArgs,t=s.getTranslate(),n.styledMode||(i=r.pointAttribs(s,s.selected&&"select")),s.delayedRendering?(e.setRadialReference(r.center).attr(o).attr(t),n.styledMode||e.attr(i).attr({"stroke-linejoin":"round"}),s.delayedRendering=!1):(e.setRadialReference(r.center),n.styledMode||hY(!0,a,i),hY(!0,a,o,t),e.animate(a)),e.attr({visibility:s.visible?"inherit":"hidden"}),e.addClass(s.getClassName(),!0)):e&&(s.graphic=e.destroy())})},e.prototype.sortByAngle=function(t,e){t.sort(function(t,i){return void 0!==t.angle&&(i.angle-t.angle)*e})},e.prototype.translate=function(t){hF(this,"translate"),this.generatePoints();var e,i,o,r,n,s,a,l=this.options,h=l.slicedOffset,c=hR(l.startAngle,l.endAngle),d=this.startAngleRad=c.start,p=(this.endAngleRad=c.end)-d,u=this.points,f=l.ignoreHiddenPoint,g=u.length,v=0;for(t||(this.center=t=this.getCenter()),s=0;s<g;s++){a=u[s],e=d+v*p,a.isValid()&&(!f||a.visible)&&(v+=a.percentage/100),i=d+v*p;var m={x:t[0],y:t[1],r:t[2]/2,innerR:t[3]/2,start:Math.round(1e3*e)/1e3,end:Math.round(1e3*i)/1e3};a.shapeType="arc",a.shapeArgs=m,(o=(i+e)/2)>1.5*Math.PI?o-=2*Math.PI:o<-Math.PI/2&&(o+=2*Math.PI),a.slicedTranslation={translateX:Math.round(Math.cos(o)*h),translateY:Math.round(Math.sin(o)*h)},r=Math.cos(o)*t[2]/2,n=Math.sin(o)*t[2]/2,a.tooltipPos=[t[0]+.7*r,t[1]+.7*n],a.half=+(o<-Math.PI/2||o>Math.PI/2),a.angle=o}hF(this,"afterTranslate")},e.prototype.updateTotals=function(){var t,e,i=this.points,o=i.length,r=this.options.ignoreHiddenPoint,n=0;for(t=0;t<o;t++)(e=i[t]).isValid()&&(!r||e.visible)&&(n+=e.y);for(t=0,this.total=n;t<o;t++)(e=i[t]).percentage=n>0&&(e.visible||!r)?e.y/n*100:0,e.total=n},e.defaultOptions=hY(sF.defaultOptions,hj),e}(sF);h_(hH.prototype,{axisTypes:[],directTouch:!0,drawGraph:void 0,drawTracker:lQ.prototype.drawTracker,getCenter:hC.getCenter,getSymbol:hW,invertible:!1,isCartesian:!1,noSharedTooltip:!0,pointAttribs:lQ.prototype.pointAttribs,pointClass:hz,requireSorting:!1,searchPoint:hW,trackerGroups:["group","dataLabelsGroup"]}),sc.registerSeriesType("pie",hH);var hV=tb.composed,hU=tb.noop,hZ=ih.distribute,hq=sc.series,hK=tV.arrayMax,h$=tV.clamp,hJ=tV.defined,hQ=tV.pick,h0=tV.pushUnique,h1=tV.relativeLength;!function(t){var e={radialDistributionY:function(t,e){var i;return((null===(i=e.dataLabelPosition)||void 0===i?void 0:i.top)||0)+t.distributeBox.pos},radialDistributionX:function(t,e,i,o,r){var n=r.dataLabelPosition;return t.getX(i<((null==n?void 0:n.top)||0)+2||i>((null==n?void 0:n.bottom)||0)-2?o:i,e.half,e,r)},justify:function(t,e,i,o){var r;return o[0]+(t.half?-1:1)*(i+((null===(r=e.dataLabelPosition)||void 0===r?void 0:r.distance)||0))},alignToPlotEdges:function(t,e,i,o){var r=t.getBBox().width;return e?r+o:i-r-o},alignToConnectors:function(t,e,i,o){var r,n=0;return t.forEach(function(t){(r=t.dataLabel.getBBox().width)>n&&(n=r)}),e?n+o:i-n-o}};function i(t,e){var i=Math.PI/2,o=t.shapeArgs||{},r=o.start,n=void 0===r?0:r,s=o.end,a=void 0===s?0:s,l=t.angle||0;e>0&&n<i&&a>i&&l>i/2&&l<1.5*i&&(l=l<=i?Math.max(i/2,(n+i)/2):Math.min(1.5*i,(i+a)/2));var h=this.center,c=this.options,d=h[2]/2,p=Math.cos(l),u=Math.sin(l),f=h[0]+p*d,g=h[1]+u*d,v=Math.min((c.slicedOffset||0)+(c.borderWidth||0),e/5);return{natural:{x:f+p*e,y:g+u*e},computed:{},alignment:e<0?"center":t.half?"right":"left",connectorPosition:{angle:l,breakAt:{x:f+p*v,y:g+u*v},touchingSliceAt:{x:f,y:g}},distance:e}}function o(){var t,e,i,o,r=this,n=this,s=n.points,a=n.chart,l=a.plotWidth,h=a.plotHeight,c=a.plotLeft,d=Math.round(a.chartWidth/3),p=n.center,u=p[2]/2,f=p[1],g=[[],[]],v=[0,0,0,0],m=n.dataLabelPositioners,y=0;n.visible&&(null===(t=n.hasDataLabels)||void 0===t?void 0:t.call(n))&&(s.forEach(function(t){(t.dataLabels||[]).forEach(function(t){t.shortened&&(t.attr({width:"auto"}).css({width:"auto",textOverflow:"clip"}),t.shortened=!1)})}),hq.prototype.drawDataLabels.apply(n),s.forEach(function(t){(t.dataLabels||[]).forEach(function(e,i){var o,n=p[2]/2,s=e.options,a=h1((null==s?void 0:s.distance)||0,n);0===i&&g[t.half].push(t),!hJ(null===(o=null==s?void 0:s.style)||void 0===o?void 0:o.width)&&e.getBBox().width>d&&(e.css({width:Math.round(.7*d)+"px"}),e.shortened=!0),e.dataLabelPosition=r.getDataLabelPosition(t,a),y=Math.max(y,a)})}),g.forEach(function(t,e){var r,s,d,g=t.length,x=[],b=0;g&&(n.sortByAngle(t,e-.5),y>0&&(r=Math.max(0,f-u-y),s=Math.min(f+u+y,a.plotHeight),t.forEach(function(t){(t.dataLabels||[]).forEach(function(e){var i,o=e.dataLabelPosition;o&&o.distance>0&&(o.top=Math.max(0,f-u-o.distance),o.bottom=Math.min(f+u+o.distance,a.plotHeight),b=e.getBBox().height||21,e.lineHeight=a.renderer.fontMetrics(e.text||e).h+2*e.padding,t.distributeBox={target:((null===(i=e.dataLabelPosition)||void 0===i?void 0:i.natural.y)||0)-o.top+e.lineHeight/2,size:b,rank:t.y},x.push(t.distributeBox))})}),hZ(x,d=s+b-r,d/5)),t.forEach(function(r){(r.dataLabels||[]).forEach(function(s){var a=s.options||{},d=r.distributeBox,f=s.dataLabelPosition,g=(null==f?void 0:f.natural.y)||0,y=a.connectorPadding||0,b=s.lineHeight||21,w=(b-s.getBBox().height)/2,M=0,k=g,S="inherit";if(f){if(x&&hJ(d)&&f.distance>0&&(void 0===d.pos?S="hidden":(o=d.size,k=m.radialDistributionY(r,s))),a.justify)M=m.justify(r,s,u,p);else switch(a.alignTo){case"connectors":M=m.alignToConnectors(t,e,l,c);break;case"plotEdges":M=m.alignToPlotEdges(s,e,l,c);break;default:M=m.radialDistributionX(n,r,k-w,g,s)}if(f.attribs={visibility:S,align:f.alignment},f.posAttribs={x:M+(a.x||0)+(({left:y,right:-y})[f.alignment]||0),y:k+(a.y||0)-b/2},f.computed.x=M,f.computed.y=k-w,hQ(a.crop,!0)){i=s.getBBox().width;var T=void 0;M-i<y&&1===e?(T=Math.round(i-M+y),v[3]=Math.max(T,v[3])):M+i>l-y&&0===e&&(T=Math.round(M+i-l+y),v[1]=Math.max(T,v[1])),k-o/2<0?v[0]=Math.max(Math.round(-k+o/2),v[0]):k+o/2>h&&(v[2]=Math.max(Math.round(k+o/2-h),v[2])),f.sideOverflow=T}}})}))}),(0===hK(v)||this.verifyDataLabelOverflow(v))&&(this.placeDataLabels(),this.points.forEach(function(t){(t.dataLabels||[]).forEach(function(i){var o,r=i.options||{},s=r.connectorColor,l=r.connectorWidth,h=void 0===l?1:l,c=i.dataLabelPosition;if(h){var d=void 0;e=i.connector,c&&c.distance>0?(d=!e,e||(i.connector=e=a.renderer.path().addClass("highcharts-data-label-connector  highcharts-color-"+t.colorIndex+(t.className?" "+t.className:"")).add(n.dataLabelsGroup)),a.styledMode||e.attr({"stroke-width":h,stroke:s||t.color||"#666666"}),e[d?"attr":"animate"]({d:t.getConnectorPath(i)}),e.attr({visibility:null===(o=c.attribs)||void 0===o?void 0:o.visibility})):e&&(i.connector=e.destroy())}})})))}function r(){this.points.forEach(function(t){(t.dataLabels||[]).forEach(function(t){var e,i,o=t.dataLabelPosition;o?(o.sideOverflow&&(t.css({width:Math.max(t.getBBox().width-o.sideOverflow,0)+"px",textOverflow:(null===(i=null===(e=t.options)||void 0===e?void 0:e.style)||void 0===i?void 0:i.textOverflow)||"ellipsis"}),t.shortened=!0),t.attr(o.attribs),t[t.moved?"animate":"attr"](o.posAttribs),t.moved=!0):t&&t.attr({y:-9999})}),delete t.distributeBox},this)}function n(t){var e=this.center,i=this.options,o=i.center,r=i.minSize||80,n=r,s=null!==i.size;return!s&&(null!==o[0]?n=Math.max(e[2]-Math.max(t[1],t[3]),r):(n=Math.max(e[2]-t[1]-t[3],r),e[0]+=(t[3]-t[1])/2),null!==o[1]?n=h$(n,r,e[2]-Math.max(t[0],t[2])):(n=h$(n,r,e[2]-t[0]-t[2]),e[1]+=(t[0]-t[2])/2),n<e[2]?(e[2]=n,e[3]=Math.min(i.thickness?Math.max(0,n-2*i.thickness):Math.max(0,h1(i.innerSize||0,n)),n),this.translate(e),this.drawDataLabels&&this.drawDataLabels()):s=!0),s}t.compose=function(t){if(hi.compose(hq),h0(hV,"PieDataLabel")){var s=t.prototype;s.dataLabelPositioners=e,s.alignDataLabel=hU,s.drawDataLabels=o,s.getDataLabelPosition=i,s.placeDataLabels=r,s.verifyDataLabelOverflow=n}}}(th||(th={}));var h2=th;(O=tc||(tc={})).getCenterOfPoints=function(t){var e=t.reduce(function(t,e){return t.x+=e.x,t.y+=e.y,t},{x:0,y:0});return{x:e.x/t.length,y:e.y/t.length}},O.getDistanceBetweenPoints=function(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))},O.getAngleBetweenPoints=function(t,e){return Math.atan2(e.x-t.x,e.y-t.y)},O.pointInPolygon=function(t,e){var i,o,r=t.x,n=t.y,s=e.length,a=!1;for(i=0,o=s-1;i<s;o=i++){var l=e[i],h=l[0],c=l[1],d=e[o],p=d[0],u=d[1];c>n!=u>n&&r<(p-h)*(n-c)/(u-c)+h&&(a=!a)}return a};var h3=tc,h6=h3.pointInPolygon,h5=tV.addEvent,h9=tV.getAlignFactor,h8=tV.fireEvent,h4=tV.objectEach,h7=tV.pick;function ct(t){for(var e,i,o,r,n,s=t.length,a=!1,l=0;l<s;l++)(e=t[l])&&(e.oldOpacity=e.opacity,e.newOpacity=1,e.absoluteBox=function(t){var e,i;if(t&&(!t.alignAttr||t.placed)){var o=t.box?0:t.padding||0,r=t.alignAttr||{x:t.attr("x"),y:t.attr("y")},n=t.getBBox(),s=n.height,a=n.polygon,l=n.width,h=h9(t.alignValue)*l;return t.width=l,t.height=s,{x:r.x+((null===(e=t.parentGroup)||void 0===e?void 0:e.translateX)||0)+o-h,y:r.y+((null===(i=t.parentGroup)||void 0===i?void 0:i.translateY)||0)+o,width:l-2*o,height:s-2*o,polygon:a}}}(e));t.sort(function(t,e){return(e.labelrank||0)-(t.labelrank||0)});for(var l=0;l<s;++l)for(var h=null==(r=(i=t[l])&&i.absoluteBox)?void 0:r.polygon,c=l+1;c<s;++c){n=(o=t[c])&&o.absoluteBox;var d=!1;if(r&&n&&i!==o&&0!==i.newOpacity&&0!==o.newOpacity&&"hidden"!==i.visibility&&"hidden"!==o.visibility){var p=n.polygon;if(h&&p&&h!==p?function(t,e){for(var i=0;i<t.length;i++){var o=t[i];if(h6({x:o[0],y:o[1]},e))return!0}return!1}(h,p)&&(d=!0):!(n.x>=r.x+r.width||n.x+n.width<=r.x||n.y>=r.y+r.height||n.y+n.height<=r.y)&&(d=!0),d){var u=i.labelrank<o.labelrank?i:o,f=u.text;u.newOpacity=0,(null==f?void 0:f.element.querySelector("textPath"))&&f.hide()}}}for(var g=0;g<t.length;g++)ce(t[g],this)&&(a=!0);a&&h8(this,"afterHideAllOverlappingLabels")}function ce(t,e){var i,o=!1;return t&&(i=t.newOpacity,t.oldOpacity!==i&&(t.hasClass("highcharts-data-label")?(t[i?"removeClass":"addClass"]("highcharts-data-label-hidden"),o=!0,t[t.isOld?"animate":"attr"]({opacity:i},void 0,function(){e.styledMode||t.css({pointerEvents:i?"auto":"none"})}),h8(e,"afterHideOverlappingLabel")):t.attr({opacity:i})),t.isOld=!0),o}function ci(){for(var t,e=this,i=[],o=0,r=e.labelCollectors||[];o<r.length;o++){var n=r[o];i=i.concat(n())}for(var s=0,a=e.yAxis||[];s<a.length;s++){var l=a[s];l.stacking&&l.options.stackLabels&&!l.options.stackLabels.allowOverlap&&h4(l.stacking.stacks,function(t){h4(t,function(t){t.label&&i.push(t.label)})})}for(var h=0,c=e.series||[];h<c.length;h++){var d=c[h];if(d.visible&&(null===(t=d.hasDataLabels)||void 0===t?void 0:t.call(d))){var p=function(t){for(var o=function(t){t.visible&&(t.dataLabels||[]).forEach(function(o){var r,n,s=o.options||{};o.labelrank=h7(s.labelrank,t.labelrank,null===(r=t.shapeArgs)||void 0===r?void 0:r.height),(null!==(n=s.allowOverlap)&&void 0!==n?n:Number(s.distance)>0)?(o.oldOpacity=o.opacity,o.newOpacity=1,ce(o,e)):i.push(o)})},r=0;r<t.length;r++)o(t[r])};p(d.nodes||[]),p(d.points)}}this.hideOverlappingLabels(i)}var co={compose:function(t){var e=t.prototype;e.hideOverlappingLabels||(e.hideOverlappingLabels=ct,h5(t,"render",ci))}},cr=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},cn=tb.noop,cs=tV.addEvent,ca=tV.extend,cl=tV.isObject,ch=tV.merge,cc=tV.relativeLength,cd={radius:0,scope:"stack",where:void 0},cp=cn,cu=cn;function cf(t,e,i,o,r){void 0===r&&(r={});var n=cp(t,e,i,o,r),s=r.innerR,a=void 0===s?0:s,l=r.r,h=void 0===l?i:l,c=r.start,d=r.end;if(r.open||!r.borderRadius)return n;for(var p=(void 0===d?0:d)-(void 0===c?0:c),u=Math.sin(p/2),f=Math.max(Math.min(cc(r.borderRadius||0,h-a),(h-a)/2,h*u/(1+u)),0),g=Math.min(f,p/Math.PI*2*a),v=n.length-1;v--;)!function(t,e,i){var o,r,n,s=t[e],a=t[e+1];if("Z"===a[0]&&(a=t[0]),("M"===s[0]||"L"===s[0])&&"A"===a[0]?(o=s,r=a,n=!0):"A"===s[0]&&("M"===a[0]||"L"===a[0])&&(o=a,r=s),o&&r&&r.params){var l=r[1],h=r[5],c=r.params,d=c.start,p=c.end,u=c.cx,f=c.cy,g=h?l-i:l+i,v=g?Math.asin(i/g):0,m=h?v:-v,y=Math.cos(v)*g;n?(c.start=d+m,o[1]=u+y*Math.cos(d),o[2]=f+y*Math.sin(d),t.splice(e+1,0,["A",i,i,0,0,1,u+l*Math.cos(c.start),f+l*Math.sin(c.start)])):(c.end=p-m,r[6]=u+l*Math.cos(c.end),r[7]=f+l*Math.sin(c.end),t.splice(e+1,0,["A",i,i,0,0,1,u+y*Math.cos(p),f+y*Math.sin(p)])),r[4]=Math.abs(c.end-c.start)<Math.PI?0:1}}(n,v,v>1?g:f);return n}function cg(){var t,e;if(this.options.borderRadius&&!(this.chart.is3d&&this.chart.is3d()))for(var i=this.options,o=this.yAxis,r="percent"===i.stacking,n=null===(e=null===(t=el.plotOptions)||void 0===t?void 0:t[this.type])||void 0===e?void 0:e.borderRadius,s=cv(i.borderRadius,cl(n)?n:{}),a=o.options.reversed,l=0,h=this.points;l<h.length;l++){var c=h[l],d=c.shapeArgs;if("roundedRect"===c.shapeType&&d){var p=d.width,u=void 0===p?0:p,f=d.height,g=void 0===f?0:f,v=d.y,m=void 0===v?0:v,y=g;if("stack"===s.scope&&c.stackTotal){var x=o.translate(r?100:c.stackTotal,!1,!0,!1,!0),b=o.translate(i.threshold||0,!1,!0,!1,!0),w=this.crispCol(0,Math.min(x,b),0,Math.abs(x-b));m=w.y,y=w.height}var M=(c.negative?-1:1)*(a?-1:1)==-1,k=s.where;!k&&this.is("waterfall")&&Math.abs((c.yBottom||0)-(this.translatedThreshold||0))>this.borderWidth&&(k="all"),k||(k="end");var S=Math.min(cc(s.radius,u),u/2,"all"===k?g/2:1/0)||0;"end"===k&&(M&&(m-=S),y+=S),ca(d,{brBoxHeight:y,brBoxY:m,r:S})}}}function cv(t,e){return cl(t)||(t={radius:t||0}),ch(cd,e,t)}function cm(){for(var t=cv(this.options.borderRadius),e=0,i=this.points;e<i.length;e++){var o=i[e].shapeArgs;o&&(o.borderRadius=cc(t.radius,(o.r||0)-(o.innerR||0)))}}function cy(t,e,i,o,r){void 0===r&&(r={});var n=cu(t,e,i,o,r),s=r.r,a=void 0===s?0:s,l=r.brBoxHeight,h=void 0===l?o:l,c=r.brBoxY,d=void 0===c?e:c,p=e-d,u=d+h-(e+o),f=p-a>-.1?0:a,g=u-a>-.1?0:a,v=Math.max(f&&p,0),m=Math.max(g&&u,0),y=[t+f,e],x=[t+i-f,e],b=[t+i,e+f],w=[t+i,e+o-g],M=[t+i-g,e+o],k=[t+g,e+o],S=[t,e+o-g],T=[t,e+f],C=function(t,e){return Math.sqrt(Math.pow(t,2)-Math.pow(e,2))};if(v){var A=C(f,f-v);y[0]-=A,x[0]+=A,b[1]=T[1]=e+f-v}if(o<f-v){var A=C(f,f-v-o);b[0]=w[0]=t+i-f+A,M[0]=Math.min(b[0],M[0]),k[0]=Math.max(w[0],k[0]),S[0]=T[0]=t+f-A,b[1]=T[1]=e+o}if(m){var A=C(g,g-m);M[0]+=A,k[0]-=A,w[1]=S[1]=e+o-g+m}if(o<g-m){var A=C(g,g-m-o);b[0]=w[0]=t+i-g+A,x[0]=Math.min(b[0],x[0]),y[0]=Math.max(w[0],y[0]),S[0]=T[0]=t+g-A,w[1]=S[1]=e}return n.length=0,n.push(cr(["M"],y,!0),cr(["L"],x,!0),cr(["A",f,f,0,0,1],b,!0),cr(["L"],w,!0),cr(["A",g,g,0,0,1],M,!0),cr(["L"],k,!0),cr(["A",g,g,0,0,1],S,!0),cr(["L"],T,!0),cr(["A",f,f,0,0,1],y,!0),["Z"]),n}var cx=tV.diffObjects,cb=tV.extend,cw=tV.find,cM=tV.merge,ck=tV.pick,cS=tV.uniqueKey;!function(t){function e(t,e){var i=t.condition;(i.callback||function(){return this.chartWidth<=ck(i.maxWidth,Number.MAX_VALUE)&&this.chartHeight<=ck(i.maxHeight,Number.MAX_VALUE)&&this.chartWidth>=ck(i.minWidth,0)&&this.chartHeight>=ck(i.minHeight,0)}).call(this)&&e.push(t._id)}function i(t,e){var i,o=this,r=this.options.responsive,n=this.currentResponsive,s=[];!e&&r&&r.rules&&r.rules.forEach(function(t){void 0===t._id&&(t._id=cS()),o.matchResponsiveRule(t,s)},this);var a=cM.apply(void 0,s.map(function(t){return cw((null==r?void 0:r.rules)||[],function(e){return e._id===t})}).map(function(t){return null==t?void 0:t.chartOptions}));a.isResponsiveOptions=!0,s=s.toString()||void 0;var l=null==n?void 0:n.ruleIds;s===l||(n&&(this.currentResponsive=void 0,this.updatingResponsive=!0,this.update(n.undoOptions,t,!0),this.updatingResponsive=!1),s?((i=cx(a,this.options,!0,this.collectionsWithUpdate)).isResponsiveOptions=!0,this.currentResponsive={ruleIds:s,mergedOptions:a,undoOptions:i},this.updatingResponsive||this.update(a,t,!0)):this.currentResponsive=void 0)}t.compose=function(t){var o=t.prototype;return o.matchResponsiveRule||cb(o,{matchResponsiveRule:e,setResponsive:i}),t}}(td||(td={}));var cT=td;tb.AST=eJ,tb.Axis=rD,tb.Chart=aN,tb.Color=eb,tb.DataLabel=hi,tb.DataTableCore=n7,tb.Fx=eT,tb.HTMLElement=oZ,tb.Legend=s7,tb.LegendSymbol=sr,tb.OverlappingDataLabels=tb.OverlappingDataLabels||co,tb.PlotLineOrBand=r6,tb.Point=nz,tb.Pointer=n1,tb.RendererRegistry=io,tb.Series=sF,tb.SeriesRegistry=sc,tb.StackItem=a2,tb.SVGElement=iX,tb.SVGRenderer=oI,tb.Templating=ii,tb.Tick=rr,tb.Time=eo,tb.Tooltip=ny,tb.animate=ej,tb.animObject=eB,tb.chart=aN.chart,tb.color=eb.parse,tb.dateFormat=ii.dateFormat,tb.defaultOptions=el,tb.distribute=ih.distribute,tb.format=ii.format,tb.getDeferredAnimation=eN,tb.getOptions=ec,tb.numberFormat=ii.numberFormat,tb.seriesType=sc.seriesType,tb.setAnimation=eR,tb.setOptions=ed,tb.stop=ez,tb.time=eh,tb.timers=eT.timers,({compose:function(t,e,i){var o=t.types.pie;if(!e.symbolCustomAttribs.includes("borderRadius")){var r=i.prototype.symbols;cs(t,"afterColumnTranslate",cg,{order:9}),cs(o,"afterTranslate",cm),e.symbolCustomAttribs.push("borderRadius","brBoxHeight","brBoxY"),cp=r.arc,cu=r.roundedRect,r.arc=cf,r.roundedRect=cy}},optionsToObject:cv}).compose(tb.Series,tb.SVGElement,tb.SVGRenderer),ha.compose(tb.Series.types.column),hi.compose(tb.Series),rR.compose(tb.Axis),oZ.compose(tb.SVGRenderer),s7.compose(tb.Chart),rF.compose(tb.Axis),co.compose(tb.Chart),h2.compose(tb.Series.types.pie),r6.compose(tb.Chart,tb.Axis),n1.compose(tb.Chart),cT.compose(tb.Chart),aZ.compose(tb.Axis,tb.Chart,tb.Series),ld.compose(tb.Axis,tb.Chart,tb.Series),ny.compose(tb.Pointer),tV.extend(tb,tV);var cC=eb.parse,cA=tV.addEvent,cP=tV.extend,cO=tV.merge,cL=tV.pick,cE=tV.splat;!function(t){var e;function i(){var t=this,i=this.userOptions;this.colorAxis=[],i.colorAxis&&(i.colorAxis=cE(i.colorAxis),i.colorAxis.map(function(i){return new e(t,i)}))}function o(t){var e,i,o=this,r=this.chart.colorAxis||[],n=function(e){var i=t.allItems.indexOf(e);-1!==i&&(o.destroyItem(t.allItems[i]),t.allItems.splice(i,1))},s=[];for(r.forEach(function(t){(null==(e=t.options)?void 0:e.showInLegend)&&(e.dataClasses&&e.visible?s=s.concat(t.getDataClassLegendSymbols()):e.visible&&s.push(t),t.series.forEach(function(t){(!t.options.showInLegend||e.dataClasses)&&("point"===t.options.legendType?t.points.forEach(function(t){n(t)}):n(t))}))}),i=s.length;i--;)t.allItems.unshift(s[i])}function r(t){t.visible&&t.item.legendColor&&t.item.legendItem.symbol.attr({fill:t.item.legendColor})}function n(t){var e;null===(e=this.chart.colorAxis)||void 0===e||e.forEach(function(e){e.update({},t.redraw)})}function s(){var t;((null===(t=this.chart.colorAxis)||void 0===t?void 0:t.length)||this.colorAttribs)&&this.translateColors()}function a(){var t=this.axisTypes;t?-1===t.indexOf("colorAxis")&&t.push("colorAxis"):this.axisTypes=["colorAxis"]}function l(t){var e=this,i=t?"show":"hide";e.visible=e.options.visible=!!t,["graphic","dataLabel"].forEach(function(t){e[t]&&e[t][i]()}),this.series.buildKDTree()}function h(){var t=this,e=this.getPointsCollection(),i=this.options.nullColor,o=this.colorAxis,r=this.colorKey;e.forEach(function(e){var n=e.getNestedProperty(r),s=e.options.color||(e.isNull||null===e.value?i:o&&void 0!==n?o.toColor(n,e):e.color||t.color);s&&e.color!==s&&(e.color=s,"point"===t.options.legendType&&e.legendItem&&e.legendItem.label&&t.chart.legend.colorizeItem(e,e.visible))})}function c(){this.elem.attr("fill",cC(this.start).tweenTo(cC(this.end),this.pos),void 0,!0)}function d(){this.elem.attr("stroke",cC(this.start).tweenTo(cC(this.end),this.pos),void 0,!0)}t.compose=function(t,p,u,f,g){var v,m,y=p.prototype,x=u.prototype,b=g.prototype;y.collectionsWithUpdate.includes("colorAxis")||(e=t,y.collectionsWithUpdate.push("colorAxis"),y.collectionsWithInit.colorAxis=[y.addColorAxis],cA(p,"afterCreateAxes",i),m=(v=p).prototype.createAxis,v.prototype.createAxis=function(t,i){if("colorAxis"!==t)return m.apply(this,arguments);var o=new e(this,cO(i.axis,{index:this[t].length,isX:!1}));return this.isDirtyLegend=!0,this.axes.forEach(function(t){t.series=[]}),this.series.forEach(function(t){t.bindAxes(),t.isDirtyData=!0}),cL(i.redraw,!0)&&this.redraw(i.animation),o},x.fillSetter=c,x.strokeSetter=d,cA(f,"afterGetAllItems",o),cA(f,"afterColorizeItem",r),cA(f,"afterUpdate",n),cP(b,{optionalAxis:"colorAxis",translateColors:h}),cP(b.pointClass.prototype,{setVisible:l}),cA(g,"afterTranslate",s,{order:1}),cA(g,"bindAxes",a))},t.pointSetVisible=l}(tp||(tp={}));var cI=tp,cD=eb.parse,cB=tV.merge;(L=tu||(tu={})).initDataClasses=function(t){var e,i,o,r=this.chart,n=this.legendItem=this.legendItem||{},s=this.options,a=t.dataClasses||[],l=r.options.chart.colorCount,h=0;this.dataClasses=i=[],n.labels=[];for(var c=0,d=a.length;c<d;++c)e=cB(e=a[c]),i.push(e),(r.styledMode||!e.color)&&("category"===s.dataClassColor?(r.styledMode||(l=(o=r.options.colors||[]).length,e.color=o[h]),e.colorIndex=h,++h===l&&(h=0)):e.color=cD(s.minColor).tweenTo(cD(s.maxColor),d<2?.5:c/(d-1)))},L.initStops=function(){for(var t=this.options,e=this.stops=t.stops||[[0,t.minColor||""],[1,t.maxColor||""]],i=0,o=e.length;i<o;++i)e[i].color=cD(e[i][1])},L.normalizedValue=function(t){var e=this.max||0,i=this.min||0;return this.logarithmic&&(t=this.logarithmic.log2lin(t)),1-(e-t)/(e-i||1)},L.toColor=function(t,e){var i,o,r,n,s,a,l=this.dataClasses,h=this.stops;if(l){for(a=l.length;a--;)if(o=(s=l[a]).from,r=s.to,(void 0===o||t>=o)&&(void 0===r||t<=r)){n=s.color,e&&(e.dataClass=a,e.colorIndex=s.colorIndex);break}}else{for(i=this.normalizedValue(t),a=h.length;a--&&!(i>h[a][0]););o=h[a]||h[a+1],i=1-((r=h[a+1]||o)[0]-i)/(r[0]-o[0]||1),n=o.color.tweenTo(r.color,i)}return n};var cz=tu,cj=(E=function(t,e){return(E=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}E(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),cN=sc.series,cR=tV.defined,cW=tV.extend,cX=tV.fireEvent,c_=tV.isArray,cF=tV.isNumber,cY=tV.merge,cG=tV.pick,cH=tV.relativeLength;el.colorAxis=cY(el.xAxis,{lineWidth:0,minPadding:0,maxPadding:0,gridLineColor:"#ffffff",gridLineWidth:1,tickPixelInterval:72,startOnTick:!0,endOnTick:!0,offset:0,marker:{animation:{duration:50},width:.01,color:"#999999"},labels:{distance:8,overflow:"justify",rotation:0},minColor:"#e6e9ff",maxColor:"#0022ff",tickLength:5,showInLegend:!0});var cV=function(t){function e(e,i){var o=t.call(this,e,i)||this;return o.coll="colorAxis",o.visible=!0,o.init(e,i),o}return cj(e,t),e.compose=function(t,i,o,r){cI.compose(e,t,i,o,r)},e.prototype.init=function(e,i){var o=e.options.legend||{},r=i.layout?"vertical"!==i.layout:"vertical"!==o.layout;this.side=i.side||r?2:1,this.reversed=i.reversed||!r,this.opposite=!r,t.prototype.init.call(this,e,i,"colorAxis"),this.userOptions=i,c_(e.userOptions.colorAxis)&&(e.userOptions.colorAxis[this.index]=i),i.dataClasses&&this.initDataClasses(i),this.initStops(),this.horiz=r,this.zoomEnabled=!1},e.prototype.hasData=function(){return!!(this.tickPositions||[]).length},e.prototype.setTickPositions=function(){if(!this.dataClasses)return t.prototype.setTickPositions.call(this)},e.prototype.setOptions=function(e){var i=cY(el.colorAxis,e,{showEmpty:!1,title:null,visible:this.chart.options.legend.enabled&&!1!==e.visible});t.prototype.setOptions.call(this,i),this.options.crosshair=this.options.marker},e.prototype.setAxisSize=function(){var t,i=this.chart,o=null===(t=this.legendItem)||void 0===t?void 0:t.symbol,r=this.getSize(),n=r.width,s=r.height;o&&(this.left=+o.attr("x"),this.top=+o.attr("y"),this.width=n=+o.attr("width"),this.height=s=+o.attr("height"),this.right=i.chartWidth-this.left-n,this.bottom=i.chartHeight-this.top-s,this.pos=this.horiz?this.left:this.top),this.len=(this.horiz?n:s)||e.defaultLegendLength},e.prototype.getOffset=function(){var i,o=null===(i=this.legendItem)||void 0===i?void 0:i.group,r=this.chart.axisOffset[this.side];if(o){this.axisParent=o,t.prototype.getOffset.call(this);var n=this.chart.legend;n.allItems.forEach(function(t){t instanceof e&&t.drawLegendSymbol(n,t)}),n.render(),this.chart.getMargins(!0),this.chart.series.some(function(t){return t.isDrilling})||(this.isDirty=!0),this.added||(this.added=!0,this.labelLeft=0,this.labelRight=this.width),this.chart.axisOffset[this.side]=r}},e.prototype.setLegendColor=function(){var t=this.horiz,e=this.reversed,i=+!!e,o=+!e,r=t?[i,0,o,0]:[0,o,0,i];this.legendColor={linearGradient:{x1:r[0],y1:r[1],x2:r[2],y2:r[3]},stops:this.stops}},e.prototype.drawLegendSymbol=function(t,e){var i,o=e.legendItem||{},r=t.padding,n=t.options,s=this.options.labels,a=cG(n.itemDistance,10),l=this.horiz,h=this.getSize(),c=h.width,d=h.height,p=cG(n.labelPadding,l?16:30);this.setLegendColor(),o.symbol||(o.symbol=this.chart.renderer.symbol("roundedRect").attr({r:null!==(i=n.symbolRadius)&&void 0!==i?i:3,zIndex:1}).add(o.group)),o.symbol.attr({x:0,y:(t.baseline||0)-11,width:c,height:d}),o.labelWidth=c+r+(l?a:cG(s.x,s.distance)+(this.maxLabelLength||0)),o.labelHeight=d+r+(l?p:0)},e.prototype.setState=function(t){this.series.forEach(function(e){e.setState(t)})},e.prototype.setVisible=function(){},e.prototype.getSeriesExtremes=function(){var t,e,i,o,r=this.series,n=r.length;for(this.dataMin=1/0,this.dataMax=-1/0;n--;){e=(o=r[n]).colorKey=cG(o.options.colorKey,o.colorKey,o.pointValKey,o.zoneAxis,"y"),i=o[e+"Min"]&&o[e+"Max"];for(var s=0,a=[e,"value","y"];s<a.length;s++){var l=a[s];if((t=o.getColumn(l)).length)break}if(i)o.minColorValue=o[e+"Min"],o.maxColorValue=o[e+"Max"];else{var h=cN.prototype.getExtremes.call(o,t);o.minColorValue=h.dataMin,o.maxColorValue=h.dataMax}cR(o.minColorValue)&&cR(o.maxColorValue)&&(this.dataMin=Math.min(this.dataMin,o.minColorValue),this.dataMax=Math.max(this.dataMax,o.maxColorValue)),i||cN.prototype.applyExtremes.call(o)}},e.prototype.drawCrosshair=function(e,i){var o,r=this.legendItem||{},n=null==i?void 0:i.plotX,s=null==i?void 0:i.plotY,a=this.pos,l=this.len;i&&((o=this.toPixels(i.getNestedProperty(i.series.colorKey)))<a?o=a-2:o>a+l&&(o=a+l+2),i.plotX=o,i.plotY=this.len-o,t.prototype.drawCrosshair.call(this,e,i),i.plotX=n,i.plotY=s,this.cross&&!this.cross.addedToColorAxis&&r.group&&(this.cross.addClass("highcharts-coloraxis-marker").add(r.group),this.cross.addedToColorAxis=!0,this.chart.styledMode||"object"!=typeof this.crosshair||this.cross.attr({fill:this.crosshair.color})))},e.prototype.getPlotLinePath=function(e){var i=this.left,o=e.translatedValue,r=this.top;return cF(o)?this.horiz?[["M",o-4,r-6],["L",o+4,r-6],["L",o,r],["Z"]]:[["M",i,o],["L",i-6,o+6],["L",i-6,o-6],["Z"]]:t.prototype.getPlotLinePath.call(this,e)},e.prototype.update=function(e,i){var o,r=this.chart.legend;this.series.forEach(function(t){t.isDirtyData=!0}),(e.dataClasses&&r.allItems||this.dataClasses)&&this.destroyItems(),t.prototype.update.call(this,e,i),(null===(o=this.legendItem)||void 0===o?void 0:o.label)&&(this.setLegendColor(),r.colorizeItem(this,!0))},e.prototype.destroyItems=function(){var t=this.chart,e=this.legendItem||{};if(e.label)t.legend.destroyItem(this);else if(e.labels)for(var i=0,o=e.labels;i<o.length;i++){var r=o[i];t.legend.destroyItem(r)}t.isDirtyLegend=!0},e.prototype.destroy=function(){this.chart.isDirtyLegend=!0,this.destroyItems(),t.prototype.destroy.apply(this,[].slice.call(arguments))},e.prototype.remove=function(e){this.destroyItems(),t.prototype.remove.call(this,e)},e.prototype.getDataClassLegendSymbols=function(){var t,e=this,i=e.chart,o=e.legendItem&&e.legendItem.labels||[],r=i.options.legend,n=cG(r.valueDecimals,-1),s=cG(r.valueSuffix,""),a=function(t){return e.series.reduce(function(e,i){return e.push.apply(e,i.points.filter(function(e){return e.dataClass===t})),e},[])};return o.length||e.dataClasses.forEach(function(r,l){var h=r.from,c=r.to,d=i.numberFormatter,p=!0;t="",void 0===h?t="< ":void 0===c&&(t="> "),void 0!==h&&(t+=d(h,n)+s),void 0!==h&&void 0!==c&&(t+=" - "),void 0!==c&&(t+=d(c,n)+s),o.push(cW({chart:i,name:t,options:{},drawLegendSymbol:sr.rectangle,visible:!0,isDataClass:!0,setState:function(t){for(var e=0,i=a(l);e<i.length;e++)i[e].setState(t)},setVisible:function(){this.visible=p=e.visible=!p;for(var t=[],o=0,r=a(l);o<r.length;o++){var n=r[o];n.setVisible(p),n.hiddenInDataClass=!p,-1===t.indexOf(n.series)&&t.push(n.series)}i.legend.colorizeItem(this,p),t.forEach(function(t){cX(t,"afterDataClassLegendClick")})}},r))}),o},e.prototype.getSize=function(){var t=this.chart,i=this.horiz,o=this.options,r=o.height,n=o.width,s=t.options.legend;return{width:cG(cR(n)?cH(n,t.chartWidth):void 0,null==s?void 0:s.symbolWidth,i?e.defaultLegendLength:12),height:cG(cR(r)?cH(r,t.chartHeight):void 0,null==s?void 0:s.symbolHeight,i?12:e.defaultLegendLength)}},e.defaultLegendLength=200,e.keepProps=["legendItem"],e}(rD);cW(cV.prototype,cz),Array.prototype.push.apply(rD.keepProps,cV.keepProps),tb.ColorAxis=tb.ColorAxis||cV,tb.ColorAxis.compose(tb.Chart,tb.Fx,tb.Legend,tb.Series);var cU={lang:{zoomIn:"Zoom in",zoomOut:"Zoom out"},mapNavigation:{buttonOptions:{alignTo:"plotBox",align:"left",verticalAlign:"top",x:0,width:18,height:18,padding:5,style:{color:"#666666",fontSize:"1em",fontWeight:"bold"},theme:{fill:"#ffffff",stroke:"#e6e6e6","stroke-width":1,"text-align":"center"}},buttons:{zoomIn:{onclick:function(){this.mapZoom(.5)},text:"+",y:0},zoomOut:{onclick:function(){this.mapZoom(2)},text:"-",y:28}},mouseWheelSensitivity:1.1}},cZ=tV.defined,cq=tV.extend,cK=tV.pick,c$=tV.wrap;!function(t){var e,i=0;function o(t){var e=this.chart;t=this.normalize(t),e.options.mapNavigation.enableDoubleClickZoomTo?e.pointer.inClass(t.target,"highcharts-tracker")&&e.hoverPoint&&e.hoverPoint.zoomTo():e.isInsidePlot(t.chartX-e.plotLeft,t.chartY-e.plotTop)&&e.mapZoom(.5,void 0,void 0,t.chartX,t.chartY)}function r(t){var o=this.chart,r=cZ((t=this.normalize(t)).wheelDelta)&&-t.wheelDelta/120||t.deltaY||t.detail;Math.abs(r)>=1&&(i+=Math.abs(r),e&&clearTimeout(e),e=setTimeout(function(){i=0},50)),i<10&&o.isInsidePlot(t.chartX-o.plotLeft,t.chartY-o.plotTop)&&o.mapView&&o.mapView.zoomBy(-((o.options.mapNavigation.mouseWheelSensitivity-1)*r),void 0,[t.chartX,t.chartY],!(1>Math.abs(r))&&void 0)}function n(t,e,i){var o=this.chart;if(e=t.call(this,e,i),o&&o.mapView){var r=o.mapView.pixelsToLonLat({x:e.chartX-o.plotLeft,y:e.chartY-o.plotTop});r&&cq(e,r)}return e}function s(t){var e=this.chart.options.mapNavigation;e&&cK(e.enableTouchZoom,e.enabled)&&(this.chart.zooming.pinchType="xy"),t.apply(this,[].slice.call(arguments,1))}t.compose=function(t){var e=t.prototype;e.onContainerDblClick||(cq(e,{onContainerDblClick:o,onContainerMouseWheel:r}),c$(e,"normalize",n),c$(e,"zoomOption",s))}}(tf||(tf={}));var cJ=tf;function cQ(t,e,i,o,r){if(r){var n=(null==r?void 0:r.r)||0;r.brBoxY=e-n,r.brBoxHeight=o+n}return tg.roundedRect(t,e,i,o,r)}function c0(t,e,i,o,r){if(r){var n=(null==r?void 0:r.r)||0;r.brBoxHeight=o+n}return tg.roundedRect(t,e,i,o,r)}var c1=function(t){(tg=t.prototype.symbols).bottombutton=cQ,tg.topbutton=c0},c2=tb.composed,c3=tV.addEvent,c6=tV.extend,c5=tV.merge,c9=tV.objectEach,c8=tV.pick,c4=tV.pushUnique;function c7(t){var e,i;t&&(null===(e=t.preventDefault)||void 0===e||e.call(t),null===(i=t.stopPropagation)||void 0===i||i.call(t),t.cancelBubble=!0)}var dt=function(){function t(t){this.chart=t,this.navButtons=[]}return t.compose=function(e,i,o){cJ.compose(i),c1(o),c4(c2,"Map.Navigation")&&(c3(e,"beforeRender",function(){this.mapNavigation=new t(this),this.mapNavigation.update()}),ed(cU))},t.prototype.update=function(t){var e,i=this,o=i.chart,r=i.navButtons,n=function(t){this.handler.call(o,t),c7(t)},s=o.options.mapNavigation;for(t&&(s=o.options.mapNavigation=c5(o.options.mapNavigation,t));r.length;)null===(e=r.pop())||void 0===e||e.destroy();!o.renderer.forExport&&c8(s.enableButtons,s.enabled)&&(i.navButtonsGroup||(i.navButtonsGroup=o.renderer.g().attr({zIndex:7}).add()),c9(s.buttons,function(t,e){var a,l={padding:(t=c5(s.buttonOptions,t)).padding};!o.styledMode&&t.theme&&(c6(l,t.theme),l.style=c5(t.theme.style,t.style));var h=t.text,c=t.width,d=void 0===c?0:c,p=t.height,u=void 0===p?0:p,f=t.padding,g=void 0===f?0:f,v=o.renderer.button("+"!==h&&"-"!==h&&h||"",0,0,n,l,void 0,void 0,void 0,"zoomIn"===e?"topbutton":"bottombutton").addClass("highcharts-map-navigation highcharts-"+({zoomIn:"zoom-in",zoomOut:"zoom-out"})[e]).attr({width:d,height:u,title:o.options.lang[e],zIndex:5}).add(i.navButtonsGroup);if("+"===h||"-"===h){var m=d+1,y=[["M",g+3,g+u/2],["L",g+m-3,g+u/2]];"+"===h&&y.push(["M",g+m/2,g+3],["L",g+m/2,g+u-3]),o.renderer.path(y).addClass("highcharts-button-symbol").attr(o.styledMode?{}:{stroke:null===(a=t.style)||void 0===a?void 0:a.color,"stroke-width":3,"stroke-linecap":"round"}).add(v)}if(v.handler=t.onclick,c3(v.element,"dblclick",c7),r.push(v),c6(t,{width:v.width,height:2*(v.height||0)}),o.hasLoaded)v.align(t,!1,t.alignTo);else var x=c3(o,"load",function(){v.element&&v.align(t,!1,t.alignTo),x()})}),o.hasLoaded||c3(o,"render",function(){var t,e=null===(t=o.exportingGroup)||void 0===t?void 0:t.getBBox();if(e){var r=i.navButtonsGroup.getBBox();if(!(r.x>=e.x+e.width||r.x+r.width<=e.x||r.y>=e.y+e.height||r.y+r.height<=e.y)){var n=-r.y-r.height+e.y-5,a=e.y+e.height-r.y+5,l=s.buttonOptions&&s.buttonOptions.verticalAlign;i.navButtonsGroup.attr({translateY:"bottom"===l?n:a})}}})),this.updateEvents(s)},t.prototype.updateEvents=function(t){var e=this.chart;c8(t.enableDoubleClickZoom,t.enabled)||t.enableDoubleClickZoomTo?this.unbindDblClick=this.unbindDblClick||c3(e.container,"dblclick",function(t){e.pointer.onContainerDblClick(t)}):this.unbindDblClick&&(this.unbindDblClick=this.unbindDblClick()),c8(t.enableMouseWheelZoom,t.enabled)?this.unbindMouseWheel=this.unbindMouseWheel||c3(e.container,"wheel",function(t){var i,o;if(!e.pointer.inClass(t.target,"highcharts-no-mousewheel")){var r=null===(i=e.mapView)||void 0===i?void 0:i.zoom;e.pointer.onContainerMouseWheel(t),r!==(null===(o=e.mapView)||void 0===o?void 0:o.zoom)&&c7(t)}return!1}):this.unbindMouseWheel&&(this.unbindMouseWheel=this.unbindMouseWheel())},t}(),de=sc.seriesTypes.column.prototype,di=tV.addEvent,dr=tV.defined;!function(t){function e(t){var e=this.series,i=e.chart.renderer;this.moveToTopOnHover&&this.graphic&&(e.stateMarkerGraphic||(e.stateMarkerGraphic=new iX(i,"use").css({pointerEvents:"none"}).add(this.graphic.parentGroup)),(null==t?void 0:t.state)==="hover"?(this.graphic.attr({id:this.id}),e.stateMarkerGraphic.attr({href:""+i.url+"#".concat(this.id),visibility:"visible"})):e.stateMarkerGraphic.attr({href:""}))}t.pointMembers={dataLabelOnNull:!0,moveToTopOnHover:!0,isValid:function(){return null!==this.value&&this.value!==1/0&&this.value!==-1/0&&(void 0===this.value||!isNaN(this.value))}},t.seriesMembers={colorKey:"value",axisTypes:["xAxis","yAxis","colorAxis"],parallelArrays:["x","y","value"],pointArrayMap:["value"],trackerGroups:["group","markerGroup","dataLabelsGroup"],colorAttribs:function(t){var e={};return dr(t.color)&&(!t.state||"normal"===t.state)&&(e[this.colorProp||"fill"]=t.color),e},pointAttribs:de.pointAttribs},t.compose=function(t){return di(t.prototype.pointClass,"afterSetState",e),t}}(tv||(tv={}));var dn=tv,ds=(I=function(t,e){return(I=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}I(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),da=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},dl=tV.isNumber,dh=tV.merge,dc=tV.pick,dd=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ds(e,t),e.prototype.init=function(e,i){var o=ec().credits,r=dh({chart:{panning:{enabled:!0,type:"xy"},type:"map"},credits:{mapText:dc(o.mapText,' \xa9 <a href="{geojson.copyrightUrl}">{geojson.copyrightShort}</a>'),mapTextFull:dc(o.mapTextFull,"{geojson.copyright}")},mapView:{},tooltip:{followTouchMove:!1}},e);t.prototype.init.call(this,r,i)},e.prototype.mapZoom=function(t,e,i,o,r){this.mapView&&(dl(t)&&(t=Math.log(t)/Math.log(.5)),this.mapView.zoomBy(t,dl(e)&&dl(i)?this.mapView.projection.inverse([e,i]):void 0,dl(o)&&dl(r)?[o,r]:void 0))},e.prototype.update=function(e){var i;e.chart&&"map"in e.chart&&(null===(i=this.mapView)||void 0===i||i.recommendMapView(this,da([e.chart.map],(this.options.series||[]).map(function(t){return t.mapData}),!0),!0)),t.prototype.update.apply(this,arguments)},e}(aN);(D=dd||(dd={})).maps={},D.mapChart=function(t,e,i){return new D(t,e,i)},D.splitPath=function(t){var e;return e="string"==typeof t?(t=t.replace(/([A-Z])/gi," $1 ").replace(/^\s*/,"").replace(/\s*$/,"")).split(/[ ,;]+/).map(function(t){return/[A-Z]/i.test(t)?t:parseFloat(t)}):t,oI.prototype.pathToSegments(e)};var dp=dd,du=function(t){var e,i=-Number.MAX_VALUE,o=Number.MAX_VALUE,r=-Number.MAX_VALUE,n=Number.MAX_VALUE;if(t.forEach(function(t){var s=t[t.length-2],a=t[t.length-1];"number"==typeof s&&"number"==typeof a&&(o=Math.min(o,s),i=Math.max(i,s),n=Math.min(n,a),r=Math.max(r,a),e=!0)}),e)return{x1:o,y1:n,x2:i,y2:r}},df=(B=function(t,e){return(B=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}B(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),dg=function(){return(dg=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},dv=sc.seriesTypes.scatter.prototype.pointClass,dm=tV.extend,dy=tV.isNumber,dx=tV.pick,db=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return df(e,t),e.getProjectedPath=function(t,e){return t.projectedPath||(e&&t.geometry?(e.hasCoordinates=!0,t.projectedPath=e.path(t.geometry)):t.projectedPath=t.path),t.projectedPath||[]},e.prototype.applyOptions=function(e,i){var o,r=this.series,n=t.prototype.applyOptions.call(this,e,i),s=r.joinBy;if(r.mapData&&r.mapMap){var a=s[1],l=t.prototype.getNestedProperty.call(this,a),h=void 0!==l&&r.mapMap[l];h?dm(n,dg(dg({},h),{name:null!==(o=n.name)&&void 0!==o?o:h.name})):-1!==r.pointArrayMap.indexOf("value")&&(n.value=n.value||null)}return n},e.prototype.getProjectedBounds=function(t){var i=du(e.getProjectedPath(this,t)),o=this.properties,r=this.series.chart.mapView;if(i){var n=null==o?void 0:o["hc-middle-lon"],s=null==o?void 0:o["hc-middle-lat"];if(r&&dy(n)&&dy(s)){var a=t.forward([n,s]);i.midX=a[0],i.midY=a[1]}else{var l=null==o?void 0:o["hc-middle-x"],h=null==o?void 0:o["hc-middle-y"];i.midX=i.x1+(i.x2-i.x1)*dx(this.middleX,dy(l)?l:.5);var c=dx(this.middleY,dy(h)?h:.5);this.geometry||(c=1-c),i.midY=i.y2-(i.y2-i.y1)*c}return i}},e.prototype.onMouseOver=function(e){tV.clearTimeout(this.colorInterval),!this.isNull&&this.visible||this.series.options.nullInteraction?t.prototype.onMouseOver.call(this,e):this.series.onMouseOut()},e.prototype.setVisible=function(t){this.visible=this.options.visible=!!t,this.dataLabel&&this.dataLabel[t?"show":"hide"](),this.graphic&&this.graphic.attr(this.series.pointAttribs(this))},e.prototype.zoomTo=function(t){var e=this.series.chart,i=e.mapView,o=this.bounds;if(i&&o){var r=dy(this.insetIndex)&&i.insets[this.insetIndex];if(r){var n=r.projectedUnitsToPixels({x:o.x1,y:o.y1}),s=r.projectedUnitsToPixels({x:o.x2,y:o.y2}),a=i.pixelsToProjectedUnits({x:n.x,y:n.y}),l=i.pixelsToProjectedUnits({x:s.x,y:s.y});o={x1:a.x,y1:a.y,x2:l.x,y2:l.y}}i.fitToBounds(o,void 0,!1),this.series.isDirty=!0,e.redraw(t)}},e}(dv);dm(db.prototype,{dataLabelOnNull:dn.pointMembers.dataLabelOnNull,moveToTopOnHover:dn.pointMembers.moveToTopOnHover,isValid:dn.pointMembers.isValid});var dw=tV.isNumber,dM={affectsMapView:!0,animation:!1,dataLabels:{crop:!1,formatter:function(){var t=this.series.chart.numberFormatter,e=this.point.value;return dw(e)?t(e,-1):this.point.name||""},inside:!0,overflow:!1,padding:0,verticalAlign:"middle"},linecap:"round",marker:null,nullColor:"#f7f7f7",stickyTracking:!1,tooltip:{followPointer:!0,pointFormat:"{point.name}: {point.value}<br/>"},turboThreshold:0,allAreas:!0,borderColor:"#e6e6e6",borderWidth:1,joinBy:"hc-key",states:{hover:{halo:void 0,borderColor:"#666666",borderWidth:2},normal:{animation:!0},select:{color:"#cccccc"}},legendSymbol:"rectangle"},dk={center:[0,0],fitToGeometry:void 0,maxZoom:void 0,padding:0,projection:{name:void 0,parallels:void 0,rotation:void 0},zoom:void 0,insetOptions:{borderColor:"#cccccc",borderWidth:1,padding:"10%",relativeTo:"mapBoundingBox",units:"percent"}},dS=tb.win,dT=ii.format,dC=tV.error,dA=tV.extend,dP=tV.merge,dO=tV.wrap;!function(t){function e(t){return this.mapView&&this.mapView.lonLatToProjectedUnits(t)}function i(t){return this.mapView&&this.mapView.projectedUnitsToLonLat(t)}function o(t,e){var i=this.options.chart.proj4||dS.proj4;if(!i){dC(21,!1,this);return}var o=e.jsonmarginX,r=e.jsonmarginY,n=e.jsonres,s=void 0===n?1:n,a=e.scale,l=void 0===a?1:a,h=e.xoffset,c=e.xpan,d=e.yoffset,p=e.ypan,u=i(e.crs,[t.lon,t.lat]),f=e.cosAngle||e.rotation&&Math.cos(e.rotation),g=e.sinAngle||e.rotation&&Math.sin(e.rotation),v=e.rotation?[u[0]*f+u[1]*g,-u[0]*g+u[1]*f]:u;return{x:((v[0]-(void 0===h?0:h))*l+(void 0===c?0:c))*s+(void 0===o?0:o),y:-((((void 0===d?0:d)-v[1])*l+(void 0===p?0:p))*s-(void 0===r?0:r))}}function r(t,e){var i=this.options.chart.proj4||dS.proj4;if(!i){dC(21,!1,this);return}if(null!==t.y){var o=e.jsonmarginX,r=e.jsonmarginY,n=e.jsonres,s=void 0===n?1:n,a=e.scale,l=void 0===a?1:a,h=e.xoffset,c=e.xpan,d=e.yoffset,p=e.ypan,u={x:((t.x-(void 0===o?0:o))/s-(void 0===c?0:c))/l+(void 0===h?0:h),y:((t.y-(void 0===r?0:r))/s+(void 0===p?0:p))/l+(void 0===d?0:d)},f=e.cosAngle||e.rotation&&Math.cos(e.rotation),g=e.sinAngle||e.rotation&&Math.sin(e.rotation),v=i(e.crs,"WGS84",e.rotation?{x:u.x*f+-(u.y*g),y:u.x*g+u.y*f}:u);return{lat:v.y,lon:v.x}}}function n(t,e){e||(e=Object.keys(t.objects)[0]);var i=t.objects[e];if(i["hc-decoded-geojson"]&&i["hc-decoded-geojson"].title===t.title)return i["hc-decoded-geojson"];var o=t.arcs;if(t.transform){var r=t.arcs,n=t.transform,s=n.scale,a=n.translate,l=void 0,h=void 0,c=void 0;o=[];for(var d=0,p=r.length;d<p;++d){var u=r[d];o.push(l=[]),h=0,c=0;for(var f=0,g=u.length;f<g;++f)l.push([(h+=u[f][0])*s[0]+a[0],(c+=u[f][1])*s[1]+a[1]])}}for(var v=function(t){return"number"==typeof t[0]?t.reduce(function(t,e,i){var r=e<0?o[~e]:o[e];return e<0?(r=r.slice(0,0===i?r.length:r.length-1)).reverse():i&&(r=r.slice(1)),t.concat(r)},[]):t.map(v)},m=i.geometries,y=[],d=0,p=m.length;d<p;++d)y.push({type:"Feature",properties:m[d].properties,geometry:{type:m[d].type,coordinates:m[d].coordinates||v(m[d].arcs)}});var x={type:"FeatureCollection",copyright:t.copyright,copyrightShort:t.copyrightShort,copyrightUrl:t.copyrightUrl,features:y,"hc-recommended-mapview":i["hc-recommended-mapview"],bbox:t.bbox,title:t.title};return i["hc-decoded-geojson"]=x,x}function s(t,e){e=dP(!0,this.options.credits,e),t.call(this,e),this.credits&&this.mapCreditsFull&&this.credits.attr({title:this.mapCreditsFull})}t.compose=function(t){var n=t.prototype;n.transformFromLatLon||(n.fromLatLonToPoint=e,n.fromPointToLatLon=i,n.transformFromLatLon=o,n.transformToLatLon=r,dO(n,"addCredits",s))},t.geojson=function(t,e,i){void 0===e&&(e="map");for(var o,r,s=[],a="Topology"===t.type?n(t):t,l=a.features,h=0,c=l.length;h<c;++h){var d=l[h],p=d.geometry||{},u=p.type,f=p.coordinates,g=d.properties,v=void 0;if(("map"===e||"mapbubble"===e)&&("Polygon"===u||"MultiPolygon"===u)?f.length&&(v={geometry:{coordinates:f,type:u}}):"mapline"===e&&("LineString"===u||"MultiLineString"===u)?f.length&&(v={geometry:{coordinates:f,type:u}}):"mappoint"===e&&"Point"===u&&f.length&&(v={geometry:{coordinates:f,type:u}}),v){var m=g&&(g.name||g.NAME),y=g&&g.lon,x=g&&g.lat;s.push(dA(v,{lat:"number"==typeof x?x:void 0,lon:"number"==typeof y?y:void 0,name:"string"==typeof m?m:void 0,properties:g}))}}return i&&a.copyrightShort&&(i.chart.mapCredits=dT(null===(o=i.chart.options.credits)||void 0===o?void 0:o.mapText,{geojson:a}),i.chart.mapCreditsFull=dT(null===(r=i.chart.options.credits)||void 0===r?void 0:r.mapTextFull,{geojson:a})),s},t.topo2geo=n}(tm||(tm={}));var dL=tm;function dE(t,e,i){void 0===i&&(i=!0);for(var o,r,n,s=e[e.length-1],a=t,l=0;l<e.length;l++){var h=a;o=e[l],a=[],r=i?h[h.length-1]:h[0];for(var c=0;c<h.length;c++)dI(s,o,n=h[c])?(dI(s,o,r)||a.push(dD(s,o,r,n)),a.push(n)):dI(s,o,r)&&a.push(dD(s,o,r,n)),r=n;s=o}return a}function dI(t,e,i){return(e[0]-t[0])*(i[1]-t[1])>(e[1]-t[1])*(i[0]-t[0])}function dD(t,e,i,o){var r=[t[0]-e[0],t[1]-e[1]],n=[i[0]-o[0],i[1]-o[1]],s=t[0]*e[1]-t[1]*e[0],a=i[0]*o[1]-i[1]*o[0],l=1/(r[0]*n[1]-r[1]*n[0]),h=[(s*n[0]-a*r[0])*l,(s*n[1]-a*r[1])*l];return h.isIntersection=!0,h}var dB=function(t,e){for(var i=[],o=dE(t,e,!1),r=1;r<o.length;r++)o[r].isIntersection&&o[r-1].isIntersection&&(i.push(o.splice(0,r)),r=0),r===o.length-1&&i.push(o);return i},dz=Math.sign||function(t){return 0===t?0:t>0?1:-1},dj=Math.PI/180,dN=Math.PI/2,dR=function(t){return Math.tan((dN+t)/2)},dW=function(){function t(t){var e,i=(t.parallels||[]).map(function(t){return t*dj}),o=i[0]||0,r=null!==(e=i[1])&&void 0!==e?e:o,n=Math.cos(o);"object"==typeof t.projectedBounds&&(this.projectedBounds=t.projectedBounds);var s=o===r?Math.sin(o):Math.log(n/Math.cos(r))/Math.log(dR(r)/dR(o));1e-10>Math.abs(s)&&(s=1e-10*(dz(s)||1)),this.n=s,this.c=n*Math.pow(dR(o),s)/s}return t.prototype.forward=function(t){var e=this.c,i=this.n,o=this.projectedBounds,r=t[0]*dj,n=t[1]*dj;e>0?n<-dN+1e-6&&(n=-dN+1e-6):n>dN-1e-6&&(n=dN-1e-6);var s=e/Math.pow(dR(n),i),a=s*Math.sin(i*r)*63.78137,l=(e-s*Math.cos(i*r))*63.78137,h=[a,l];return o&&(a<o.x1||a>o.x2||l<o.y1||l>o.y2)&&(h.outside=!0),h},t.prototype.inverse=function(t){var e=this.c,i=this.n,o=t[0]/63.78137,r=e-t[1]/63.78137,n=dz(i)*Math.sqrt(o*o+r*r),s=Math.atan2(o,Math.abs(r))*dz(r);return r*i<0&&(s-=Math.PI*dz(o)*dz(r)),[s/i/dj,(2*Math.atan(Math.pow(e/n,1/i))-dN)/dj]},t}(),dX=Math.sqrt(3)/2,d_=function(){function t(){this.bounds={x1:-200.37508342789243,x2:200.37508342789243,y1:-97.52595454902263,y2:97.52595454902263}}return t.prototype.forward=function(t){var e=Math.PI/180,i=Math.asin(dX*Math.sin(t[1]*e)),o=i*i,r=o*o*o;return[t[0]*e*Math.cos(i)*74.03120656864502/(dX*(1.340264+-.24331799999999998*o+r*(.0062510000000000005+.034164*o))),74.03120656864502*i*(1.340264+-.081106*o+r*(893e-6+.003796*o))]},t.prototype.inverse=function(t){for(var e,i,o,r,n=t[0]/74.03120656864502,s=t[1]/74.03120656864502,a=180/Math.PI,l=s,h=0;h<12&&(i=(e=l*l)*e*e,o=l*(1.340264+-.081106*e+i*(893e-6+.003796*e))-s,l-=r=o/(1.340264+-.24331799999999998*e+i*(.0062510000000000005+.034164*e)),!(1e-9>Math.abs(r)));++h);i=(e=l*l)*e*e;var c=a*dX*n*(1.340264+-.24331799999999998*e+i*(.0062510000000000005+.034164*e))/Math.cos(l),d=a*Math.asin(Math.sin(l)/dX);return Math.abs(c)>180?[NaN,NaN]:[c,d]},t}(),dF=Math.PI/4,dY=Math.PI/180,dG=function(){function t(){this.bounds={x1:-200.37508342789243,x2:200.37508342789243,y1:-146.91480769173063,y2:146.91480769173063}}return t.prototype.forward=function(t){return[t[0]*dY*63.78137,79.7267125*Math.log(Math.tan(dF+.4*t[1]*dY))]},t.prototype.inverse=function(t){return[t[0]/63.78137/dY,2.5*(Math.atan(Math.exp(.8*(t[1]/63.78137)))-dF)/dY]},t}(),dH=Math.PI/180,dV=function(){function t(){this.antimeridianCutting=!1,this.bounds={x1:-63.78460826781007,x2:63.78460826781007,y1:-63.78460826781007,y2:63.78460826781007}}return t.prototype.forward=function(t){var e=t[0],i=t[1]*dH,o=[Math.cos(i)*Math.sin(e*dH)*63.78460826781007,63.78460826781007*Math.sin(i)];return(e<-90||e>90)&&(o.outside=!0),o},t.prototype.inverse=function(t){var e=t[0]/63.78460826781007,i=t[1]/63.78460826781007,o=Math.sqrt(e*e+i*i),r=Math.asin(o),n=Math.sin(r);return[Math.atan2(e*n,o*Math.cos(r))/dH,Math.asin(o&&i*n/o)/dH]},t}(),dU=Math.PI/180,dZ={EqualEarth:d_,LambertConformalConic:dW,Miller:dG,Orthographic:dV,WebMercator:function(){function t(){this.bounds={x1:-200.37508342789243,x2:200.37508342789243,y1:-200.3750834278071,y2:200.3750834278071},this.maxLatitude=85.0511287798}return t.prototype.forward=function(t){var e=Math.sin(t[1]*dU),i=[63.78137*t[0]*dU,63.78137*Math.log((1+e)/(1-e))/2];return Math.abs(t[1])>this.maxLatitude&&(i.outside=!0),i},t.prototype.inverse=function(t){return[t[0]/(63.78137*dU),(2*Math.atan(Math.exp(t[1]/63.78137))-Math.PI/2)/dU]},t}()},dq=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},dK=tV.clamp,d$=tV.erase,dJ=2*Math.PI/360,dQ=function(t){return t<-180&&(t+=360),t>180&&(t-=360),t},d0=function(t){return(1-Math.cos(t))/2},d1=function(t,e){var i=Math.cos,o=t[1]*dJ,r=t[0]*dJ,n=e[1]*dJ,s=e[0]*dJ;return d0(n-o)+i(o)*i(n)*d0(s-r)},d2=function(){function t(e){void 0===e&&(e={}),this.hasCoordinates=!1,this.hasGeoProjection=!1,this.maxLatitude=90,this.options=e;var i=e.name,o=e.projectedBounds,r=e.rotation;this.rotator=r?this.getRotator(r):void 0;var n=i?t.registry[i]:void 0;n&&(this.def=new n(e));var s=this.def,a=this.rotator;s&&(this.maxLatitude=s.maxLatitude||90,this.hasGeoProjection=!0),a&&s?(this.forward=function(t){return s.forward(a.forward(t))},this.inverse=function(t){return a.inverse(s.inverse(t))}):s?(this.forward=function(t){return s.forward(t)},this.inverse=function(t){return s.inverse(t)}):a&&(this.forward=a.forward,this.inverse=a.inverse),this.bounds="world"===o?s&&s.bounds:o}return t.add=function(e,i){t.registry[e]=i},t.distance=function(t,e){var i=Math.atan2,o=Math.sqrt,r=d1(t,e);return 6371e3*(2*i(o(r),o(1-r)))},t.geodesic=function(e,i,o,r){void 0===r&&(r=5e5);var n=Math.atan2,s=Math.cos,a=Math.sin,l=Math.sqrt,h=t.distance,c=e[1]*dJ,d=e[0]*dJ,p=i[1]*dJ,u=i[0]*dJ,f=s(c)*s(d),g=s(p)*s(u),v=s(c)*a(d),m=s(p)*a(u),y=a(c),x=a(p),b=h(e,i),w=b/6371e3,M=a(w),k=Math.round(b/r),S=[];if(o&&S.push(e),k>1)for(var T=1/k,C=T;C<.999;C+=T){var A=a((1-C)*w)/M,P=a(C*w)/M,O=A*f+P*g,L=A*v+P*m,E=n(A*y+P*x,l(O*O+L*L)),I=n(L,O);S.push([I/dJ,E/dJ])}return o&&S.push(i),S},t.insertGeodesics=function(e){for(var i=e.length-1;i--;)if(Math.max(Math.abs(e[i][0]-e[i+1][0]),Math.abs(e[i][1]-e[i+1][1]))>10){var o=t.geodesic(e[i],e[i+1]);o.length&&e.splice.apply(e,dq([i+1,0],o,!1))}},t.toString=function(t){var e=t||{},i=e.name,o=e.rotation;return[i,o&&o.join(",")].join(";")},t.prototype.lineIntersectsBounds=function(t){var e,i=this.bounds||{},o=i.x1,r=i.x2,n=i.y1,s=i.y2,a=function(t,e,i){var o=t[0],r=t[1],n=+!e;if("number"==typeof i&&o[e]>=i!=r[e]>=i){var s=(i-o[e])/(r[e]-o[e]),a=o[n]+s*(r[n]-o[n]);return e?[a,i]:[i,a]}},l=t[0];return(e=a(t,0,o))?(l=e,t[1]=e):(e=a(t,0,r))&&(l=e,t[1]=e),(e=a(t,1,n))?l=e:(e=a(t,1,s))&&(l=e),l},t.prototype.getRotator=function(t){var e=t[0]*dJ,i=(t[1]||0)*dJ,o=(t[2]||0)*dJ,r=Math.cos(i),n=Math.sin(i),s=Math.cos(o),a=Math.sin(o);if(0!==e||0!==i||0!==o)return{forward:function(t){var i=t[0]*dJ+e,o=t[1]*dJ,l=Math.cos(o),h=Math.cos(i)*l,c=Math.sin(i)*l,d=Math.sin(o),p=d*r+h*n;return[Math.atan2(c*s-p*a,h*r-d*n)/dJ,Math.asin(p*s+c*a)/dJ]},inverse:function(t){var i=t[0]*dJ,o=t[1]*dJ,l=Math.cos(o),h=Math.cos(i)*l,c=Math.sin(i)*l,d=Math.sin(o),p=d*s-c*a;return[(Math.atan2(c*s+d*a,h*r+p*n)-e)/dJ,Math.asin(p*r-h*n)/dJ]}}},t.prototype.forward=function(t){return t},t.prototype.inverse=function(t){return t},t.prototype.cutOnAntimeridian=function(e,i){for(var o,r=[],n=[e],s=0,a=e.length;s<a;++s){var l=e[s],h=e[s-1];if(!s){if(!i)continue;h=e[e.length-1]}var c=h[0],d=l[0];if((c<-90||c>90)&&(d<-90||d>90)&&c>0!=d>0){var p=dK((180-(c+360)%360)/((d+360)%360-(c+360)%360),0,1),u=h[1]+p*(l[1]-h[1]);r.push({i:s,lat:u,direction:c<0?1:-1,previousLonLat:h,lonLat:l})}}if(r.length){if(i){r.length%2==1&&(o=r.slice().sort(function(t,e){return Math.abs(e.lat)-Math.abs(t.lat)})[0],d$(r,o));for(var s=r.length-2;s>=0;){var f=r[s].i,g=dQ(180+1e-6*r[s].direction),v=dQ(180-1e-6*r[s].direction),m=e.splice.apply(e,dq([f,r[s+1].i-f],t.geodesic([g,r[s].lat],[g,r[s+1].lat],!0),!1));m.push.apply(m,t.geodesic([v,r[s+1].lat],[v,r[s].lat],!0)),n.push(m),s-=2}if(o)for(var y=0;y<n.length;y++){var x=o.direction,u=o.lat,b=n[y],w=b.indexOf(o.lonLat);if(w>-1){for(var M=(u<0?-1:1)*this.maxLatitude,c=dQ(180+1e-6*x),d=dQ(180-1e-6*x),k=t.geodesic([c,u],[c,M],!0),S=c+120*x;S>-180&&S<180;S+=120*x)k.push([S,M]);k.push.apply(k,t.geodesic([d,M],[d,o.lat],!0)),b.splice.apply(b,dq([w,0],k,!1));break}}}else for(var s=r.length;s--;){var f=r[s].i,m=e.splice(f,e.length,[dQ(180+1e-6*r[s].direction),r[s].lat]);m.unshift([dQ(180-1e-6*r[s].direction),r[s].lat]),n.push(m)}}return n},t.prototype.path=function(e){var i,o=this,r=this.bounds,n=this.def,s=this.rotator,a=[],l="Polygon"===e.type||"MultiPolygon"===e.type,h=this.hasGeoProjection,c=!n||!1!==n.antimeridianCutting,d=c?s:void 0,p=c&&n||this;r&&(i=[[r.x1,r.y1],[r.x2,r.y1],[r.x2,r.y2],[r.x1,r.y2]]);var u=function(e){var n=e.map(function(t){if(c){d&&(t=d.forward(t));var e=t[0];1e-6>Math.abs(e-180)&&(e=e<180?179.999999:180.000001),t=[e,t[1]]}return t}),s=[n];h&&(t.insertGeodesics(n),c&&(s=o.cutOnAntimeridian(n,l))),s.forEach(function(e){if(!(e.length<2)){var o,n,s=!1,d=!1,u=function(t){s?a.push(["L",t[0],t[1]]):(a.push(["M",t[0],t[1]]),s=!0)},f=!1,g=!1,v=e.map(function(t){var e=p.forward(t);return e.outside?f=!0:g=!0,e[1]===1/0?e[1]=1e10:e[1]===-1/0&&(e[1]=-1e10),e});if(c){if(l&&v.push(v[0]),f){if(!g)return;if(i){if(l)v=dE(v,i);else if(r){dB(v,i).forEach(function(t){s=!1,t.forEach(u)});return}}}v.forEach(u)}else for(var m=0;m<v.length;m++){var y=e[m],x=v[m];x.outside?d=!0:(l&&!o&&(o=y,e.push(y),v.push(x)),d&&n&&(l&&h?t.geodesic(n,y).forEach(function(t){return u(p.forward(t))}):s=!1),u(x),n=y,d=!1)}}})};return"LineString"===e.type?u(e.coordinates):"MultiLineString"===e.type?e.coordinates.forEach(function(t){return u(t)}):"Polygon"===e.type?(e.coordinates.forEach(function(t){return u(t)}),a.length&&a.push(["Z"])):"MultiPolygon"===e.type&&(e.coordinates.forEach(function(t){t.forEach(function(t){return u(t)})}),a.length&&a.push(["Z"])),a},t.registry=dZ,t}(),d3=(z=function(t,e){return(z=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}z(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),d6=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},d5=tb.composed,d9=h3.pointInPolygon,d8=dL.topo2geo,d4=tV.addEvent,d7=tV.clamp,pt=tV.crisp,pe=tV.fireEvent,pi=tV.isArray,po=tV.isNumber,pr=tV.isObject,pn=tV.isString,ps=tV.merge,pa=tV.pick,pl=tV.pushUnique,ph=tV.relativeLength,pc={};function pd(t,e){var i=e.width,o=e.height;return Math.log(400.979322/Math.max((t.x2-t.x1)/(i/256),(t.y2-t.y1)/(o/256)))/Math.log(2)}function pp(t){var e,i;t.seriesOptions.mapData&&(null===(e=this.mapView)||void 0===e||e.recommendMapView(this,[this.options.chart.map,t.seriesOptions.mapData],null===(i=this.options.drilldown)||void 0===i?void 0:i.mapZooming))}var pu=function(){function t(e,i){var o,r=this;this.allowTransformAnimation=!0,this.eventsToUnbind=[],this.insets=[],this.padding=[0,0,0,0],this.recommendedMapView={},this instanceof pf||this.recommendMapView(e,d6([e.options.chart.map],(e.options.series||[]).map(function(t){return t.mapData}),!0)),this.userOptions=i||{};var n=ps(dk,this.recommendedMapView,i),s=null===(o=this.recommendedMapView)||void 0===o?void 0:o.insets,a=i&&i.insets;s&&a&&(n.insets=t.mergeInsets(s,a)),this.chart=e,this.center=n.center,this.options=n,this.projection=new d2(n.projection),this.playingField=e.plotBox,this.zoom=n.zoom||0,this.minZoom=n.minZoom,this.createInsets(),this.eventsToUnbind.push(d4(e,"afterSetChartSize",function(){r.playingField=r.getField(),(void 0===r.minZoom||r.minZoom===r.zoom)&&(r.fitToBounds(void 0,void 0,!1),!r.chart.hasRendered&&po(r.userOptions.zoom)&&(r.zoom=r.userOptions.zoom),r.userOptions.center&&ps(!0,r.center,r.userOptions.center))})),this.setUpEvents()}return t.compose=function(e){pl(d5,"MapView")&&(pc=e.maps,d4(e,"afterInit",function(){this.mapView=new t(this,this.options.mapView)},{order:0}),d4(e,"addSeriesAsDrilldown",pp),d4(e,"afterDrillUp",pp))},t.compositeBounds=function(t){if(t.length)return t.slice(1).reduce(function(t,e){return t.x1=Math.min(t.x1,e.x1),t.y1=Math.min(t.y1,e.y1),t.x2=Math.max(t.x2,e.x2),t.y2=Math.max(t.y2,e.y2),t},ps(t[0]))},t.mergeInsets=function(t,e){var i=function(t){var e={};return t.forEach(function(t,i){e[t&&t.id||"i".concat(i)]=t}),e},o=ps(i(t),i(e));return Object.keys(o).map(function(t){return o[t]})},t.prototype.createInsets=function(){var t=this,e=this.options,i=e.insets;i&&i.forEach(function(i){var o=new pf(t,ps(e.insetOptions,i));t.insets.push(o)})},t.prototype.fitToBounds=function(t,e,i,o){void 0===i&&(i=!0);var r=t||this.getProjectedBounds();if(r){var n=pa(e,t?0:this.options.padding),s=this.getField(!1),a=pi(n)?n:[n,n,n,n];this.padding=[ph(a[0],s.height),ph(a[1],s.width),ph(a[2],s.height),ph(a[3],s.width)],this.playingField=this.getField();var l=pd(r,this.playingField);t||(this.minZoom=l);var h=this.projection.inverse([(r.x2+r.x1)/2,(r.y2+r.y1)/2]);this.setView(h,l,i,o)}},t.prototype.getField=function(t){void 0===t&&(t=!0);var e=t?this.padding:[0,0,0,0];return{x:e[3],y:e[0],width:this.chart.plotWidth-e[1]-e[3],height:this.chart.plotHeight-e[0]-e[2]}},t.prototype.getGeoMap=function(t){if(pn(t))return pc[t]&&"Topology"===pc[t].type?d8(pc[t]):pc[t];if(pr(t,!0)){if("FeatureCollection"===t.type)return t;if("Topology"===t.type)return d8(t)}},t.prototype.getMapBBox=function(){var t=this.getProjectedBounds(),e=this.getScale();if(t){var i=this.padding,o=this.projectedUnitsToPixels({x:t.x1,y:t.y2});return{width:(t.x2-t.x1)*e+i[1]+i[3],height:(t.y2-t.y1)*e+i[0]+i[2],x:o.x-i[3],y:o.y-i[0]}}},t.prototype.getProjectedBounds=function(){var e=this.projection,i=this.chart.series.reduce(function(t,e){var i=e.getProjectedBounds&&e.getProjectedBounds();return i&&!1!==e.options.affectsMapView&&t.push(i),t},[]),o=this.options.fitToGeometry;if(o){if(!this.fitToGeometryCache){if("MultiPoint"===o.type){var r=o.coordinates.map(function(t){return e.forward(t)}),n=r.map(function(t){return t[0]}),s=r.map(function(t){return t[1]});this.fitToGeometryCache={x1:Math.min.apply(0,n),x2:Math.max.apply(0,n),y1:Math.min.apply(0,s),y2:Math.max.apply(0,s)}}else this.fitToGeometryCache=du(e.path(o))}return this.fitToGeometryCache}return this.projection.bounds||t.compositeBounds(i)},t.prototype.getScale=function(){return 256/400.979322*Math.pow(2,this.zoom)},t.prototype.getSVGTransform=function(){var t=this.playingField,e=t.x,i=t.y,o=t.width,r=t.height,n=this.projection.forward(this.center),s=this.projection.hasCoordinates?-1:1,a=this.getScale(),l=a*s,h=e+o/2-n[0]*a,c=i+r/2-n[1]*l;return{scaleX:a,scaleY:l,translateX:h,translateY:c}},t.prototype.lonLatToPixels=function(t){var e=this.lonLatToProjectedUnits(t);if(e)return this.projectedUnitsToPixels(e)},t.prototype.lonLatToProjectedUnits=function(t){var e=this.chart,i=e.mapTransforms;if(i){for(var o in i)if(Object.hasOwnProperty.call(i,o)&&i[o].hitZone){var r=e.transformFromLatLon(t,i[o]);if(r&&d9(r,i[o].hitZone.coordinates[0]))return r}return e.transformFromLatLon(t,i.default)}for(var n=0,s=this.insets;n<s.length;n++){var a=s[n];if(a.options.geoBounds&&d9({x:t.lon,y:t.lat},a.options.geoBounds.coordinates[0])){var l=a.projection.forward([t.lon,t.lat]),h=a.projectedUnitsToPixels({x:l[0],y:l[1]});return this.pixelsToProjectedUnits(h)}}var c=this.projection.forward([t.lon,t.lat]);if(!c.outside)return{x:c[0],y:c[1]}},t.prototype.projectedUnitsToLonLat=function(t){var e=this.chart,i=e.mapTransforms;if(i){for(var o in i)if(Object.hasOwnProperty.call(i,o)&&i[o].hitZone&&d9(t,i[o].hitZone.coordinates[0]))return e.transformToLatLon(t,i[o]);return e.transformToLatLon(t,i.default)}for(var r=this.projectedUnitsToPixels(t),n=0,s=this.insets;n<s.length;n++){var a=s[n];if(a.hitZone&&d9(r,a.hitZone.coordinates[0])){var l=a.pixelsToProjectedUnits(r),h=a.projection.inverse([l.x,l.y]);return{lon:h[0],lat:h[1]}}}var c=this.projection.inverse([t.x,t.y]);return{lon:c[0],lat:c[1]}},t.prototype.recommendMapView=function(e,i,o){var r,n=this;void 0===o&&(o=!1),this.recommendedMapView={};var s=i.map(function(t){return n.getGeoMap(t)}),a=[];s.forEach(function(t){if(t&&(Object.keys(n.recommendedMapView).length||(n.recommendedMapView=t["hc-recommended-mapview"]||{}),t.bbox)){var e=t.bbox,i=e[0],o=e[1],r=e[2],s=e[3];a.push({x1:i,y1:o,x2:r,y2:s})}});var l=a.length&&t.compositeBounds(a);pe(this,"onRecommendMapView",{geoBounds:l,chart:e},function(){if(l&&this.recommendedMapView){if(!this.recommendedMapView.projection){var t=l.x1,e=l.y1,i=l.x2,o=l.y2;this.recommendedMapView.projection=i-t>180&&o-e>90?{name:"EqualEarth",parallels:[0,0],rotation:[0]}:{name:"LambertConformalConic",parallels:[e,o],rotation:[-(t+i)/2]}}this.recommendedMapView.insets||(this.recommendedMapView.insets=void 0)}}),this.geoMap=s[0],o&&e.hasRendered&&!(null===(r=e.userOptions.mapView)||void 0===r?void 0:r.projection)&&this.recommendedMapView&&this.update(this.recommendedMapView)},t.prototype.redraw=function(t){this.chart.series.forEach(function(t){t.useMapGeometry&&(t.isDirty=!0)}),this.chart.redraw(t)},t.prototype.setView=function(t,e,i,o){void 0===i&&(i=!0),t&&(this.center=t),"number"==typeof e&&("number"==typeof this.minZoom&&(e=Math.max(e,this.minZoom)),"number"==typeof this.options.maxZoom&&(e=Math.min(e,this.options.maxZoom)),po(e)&&(this.zoom=e));var r=this.getProjectedBounds();if(r){var n=this.projection.forward(this.center),s=this.playingField,a=s.x,l=s.y,h=s.width,c=s.height,d=this.getScale(),p=this.projectedUnitsToPixels({x:r.x1,y:r.y1}),u=this.projectedUnitsToPixels({x:r.x2,y:r.y2}),f=[(r.x1+r.x2)/2,(r.y1+r.y2)/2];if(!this.chart.series.some(function(t){return t.isDrilling})){var g=p.x,v=u.y,m=u.x,y=p.y;m-g<h?n[0]=f[0]:g<a&&m<a+h?n[0]+=Math.max(g-a,m-h-a)/d:m>a+h&&g>a&&(n[0]+=Math.min(m-h-a,g-a)/d),y-v<c?n[1]=f[1]:v<l&&y<l+c?n[1]-=Math.max(v-l,y-c-l)/d:y>l+c&&v>l&&(n[1]-=Math.min(y-c-l,v-l)/d),this.center=this.projection.inverse(n)}this.insets.forEach(function(t){t.options.field&&(t.hitZone=t.getHitZone(),t.playingField=t.getField())}),this.render()}pe(this,"afterSetView"),i&&this.redraw(o)},t.prototype.projectedUnitsToPixels=function(t){var e=this.getScale(),i=this.projection.forward(this.center),o=this.playingField,r=o.x+o.width/2,n=o.y+o.height/2;return{x:r-e*(i[0]-t.x),y:n+e*(i[1]-t.y)}},t.prototype.pixelsToLonLat=function(t){return this.projectedUnitsToLonLat(this.pixelsToProjectedUnits(t))},t.prototype.pixelsToProjectedUnits=function(t){var e=t.x,i=t.y,o=this.getScale(),r=this.projection.forward(this.center),n=this.playingField,s=n.x+n.width/2,a=n.y+n.height/2;return{x:r[0]+(e-s)/o,y:r[1]-(i-a)/o}},t.prototype.setUpEvents=function(){var t,e,i,o=this,r=this.chart,n=function(n){var s=r.pointer,a=s.lastTouches,l=s.pinchDown,h=o.projection,c=n.touches,d=r.mouseDownX,p=r.mouseDownY,u=0;if((null==l?void 0:l.length)===1?(d=l[0].chartX,p=l[0].chartY):(null==l?void 0:l.length)===2&&(d=(l[0].chartX+l[1].chartX)/2,p=(l[0].chartY+l[1].chartY)/2),(null==c?void 0:c.length)===2&&a&&(u=Math.log(Math.sqrt(Math.pow(a[0].chartX-a[1].chartX,2)+Math.pow(a[0].chartY-a[1].chartY,2))/Math.sqrt(Math.pow(c[0].chartX-c[1].chartX,2)+Math.pow(c[0].chartY-c[1].chartY,2)))/Math.log(.5)),po(d)&&po(p)){var f=""+d+",".concat(p),g=n.originalEvent,v=g.chartX,m=g.chartY;(null==c?void 0:c.length)===2&&(v=(c[0].chartX+c[1].chartX)/2,m=(c[0].chartY+c[1].chartY)/2),f!==e&&(e=f,t=o.projection.forward(o.center),i=(o.projection.options.rotation||[0,0]).slice());var y=h.def&&h.def.bounds,x=y&&pd(y,o.playingField)||-1/0;if("Orthographic"===h.options.name&&2>((null==c?void 0:c.length)||0)&&(o.minZoom||1/0)<1.3*x){var b=440/(o.getScale()*Math.min(r.plotWidth,r.plotHeight));if(i){var w=(d-v)*b-i[0],M=d7(-i[1]-(p-m)*b,-80,80),k=o.zoom;o.update({projection:{rotation:[-w,-M]}},!1),o.fitToBounds(void 0,void 0,!1),o.zoom=k,r.redraw(!1)}}else if(po(v)&&po(m)){var S=o.getScale(),T=o.projection.hasCoordinates?1:-1,C=o.projection.inverse([t[0]+(d-v)/S,t[1]-(p-m)/S*T]);isNaN(C[0]+C[1])||o.zoomBy(u,C,void 0,!1)}n.preventDefault()}};d4(r,"pan",n),d4(r,"touchpan",n),d4(r,"selection",function(t){if(t.resetSelection)o.zoomBy();else{var e=t.x-r.plotLeft,i=t.y-r.plotTop,n=o.pixelsToProjectedUnits({x:e,y:i}),s=n.y,a=n.x,l=o.pixelsToProjectedUnits({x:e+t.width,y:i+t.height}),h=l.y,c=l.x;o.fitToBounds({x1:a,y1:s,x2:c,y2:h},void 0,!0,!t.originalEvent.touches&&void 0),/^touch/.test(t.originalEvent.type)||r.showResetZoom(),t.preventDefault()}})},t.prototype.render=function(){this.group||(this.group=this.chart.renderer.g("map-view").attr({zIndex:4}).add())},t.prototype.update=function(t,e,i){void 0===e&&(e=!0);var o=t.projection,r=o&&d2.toString(o)!==d2.toString(this.options.projection),n=!1;ps(!0,this.userOptions,t),ps(!0,this.options,t),"insets"in t&&(this.insets.forEach(function(t){return t.destroy()}),this.insets.length=0,n=!0),(r||"fitToGeometry"in t)&&delete this.fitToGeometryCache,(r||n)&&(this.chart.series.forEach(function(t){var e=t.transformGroups;if(t.clearBounds&&t.clearBounds(),t.isDirty=!0,t.isDirtyData=!0,n&&e)for(;e.length>1;){var i=e.pop();i&&i.destroy()}}),r&&(this.projection=new d2(this.options.projection)),n&&this.createInsets(),!t.center&&Object.hasOwnProperty.call(t,"zoom")&&!po(t.zoom)&&this.fitToBounds(void 0,void 0,!1)),t.center||po(t.zoom)?this.setView(this.options.center,t.zoom,!1):"fitToGeometry"in t&&this.fitToBounds(void 0,void 0,!1),e&&this.chart.redraw(i)},t.prototype.zoomBy=function(t,e,i,o){var r=this.chart,n=this.projection.forward(this.center);if("number"==typeof t){var s=this.zoom+t,a=void 0,l=void 0,h=void 0;if(i){var c=i[0],d=i[1],p=this.getScale(),u=c-r.plotLeft-r.plotWidth/2,f=d-r.plotTop-r.plotHeight/2;l=n[0]+u/p,h=n[1]+f/p}if("number"==typeof l&&"number"==typeof h){var p=1-Math.pow(2,this.zoom)/Math.pow(2,s),u=n[0]-l,f=n[1]-h;n[0]-=u*p,n[1]+=f*p,a=this.projection.inverse(n)}this.setView(e||a,s,void 0,o)}else this.fitToBounds(void 0,void 0,void 0,o)},t}(),pf=function(t){function e(e,i){var o=t.call(this,e.chart,i)||this;if(o.id=i.id,o.mapView=e,o.options=ps({center:[0,0]},e.options.insetOptions,i),o.allBounds=[],o.options.geoBounds){var r=e.projection.path(o.options.geoBounds);o.geoBoundsProjectedBox=du(r),o.geoBoundsProjectedPolygon=r.map(function(t){return[t[1]||0,t[2]||0]})}return o}return d3(e,t),e.prototype.getField=function(e){void 0===e&&(e=!0);var i=this.hitZone;if(i){var o=e?this.padding:[0,0,0,0],r=i.coordinates[0],n=r.map(function(t){return t[0]}),s=r.map(function(t){return t[1]}),a=Math.min.apply(0,n)+o[3],l=Math.max.apply(0,n)-o[1],h=Math.min.apply(0,s)+o[0],c=Math.max.apply(0,s)-o[2];if(po(a)&&po(h))return{x:a,y:h,width:l-a,height:c-h}}return t.prototype.getField.call(this,e)},e.prototype.getHitZone=function(){var t=this.chart,e=this.mapView,i=this.options,o=(i.field||{}).coordinates;if(o){var r=o[0];if("percent"===i.units){var n="mapBoundingBox"===i.relativeTo&&e.getMapBBox()||ps(t.plotBox,{x:0,y:0});r=r.map(function(t){return[ph(""+t[0]+"%",n.width,n.x),ph(""+t[1]+"%",n.height,n.y)]})}return{type:"Polygon",coordinates:[r]}}},e.prototype.getProjectedBounds=function(){return pu.compositeBounds(this.allBounds)},e.prototype.isInside=function(t){var e=this.geoBoundsProjectedBox,i=this.geoBoundsProjectedPolygon;return!!(e&&t.x>=e.x1&&t.x<=e.x2&&t.y>=e.y1&&t.y<=e.y2&&i&&d9(t,i))},e.prototype.render=function(){var t=this.chart,e=this.mapView,i=this.options,o=i.borderPath||i.field;if(o&&e.group){var r=!0;this.border||(this.border=t.renderer.path().addClass("highcharts-mapview-inset-border").add(e.group),r=!1),t.styledMode||this.border.attr({stroke:i.borderColor,"stroke-width":i.borderWidth});var n=this.border.strokeWidth(),s="mapBoundingBox"===i.relativeTo&&e.getMapBBox()||e.playingField,a=(o.coordinates||[]).reduce(function(e,o){return o.reduce(function(e,o,r){var a=o[0],l=o[1];return"percent"===i.units&&(a=t.plotLeft+ph(""+a+"%",s.width,s.x),l=t.plotTop+ph(""+l+"%",s.height,s.y)),a=pt(a,n),l=pt(l,n),e.push(0===r?["M",a,l]:["L",a,l]),e},e)},[]);this.border[r?"animate":"attr"]({d:a})}},e.prototype.destroy=function(){this.border&&(this.border=this.border.destroy()),this.eventsToUnbind.forEach(function(t){return t()})},e.prototype.setUpEvents=function(){},e}(pu),pg=(j=function(t,e){return(j=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}j(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),pv=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},pm=tb.noop,py=dp.splitPath,px=sc.seriesTypes,pb=px.column,pw=px.scatter,pM=tV.extend,pk=tV.find,pS=tV.fireEvent,pT=tV.getNestedProperty,pC=tV.isArray,pA=tV.defined,pP=tV.isNumber,pO=tV.isObject,pL=tV.merge,pE=tV.objectEach,pI=tV.pick,pD=tV.splat,pB=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.processedData=[],e}return pg(e,t),e.prototype.animate=function(t){var e=this.chart,i=this.group,o=eB(this.options.animation);t?i.attr({translateX:e.plotLeft+e.plotWidth/2,translateY:e.plotTop+e.plotHeight/2,scaleX:.001,scaleY:.001}):i.animate({translateX:e.plotLeft,translateY:e.plotTop,scaleX:1,scaleY:1},o)},e.prototype.clearBounds=function(){this.points.forEach(function(t){delete t.bounds,delete t.insetIndex,delete t.projectedPath}),delete this.bounds},e.prototype.doFullTranslate=function(){return!!(this.isDirtyData||this.chart.isResizing||!this.hasRendered)},e.prototype.drawMapDataLabels=function(){t.prototype.drawDataLabels.call(this),this.dataLabelsGroup&&this.dataLabelsGroup.clip(this.chart.clipRect)},e.prototype.drawPoints=function(){var t=this,e=this,i=this.chart,o=this.group,r=this.transformGroups,n=void 0===r?[]:r,s=i.mapView,a=i.renderer;if(s){this.transformGroups=n,n[0]||(n[0]=a.g().add(o));for(var l=0,h=s.insets.length;l<h;++l)n[l+1]||n.push(a.g().add(o));this.doFullTranslate()&&(this.points.forEach(function(t){var e=t.graphic;t.group=n["number"==typeof t.insetIndex?t.insetIndex+1:0],e&&e.parentGroup!==t.group&&e.add(t.group)}),pb.prototype.drawPoints.apply(this),this.points.forEach(function(o){var r,n=o.graphic;if(n){var s=n.animate,a="";o.name&&(a+="highcharts-name-"+o.name.replace(/ /g,"-").toLowerCase()),(null===(r=o.properties)||void 0===r?void 0:r["hc-key"])&&(a+=" highcharts-key-"+o.properties["hc-key"].toString().toLowerCase()),a&&n.addClass(a),i.styledMode&&n.css(t.pointAttribs(o,o.selected&&"select"||void 0)),n.attr({visibility:!o.visible&&(o.visible||o.isNull)?"hidden":"inherit"}),n.animate=function(t,o,r){var a,l=pP(t["stroke-width"])&&!pP(n["stroke-width"]),h=pP(n["stroke-width"])&&!pP(t["stroke-width"]);if(l||h){var c=pI(e.getStrokeWidth(e.options),1)/((null===(a=i.mapView)||void 0===a?void 0:a.getScale())||1);l&&(n["stroke-width"]=c),h&&(t["stroke-width"]=c)}return s.call(n,t,o,h?function(){n.element.removeAttribute("stroke-width"),delete n["stroke-width"],r&&r.apply(this,arguments)}:r)}}})),n.forEach(function(o,r){var n=(0===r?s:s.insets[r-1]).getSVGTransform(),l=pI(t.getStrokeWidth(t.options),1),h=n.scaleX,c=n.scaleY>0?1:-1,d=function(i){(e.points||[]).forEach(function(e){var o,r=e.graphic;(null==r?void 0:r["stroke-width"])&&(o=t.getStrokeWidth(e.options))&&r.attr({"stroke-width":o/i})})};if(a.globalAnimation&&i.hasRendered&&s.allowTransformAnimation){var p=Number(o.attr("translateX")),u=Number(o.attr("translateY")),f=Number(o.attr("scaleX")),g=function(t,e){var i=f+(h-f)*e.pos;o.attr({translateX:p+(n.translateX-p)*e.pos,translateY:u+(n.translateY-u)*e.pos,scaleX:i,scaleY:i*c,"stroke-width":l/i}),d(i)},v=pL(eB(a.globalAnimation)),m=v.step;v.step=function(){m&&m.apply(this,arguments),g.apply(this,arguments)},o.attr({animator:0}).animate({animator:1},v,(function(){"boolean"!=typeof a.globalAnimation&&a.globalAnimation.complete&&a.globalAnimation.complete({applyDrilldown:!0}),pS(this,"mapZoomComplete")}).bind(t))}else ez(o),o.attr(pL(n,{"stroke-width":l/h})),d(h)}),this.isDrilling||this.drawMapDataLabels()}},e.prototype.getProjectedBounds=function(){var t=this;if(!this.bounds&&this.chart.mapView){var e=this.chart.mapView,i=e.insets,o=e.projection,r=[];(this.points||[]).forEach(function(e){if(e.path||e.geometry){if("string"==typeof e.path?e.path=py(e.path):pC(e.path)&&"M"===e.path[0]&&(e.path=t.chart.renderer.pathToSegments(e.path)),!e.bounds){var n=e.getProjectedBounds(o);if(n){e.labelrank=pI(e.labelrank,(n.x2-n.x1)*(n.y2-n.y1));var s=n.midX,a=n.midY;if(i&&pP(s)&&pP(a)){var l=pk(i,function(t){return t.isInside({x:s,y:a})});l&&(delete e.projectedPath,(n=e.getProjectedBounds(l.projection))&&l.allBounds.push(n),e.insetIndex=i.indexOf(l))}e.bounds=n}}e.bounds&&void 0===e.insetIndex&&r.push(e.bounds)}}),this.bounds=pu.compositeBounds(r)}return this.bounds},e.prototype.getStrokeWidth=function(t){var e=this.pointAttrToOptions;return t[(null==e?void 0:e["stroke-width"])||"borderWidth"]},e.prototype.hasData=function(){return!!this.dataTable.rowCount},e.prototype.pointAttribs=function(t,e){var i,o=t.series.chart,r=o.mapView,n=o.styledMode?this.colorAttribs(t):pb.prototype.pointAttribs.call(this,t,e),s=this.getStrokeWidth(t.options);if(e){var a=pL(this.options.states&&this.options.states[e],t.options.states&&t.options.states[e]||{}),l=this.getStrokeWidth(a);pA(l)&&(s=l),n.stroke=null!==(i=a.borderColor)&&void 0!==i?i:t.color}s&&r&&(s/=r.getScale());var h=this.getStrokeWidth(this.options);return n.dashstyle&&r&&pP(h)&&(s=h/r.getScale()),t.visible||(n.fill=this.options.nullColor),pA(s)?n["stroke-width"]=s:delete n["stroke-width"],n["stroke-linecap"]=n["stroke-linejoin"]=this.options.linecap,n},e.prototype.updateData=function(){return!this.processedData&&t.prototype.updateData.apply(this,arguments)},e.prototype.setData=function(e,i,o,r){void 0===i&&(i=!0),delete this.bounds,t.prototype.setData.call(this,e,!1,void 0,r),this.processData(),this.generatePoints(),i&&this.chart.redraw(o)},e.prototype.dataColumnKeys=function(){return this.pointArrayMap},e.prototype.processData=function(){var t,e,i,o=this.options,r=o.data,n=this.chart,s=n.options.chart,a=this.joinBy,l=o.keys||this.pointArrayMap,h=[],c={},d=this.chart.mapView,p=d&&(pO(o.mapData,!0)?d.getGeoMap(o.mapData):d.geoMap),u=n.mapTransforms=s.mapTransforms||(null==p?void 0:p["hc-transform"])||n.mapTransforms;u&&pE(u,function(t){t.rotation&&(t.cosAngle=Math.cos(t.rotation),t.sinAngle=Math.sin(t.rotation))}),pC(o.mapData)?i=o.mapData:p&&"FeatureCollection"===p.type&&(this.mapTitle=p.title,i=tb.geojson(p,this.type,this)),this.processedData=[];var f=this.processedData;if(r)for(var g=void 0,v=0,m=r.length;v<m;++v){if(pP(g=r[v]))f[v]={value:g};else if(pC(g)){var y=0;f[v]={},!o.keys&&g.length>l.length&&"string"==typeof g[0]&&(f[v]["hc-key"]=g[0],++y);for(var x=0;x<l.length;++x,++y)l[x]&&void 0!==g[y]&&(l[x].indexOf(".")>0?db.prototype.setNestedProperty(f[v],g[y],l[x]):f[v][l[x]]=g[y])}else f[v]=r[v];a&&"_i"===a[0]&&(f[v]._i=v)}if(i){this.mapData=i,this.mapMap={};for(var v=0;v<i.length;v++)e=(t=i[v]).properties,t._i=v,a[0]&&e&&e[a[0]]&&(t[a[0]]=e[a[0]]),c[t[a[0]]]=t;if(this.mapMap=c,a[1]){var b=a[1];f.forEach(function(t){var e=pT(b,t);c[e]&&h.push(c[e])})}if(o.allAreas){if(a[1]){var w=a[1];f.forEach(function(t){h.push(pT(w,t))})}var M="|"+h.map(function(t){return t&&t[a[0]]}).join("|")+"|";i.forEach(function(t){a[0]&&-1!==M.indexOf("|"+t[a[0]]+"|")||f.push(pL(t,{value:null}))})}}this.dataTable.rowCount=f.length},e.prototype.setOptions=function(e){var i=t.prototype.setOptions.call(this,e),o=i.joinBy;return null===i.joinBy&&(o="_i"),o&&(this.joinBy=pD(o),this.joinBy[1]||(this.joinBy[1]=this.joinBy[0])),i},e.prototype.translate=function(){var t=this.doFullTranslate(),e=this.chart.mapView,i=null==e?void 0:e.projection;if(this.chart.hasRendered&&(this.isDirtyData||!this.hasRendered)&&(this.processData(),this.generatePoints(),delete this.bounds,!e||e.userOptions.center||pP(e.userOptions.zoom)||e.zoom!==e.minZoom?this.getProjectedBounds():e.fitToBounds(void 0,void 0,!1)),e){var o=e.getSVGTransform();this.points.forEach(function(r){var n=pP(r.insetIndex)&&e.insets[r.insetIndex].getSVGTransform()||o;n&&r.bounds&&pP(r.bounds.midX)&&pP(r.bounds.midY)&&(r.plotX=r.bounds.midX*n.scaleX+n.translateX,r.plotY=r.bounds.midY*n.scaleY+n.translateY),t&&(r.shapeType="path",r.shapeArgs={d:db.getProjectedPath(r,i)}),r.hiddenInDataClass||(r.projectedPath&&!r.projectedPath.length?r.setVisible(!1):r.visible||r.setVisible(!0))})}pS(this,"afterTranslate")},e.prototype.update=function(e){var i,o=this;e.mapData&&(null===(i=this.chart.mapView)||void 0===i||i.recommendMapView(this.chart,pv([this.chart.options.chart.map],(this.chart.options.series||[]).map(function(t,i){return i===o._i?e.mapData:t.mapData}),!0),!0)),t.prototype.update.apply(this,arguments)},e.defaultOptions=pL(pw.defaultOptions,dM),e}(pw);pM(pB.prototype,{type:"map",axisTypes:dn.seriesMembers.axisTypes,colorAttribs:dn.seriesMembers.colorAttribs,colorKey:dn.seriesMembers.colorKey,directTouch:!0,drawDataLabels:pm,drawGraph:pm,forceDL:!0,getCenter:hC.getCenter,getExtremesFromAll:!0,getSymbol:pm,isCartesian:!1,parallelArrays:dn.seriesMembers.parallelArrays,pointArrayMap:dn.seriesMembers.pointArrayMap,pointClass:db,preserveAspectRatio:!0,searchPoint:pm,trackerGroups:dn.seriesMembers.trackerGroups,useMapGeometry:!0}),dn.compose(pB),sc.registerSeriesType("map",pB);var pz={lineWidth:1,fillColor:"none",legendSymbol:"lineMarker"},pj=(N=function(t,e){return(N=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}N(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),pN=tV.extend,pR=tV.merge,pW=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return pj(e,t),e.prototype.pointAttribs=function(e,i){var o=t.prototype.pointAttribs.call(this,e,i);return o.fill=this.options.fillColor,o},e.defaultOptions=pR(pB.defaultOptions,pz),e}(pB);pN(pW.prototype,{type:"mapline",colorProp:"stroke",pointAttrToOptions:{stroke:"color","stroke-width":"lineWidth"}}),sc.registerSeriesType("mapline",pW);var pX=(R=function(t,e){return(R=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}R(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),p_=sc.seriesTypes.scatter,pF=tV.isNumber,pY=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return pX(e,t),e.prototype.isValid=function(){return!!(this.options.geometry||pF(this.x)&&pF(this.y)||pF(this.options.lon)&&pF(this.options.lat))},e}(p_.prototype.pointClass),pG={dataLabels:{crop:!1,defer:!1,enabled:!0,formatter:function(){return this.point.name},overflow:!1,style:{color:"#000000"}},legendSymbol:"lineMarker"},pH=(W=function(t,e){return(W=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}W(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),pV=tb.noop,pU=sc.seriesTypes,pZ=pU.map,pq=pU.scatter,pK=tV.extend,p$=tV.fireEvent,pJ=tV.isNumber,pQ=tV.merge,p0=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.clearBounds=pZ.prototype.clearBounds,e}return pH(e,t),e.prototype.drawDataLabels=function(){t.prototype.drawDataLabels.call(this),this.dataLabelsGroup&&this.dataLabelsGroup.clip(this.chart.clipRect)},e.prototype.projectPoint=function(t){var e=this.chart.mapView;if(e){var i=t.geometry,o=t.lon,r=t.lat,n=i&&"Point"===i.type&&i.coordinates;if(pJ(o)&&pJ(r)&&(n=[o,r]),n)return e.lonLatToProjectedUnits({lon:n[0],lat:n[1]})}},e.prototype.translate=function(){var t=this,e=this.chart.mapView;if(this.generatePoints(),this.getProjectedBounds&&this.isDirtyData&&(delete this.bounds,this.getProjectedBounds()),e){var i=e.getSVGTransform(),o=e.projection.hasCoordinates;this.points.forEach(function(r){var n,s=r.x,a=void 0===s?void 0:s,l=r.y,h=void 0===l?void 0:l,c=pJ(r.insetIndex)&&e.insets[r.insetIndex].getSVGTransform()||i,d=t.projectPoint(r.options)||r.properties&&t.projectPoint(r.properties);if(d?(a=d.x,h=d.y):r.bounds&&(a=r.bounds.midX,h=r.bounds.midY,c&&pJ(a)&&pJ(h)&&(r.plotX=a*c.scaleX+c.translateX,r.plotY=h*c.scaleY+c.translateY,n=!0)),pJ(a)&&pJ(h)){if(!n){var p=e.projectedUnitsToPixels({x:a,y:h});r.plotX=p.x,r.plotY=o?p.y:t.chart.plotHeight-p.y}}else r.y=r.plotX=r.plotY=void 0;r.isInside=t.isPointInside(r),r.zone=t.zones.length?r.getZone():void 0})}p$(this,"afterTranslate")},e.defaultOptions=pQ(pq.defaultOptions,pG),e}(pq);oI.prototype.symbols.mapmarker=function(t,e,i,o,r){var n,s,a=r&&"legend"===r.context;a?(n=t+i/2,s=e+o):r&&"number"==typeof r.anchorX&&"number"==typeof r.anchorY?(n=r.anchorX,s=r.anchorY):(n=t+i/2,s=e+o/2,e-=o);var l=a?o/3:o/2;return[["M",n,s],["C",n,s,n-l,e+1.5*l,n-l,e+l],["A",l,l,1,1,1,n+l,e+l],["C",n+l,e+1.5*l,n,s,n,s],["Z"]]},pK(p0.prototype,{type:"mappoint",axisTypes:["colorAxis"],forceDL:!0,isCartesian:!1,pointClass:pY,searchPoint:pV,useMapGeometry:!0}),sc.registerSeriesType("mappoint",p0);var p1={borderColor:void 0,borderWidth:2,className:void 0,color:void 0,connectorClassName:void 0,connectorColor:void 0,connectorDistance:60,connectorWidth:1,enabled:!1,labels:{className:void 0,allowOverlap:!1,format:"",formatter:void 0,align:"right",style:{fontSize:"0.9em",color:"#000000"},x:0,y:0},maxSize:60,minSize:10,legendIndex:0,ranges:{value:void 0,borderColor:void 0,color:void 0,connectorColor:void 0},sizeBy:"area",sizeByAbsoluteValue:!1,zIndex:1,zThreshold:0},p2=tb.noop,p3=tV.arrayMax,p6=tV.arrayMin,p5=tV.isNumber,p9=tV.merge,p8=tV.pick,p4=tV.stableSort,p7=function(){function t(t,e){this.setState=p2,this.init(t,e)}return t.prototype.init=function(t,e){this.options=t,this.visible=!0,this.chart=e.chart,this.legend=e},t.prototype.addToLegend=function(t){t.splice(this.options.legendIndex,0,this)},t.prototype.drawLegendSymbol=function(t){var e,i=p8(t.options.itemDistance,20),o=this.legendItem||{},r=this.options,n=r.ranges,s=r.connectorDistance;if(!n||!n.length||!p5(n[0].value)){t.options.bubbleLegend.autoRanges=!0;return}p4(n,function(t,e){return e.value-t.value}),this.ranges=n,this.setOptions(),this.render();var a=this.getMaxLabelSize(),l=this.ranges[0].radius,h=2*l;e=(e=s-l+a.width)>0?e:0,this.maxLabel=a,this.movementX="left"===r.labels.align?e:0,o.labelWidth=h+e+i,o.labelHeight=h+a.height/2},t.prototype.setOptions=function(){var t=this.ranges,e=this.options,i=this.chart.series[e.seriesIndex],o=this.legend.baseline,r={zIndex:e.zIndex,"stroke-width":e.borderWidth},n={zIndex:e.zIndex,"stroke-width":e.connectorWidth},s={align:this.legend.options.rtl||"left"===e.labels.align?"right":"left",zIndex:e.zIndex},a=i.options.marker.fillOpacity,l=this.chart.styledMode;t.forEach(function(h,c){l||(r.stroke=p8(h.borderColor,e.borderColor,i.color),r.fill=h.color||e.color,r.fill||(r.fill=i.color,r["fill-opacity"]=null!=a?a:1),n.stroke=p8(h.connectorColor,e.connectorColor,i.color)),t[c].radius=this.getRangeRadius(h.value),t[c]=p9(t[c],{center:t[0].radius-t[c].radius+o}),l||p9(!0,t[c],{bubbleAttribs:p9(r),connectorAttribs:p9(n),labelAttribs:s})},this)},t.prototype.getRangeRadius=function(t){var e=this.options,i=this.options.seriesIndex,o=this.chart.series[i],r=e.ranges[0].value,n=e.ranges[e.ranges.length-1].value,s=e.minSize,a=e.maxSize;return o.getRadius.call(this,n,r,s,a,t)},t.prototype.render=function(){var t=this.legendItem||{},e=this.chart.renderer,i=this.options.zThreshold;this.symbols||(this.symbols={connectors:[],bubbleItems:[],labels:[]}),t.symbol=e.g("bubble-legend"),t.label=e.g("bubble-legend-item").css(this.legend.itemStyle||{}),t.symbol.translateX=0,t.symbol.translateY=0,t.symbol.add(t.label),t.label.add(t.group);for(var o=0,r=this.ranges;o<r.length;o++){var n=r[o];n.value>=i&&this.renderRange(n)}this.hideOverlappingLabels()},t.prototype.renderRange=function(t){var e=this.ranges[0],i=this.legend,o=this.options,r=o.labels,n=this.chart,s=n.series[o.seriesIndex],a=n.renderer,l=this.symbols,h=l.labels,c=t.center,d=Math.abs(t.radius),p=o.connectorDistance||0,u=r.align,f=i.options.rtl,g=o.borderWidth,v=o.connectorWidth,m=e.radius||0,y=c-d-g/2+v/2,x=(y%1?1:.5)-(v%2?0:.5),b=a.styledMode,w=f||"left"===u?-p:p;"center"===u&&(w=0,o.connectorDistance=0,t.labelAttribs.align="center"),l.bubbleItems.push(a.circle(m,c+x,d).attr(b?{}:t.bubbleAttribs).addClass((b?"highcharts-color-"+s.colorIndex+" ":"")+"highcharts-bubble-legend-symbol "+(o.className||"")).add(this.legendItem.symbol)),l.connectors.push(a.path(a.crispLine([["M",m,y],["L",m+w,y]],o.connectorWidth)).attr(b?{}:t.connectorAttribs).addClass((b?"highcharts-color-"+this.options.seriesIndex+" ":"")+"highcharts-bubble-legend-connectors "+(o.connectorClassName||"")).add(this.legendItem.symbol));var M=a.text(this.formatLabel(t)).attr(b?{}:t.labelAttribs).css(b?{}:r.style).addClass("highcharts-bubble-legend-labels "+(o.labels.className||"")).add(this.legendItem.symbol),k={x:m+w+o.labels.x,y:y+o.labels.y+.4*M.getBBox().height};M.attr(k),h.push(M),M.placed=!0,M.alignAttr=k},t.prototype.getMaxLabelSize=function(){var t,e;return this.symbols.labels.forEach(function(i){e=i.getBBox(!0),t=t?e.width>t.width?e:t:e}),t||{}},t.prototype.formatLabel=function(t){var e=this.options,i=e.labels.formatter,o=e.labels.format,r=this.chart.numberFormatter;return o?ii.format(o,t,this.chart):i?i.call(t):r(t.value,1)},t.prototype.hideOverlappingLabels=function(){var t=this.chart,e=this.options.labels.allowOverlap,i=this.symbols;!e&&i&&(t.hideOverlappingLabels(i.labels),i.labels.forEach(function(t,e){t.newOpacity?t.newOpacity!==t.oldOpacity&&i.connectors[e].show():i.connectors[e].hide()}))},t.prototype.getRanges=function(){var t,e,i=this.legend.bubbleLegend,o=i.chart.series,r=i.options.ranges,n=Number.MAX_VALUE,s=-Number.MAX_VALUE;return o.forEach(function(t){t.isBubble&&!t.ignoreSeries&&(e=t.getColumn("z").filter(p5)).length&&(n=p8(t.options.zMin,Math.min(n,Math.max(p6(e),!1===t.options.displayNegative?t.options.zThreshold:-Number.MAX_VALUE))),s=p8(t.options.zMax,Math.max(s,p3(e))))}),t=n===s?[{value:s}]:[{value:n},{value:(n+s)/2},{value:s,autoRanges:!0}],r.length&&r[0].radius&&t.reverse(),t.forEach(function(e,i){r&&r[i]&&(t[i]=p9(r[i],e))}),t},t.prototype.predictBubbleSizes=function(){var t,e=this.chart,i=e.legend.options,o=i.floating,r="horizontal"===i.layout,n=r?e.legend.lastLineHeight:0,s=e.plotSizeX,a=e.plotSizeY,l=e.series[this.options.seriesIndex],h=l.getPxExtremes(),c=Math.ceil(h.minPxSize),d=Math.ceil(h.maxPxSize),p=Math.min(a,s),u=l.options.maxSize;return o||!/%$/.test(u)?t=d:(t=(p+n)*(u=parseFloat(u))/100/(u/100+1),(r&&a-t>=s||!r&&s-t>=a)&&(t=d)),[c,Math.ceil(t)]},t.prototype.updateRanges=function(t,e){var i=this.legend.options.bubbleLegend;i.minSize=t,i.maxSize=e,i.ranges=this.getRanges()},t.prototype.correctSizes=function(){var t=this.legend,e=this.chart.series[this.options.seriesIndex].getPxExtremes();Math.abs(Math.ceil(e.maxPxSize)-this.options.maxSize)>1&&(this.updateRanges(this.options.minSize,e.maxPxSize),t.render())},t}(),ut=tb.composed,ue=tV.addEvent,ui=tV.objectEach,uo=tV.pushUnique,ur=tV.wrap;function un(t,e,i){var o,r,n,s=this.legend,a=us(this)>=0;s&&s.options.enabled&&s.bubbleLegend&&s.options.bubbleLegend.autoRanges&&a?(o=s.bubbleLegend.options,r=s.bubbleLegend.predictBubbleSizes(),s.bubbleLegend.updateRanges(r[0],r[1]),o.placed||(s.group.placed=!1,s.allItems.forEach(function(t){(n=t.legendItem||{}).group&&(n.group.translateY=void 0)})),s.render(),o.placed||(this.getMargins(),this.axes.forEach(function(t){t.setScale(),t.updateNames(),ui(t.ticks,function(t){t.isNew=!0,t.isNewLabel=!0})}),this.getMargins()),o.placed=!0,t.call(this,e,i),s.bubbleLegend.correctSizes(),uc(s,ua(s))):(t.call(this,e,i),s&&s.options.enabled&&s.bubbleLegend&&(s.render(),uc(s,ua(s))))}function us(t){for(var e=t.series,i=0;i<e.length;){if(e[i]&&e[i].isBubble&&e[i].visible&&e[i].dataTable.rowCount)return i;i++}return -1}function ua(t){var e,i,o,r=t.allItems,n=[],s=r.length,a=0,l=0;for(a=0;a<s;a++)if(i=r[a].legendItem||{},o=(r[a+1]||{}).legendItem||{},i.labelHeight&&(r[a].itemHeight=i.labelHeight),r[a]===r[s-1]||i.y!==o.y){for(n.push({height:0}),e=n[n.length-1];l<=a;l++)r[l].itemHeight>e.height&&(e.height=r[l].itemHeight);e.step=a}return n}function ul(t){var e=this.bubbleLegend,i=this.options,o=i.bubbleLegend,r=us(this.chart);e&&e.ranges&&e.ranges.length&&(o.ranges.length&&(o.autoRanges=!!o.ranges[0].autoRanges),this.destroyItem(e)),r>=0&&i.enabled&&o.enabled&&(o.seriesIndex=r,this.bubbleLegend=new p7(o,this),this.bubbleLegend.addToLegend(t.allItems))}function uh(t){if(t.defaultPrevented)return!1;var e,i=t.legendItem,o=this.chart,r=i.visible;this&&this.bubbleLegend&&(i.visible=!r,i.ignoreSeries=r,e=us(o)>=0,this.bubbleLegend.visible!==e&&(this.update({bubbleLegend:{enabled:e}}),this.bubbleLegend.visible=e),i.visible=r)}function uc(t,e){var i,o,r,n,s=t.allItems,a=t.options.rtl,l=0;s.forEach(function(t,s){(n=t.legendItem||{}).group&&(i=n.group.translateX||0,o=n.y||0,((r=t.movementX)||a&&t.ranges)&&(r=a?i-t.options.maxSize/2:i+r,n.group.attr({translateX:r})),s>e[l].step&&l++,n.group.attr({translateY:Math.round(o+e[l].height/2)}),n.y=o+e[l].height/2)})}var ud=function(t,e){uo(ut,"Series.BubbleLegend")&&(ed({legend:{bubbleLegend:p1}}),ur(t.prototype,"drawChartBox",un),ue(e,"afterGetAllItems",ul),ue(e,"itemClick",uh))},up=(X=function(t,e){return(X=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}X(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),uu=sc.seriesTypes.scatter.prototype.pointClass,uf=tV.extend,ug=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return up(e,t),e.prototype.haloPath=function(t){var e=(t&&this.marker&&this.marker.radius||0)+t;if(this.series.chart.inverted){var i=this.pos()||[0,0],o=this.series,r=o.xAxis,n=o.yAxis;return o.chart.renderer.symbols.circle(r.len-i[1]-e,n.len-i[0]-e,2*e,2*e)}return nz.prototype.haloPath.call(this,e)},e}(uu);uf(ug.prototype,{ttBelow:!1});var uv=(_=function(t,e){return(_=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}_(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),um=tb.composed,uy=tb.noop,ux=sc.series,ub=sc.seriesTypes,uw=ub.column.prototype,uM=ub.scatter,uk=tV.addEvent,uS=tV.arrayMax,uT=tV.arrayMin,uC=tV.clamp,uA=tV.extend,uP=tV.isNumber,uO=tV.merge,uL=tV.pick,uE=tV.pushUnique;function uI(){var t,e=this,i=this.len,o=this.coll,r=this.isXAxis,n=this.min,s=(this.max||0)-(n||0),a=0,l=i,h=i/s;("xAxis"===o||"yAxis"===o)&&(this.series.forEach(function(i){if(i.bubblePadding&&i.reserveSpace()){e.allowZoomOutside=!0,t=!0;var o=i.getColumn(r?"x":"y");if(r&&((i.onPoint||i).getRadii(0,0,i),i.onPoint&&(i.radii=i.onPoint.radii)),s>0){for(var c=o.length;c--;)if(uP(o[c])&&e.dataMin<=o[c]&&o[c]<=e.max){var d=i.radii&&i.radii[c]||0;a=Math.min((o[c]-n)*h-d,a),l=Math.max((o[c]-n)*h+d,l)}}}}),t&&s>0&&!this.logarithmic&&(l-=i,h*=(i+Math.max(0,a)-Math.min(l,i))/i,[["min","userMin",a],["max","userMax",l]].forEach(function(t){void 0===uL(e.options[t[0]],e[t[1]])&&(e[t[0]]+=t[2]/h)})))}function uD(){var t,e=this.ticks,i=this.tickPositions,o=this.dataMin,r=void 0===o?0:o,n=this.dataMax,s=void 0===n?0:n,a=this.categories,l=this.options.type;if(((null==a?void 0:a.length)||"category"===l)&&this.series.find(function(t){return t.bubblePadding}))for(var h=i.length;h--;){var c=e[i[h]],d=c.pos||0;(d>s||d<r)&&(null===(t=c.label)||void 0===t||t.hide())}}var uB=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return uv(e,t),e.compose=function(t,e,i){ud(e,i),uE(um,"Series.Bubble")&&(uk(t,"foundExtremes",uI),uk(t,"afterRender",uD))},e.prototype.animate=function(t){!t&&this.points.length<this.options.animationLimit&&this.points.forEach(function(t){var e=t.graphic,i=t.plotX,o=t.plotY;e&&e.width&&(this.hasRendered||e.attr({x:void 0===i?0:i,y:void 0===o?0:o,width:1,height:1}),e.animate(this.markerAttribs(t),this.options.animation))},this)},e.prototype.getRadii=function(){var t,e,i,o=this.getColumn("z"),r=this.getColumn("y"),n=[],s=this.chart.bubbleZExtremes,a=this.getPxExtremes(),l=a.minPxSize,h=a.maxPxSize;if(!s){var c,d=Number.MAX_VALUE,p=-Number.MAX_VALUE;this.chart.series.forEach(function(t){if(t.bubblePadding&&t.reserveSpace()){var e=(t.onPoint||t).getZExtremes();e&&(d=Math.min(uL(d,e.zMin),e.zMin),p=Math.max(uL(p,e.zMax),e.zMax),c=!0)}}),c?(s={zMin:d,zMax:p},this.chart.bubbleZExtremes=s):s={zMin:0,zMax:0}}for(e=0,t=o.length;e<t;e++)i=o[e],n.push(this.getRadius(s.zMin,s.zMax,l,h,i,r&&r[e]));this.radii=n},e.prototype.getRadius=function(t,e,i,o,r,n){var s=this.options,a="width"!==s.sizeBy,l=s.zThreshold,h=e-t,c=.5;if(null===n||null===r)return null;if(uP(r)){if(s.sizeByAbsoluteValue&&(r=Math.abs(r-l),e=h=Math.max(e-l,Math.abs(t-l)),t=0),r<t)return i/2-1;h>0&&(c=(r-t)/h)}return a&&c>=0&&(c=Math.sqrt(c)),Math.ceil(i+c*(o-i))/2},e.prototype.hasData=function(){return!!this.dataTable.rowCount},e.prototype.markerAttribs=function(e,i){var o=t.prototype.markerAttribs.call(this,e,i),r=o.height,n=o.width;return this.chart.inverted?uA(o,{x:(e.plotX||0)-(void 0===n?0:n)/2,y:(e.plotY||0)-(void 0===r?0:r)/2}):o},e.prototype.pointAttribs=function(t,e){var i=this.options.marker,o=null==i?void 0:i.fillOpacity,r=ux.prototype.pointAttribs.call(this,t,e);return r["fill-opacity"]=null!=o?o:1,r},e.prototype.translate=function(){t.prototype.translate.call(this),this.getRadii(),this.translateBubble()},e.prototype.translateBubble=function(){for(var t=this.data,e=this.options,i=this.radii,o=this.getPxExtremes().minPxSize,r=t.length;r--;){var n=t[r],s=i?i[r]:0;"z"===this.zoneAxis&&(n.negative=(n.z||0)<(e.zThreshold||0)),uP(s)&&s>=o/2?(n.marker=uA(n.marker,{radius:s,width:2*s,height:2*s}),n.dlBox={x:n.plotX-s,y:n.plotY-s,width:2*s,height:2*s}):(n.shapeArgs=n.plotY=n.dlBox=void 0,n.isInside=!1)}},e.prototype.getPxExtremes=function(){var t=Math.min(this.chart.plotWidth,this.chart.plotHeight),e=function(e){var i;return"string"==typeof e&&(i=/%$/.test(e),e=parseInt(e,10)),i?t*e/100:e},i=e(uL(this.options.minSize,8)),o=Math.max(e(uL(this.options.maxSize,"20%")),i);return{minPxSize:i,maxPxSize:o}},e.prototype.getZExtremes=function(){var t=this.options,e=this.getColumn("z").filter(uP);if(e.length){var i=uL(t.zMin,uC(uT(e),!1===t.displayNegative?t.zThreshold||0:-Number.MAX_VALUE,Number.MAX_VALUE)),o=uL(t.zMax,uS(e));if(uP(i)&&uP(o))return{zMin:i,zMax:o}}},e.prototype.searchKDTree=function(e,i,o,r,n){return void 0===r&&(r=uy),void 0===n&&(n=uy),r=function(t,e,i){var o,r,n,s=t[i]||0,a=e[i]||0,l=!1;return s===a?n=t.index>e.index?t:e:s<0&&a<0?(n=s-((null===(o=t.marker)||void 0===o?void 0:o.radius)||0)>=a-((null===(r=e.marker)||void 0===r?void 0:r.radius)||0)?t:e,l=!0):n=s<a?t:e,[n,l]},n=function(t,e,i){return!i&&t>e||t<e},t.prototype.searchKDTree.call(this,e,i,o,r,n)},e.defaultOptions=uO(uM.defaultOptions,{dataLabels:{formatter:function(){var t=this.series.chart.numberFormatter,e=this.point.z;return uP(e)?t(e,-1):""},inside:!0,verticalAlign:"middle"},animationLimit:250,marker:{lineColor:null,lineWidth:1,fillOpacity:.5,radius:null,states:{hover:{radiusPlus:0}},symbol:"circle"},minSize:8,maxSize:"20%",softThreshold:!1,states:{hover:{halo:{size:5}}},tooltip:{pointFormat:"({point.x}, {point.y}), Size: {point.z}"},turboThreshold:0,zThreshold:0,zoneAxis:"z"}),e}(uM);uA(uB.prototype,{alignDataLabel:uw.alignDataLabel,applyZones:uy,bubblePadding:!0,isBubble:!0,keysAffectYAxis:["y"],pointArrayMap:["y","z"],pointClass:ug,parallelArrays:["x","y","z"],trackerGroups:["group","dataLabelsGroup"],specialGroup:"group",zoneAxis:"z"}),uk(uB,"updatedData",function(t){delete t.target.chart.bubbleZExtremes}),uk(uB,"remove",function(t){delete t.target.chart.bubbleZExtremes}),sc.registerSeriesType("bubble",uB);var uz=(F=function(t,e){return(F=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}F(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),uj=sc.seriesTypes.map.prototype.pointClass.prototype,uN=tV.extend,uR=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return uz(e,t),e.prototype.isValid=function(){return"number"==typeof this.z},e}(ug);uN(uR.prototype,{applyOptions:uj.applyOptions,getProjectedBounds:uj.getProjectedBounds});var uW=(Y=function(t,e){return(Y=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}Y(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),uX=sc.seriesTypes,u_=uX.map.prototype,uF=uX.mappoint.prototype,uY=tV.extend,uG=tV.merge,uH=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.clearBounds=u_.clearBounds,e}return uW(e,t),e.prototype.searchPoint=function(t,e){return this.searchKDTree({plotX:t.chartX-this.chart.plotLeft,plotY:t.chartY-this.chart.plotTop},e,t)},e.prototype.translate=function(){uF.translate.call(this),this.getRadii(),this.translateBubble()},e.defaultOptions=uG(uB.defaultOptions,{lineWidth:0,animationLimit:500,joinBy:"hc-key",tooltip:{pointFormat:"{point.name}: {point.z}"}}),e}(uB);uY(uH.prototype,{type:"mapbubble",axisTypes:["colorAxis"],getProjectedBounds:u_.getProjectedBounds,isCartesian:!1,pointArrayMap:["z"],pointClass:uR,processData:u_.processData,projectPoint:uF.projectPoint,kdAxisArray:["plotX","plotY"],setData:u_.setData,setOptions:u_.setOptions,updateData:u_.updateData,useMapGeometry:!0,xyFromShape:!0}),sc.registerSeriesType("mapbubble",uH);var uV=(G=function(t,e){return(G=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}G(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),uU=sc.seriesTypes.scatter.prototype.pointClass,uZ=tV.clamp,uq=tV.defined,uK=tV.extend,u$=tV.pick,uJ=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return uV(e,t),e.prototype.applyOptions=function(e,i){return(this.isNull||null===this.value)&&delete this.color,t.prototype.applyOptions.call(this,e,i),this.formatPrefix=this.isNull||null===this.value?"null":"point",this},e.prototype.getCellAttributes=function(){for(var t=this.series,e=t.options,i=(e.colsize||1)/2,o=(e.rowsize||1)/2,r=t.xAxis,n=t.yAxis,s=this.options.marker||t.options.marker,a=t.pointPlacementToXValue(),l=u$(this.pointPadding,e.pointPadding,0),h={x1:uZ(Math.round(r.len-r.translate(this.x-i,!1,!0,!1,!0,-a)),-r.len,2*r.len),x2:uZ(Math.round(r.len-r.translate(this.x+i,!1,!0,!1,!0,-a)),-r.len,2*r.len),y1:uZ(Math.round(n.translate(this.y-o,!1,!0,!1,!0)),-n.len,2*n.len),y2:uZ(Math.round(n.translate(this.y+o,!1,!0,!1,!0)),-n.len,2*n.len)},c=0,d=[["width","x"],["height","y"]];c<d.length;c++){var p=d[c],u=p[0],f=p[1],g=f+"1",v=f+"2",m=Math.abs(h[g]-h[v]),y=s&&s.lineWidth||0,x=Math.abs(h[g]+h[v])/2,b=s&&s[u];if(uq(b)&&b<m){var w=b/2+y/2;h[g]=x-w,h[v]=x+w}l&&(("x"===f&&r.reversed||"y"===f&&!n.reversed)&&(g=v,v=f+"1"),h[g]+=l,h[v]-=l)}return h},e.prototype.haloPath=function(t){if(!t)return[];var e=this.shapeArgs||{},i=e.x,o=void 0===i?0:i,r=e.y,n=void 0===r?0:r,s=e.width,a=void 0===s?0:s,l=e.height,h=void 0===l?0:l;return[["M",o-t,n-t],["L",o-t,n+h+t],["L",o+a+t,n+h+t],["L",o+a+t,n-t],["Z"]]},e.prototype.isValid=function(){return this.value!==1/0&&this.value!==-1/0},e}(uU);uK(uJ.prototype,{dataLabelOnNull:!0,moveToTopOnHover:!0,ttBelow:!1});var uQ=tV.isNumber,u0={animation:!1,borderRadius:0,borderWidth:0,interpolation:!1,nullColor:"#f7f7f7",dataLabels:{formatter:function(){var t=this.series.chart.numberFormatter,e=this.point.value;return uQ(e)?t(e,-1):""},inside:!0,verticalAlign:"middle",crop:!1,overflow:"allow",padding:0},marker:{symbol:"rect",radius:0,lineColor:void 0,states:{hover:{lineWidthPlus:0},select:{}}},clip:!0,pointRange:null,tooltip:{pointFormat:"{point.x}, {point.y}: {point.value}<br/>"},states:{hover:{halo:!1,brightness:.2}},legendSymbol:"rectangle"},u1=tb.doc,u2=tV.defined,u3=tV.pick,u6=function(t){var e=t.canvas,i=t.context;return e&&i?(i.clearRect(0,0,e.width,e.height),i):(t.canvas=u1.createElement("canvas"),t.context=t.canvas.getContext("2d",{willReadFrequently:!0})||void 0,t.context)},u5=(H=function(t,e){return(H=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}H(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),u9=function(){return(u9=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},u8=sc.series,u4=sc.seriesTypes,u7=u4.column,ft=u4.scatter,fe=oI.prototype.symbols,fi=tV.addEvent,fo=tV.extend,fr=tV.fireEvent,fn=tV.isNumber,fs=tV.merge,fa=tV.pick,fl=function(t,e){var i=e.series.colorAxis;if(i){var o=i.toColor(t||0,e).split(")")[0].split("(")[1].split(",").map(function(t){return u3(parseFloat(t),parseInt(t,10))});return o[3]=255*u3(o[3],1),u2(t)&&e.visible||(o[3]=0),o}return[0,0,0,0]},fh=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.valueMax=NaN,e.valueMin=NaN,e.isDirtyCanvas=!0,e}return u5(e,t),e.prototype.drawPoints=function(){var t=this,e=t.options,i=e.interpolation,o=e.marker||{};if(i){var r=t.image,n=t.chart,s=t.xAxis,a=t.yAxis,l=s.reversed,h=s.len,c=a.reversed,d=a.len,p={width:h,height:d};if(!r||t.isDirtyData||t.isDirtyCanvas){var u=u6(t),f=t.canvas,g=t.options,v=g.colsize,m=g.rowsize,y=t.points,x=t.points.length,b=n.colorAxis&&n.colorAxis[0];if(f&&u&&b){var w=s.getExtremes(),M=w.min,k=w.max,S=a.getExtremes(),T=S.min,C=S.max,A=k-M,P=C-T,O=Math.round(A/(void 0===v?1:v)/8*8),L=Math.round(P/(void 0===m?1:m)/8*8),E=[[O,O/A,void 0!==l&&l,"ceil"],[L,L/P,!(void 0!==c&&c),"floor"]].map(function(t){var e=t[0],i=t[1],o=t[2],r=t[3];return o?function(t){return Math[r](e-i*t)}:function(t){return Math[r](i*t)}}),I=E[0],D=E[1],B=f.width=O+1,z=B*(f.height=L+1),j=(x-1)/z,N=new Uint8ClampedArray(4*z);t.buildKDTree();for(var R=0;R<z;R++){var W=y[Math.ceil(j*R)],X=W.x,_=W.y;N.set(fl(W.value,W),4*Math.ceil(B*D(_-T)+I(X-M)))}u.putImageData(new ImageData(N,B),0,0),r?r.attr(u9(u9({},p),{href:f.toDataURL("image/png",1)})):(t.directTouch=!1,t.image=n.renderer.image(f.toDataURL("image/png",1)).attr(p).add(t.group))}t.isDirtyCanvas=!1}else(r.width!==h||r.height!==d)&&r.attr(p)}else(o.enabled||t._hasPointMarkers)&&(u8.prototype.drawPoints.call(t),t.points.forEach(function(e){e.graphic&&(e.graphic[t.chart.styledMode?"css":"animate"](t.colorAttribs(e)),null===e.value&&e.graphic.addClass("highcharts-null-point"))}))},e.prototype.getExtremes=function(){var t=u8.prototype.getExtremes.call(this,this.getColumn("value")),e=t.dataMin,i=t.dataMax;return fn(e)&&(this.valueMin=e),fn(i)&&(this.valueMax=i),u8.prototype.getExtremes.call(this)},e.prototype.getValidPoints=function(t,e){return u8.prototype.getValidPoints.call(this,t,e,!0)},e.prototype.hasData=function(){return!!this.dataTable.rowCount},e.prototype.init=function(){t.prototype.init.apply(this,arguments);var e=this.options;e.pointRange=fa(e.pointRange,e.colsize||1),this.yAxis.axisPointRange=e.rowsize||1,fe.ellipse=fe.circle,e.marker&&fn(e.borderRadius)&&(e.marker.r=e.borderRadius)},e.prototype.markerAttribs=function(t,e){var i,o,r=t.shapeArgs||{};if(t.hasImage)return{x:t.plotX,y:t.plotY};if(e&&"normal"!==e){var n=t.options.marker||{},s=(null===(i=(this.options.marker||{}).states)||void 0===i?void 0:i[e])||{},a=(null===(o=n.states)||void 0===o?void 0:o[e])||{},l=(a.width||s.width||r.width||0)+(a.widthPlus||s.widthPlus||0),h=(a.height||s.height||r.height||0)+(a.heightPlus||s.heightPlus||0);return{x:(r.x||0)+((r.width||0)-l)/2,y:(r.y||0)+((r.height||0)-h)/2,width:l,height:h}}return r},e.prototype.pointAttribs=function(t,e){var i,o,r,n,s,a,l=u8.prototype.pointAttribs.call(this,t,e),h=this.options||{},c=this.chart.options.plotOptions||{},d=c.series||{},p=c.heatmap||{},u=(null==t?void 0:t.options.borderColor)||h.borderColor||p.borderColor||d.borderColor,f=(null==t?void 0:t.options.borderWidth)||h.borderWidth||p.borderWidth||d.borderWidth||l["stroke-width"];if(l.stroke=(null===(i=null==t?void 0:t.marker)||void 0===i?void 0:i.lineColor)||(null===(o=h.marker)||void 0===o?void 0:o.lineColor)||u||this.color,l["stroke-width"]=f,e&&"normal"!==e){var g=fs(null===(r=h.states)||void 0===r?void 0:r[e],null===(s=null===(n=h.marker)||void 0===n?void 0:n.states)||void 0===s?void 0:s[e],(null===(a=null==t?void 0:t.options.states)||void 0===a?void 0:a[e])||{});l.fill=g.color||eb.parse(l.fill).brighten(g.brightness||0).get(),l.stroke=g.lineColor||l.stroke}return l},e.prototype.translate=function(){var t,e=this.options,i=e.borderRadius,o=e.marker,r=(null==o?void 0:o.symbol)||"rect",n=fe[r]?r:"rect",s=-1!==["circle","square"].indexOf(n);this.generatePoints();for(var a=0,l=this.points;a<l.length;a++){var h=l[a],c=h.getCellAttributes(),d=Math.min(c.x1,c.x2),p=Math.min(c.y1,c.y2),u=Math.max(Math.abs(c.x2-c.x1),0),f=Math.max(Math.abs(c.y2-c.y1),0);if(h.hasImage=0===((null===(t=h.marker)||void 0===t?void 0:t.symbol)||r||"").indexOf("url"),s){var g=Math.abs(u-f);d=Math.min(c.x1,c.x2)+(u<f?0:g/2),p=Math.min(c.y1,c.y2)+(u<f?g/2:0),u=f=Math.min(u,f)}h.hasImage&&(h.marker={width:u,height:f}),h.plotX=h.clientX=(c.x1+c.x2)/2,h.plotY=(c.y1+c.y2)/2,h.shapeType="path",h.shapeArgs=fs(!0,{x:d,y:p,width:u,height:f},{d:fe[n](d,p,u,f,{r:fn(i)?i:0})})}fr(this,"afterTranslate")},e.defaultOptions=fs(ft.defaultOptions,u0),e}(ft);fi(fh,"afterDataClassLegendClick",function(){this.isDirtyCanvas=!0,this.drawPoints()}),fo(fh.prototype,{axisTypes:dn.seriesMembers.axisTypes,colorKey:dn.seriesMembers.colorKey,directTouch:!0,getExtremesFromAll:!0,keysAffectYAxis:["y"],parallelArrays:dn.seriesMembers.parallelArrays,pointArrayMap:["y","value"],pointClass:uJ,specialGroup:"group",trackerGroups:dn.seriesMembers.trackerGroups,alignDataLabel:u7.prototype.alignDataLabel,colorAttribs:dn.seriesMembers.colorAttribs,getSymbol:u8.prototype.getSymbol}),dn.compose(fh),sc.registerSeriesType("heatmap",fh),tb.ColorMapComposition=dn,tb.MapChart=tb.MapChart||dp,tb.MapNavigation=tb.MapNavigation||dt,tb.MapView=tb.MapView||pu,tb.Projection=tb.Projection||d2,tb.mapChart=tb.Map=tb.MapChart.mapChart,tb.maps=tb.MapChart.maps,tb.geojson=dL.geojson,tb.topo2geo=dL.topo2geo,dL.compose(tb.Chart),uH.compose(tb.Axis,tb.Chart,tb.Legend),dt.compose(dp,tb.Pointer,tb.SVGRenderer),pu.compose(dp),tb.product="Highmaps";var fc=tb;return tx.default}()});