{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/highcharts-3d\n * @requires highcharts\n *\n * 3D features for Highcharts JS\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"Color\"], require(\"highcharts\")[\"SeriesRegistry\"], require(\"highcharts\")[\"RendererRegistry\"], require(\"highcharts\")[\"Series\"], require(\"highcharts\")[\"StackItem\"], require(\"highcharts\")[\"Axis\"], require(\"highcharts\")[\"Series\"][\"types\"][\"scatter\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/highcharts-3d\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"Color\"], [\"highcharts/highcharts\",\"SeriesRegistry\"], [\"highcharts/highcharts\",\"RendererRegistry\"], [\"highcharts/highcharts\",\"Series\"], [\"highcharts/highcharts\",\"StackItem\"], [\"highcharts/highcharts\",\"Axis\"], [\"highcharts/highcharts\",\"Series\",\"types\",\"scatter\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/highcharts-3d\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"Color\"], require(\"highcharts\")[\"SeriesRegistry\"], require(\"highcharts\")[\"RendererRegistry\"], require(\"highcharts\")[\"Series\"], require(\"highcharts\")[\"StackItem\"], require(\"highcharts\")[\"Axis\"], require(\"highcharts\")[\"Series\"][\"types\"][\"scatter\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"RendererRegistry\"], root[\"Highcharts\"][\"Series\"], root[\"Highcharts\"][\"StackItem\"], root[\"Highcharts\"][\"Axis\"], root[\"Highcharts\"][\"Series\"][\"types\"][\"scatter\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__608__, __WEBPACK_EXTERNAL_MODULE__820__, __WEBPACK_EXTERNAL_MODULE__184__, __WEBPACK_EXTERNAL_MODULE__532__, __WEBPACK_EXTERNAL_MODULE__632__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 184:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__184__;\n\n/***/ }),\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 532:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__532__;\n\n/***/ }),\n\n/***/ 608:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__608__;\n\n/***/ }),\n\n/***/ 620:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 632:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__632__;\n\n/***/ }),\n\n/***/ 820:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__820__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ highcharts_3d_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n;// ./code/es5/es-modules/Core/Math3D.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar deg2rad = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).deg2rad;\n\nvar pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Functions\n *\n * */\n/* eslint-disable max-len */\n/**\n * Apply 3-D rotation\n * Euler Angles (XYZ):\n *     cosA = cos(Alfa|Roll)\n *     cosB = cos(Beta|Pitch)\n *     cosG = cos(Gamma|Yaw)\n *\n * Composite rotation:\n * |          cosB * cosG             |           cosB * sinG            |    -sinB    |\n * | sinA * sinB * cosG - cosA * sinG | sinA * sinB * sinG + cosA * cosG | sinA * cosB |\n * | cosA * sinB * cosG + sinA * sinG | cosA * sinB * sinG - sinA * cosG | cosA * cosB |\n *\n * Now, Gamma/Yaw is not used (angle=0), so we assume cosG = 1 and sinG = 0, so\n * we get:\n * |     cosB    |   0    |   - sinB    |\n * | sinA * sinB |  cosA  | sinA * cosB |\n * | cosA * sinB | - sinA | cosA * cosB |\n *\n * But in browsers, y is reversed, so we get sinA => -sinA. The general result\n * is:\n * |      cosB     |   0    |    - sinB     |     | x |     | px |\n * | - sinA * sinB |  cosA  | - sinA * cosB |  x  | y |  =  | py |\n * |  cosA * sinB  |  sinA  |  cosA * cosB  |     | z |     | pz |\n *\n * @private\n * @function rotate3D\n */\n/* eslint-enable max-len */\n/**\n * Rotates the position as defined in angles.\n * @private\n * @param {number} x\n *        X coordinate\n * @param {number} y\n *        Y coordinate\n * @param {number} z\n *        Z coordinate\n * @param {Highcharts.Rotation3DObject} angles\n *        Rotation angles\n * @return {Highcharts.Position3DObject}\n *         Rotated position\n */\nfunction rotate3D(x, y, z, angles) {\n    return {\n        x: angles.cosB * x - angles.sinB * z,\n        y: -angles.sinA * angles.sinB * x + angles.cosA * y -\n            angles.cosB * angles.sinA * z,\n        z: angles.cosA * angles.sinB * x + angles.sinA * y +\n            angles.cosA * angles.cosB * z\n    };\n}\n/**\n * Transforms a given array of points according to the angles in chart.options.\n *\n * @private\n * @function Highcharts.perspective\n *\n * @param {Array<Highcharts.Position3DObject>} points\n * The array of points\n *\n * @param {Highcharts.Chart} chart\n * The chart\n *\n * @param {boolean} [insidePlotArea]\n * Whether to verify that the points are inside the plotArea\n *\n * @param {boolean} [useInvertedPersp]\n * Whether to use inverted perspective in calculations\n *\n * @return {Array<Highcharts.Position3DObject>}\n * An array of transformed points\n *\n * @requires highcharts-3d\n */\nfunction perspective(points, chart, insidePlotArea, useInvertedPersp) {\n    var options3d = chart.options.chart.options3d, \n        /* The useInvertedPersp argument is used for inverted charts with\n         * already inverted elements,\n        such as dataLabels or tooltip positions.\n         */\n        inverted = pick(useInvertedPersp,\n        insidePlotArea ? chart.inverted : false),\n        origin = {\n            x: chart.plotWidth / 2,\n            y: chart.plotHeight / 2,\n            z: options3d.depth / 2,\n            vd: pick(options3d.depth, 1) * pick(options3d.viewDistance, 0)\n        },\n        scale = chart.scale3d || 1,\n        beta = deg2rad * options3d.beta * (inverted ? -1 : 1),\n        alpha = deg2rad * options3d.alpha * (inverted ? -1 : 1),\n        angles = {\n            cosA: Math.cos(alpha),\n            cosB: Math.cos(-beta),\n            sinA: Math.sin(alpha),\n            sinB: Math.sin(-beta)\n        };\n    if (!insidePlotArea) {\n        origin.x += chart.plotLeft;\n        origin.y += chart.plotTop;\n    }\n    // Transform each point\n    return points.map(function (point) {\n        var rotated = rotate3D((inverted ? point.y : point.x) - origin.x, (inverted ? point.x : point.y) - origin.y, (point.z || 0) - origin.z,\n            angles), \n            // Apply perspective\n            coordinate = perspective3D(rotated,\n            origin,\n            origin.vd);\n        // Apply translation\n        coordinate.x = coordinate.x * scale + origin.x;\n        coordinate.y = coordinate.y * scale + origin.y;\n        coordinate.z = rotated.z * scale + origin.z;\n        return {\n            x: (inverted ? coordinate.y : coordinate.x),\n            y: (inverted ? coordinate.x : coordinate.y),\n            z: coordinate.z\n        };\n    });\n}\n/**\n * Perspective3D function is available in global Highcharts scope because is\n * needed also outside of perspective() function (#8042).\n * @private\n * @function Highcharts.perspective3D\n *\n * @param {Highcharts.Position3DObject} coordinate\n * 3D position\n *\n * @param {Highcharts.Position3DObject} origin\n * 3D root position\n *\n * @param {number} distance\n * Perspective distance\n *\n * @return {Highcharts.PositionObject}\n * Perspective 3D Position\n *\n * @requires highcharts-3d\n */\nfunction perspective3D(coordinate, origin, distance) {\n    var projection = ((distance > 0) &&\n            (distance < Number.POSITIVE_INFINITY)) ?\n            distance / (coordinate.z + origin.z + distance) :\n            1;\n    return {\n        x: coordinate.x * projection,\n        y: coordinate.y * projection\n    };\n}\n/**\n * Calculate a distance from camera to points - made for calculating zIndex of\n * scatter points.\n *\n * @private\n * @function Highcharts.pointCameraDistance\n *\n * @param {Highcharts.Dictionary<number>} coordinates\n * Coordinates of the specific point\n *\n * @param {Highcharts.Chart} chart\n * Related chart\n *\n * @return {number}\n * Distance from camera to point\n *\n * @requires highcharts-3d\n */\nfunction pointCameraDistance(coordinates, chart) {\n    var options3d = chart.options.chart.options3d,\n        cameraPosition = {\n            x: chart.plotWidth / 2,\n            y: chart.plotHeight / 2,\n            z: pick(options3d.depth, 1) * pick(options3d.viewDistance, 0) +\n                options3d.depth\n        }, \n        // Added support for objects with plotX or x coordinates.\n        distance = Math.sqrt(Math.pow(cameraPosition.x - pick(coordinates.plotX,\n        coordinates.x), 2) +\n            Math.pow(cameraPosition.y - pick(coordinates.plotY,\n        coordinates.y), 2) +\n            Math.pow(cameraPosition.z - pick(coordinates.plotZ,\n        coordinates.z), 2));\n    return distance;\n}\n/**\n * Calculate area of a 2D polygon using Shoelace algorithm\n * https://en.wikipedia.org/wiki/Shoelace_formula\n *\n * @private\n * @function Highcharts.shapeArea\n *\n * @param {Array<Highcharts.PositionObject>} vertexes\n * 2D Polygon\n *\n * @return {number}\n * Calculated area\n *\n * @requires highcharts-3d\n */\nfunction shapeArea(vertexes) {\n    var area = 0,\n        i,\n        j;\n    for (i = 0; i < vertexes.length; i++) {\n        j = (i + 1) % vertexes.length;\n        area += vertexes[i].x * vertexes[j].y - vertexes[j].x * vertexes[i].y;\n    }\n    return area / 2;\n}\n/**\n * Calculate area of a 3D polygon after perspective projection\n *\n * @private\n * @function Highcharts.shapeArea3d\n *\n * @param {Array<Highcharts.Position3DObject>} vertexes\n * 3D Polygon\n *\n * @param {Highcharts.Chart} chart\n * Related chart\n *\n * @param {boolean} [insidePlotArea]\n * Whether to verify that the points are inside the plotArea\n *\n * @return {number}\n * Calculated area\n *\n * @requires highcharts-3d\n */\nfunction shapeArea3D(vertexes, chart, insidePlotArea) {\n    return shapeArea(perspective(vertexes, chart, insidePlotArea));\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar Math3D = {\n    perspective: perspective,\n    perspective3D: perspective3D,\n    pointCameraDistance: pointCameraDistance,\n    shapeArea: shapeArea,\n    shapeArea3D: shapeArea3D\n};\n/* harmony default export */ var Core_Math3D = (Math3D);\n\n;// ./code/es5/es-modules/Core/Chart/Chart3D.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Extension for 3D charts\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar color = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default()).parse;\n\nvar genericDefaultOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defaultOptions;\n\nvar Chart3D_perspective = Core_Math3D.perspective, Chart3D_shapeArea3D = Core_Math3D.shapeArea3D;\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, Chart3D_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, wrap = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).wrap;\n/* *\n *\n *  Composition\n *\n * */\nvar Chart3D;\n(function (Chart3D) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    /**\n     * @optionparent\n     * @private\n     */\n    Chart3D.defaultOptions = {\n        chart: {\n            /**\n             * Options to render charts in 3 dimensions. This feature requires\n             * `highcharts-3d.js`, found in the download package or online at\n             * [code.highcharts.com/highcharts-3d.js](https://code.highcharts.com/highcharts-3d.js).\n             *\n             * @since    4.0\n             * @product  highcharts\n             * @requires highcharts-3d\n             */\n            options3d: {\n                /**\n                 * Whether to render the chart using the 3D functionality.\n                 *\n                 * @since   4.0\n                 * @product highcharts\n                 */\n                enabled: false,\n                /**\n                 * One of the two rotation angles for the chart.\n                 *\n                 * @since   4.0\n                 * @product highcharts\n                 */\n                alpha: 0,\n                /**\n                 * One of the two rotation angles for the chart.\n                 *\n                 * @since   4.0\n                 * @product highcharts\n                 */\n                beta: 0,\n                /**\n                 * The total depth of the chart.\n                 *\n                 * @since   4.0\n                 * @product highcharts\n                 */\n                depth: 100,\n                /**\n                 * Whether the 3d box should automatically adjust to the chart\n                 * plot area.\n                 *\n                 * @since   4.2.4\n                 * @product highcharts\n                 */\n                fitToPlot: true,\n                /**\n                 * Defines the distance the viewer is standing in front of the\n                 * chart, this setting is important to calculate the perspective\n                 * effect in column and scatter charts. It is not used for 3D\n                 * pie charts.\n                 *\n                 * @since   4.0\n                 * @product highcharts\n                 */\n                viewDistance: 25,\n                /**\n                 * Set it to `\"auto\"` to automatically move the labels to the\n                 * best edge.\n                 *\n                 * @type    {\"auto\"|null}\n                 * @since   5.0.12\n                 * @product highcharts\n                 */\n                axisLabelPosition: null,\n                /**\n                 * Provides the option to draw a frame around the charts by\n                 * defining a bottom, front and back panel.\n                 *\n                 * @since    4.0\n                 * @product  highcharts\n                 * @requires highcharts-3d\n                 */\n                frame: {\n                    /**\n                     * Whether the frames are visible.\n                     */\n                    visible: 'default',\n                    /**\n                     * General pixel thickness for the frame faces.\n                     */\n                    size: 1,\n                    /**\n                     * The bottom of the frame around a 3D chart.\n                     *\n                     * @since    4.0\n                     * @product  highcharts\n                     * @requires highcharts-3d\n                     */\n                    /**\n                     * The color of the panel.\n                     *\n                     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n                     * @default   transparent\n                     * @since     4.0\n                     * @product   highcharts\n                     * @apioption chart.options3d.frame.bottom.color\n                     */\n                    /**\n                     * The thickness of the panel.\n                     *\n                     * @type      {number}\n                     * @default   1\n                     * @since     4.0\n                     * @product   highcharts\n                     * @apioption chart.options3d.frame.bottom.size\n                     */\n                    /**\n                     * Whether to display the frame. Possible values are `true`,\n                     * `false`, `\"auto\"` to display only the frames behind the\n                     * data, and `\"default\"` to display faces behind the data\n                     * based on the axis layout, ignoring the point of view.\n                     *\n                     * @sample {highcharts} highcharts/3d/scatter-frame/\n                     *         Auto frames\n                     *\n                     * @type      {boolean|\"default\"|\"auto\"}\n                     * @default   default\n                     * @since     5.0.12\n                     * @product   highcharts\n                     * @apioption chart.options3d.frame.bottom.visible\n                     */\n                    /**\n                     * The bottom of the frame around a 3D chart.\n                     */\n                    bottom: {},\n                    /**\n                     * The top of the frame around a 3D chart.\n                     *\n                     * @extends chart.options3d.frame.bottom\n                     */\n                    top: {},\n                    /**\n                     * The left side of the frame around a 3D chart.\n                     *\n                     * @extends chart.options3d.frame.bottom\n                     */\n                    left: {},\n                    /**\n                     * The right of the frame around a 3D chart.\n                     *\n                     * @extends chart.options3d.frame.bottom\n                     */\n                    right: {},\n                    /**\n                     * The back side of the frame around a 3D chart.\n                     *\n                     * @extends chart.options3d.frame.bottom\n                     */\n                    back: {},\n                    /**\n                     * The front of the frame around a 3D chart.\n                     *\n                     * @extends chart.options3d.frame.bottom\n                     */\n                    front: {}\n                }\n            }\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(ChartClass, FxClass) {\n        var chartProto = ChartClass.prototype;\n        var fxProto = FxClass.prototype;\n        /**\n         * Shorthand to check the is3d flag.\n         * @private\n         * @return {boolean}\n         * Whether it is a 3D chart.\n         */\n        chartProto.is3d = function () {\n            var _a;\n            return !!((_a = this.options.chart.options3d) === null || _a === void 0 ? void 0 : _a.enabled);\n        };\n        chartProto.propsRequireDirtyBox.push('chart.options3d');\n        chartProto.propsRequireUpdateSeries.push('chart.options3d');\n        /**\n         * Animation setter for matrix property.\n         * @private\n         */\n        fxProto.matrixSetter = function () {\n            var interpolated;\n            if (this.pos < 1 &&\n                (isArray(this.start) || isArray(this.end))) {\n                var start = (this.start ||\n                        [1, 0, 0, 1, 0, 0]),\n                    end = this.end || [1, 0, 0, 1, 0, 0];\n                interpolated = [];\n                for (var i = 0; i < 6; i++) {\n                    interpolated.push(this.pos * end[i] + (1 - this.pos) * start[i]);\n                }\n            }\n            else {\n                interpolated = this.end;\n            }\n            this.elem.attr(this.prop, interpolated, null, true);\n        };\n        merge(true, genericDefaultOptions, Chart3D.defaultOptions);\n        addEvent(ChartClass, 'init', onInit);\n        addEvent(ChartClass, 'addSeries', onAddSeries);\n        addEvent(ChartClass, 'afterDrawChartBox', onAfterDrawChartBox);\n        addEvent(ChartClass, 'afterGetContainer', onAfterGetContainer);\n        addEvent(ChartClass, 'afterInit', onAfterInit);\n        addEvent(ChartClass, 'afterSetChartSize', onAfterSetChartSize);\n        addEvent(ChartClass, 'beforeRedraw', onBeforeRedraw);\n        addEvent(ChartClass, 'beforeRender', onBeforeRender);\n        wrap(chartProto, 'isInsidePlot', wrapIsInsidePlot);\n        wrap(chartProto, 'renderSeries', wrapRenderSeries);\n        wrap(chartProto, 'setClassName', wrapSetClassName);\n    }\n    Chart3D.compose = compose;\n    /**\n     * Legacy support for HC < 6 to make 'scatter' series in a 3D chart route to\n     * the real 'scatter3d' series type. (#8407)\n     * @private\n     */\n    function onAddSeries(e) {\n        if (this.is3d()) {\n            if (e.options.type === 'scatter') {\n                e.options.type = 'scatter3d';\n            }\n        }\n    }\n    /**\n     * @private\n     */\n    function onAfterDrawChartBox() {\n        if (this.chart3d &&\n            this.is3d()) {\n            var chart = this,\n                renderer = chart.renderer,\n                options3d = chart.options.chart.options3d,\n                frame = chart.chart3d.get3dFrame(),\n                xm = chart.plotLeft,\n                xp = chart.plotLeft + chart.plotWidth,\n                ym = chart.plotTop,\n                yp = chart.plotTop + chart.plotHeight,\n                zm = 0,\n                zp = options3d.depth,\n                xmm = xm - (frame.left.visible ? frame.left.size : 0),\n                xpp = xp + (frame.right.visible ? frame.right.size : 0),\n                ymm = ym - (frame.top.visible ? frame.top.size : 0),\n                ypp = yp + (frame.bottom.visible ? frame.bottom.size : 0),\n                zmm = zm - (frame.front.visible ? frame.front.size : 0),\n                zpp = zp + (frame.back.visible ? frame.back.size : 0),\n                verb = chart.hasRendered ? 'animate' : 'attr';\n            chart.chart3d.frame3d = frame;\n            if (!chart.frameShapes) {\n                chart.frameShapes = {\n                    bottom: renderer.polyhedron().add(),\n                    top: renderer.polyhedron().add(),\n                    left: renderer.polyhedron().add(),\n                    right: renderer.polyhedron().add(),\n                    back: renderer.polyhedron().add(),\n                    front: renderer.polyhedron().add()\n                };\n            }\n            chart.frameShapes.bottom[verb]({\n                'class': 'highcharts-3d-frame highcharts-3d-frame-bottom',\n                zIndex: frame.bottom.frontFacing ? -1000 : 1000,\n                faces: [{\n                        fill: color(frame.bottom.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }],\n                        enabled: frame.bottom.visible\n                    },\n                    {\n                        fill: color(frame.bottom.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }],\n                        enabled: frame.bottom.visible\n                    },\n                    {\n                        fill: color(frame.bottom.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }],\n                        enabled: frame.bottom.visible && !frame.left.visible\n                    },\n                    {\n                        fill: color(frame.bottom.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }],\n                        enabled: frame.bottom.visible && !frame.right.visible\n                    },\n                    {\n                        fill: color(frame.bottom.color).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }],\n                        enabled: frame.bottom.visible && !frame.front.visible\n                    },\n                    {\n                        fill: color(frame.bottom.color).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }],\n                        enabled: frame.bottom.visible && !frame.back.visible\n                    }]\n            });\n            chart.frameShapes.top[verb]({\n                'class': 'highcharts-3d-frame highcharts-3d-frame-top',\n                zIndex: frame.top.frontFacing ? -1000 : 1000,\n                faces: [{\n                        fill: color(frame.top.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }],\n                        enabled: frame.top.visible\n                    },\n                    {\n                        fill: color(frame.top.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }],\n                        enabled: frame.top.visible\n                    },\n                    {\n                        fill: color(frame.top.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }],\n                        enabled: frame.top.visible && !frame.left.visible\n                    },\n                    {\n                        fill: color(frame.top.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }],\n                        enabled: frame.top.visible && !frame.right.visible\n                    },\n                    {\n                        fill: color(frame.top.color).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }],\n                        enabled: frame.top.visible && !frame.front.visible\n                    },\n                    {\n                        fill: color(frame.top.color).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }],\n                        enabled: frame.top.visible && !frame.back.visible\n                    }]\n            });\n            chart.frameShapes.left[verb]({\n                'class': 'highcharts-3d-frame highcharts-3d-frame-left',\n                zIndex: frame.left.frontFacing ? -1000 : 1000,\n                faces: [{\n                        fill: color(frame.left.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }],\n                        enabled: frame.left.visible && !frame.bottom.visible\n                    },\n                    {\n                        fill: color(frame.left.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }],\n                        enabled: frame.left.visible && !frame.top.visible\n                    },\n                    {\n                        fill: color(frame.left.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }],\n                        enabled: frame.left.visible\n                    },\n                    {\n                        fill: color(frame.left.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }],\n                        enabled: frame.left.visible\n                    },\n                    {\n                        fill: color(frame.left.color).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }],\n                        enabled: frame.left.visible && !frame.front.visible\n                    },\n                    {\n                        fill: color(frame.left.color).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }],\n                        enabled: frame.left.visible && !frame.back.visible\n                    }]\n            });\n            chart.frameShapes.right[verb]({\n                'class': 'highcharts-3d-frame highcharts-3d-frame-right',\n                zIndex: frame.right.frontFacing ? -1000 : 1000,\n                faces: [{\n                        fill: color(frame.right.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }],\n                        enabled: frame.right.visible && !frame.bottom.visible\n                    },\n                    {\n                        fill: color(frame.right.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }],\n                        enabled: frame.right.visible && !frame.top.visible\n                    },\n                    {\n                        fill: color(frame.right.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }],\n                        enabled: frame.right.visible\n                    },\n                    {\n                        fill: color(frame.right.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }],\n                        enabled: frame.right.visible\n                    },\n                    {\n                        fill: color(frame.right.color).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }],\n                        enabled: frame.right.visible && !frame.front.visible\n                    },\n                    {\n                        fill: color(frame.right.color).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }],\n                        enabled: frame.right.visible && !frame.back.visible\n                    }]\n            });\n            chart.frameShapes.back[verb]({\n                'class': 'highcharts-3d-frame highcharts-3d-frame-back',\n                zIndex: frame.back.frontFacing ? -1000 : 1000,\n                faces: [{\n                        fill: color(frame.back.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }],\n                        enabled: frame.back.visible && !frame.bottom.visible\n                    },\n                    {\n                        fill: color(frame.back.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }],\n                        enabled: frame.back.visible && !frame.top.visible\n                    },\n                    {\n                        fill: color(frame.back.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }],\n                        enabled: frame.back.visible && !frame.left.visible\n                    },\n                    {\n                        fill: color(frame.back.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }],\n                        enabled: frame.back.visible && !frame.right.visible\n                    },\n                    {\n                        fill: color(frame.back.color).get(),\n                        vertexes: [{\n                                x: xm,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zp\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zp\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zp\n                            }],\n                        enabled: frame.back.visible\n                    },\n                    {\n                        fill: color(frame.back.color).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zpp\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zpp\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zpp\n                            }],\n                        enabled: frame.back.visible\n                    }]\n            });\n            chart.frameShapes.front[verb]({\n                'class': 'highcharts-3d-frame highcharts-3d-frame-front',\n                zIndex: frame.front.frontFacing ? -1000 : 1000,\n                faces: [{\n                        fill: color(frame.front.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }],\n                        enabled: frame.front.visible && !frame.bottom.visible\n                    },\n                    {\n                        fill: color(frame.front.color).brighten(0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }],\n                        enabled: frame.front.visible && !frame.top.visible\n                    },\n                    {\n                        fill: color(frame.front.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }],\n                        enabled: frame.front.visible && !frame.left.visible\n                    },\n                    {\n                        fill: color(frame.front.color).brighten(-0.1).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }],\n                        enabled: frame.front.visible && !frame.right.visible\n                    },\n                    {\n                        fill: color(frame.front.color).get(),\n                        vertexes: [{\n                                x: xp,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: ym,\n                                z: zm\n                            }, {\n                                x: xm,\n                                y: yp,\n                                z: zm\n                            }, {\n                                x: xp,\n                                y: yp,\n                                z: zm\n                            }],\n                        enabled: frame.front.visible\n                    },\n                    {\n                        fill: color(frame.front.color).get(),\n                        vertexes: [{\n                                x: xpp,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ypp,\n                                z: zmm\n                            }, {\n                                x: xmm,\n                                y: ymm,\n                                z: zmm\n                            }, {\n                                x: xpp,\n                                y: ymm,\n                                z: zmm\n                            }],\n                        enabled: frame.front.visible\n                    }]\n            });\n        }\n    }\n    /**\n     * Add the required CSS classes for column sides (#6018)\n     * @private\n     */\n    function onAfterGetContainer() {\n        if (this.styledMode) {\n            // Add definitions used by brighter and darker faces of the cuboids.\n            [{\n                    name: 'darker',\n                    slope: 0.6\n                }, {\n                    name: 'brighter',\n                    slope: 1.4\n                }].forEach(function (cfg) {\n                this.renderer.definition({\n                    tagName: 'filter',\n                    attributes: {\n                        id: 'highcharts-' + cfg.name\n                    },\n                    children: [{\n                            tagName: 'feComponentTransfer',\n                            children: [{\n                                    tagName: 'feFuncR',\n                                    attributes: {\n                                        type: 'linear',\n                                        slope: cfg.slope\n                                    }\n                                }, {\n                                    tagName: 'feFuncG',\n                                    attributes: {\n                                        type: 'linear',\n                                        slope: cfg.slope\n                                    }\n                                }, {\n                                    tagName: 'feFuncB',\n                                    attributes: {\n                                        type: 'linear',\n                                        slope: cfg.slope\n                                    }\n                                }]\n                        }]\n                });\n            }, this);\n        }\n    }\n    /**\n     * Legacy support for HC < 6 to make 'scatter' series in a 3D chart route to\n     * the real 'scatter3d' series type. (#8407)\n     * @private\n     */\n    function onAfterInit() {\n        var options = this.options;\n        if (this.is3d()) {\n            (options.series || []).forEach(function (s) {\n                var type = (s.type ||\n                        options.chart.type ||\n                        options.chart.defaultSeriesType);\n                if (type === 'scatter') {\n                    s.type = 'scatter3d';\n                }\n            });\n        }\n    }\n    /**\n     * @private\n     */\n    function onAfterSetChartSize() {\n        var chart = this,\n            options3d = chart.options.chart.options3d;\n        if (chart.chart3d &&\n            chart.is3d()) {\n            // Add a 0-360 normalisation for alfa and beta angles in 3d graph\n            if (options3d) {\n                options3d.alpha = options3d.alpha % 360 +\n                    (options3d.alpha >= 0 ? 0 : 360);\n                options3d.beta = options3d.beta % 360 +\n                    (options3d.beta >= 0 ? 0 : 360);\n            }\n            var inverted = chart.inverted, clipBox = chart.clipBox, margin = chart.margin, x = inverted ? 'y' : 'x', y = inverted ? 'x' : 'y', w = inverted ? 'height' : 'width', h = inverted ? 'width' : 'height';\n            clipBox[x] = -(margin[3] || 0);\n            clipBox[y] = -(margin[0] || 0);\n            clipBox[w] = (chart.chartWidth + (margin[3] || 0) + (margin[1] || 0));\n            clipBox[h] = (chart.chartHeight + (margin[0] || 0) + (margin[2] || 0));\n            // Set scale, used later in perspective method():\n            // getScale uses perspective, so scale3d has to be reset.\n            chart.scale3d = 1;\n            if (options3d.fitToPlot === true) {\n                chart.scale3d = chart.chart3d.getScale(options3d.depth);\n            }\n            // Recalculate the 3d frame with every call of setChartSize,\n            // instead of doing it after every redraw(). It avoids ticks\n            // and axis title outside of chart.\n            chart.chart3d.frame3d = chart.chart3d.get3dFrame(); // #7942\n        }\n    }\n    /**\n     * @private\n     */\n    function onBeforeRedraw() {\n        if (this.is3d()) {\n            // Set to force a redraw of all elements\n            this.isDirtyBox = true;\n        }\n    }\n    /**\n     * @private\n     */\n    function onBeforeRender() {\n        if (this.chart3d && this.is3d()) {\n            this.chart3d.frame3d = this.chart3d.get3dFrame();\n        }\n    }\n    /**\n     * @private\n     */\n    function onInit() {\n        if (!this.chart3d) {\n            this.chart3d = new Additions(this);\n        }\n    }\n    /**\n     * @private\n     */\n    function wrapIsInsidePlot(proceed) {\n        return this.is3d() || proceed.apply(this, [].slice.call(arguments, 1));\n    }\n    /**\n     * Draw the series in the reverse order (#3803, #3917)\n     * @private\n     */\n    function wrapRenderSeries(proceed) {\n        var series,\n            i = this.series.length;\n        if (this.is3d()) {\n            while (i--) {\n                series = this.series[i];\n                series.translate();\n                series.render();\n            }\n        }\n        else {\n            proceed.call(this);\n        }\n    }\n    /**\n     * @private\n     */\n    function wrapSetClassName(proceed) {\n        proceed.apply(this, [].slice.call(arguments, 1));\n        if (this.is3d()) {\n            this.container.className += ' highcharts-3d-chart';\n        }\n    }\n    /* *\n     *\n     *  Class\n     *\n     * */\n    var Additions = /** @class */ (function () {\n            /* *\n             *\n             *  Constructors\n             *\n             * */\n            function Additions(chart) {\n                this.chart = chart;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        Additions.prototype.get3dFrame = function () {\n            var chart = this.chart,\n                options3d = chart.options.chart.options3d,\n                frameOptions = options3d.frame,\n                xm = chart.plotLeft,\n                xp = chart.plotLeft + chart.plotWidth,\n                ym = chart.plotTop,\n                yp = chart.plotTop + chart.plotHeight,\n                zm = 0,\n                zp = options3d.depth,\n                faceOrientation = function (vertexes) {\n                    var area = Chart3D_shapeArea3D(vertexes,\n                chart);\n                // Give it 0.5 squared-pixel as a margin for rounding errors\n                if (area > 0.5) {\n                    return 1;\n                }\n                if (area < -0.5) {\n                    return -1;\n                }\n                return 0;\n            }, bottomOrientation = faceOrientation([\n                { x: xm, y: yp, z: zp },\n                { x: xp, y: yp, z: zp },\n                { x: xp, y: yp, z: zm },\n                { x: xm, y: yp, z: zm }\n            ]), topOrientation = faceOrientation([\n                { x: xm, y: ym, z: zm },\n                { x: xp, y: ym, z: zm },\n                { x: xp, y: ym, z: zp },\n                { x: xm, y: ym, z: zp }\n            ]), leftOrientation = faceOrientation([\n                { x: xm, y: ym, z: zm },\n                { x: xm, y: ym, z: zp },\n                { x: xm, y: yp, z: zp },\n                { x: xm, y: yp, z: zm }\n            ]), rightOrientation = faceOrientation([\n                { x: xp, y: ym, z: zp },\n                { x: xp, y: ym, z: zm },\n                { x: xp, y: yp, z: zm },\n                { x: xp, y: yp, z: zp }\n            ]), frontOrientation = faceOrientation([\n                { x: xm, y: yp, z: zm },\n                { x: xp, y: yp, z: zm },\n                { x: xp, y: ym, z: zm },\n                { x: xm, y: ym, z: zm }\n            ]), backOrientation = faceOrientation([\n                { x: xm, y: ym, z: zp },\n                { x: xp, y: ym, z: zp },\n                { x: xp, y: yp, z: zp },\n                { x: xm, y: yp, z: zp }\n            ]), defaultShowFront = false, defaultShowBack = true;\n            var defaultShowBottom = false,\n                defaultShowTop = false,\n                defaultShowLeft = false,\n                defaultShowRight = false;\n            // The 'default' criteria to visible faces of the frame is looking\n            // up every axis to decide whenever the left/right//top/bottom sides\n            // of the frame will be shown\n            []\n                .concat(chart.xAxis, chart.yAxis, chart.zAxis)\n                .forEach(function (axis) {\n                if (axis) {\n                    if (axis.horiz) {\n                        if (axis.opposite) {\n                            defaultShowTop = true;\n                        }\n                        else {\n                            defaultShowBottom = true;\n                        }\n                    }\n                    else {\n                        if (axis.opposite) {\n                            defaultShowRight = true;\n                        }\n                        else {\n                            defaultShowLeft = true;\n                        }\n                    }\n                }\n            });\n            var getFaceOptions = function (sources, faceOrientation, defaultVisible) {\n                    var faceAttrs = ['size', 'color', 'visible'], options = {};\n                for (var i = 0; i < faceAttrs.length; i++) {\n                    var attr = faceAttrs[i];\n                    for (var j = 0; j < sources.length; j++) {\n                        if (typeof sources[j] === 'object') {\n                            var val = sources[j][attr];\n                            if (typeof val !== 'undefined' && val !== null) {\n                                options[attr] = val;\n                                break;\n                            }\n                        }\n                    }\n                }\n                var isVisible = defaultVisible;\n                if (options.visible === true || options.visible === false) {\n                    isVisible = options.visible;\n                }\n                else if (options.visible === 'auto') {\n                    isVisible = faceOrientation > 0;\n                }\n                return {\n                    size: Chart3D_pick(options.size, 1),\n                    color: Chart3D_pick(options.color, 'none'),\n                    frontFacing: faceOrientation > 0,\n                    visible: isVisible\n                };\n            };\n            // Docs @TODO: Add all frame options (left, right, top, bottom,\n            // front, back) to apioptions JSDoc once the new system is up.\n            var ret = {\n                    axes: {},\n                    // FIXME: Previously, left/right, top/bottom and front/back\n                    // pairs shared size and color.\n                    // For compatibility and consistency sake, when one face have\n                    // size/color/visibility set, the opposite face will default to\n                    // the same values. Also, left/right used to be called 'side',\n                    // so that's also added as a fallback.\n                    bottom: getFaceOptions([frameOptions.bottom, frameOptions.top, frameOptions], bottomOrientation, defaultShowBottom),\n                    top: getFaceOptions([frameOptions.top, frameOptions.bottom, frameOptions], topOrientation, defaultShowTop),\n                    left: getFaceOptions([\n                        frameOptions.left,\n                        frameOptions.right,\n                        frameOptions.side,\n                        frameOptions\n                    ], leftOrientation, defaultShowLeft),\n                    right: getFaceOptions([\n                        frameOptions.right,\n                        frameOptions.left,\n                        frameOptions.side,\n                        frameOptions\n                    ], rightOrientation, defaultShowRight),\n                    back: getFaceOptions([frameOptions.back, frameOptions.front, frameOptions], backOrientation, defaultShowBack),\n                    front: getFaceOptions([frameOptions.front, frameOptions.back, frameOptions], frontOrientation, defaultShowFront)\n                };\n            // Decide the bast place to put axis title/labels based on the\n            // visible faces. Ideally, The labels can only be on the edge\n            // between a visible face and an invisible one. Also, the Y label\n            // should be one the left-most edge (right-most if opposite).\n            if (options3d.axisLabelPosition === 'auto') {\n                var isValidEdge = function (face1,\n                    face2) {\n                        return ((face1.visible !== face2.visible) ||\n                            (face1.visible &&\n                                face2.visible &&\n                                (face1.frontFacing !== face2.frontFacing)));\n                };\n                var yEdges = [];\n                if (isValidEdge(ret.left, ret.front)) {\n                    yEdges.push({\n                        y: (ym + yp) / 2,\n                        x: xm,\n                        z: zm,\n                        xDir: { x: 1, y: 0, z: 0 }\n                    });\n                }\n                if (isValidEdge(ret.left, ret.back)) {\n                    yEdges.push({\n                        y: (ym + yp) / 2,\n                        x: xm,\n                        z: zp,\n                        xDir: { x: 0, y: 0, z: -1 }\n                    });\n                }\n                if (isValidEdge(ret.right, ret.front)) {\n                    yEdges.push({\n                        y: (ym + yp) / 2,\n                        x: xp,\n                        z: zm,\n                        xDir: { x: 0, y: 0, z: 1 }\n                    });\n                }\n                if (isValidEdge(ret.right, ret.back)) {\n                    yEdges.push({\n                        y: (ym + yp) / 2,\n                        x: xp,\n                        z: zp,\n                        xDir: { x: -1, y: 0, z: 0 }\n                    });\n                }\n                var xBottomEdges = [];\n                if (isValidEdge(ret.bottom, ret.front)) {\n                    xBottomEdges.push({\n                        x: (xm + xp) / 2,\n                        y: yp,\n                        z: zm,\n                        xDir: { x: 1, y: 0, z: 0 }\n                    });\n                }\n                if (isValidEdge(ret.bottom, ret.back)) {\n                    xBottomEdges.push({\n                        x: (xm + xp) / 2,\n                        y: yp,\n                        z: zp,\n                        xDir: { x: -1, y: 0, z: 0 }\n                    });\n                }\n                var xTopEdges = [];\n                if (isValidEdge(ret.top, ret.front)) {\n                    xTopEdges.push({\n                        x: (xm + xp) / 2,\n                        y: ym,\n                        z: zm,\n                        xDir: { x: 1, y: 0, z: 0 }\n                    });\n                }\n                if (isValidEdge(ret.top, ret.back)) {\n                    xTopEdges.push({\n                        x: (xm + xp) / 2,\n                        y: ym,\n                        z: zp,\n                        xDir: { x: -1, y: 0, z: 0 }\n                    });\n                }\n                var zBottomEdges = [];\n                if (isValidEdge(ret.bottom, ret.left)) {\n                    zBottomEdges.push({\n                        z: (zm + zp) / 2,\n                        y: yp,\n                        x: xm,\n                        xDir: { x: 0, y: 0, z: -1 }\n                    });\n                }\n                if (isValidEdge(ret.bottom, ret.right)) {\n                    zBottomEdges.push({\n                        z: (zm + zp) / 2,\n                        y: yp,\n                        x: xp,\n                        xDir: { x: 0, y: 0, z: 1 }\n                    });\n                }\n                var zTopEdges = [];\n                if (isValidEdge(ret.top, ret.left)) {\n                    zTopEdges.push({\n                        z: (zm + zp) / 2,\n                        y: ym,\n                        x: xm,\n                        xDir: { x: 0, y: 0, z: -1 }\n                    });\n                }\n                if (isValidEdge(ret.top, ret.right)) {\n                    zTopEdges.push({\n                        z: (zm + zp) / 2,\n                        y: ym,\n                        x: xp,\n                        xDir: { x: 0, y: 0, z: 1 }\n                    });\n                }\n                var pickEdge = function (edges,\n                    axis,\n                    mult) {\n                        if (edges.length === 0) {\n                            return null;\n                    }\n                    if (edges.length === 1) {\n                        return edges[0];\n                    }\n                    var projections = Chart3D_perspective(edges,\n                        chart,\n                        false);\n                    var best = 0;\n                    for (var i = 1; i < projections.length; i++) {\n                        if (mult * projections[i][axis] >\n                            mult * projections[best][axis]) {\n                            best = i;\n                        }\n                        else if ((mult * projections[i][axis] ===\n                            mult * projections[best][axis]) &&\n                            (projections[i].z < projections[best].z)) {\n                            best = i;\n                        }\n                    }\n                    return edges[best];\n                };\n                ret.axes = {\n                    y: {\n                        'left': pickEdge(yEdges, 'x', -1),\n                        'right': pickEdge(yEdges, 'x', +1)\n                    },\n                    x: {\n                        'top': pickEdge(xTopEdges, 'y', -1),\n                        'bottom': pickEdge(xBottomEdges, 'y', +1)\n                    },\n                    z: {\n                        'top': pickEdge(zTopEdges, 'y', -1),\n                        'bottom': pickEdge(zBottomEdges, 'y', +1)\n                    }\n                };\n            }\n            else {\n                ret.axes = {\n                    y: {\n                        'left': {\n                            x: xm, z: zm, xDir: { x: 1, y: 0, z: 0 }\n                        },\n                        'right': {\n                            x: xp, z: zm, xDir: { x: 0, y: 0, z: 1 }\n                        }\n                    },\n                    x: {\n                        'top': {\n                            y: ym, z: zm, xDir: { x: 1, y: 0, z: 0 }\n                        },\n                        'bottom': {\n                            y: yp,\n                            z: zm,\n                            xDir: { x: 1, y: 0, z: 0 }\n                        }\n                    },\n                    z: {\n                        'top': {\n                            x: defaultShowLeft ? xp : xm,\n                            y: ym,\n                            xDir: defaultShowLeft ?\n                                { x: 0, y: 0, z: 1 } :\n                                { x: 0, y: 0, z: -1 }\n                        },\n                        'bottom': {\n                            x: defaultShowLeft ? xp : xm,\n                            y: yp,\n                            xDir: defaultShowLeft ?\n                                { x: 0, y: 0, z: 1 } :\n                                { x: 0, y: 0, z: -1 }\n                        }\n                    }\n                };\n            }\n            return ret;\n        };\n        /**\n         * Calculate scale of the 3D view. That is required to fit chart's 3D\n         * projection into the actual plotting area. Reported as #4933.\n         *\n         * **Note:**\n         * This function should ideally take the plot values instead of a chart\n         * object, but since the chart object is needed for perspective it is\n         * not practical. Possible to make both getScale and perspective more\n         * logical and also immutable.\n         *\n         * @private\n         * @function getScale\n         *\n         * @param {number} depth\n         * The depth of the chart\n         *\n         * @return {number}\n         * The scale to fit the 3D chart into the plotting area.\n         *\n         * @requires highcharts-3d\n         */\n        Additions.prototype.getScale = function (depth) {\n            var chart = this.chart, plotLeft = chart.plotLeft, plotRight = chart.plotWidth + plotLeft, plotTop = chart.plotTop, plotBottom = chart.plotHeight + plotTop, originX = plotLeft + chart.plotWidth / 2, originY = plotTop + chart.plotHeight / 2, bbox3d = {\n                    minX: Number.MAX_VALUE,\n                    maxX: -Number.MAX_VALUE,\n                    minY: Number.MAX_VALUE,\n                    maxY: -Number.MAX_VALUE\n                };\n            var corners,\n                scale = 1;\n            // Top left corners:\n            corners = [{\n                    x: plotLeft,\n                    y: plotTop,\n                    z: 0\n                }, {\n                    x: plotLeft,\n                    y: plotTop,\n                    z: depth\n                }];\n            // Top right corners:\n            [0, 1].forEach(function (i) {\n                corners.push({\n                    x: plotRight,\n                    y: corners[i].y,\n                    z: corners[i].z\n                });\n            });\n            // All bottom corners:\n            [0, 1, 2, 3].forEach(function (i) {\n                corners.push({\n                    x: corners[i].x,\n                    y: plotBottom,\n                    z: corners[i].z\n                });\n            });\n            // Calculate 3D corners:\n            corners = Chart3D_perspective(corners, chart, false);\n            // Get bounding box of 3D element:\n            corners.forEach(function (corner) {\n                bbox3d.minX = Math.min(bbox3d.minX, corner.x);\n                bbox3d.maxX = Math.max(bbox3d.maxX, corner.x);\n                bbox3d.minY = Math.min(bbox3d.minY, corner.y);\n                bbox3d.maxY = Math.max(bbox3d.maxY, corner.y);\n            });\n            // Left edge:\n            if (plotLeft > bbox3d.minX) {\n                scale = Math.min(scale, 1 - Math.abs((plotLeft + originX) / (bbox3d.minX + originX)) % 1);\n            }\n            // Right edge:\n            if (plotRight < bbox3d.maxX) {\n                scale = Math.min(scale, (plotRight - originX) / (bbox3d.maxX - originX));\n            }\n            // Top edge:\n            if (plotTop > bbox3d.minY) {\n                if (bbox3d.minY < 0) {\n                    scale = Math.min(scale, (plotTop + originY) / (-bbox3d.minY + plotTop + originY));\n                }\n                else {\n                    scale = Math.min(scale, 1 - (plotTop + originY) / (bbox3d.minY + originY) % 1);\n                }\n            }\n            // Bottom edge:\n            if (plotBottom < bbox3d.maxY) {\n                scale = Math.min(scale, Math.abs((plotBottom - originY) / (bbox3d.maxY - originY)));\n            }\n            return scale;\n        };\n        return Additions;\n    }());\n    Chart3D.Additions = Additions;\n})(Chart3D || (Chart3D = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Chart_Chart3D = (Chart3D);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Note: As of v5.0.12, `frame.left` or `frame.right` should be used instead.\n *\n * The side for the frame around a 3D chart.\n *\n * @deprecated\n * @since     4.0\n * @product   highcharts\n * @requires  highcharts-3d\n * @apioption chart.options3d.frame.side\n */\n/**\n * The color of the panel.\n *\n * @deprecated\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @default   transparent\n * @since     4.0\n * @product   highcharts\n * @apioption chart.options3d.frame.side.color\n */\n/**\n * The thickness of the panel.\n *\n * @deprecated\n * @type      {number}\n * @default   1\n * @since     4.0\n * @product   highcharts\n * @apioption chart.options3d.frame.side.size\n */\n''; // Keeps doclets above in JS file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Series/Area3D/Area3DSeries.js\n/* *\n *\n *  (c) 2010-2025 Grzegorz Blachliński\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed;\n\nvar Area3DSeries_perspective = Core_Math3D.perspective;\n\nvar lineProto = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.line.prototype;\n\nvar pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique, Area3DSeries_wrap = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).wrap;\n/* *\n *\n *  Functions\n *\n * */\n/**\n *\n */\nfunction compose(AreaSeriesClass) {\n    if (pushUnique(composed, 'Area3DSeries')) {\n        Area3DSeries_wrap(AreaSeriesClass.prototype, 'getGraphPath', wrapAreaSeriesGetGraphPath);\n    }\n}\n/**\n *\n */\nfunction wrapAreaSeriesGetGraphPath(proceed) {\n    var series = this,\n        svgPath = proceed.apply(series,\n        [].slice.call(arguments, 1));\n    // Do not do this if the chart is not 3D\n    if (!series.chart.is3d()) {\n        return svgPath;\n    }\n    var getGraphPath = lineProto.getGraphPath,\n        options = series.options,\n        translatedThreshold = Math.round(// #10909\n        series.yAxis.getThreshold(options.threshold));\n    var bottomPoints = [];\n    if (series.rawPointsX) {\n        for (var i = 0; i < series.points.length; i++) {\n            bottomPoints.push({\n                x: series.rawPointsX[i],\n                y: options.stacking ?\n                    series.points[i].yBottom : translatedThreshold,\n                z: series.zPadding\n            });\n        }\n    }\n    var options3d = series.chart.options.chart.options3d;\n    bottomPoints = Area3DSeries_perspective(bottomPoints, series.chart, true).map(function (point) { return ({ plotX: point.x, plotY: point.y, plotZ: point.z }); });\n    if (series.group && options3d && options3d.depth && options3d.beta) {\n        // Markers should take the global zIndex of series group.\n        if (series.markerGroup) {\n            series.markerGroup.add(series.group);\n            series.markerGroup.attr({\n                translateX: 0,\n                translateY: 0\n            });\n        }\n        series.group.attr({\n            zIndex: Math.max(1, (options3d.beta > 270 || options3d.beta < 90) ?\n                options3d.depth - Math.round(series.zPadding || 0) :\n                Math.round(series.zPadding || 0))\n        });\n    }\n    bottomPoints.reversed = true;\n    var bottomPath = getGraphPath.call(series,\n        bottomPoints,\n        true,\n        true);\n    if (bottomPath[0] && bottomPath[0][0] === 'M') {\n        bottomPath[0] = ['L', bottomPath[0][1], bottomPath[0][2]];\n    }\n    if (series.areaPath) {\n        // Remove previously used bottomPath and add the new one.\n        var areaPath = series.areaPath.splice(0,\n            series.areaPath.length / 2).concat(bottomPath);\n        // Use old xMap in the new areaPath\n        areaPath.xMap = series.areaPath.xMap;\n        series.areaPath = areaPath;\n    }\n    series.graphPath = svgPath;\n    return svgPath;\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar Area3DSeries = {\n    compose: compose\n};\n/* harmony default export */ var Area3D_Area3DSeries = (Area3DSeries);\n\n;// ./code/es5/es-modules/Core/Axis/Axis3DDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Extension for 3d axes\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @optionparent xAxis\n */\nvar Axis3DDefaults = {\n    labels: {\n        /**\n         * Defines how the labels are be repositioned according to the 3D\n         * chart orientation.\n         *\n         * - `'offset'`: Maintain a fixed horizontal/vertical distance from\n         *   the tick marks, despite the chart orientation. This is the\n         *   backwards compatible behavior, and causes skewing of X and Z\n         *   axes.\n         *\n         * - `'chart'`: Preserve 3D position relative to the chart. This\n         *   looks nice, but hard to read if the text isn't forward-facing.\n         *\n         * - `'flap'`: Rotated text along the axis to compensate for the\n         *   chart orientation. This tries to maintain text as legible as\n         *   possible on all orientations.\n         *\n         * - `'ortho'`: Rotated text along the axis direction so that the\n         *   labels are orthogonal to the axis. This is very similar to\n         *   `'flap'`, but prevents skewing the labels (X and Y scaling are\n         *   still present).\n         *\n         * @sample highcharts/3d/skewed-labels/\n         *         Skewed labels\n         *\n         * @since      5.0.15\n         * @validvalue ['offset', 'chart', 'flap', 'ortho']\n         * @product    highcharts\n         * @requires   highcharts-3d\n         */\n        position3d: 'offset',\n        /**\n         * If enabled, the axis labels will skewed to follow the\n         * perspective.\n         *\n         * This will fix overlapping labels and titles, but texts become\n         * less legible due to the distortion.\n         *\n         * The final appearance depends heavily on `labels.position3d`.\n         *\n         * @sample highcharts/3d/skewed-labels/\n         *         Skewed labels\n         *\n         * @since    5.0.15\n         * @product  highcharts\n         * @requires highcharts-3d\n         */\n        skew3d: false\n    },\n    title: {\n        /**\n         * Defines how the title is repositioned according to the 3D chart\n         * orientation.\n         *\n         * - `'offset'`: Maintain a fixed horizontal/vertical distance from\n         *   the tick marks, despite the chart orientation. This is the\n         *   backwards compatible behavior, and causes skewing of X and Z\n         *   axes.\n         *\n         * - `'chart'`: Preserve 3D position relative to the chart. This\n         *   looks nice, but hard to read if the text isn't forward-facing.\n         *\n         * - `'flap'`: Rotated text along the axis to compensate for the\n         *   chart orientation. This tries to maintain text as legible as\n         *   possible on all orientations.\n         *\n         * - `'ortho'`: Rotated text along the axis direction so that the\n         *   labels are orthogonal to the axis. This is very similar to\n         *   `'flap'`, but prevents skewing the labels (X and Y scaling are\n         *   still present).\n         *\n         * - `undefined`: Will use the config from `labels.position3d`\n         *\n         * @sample highcharts/3d/skewed-labels/\n         *         Skewed labels\n         *\n         * @type     {\"offset\"|\"chart\"|\"flap\"|\"ortho\"|null}\n         * @since    5.0.15\n         * @product  highcharts\n         * @requires highcharts-3d\n         */\n        position3d: null,\n        /**\n         * If enabled, the axis title will skewed to follow the perspective.\n         *\n         * This will fix overlapping labels and titles, but texts become\n         * less legible due to the distortion.\n         *\n         * The final appearance depends heavily on `title.position3d`.\n         *\n         * A `null` value will use the config from `labels.skew3d`.\n         *\n         * @sample highcharts/3d/skewed-labels/\n         *         Skewed labels\n         *\n         * @type     {boolean|null}\n         * @since    5.0.15\n         * @product  highcharts\n         * @requires highcharts-3d\n         */\n        skew3d: null\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Axis_Axis3DDefaults = (Axis3DDefaults);\n\n;// ./code/es5/es-modules/Core/Axis/Tick3DComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Extension for 3d axes\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar Tick3DComposition_composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed;\n\nvar Tick3DComposition_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, Tick3DComposition_pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique, Tick3DComposition_wrap = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).wrap;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction Tick3DComposition_compose(TickClass) {\n    if (Tick3DComposition_pushUnique(Tick3DComposition_composed, 'Axis.Tick3D')) {\n        Tick3DComposition_addEvent(TickClass, 'afterGetLabelPosition', onTickAfterGetLabelPosition);\n        Tick3DComposition_wrap(TickClass.prototype, 'getMarkPath', wrapTickGetMarkPath);\n    }\n}\n/**\n * @private\n */\nfunction onTickAfterGetLabelPosition(e) {\n    var axis3D = this.axis.axis3D;\n    if (axis3D) {\n        extend(e.pos, axis3D.fix3dPosition(e.pos));\n    }\n}\n/**\n * @private\n */\nfunction wrapTickGetMarkPath(proceed) {\n    var axis3D = this.axis.axis3D,\n        path = proceed.apply(this,\n        [].slice.call(arguments, 1));\n    if (axis3D) {\n        var start = path[0];\n        var end = path[1];\n        if (start[0] === 'M' && end[0] === 'L') {\n            var pArr = [\n                    axis3D.fix3dPosition({ x: start[1],\n                y: start[2],\n                z: 0 }),\n                    axis3D.fix3dPosition({ x: end[1],\n                y: end[2],\n                z: 0 })\n                ];\n            return this.axis.chart.renderer.toLineSegments(pArr);\n        }\n    }\n    return path;\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar Tick3DAdditions = {\n    compose: Tick3DComposition_compose\n};\n/* harmony default export */ var Tick3DComposition = (Tick3DAdditions);\n\n;// ./code/es5/es-modules/Core/Axis/Axis3DComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Extension for 3d axes\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar defaultOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defaultOptions;\n\nvar Axis3DComposition_deg2rad = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).deg2rad;\n\nvar Axis3DComposition_perspective = Core_Math3D.perspective, Axis3DComposition_perspective3D = Core_Math3D.perspective3D, Axis3DComposition_shapeArea = Core_Math3D.shapeArea;\n\n\nvar Axis3DComposition_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, Axis3DComposition_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, Axis3DComposition_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, Axis3DComposition_wrap = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).wrap;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction onAxisAfterSetOptions() {\n    var _a;\n    var axis = this,\n        chart = axis.chart,\n        options = axis.options;\n    if (((_a = chart.is3d) === null || _a === void 0 ? void 0 : _a.call(chart)) && axis.coll !== 'colorAxis') {\n        options.tickWidth = Axis3DComposition_pick(options.tickWidth, 0);\n        options.gridLineWidth = Axis3DComposition_pick(options.gridLineWidth, 1);\n    }\n}\n/**\n * @private\n */\nfunction onAxisDrawCrosshair(e) {\n    var axis = this;\n    if (axis.chart.is3d() &&\n        axis.coll !== 'colorAxis') {\n        if (e.point) {\n            e.point.crosshairPos = axis.isXAxis ?\n                e.point.axisXpos :\n                axis.len - e.point.axisYpos;\n        }\n    }\n}\n/**\n * @private\n */\nfunction onAxisInit() {\n    var axis = this;\n    if (!axis.axis3D) {\n        axis.axis3D = new Axis3DAdditions(axis);\n    }\n}\n/**\n * Do not draw axislines in 3D.\n * @private\n */\nfunction wrapAxisGetLinePath(proceed) {\n    var axis = this;\n    // Do not do this if the chart is not 3D\n    if (!axis.chart.is3d() || axis.coll === 'colorAxis') {\n        return proceed.apply(axis, [].slice.call(arguments, 1));\n    }\n    return [];\n}\n/**\n * @private\n */\nfunction wrapAxisGetPlotBandPath(proceed) {\n    // Do not do this if the chart is not 3D\n    if (!this.chart.is3d() || this.coll === 'colorAxis') {\n        return proceed.apply(this, [].slice.call(arguments, 1));\n    }\n    var args = arguments,\n        from = args[1],\n        to = args[2],\n        path = [],\n        fromPath = this.getPlotLinePath({ value: from }),\n        toPath = this.getPlotLinePath({ value: to });\n    if (fromPath && toPath) {\n        for (var i = 0; i < fromPath.length; i += 2) {\n            var fromStartSeg = fromPath[i],\n                fromEndSeg = fromPath[i + 1],\n                toStartSeg = toPath[i],\n                toEndSeg = toPath[i + 1];\n            if (fromStartSeg[0] === 'M' &&\n                fromEndSeg[0] === 'L' &&\n                toStartSeg[0] === 'M' &&\n                toEndSeg[0] === 'L') {\n                path.push(fromStartSeg, fromEndSeg, toEndSeg, \n                // `lineTo` instead of `moveTo`\n                ['L', toStartSeg[1], toStartSeg[2]], ['Z']);\n            }\n        }\n    }\n    return path;\n}\n/**\n * @private\n */\nfunction wrapAxisGetPlotLinePath(proceed) {\n    var axis = this,\n        axis3D = axis.axis3D,\n        chart = axis.chart,\n        path = proceed.apply(axis,\n        [].slice.call(arguments, 1));\n    // Do not do this if the chart is not 3D\n    if (axis.coll === 'colorAxis' ||\n        !chart.chart3d ||\n        !chart.is3d()) {\n        return path;\n    }\n    if (path === null) {\n        return path;\n    }\n    var options3d = chart.options.chart.options3d,\n        d = axis.isZAxis ? chart.plotWidth : options3d.depth,\n        frame = chart.chart3d.frame3d,\n        startSegment = path[0],\n        endSegment = path[1];\n    var pArr,\n        pathSegments = [];\n    if (startSegment[0] === 'M' && endSegment[0] === 'L') {\n        pArr = [\n            axis3D.swapZ({ x: startSegment[1], y: startSegment[2], z: 0 }),\n            axis3D.swapZ({ x: startSegment[1], y: startSegment[2], z: d }),\n            axis3D.swapZ({ x: endSegment[1], y: endSegment[2], z: 0 }),\n            axis3D.swapZ({ x: endSegment[1], y: endSegment[2], z: d })\n        ];\n        if (!this.horiz) { // Y-Axis\n            if (frame.front.visible) {\n                pathSegments.push(pArr[0], pArr[2]);\n            }\n            if (frame.back.visible) {\n                pathSegments.push(pArr[1], pArr[3]);\n            }\n            if (frame.left.visible) {\n                pathSegments.push(pArr[0], pArr[1]);\n            }\n            if (frame.right.visible) {\n                pathSegments.push(pArr[2], pArr[3]);\n            }\n        }\n        else if (this.isZAxis) { // Z-Axis\n            if (frame.left.visible) {\n                pathSegments.push(pArr[0], pArr[2]);\n            }\n            if (frame.right.visible) {\n                pathSegments.push(pArr[1], pArr[3]);\n            }\n            if (frame.top.visible) {\n                pathSegments.push(pArr[0], pArr[1]);\n            }\n            if (frame.bottom.visible) {\n                pathSegments.push(pArr[2], pArr[3]);\n            }\n        }\n        else { // X-Axis\n            if (frame.front.visible) {\n                pathSegments.push(pArr[0], pArr[2]);\n            }\n            if (frame.back.visible) {\n                pathSegments.push(pArr[1], pArr[3]);\n            }\n            if (frame.top.visible) {\n                pathSegments.push(pArr[0], pArr[1]);\n            }\n            if (frame.bottom.visible) {\n                pathSegments.push(pArr[2], pArr[3]);\n            }\n        }\n        pathSegments = Axis3DComposition_perspective(pathSegments, this.chart, false);\n    }\n    return chart.renderer.toLineSegments(pathSegments);\n}\n/**\n * Wrap getSlotWidth function to calculate individual width value for each\n * slot (#8042).\n * @private\n */\nfunction wrapAxisGetSlotWidth(proceed, tick) {\n    var _a,\n        _b;\n    var axis = this,\n        chart = axis.chart,\n        gridGroup = axis.gridGroup,\n        tickPositions = axis.tickPositions,\n        ticks = axis.ticks;\n    if (axis.categories &&\n        chart.frameShapes &&\n        chart.is3d() &&\n        gridGroup &&\n        tick &&\n        tick.label) {\n        var firstGridLine = (gridGroup.element.childNodes[0].getBBox()),\n            frame3DLeft = chart.frameShapes.left.getBBox(),\n            options3d = chart.options.chart.options3d,\n            origin_1 = {\n                x: chart.plotWidth / 2,\n                y: chart.plotHeight / 2,\n                z: options3d.depth / 2,\n                vd: (Axis3DComposition_pick(options3d.depth, 1) *\n                    Axis3DComposition_pick(options3d.viewDistance, 0))\n            },\n            index = tickPositions.indexOf(tick.pos),\n            prevTick = ticks[tickPositions[index - 1]],\n            nextTick = ticks[tickPositions[index + 1]];\n        var labelPos = void 0,\n            prevLabelPos = void 0,\n            nextLabelPos = void 0;\n        // Check whether the tick is not the first one and previous tick\n        // exists, then calculate position of previous label.\n        if ((_a = prevTick === null || prevTick === void 0 ? void 0 : prevTick.label) === null || _a === void 0 ? void 0 : _a.xy) {\n            prevLabelPos = Axis3DComposition_perspective3D({\n                x: prevTick.label.xy.x,\n                y: prevTick.label.xy.y,\n                z: null\n            }, origin_1, origin_1.vd);\n        }\n        // If next label position is defined, then recalculate its position\n        // basing on the perspective.\n        if ((_b = nextTick === null || nextTick === void 0 ? void 0 : nextTick.label) === null || _b === void 0 ? void 0 : _b.xy) {\n            nextLabelPos = Axis3DComposition_perspective3D({\n                x: nextTick.label.xy.x,\n                y: nextTick.label.xy.y,\n                z: null\n            }, origin_1, origin_1.vd);\n        }\n        labelPos = {\n            x: tick.label.xy.x,\n            y: tick.label.xy.y,\n            z: null\n        };\n        labelPos = Axis3DComposition_perspective3D(labelPos, origin_1, origin_1.vd);\n        // If tick is first one, check whether next label position is\n        // already calculated, then return difference between the first and\n        // the second label. If there is no next label position calculated,\n        // return the difference between the first grid line and left 3d\n        // frame.\n        return Math.abs(prevLabelPos ?\n            labelPos.x - prevLabelPos.x : nextLabelPos ?\n            nextLabelPos.x - labelPos.x :\n            firstGridLine.x - frame3DLeft.x);\n    }\n    return proceed.apply(axis, [].slice.call(arguments, 1));\n}\n/**\n * @private\n */\nfunction wrapAxisGetTitlePosition(proceed) {\n    var pos = proceed.apply(this,\n        [].slice.call(arguments, 1));\n    return this.axis3D ?\n        this.axis3D.fix3dPosition(pos, true) :\n        pos;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * Adds 3D support to axes.\n * @private\n * @class\n */\nvar Axis3DAdditions = /** @class */ (function () {\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    /**\n     * @private\n     */\n    function Axis3DAdditions(axis) {\n        this.axis = axis;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Extends axis class with 3D support.\n     * @private\n     */\n    Axis3DAdditions.compose = function (AxisClass, TickClass) {\n        Tick3DComposition.compose(TickClass);\n        if (!AxisClass.keepProps.includes('axis3D')) {\n            Axis3DComposition_merge(true, defaultOptions.xAxis, Axis_Axis3DDefaults);\n            AxisClass.keepProps.push('axis3D');\n            Axis3DComposition_addEvent(AxisClass, 'init', onAxisInit);\n            Axis3DComposition_addEvent(AxisClass, 'afterSetOptions', onAxisAfterSetOptions);\n            Axis3DComposition_addEvent(AxisClass, 'drawCrosshair', onAxisDrawCrosshair);\n            var axisProto = AxisClass.prototype;\n            Axis3DComposition_wrap(axisProto, 'getLinePath', wrapAxisGetLinePath);\n            Axis3DComposition_wrap(axisProto, 'getPlotBandPath', wrapAxisGetPlotBandPath);\n            Axis3DComposition_wrap(axisProto, 'getPlotLinePath', wrapAxisGetPlotLinePath);\n            Axis3DComposition_wrap(axisProto, 'getSlotWidth', wrapAxisGetSlotWidth);\n            Axis3DComposition_wrap(axisProto, 'getTitlePosition', wrapAxisGetTitlePosition);\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     * @param {Highcharts.Axis} axis\n     * Related axis.\n     * @param {Highcharts.Position3DObject} pos\n     * Position to fix.\n     * @param {boolean} [isTitle]\n     * Whether this is a title position.\n     * @return {Highcharts.Position3DObject}\n     * Fixed position.\n     */\n    Axis3DAdditions.prototype.fix3dPosition = function (pos, isTitle) {\n        var axis3D = this;\n        var axis = axis3D.axis;\n        var chart = axis.chart;\n        // Do not do this if the chart is not 3D\n        if (axis.coll === 'colorAxis' ||\n            !chart.chart3d ||\n            !chart.is3d()) {\n            return pos;\n        }\n        var alpha = Axis3DComposition_deg2rad * chart.options.chart.options3d.alpha,\n            beta = Axis3DComposition_deg2rad * chart.options.chart.options3d.beta,\n            positionMode = Axis3DComposition_pick(isTitle && axis.options.title.position3d,\n            axis.options.labels.position3d),\n            skew = Axis3DComposition_pick(isTitle && axis.options.title.skew3d,\n            axis.options.labels.skew3d),\n            frame = chart.chart3d.frame3d,\n            plotLeft = chart.plotLeft,\n            plotRight = chart.plotWidth + plotLeft,\n            plotTop = chart.plotTop,\n            plotBottom = chart.plotHeight + plotTop;\n        var offsetX = 0,\n            offsetY = 0,\n            vecX,\n            vecY = { x: 0,\n            y: 1,\n            z: 0 }, \n            // Indicates that we are labelling an X or Z axis on the \"back\" of\n            // the chart\n            reverseFlap = false;\n        pos = axis.axis3D.swapZ({ x: pos.x, y: pos.y, z: 0 });\n        if (axis.isZAxis) { // Z Axis\n            if (axis.opposite) {\n                if (frame.axes.z.top === null) {\n                    return {};\n                }\n                offsetY = pos.y - plotTop;\n                pos.x = frame.axes.z.top.x;\n                pos.y = frame.axes.z.top.y;\n                vecX = frame.axes.z.top.xDir;\n                reverseFlap = !frame.top.frontFacing;\n            }\n            else {\n                if (frame.axes.z.bottom === null) {\n                    return {};\n                }\n                offsetY = pos.y - plotBottom;\n                pos.x = frame.axes.z.bottom.x;\n                pos.y = frame.axes.z.bottom.y;\n                vecX = frame.axes.z.bottom.xDir;\n                reverseFlap = !frame.bottom.frontFacing;\n            }\n        }\n        else if (axis.horiz) { // X Axis\n            if (axis.opposite) {\n                if (frame.axes.x.top === null) {\n                    return {};\n                }\n                offsetY = pos.y - plotTop;\n                pos.y = frame.axes.x.top.y;\n                pos.z = frame.axes.x.top.z;\n                vecX = frame.axes.x.top.xDir;\n                reverseFlap = !frame.top.frontFacing;\n            }\n            else {\n                if (frame.axes.x.bottom === null) {\n                    return {};\n                }\n                offsetY = pos.y - plotBottom;\n                pos.y = frame.axes.x.bottom.y;\n                pos.z = frame.axes.x.bottom.z;\n                vecX = frame.axes.x.bottom.xDir;\n                reverseFlap = !frame.bottom.frontFacing;\n            }\n        }\n        else { // Y Axis\n            if (axis.opposite) {\n                if (frame.axes.y.right === null) {\n                    return {};\n                }\n                offsetX = pos.x - plotRight;\n                pos.x = frame.axes.y.right.x;\n                pos.z = frame.axes.y.right.z;\n                vecX = frame.axes.y.right.xDir;\n                // Rotate 90º on opposite edge\n                vecX = { x: vecX.z, y: vecX.y, z: -vecX.x };\n            }\n            else {\n                if (frame.axes.y.left === null) {\n                    return {};\n                }\n                offsetX = pos.x - plotLeft;\n                pos.x = frame.axes.y.left.x;\n                pos.z = frame.axes.y.left.z;\n                vecX = frame.axes.y.left.xDir;\n            }\n        }\n        if (positionMode === 'chart') {\n            // Labels preserve their direction relative to the chart\n            // nothing to do\n        }\n        else if (positionMode === 'flap') {\n            // Labels are rotated around the axis direction to face the screen\n            if (!axis.horiz) { // Y Axis\n                vecX = { x: Math.cos(beta), y: 0, z: Math.sin(beta) };\n            }\n            else { // X and Z Axis\n                var sin = Math.sin(alpha);\n                var cos = Math.cos(alpha);\n                if (axis.opposite) {\n                    sin = -sin;\n                }\n                if (reverseFlap) {\n                    sin = -sin;\n                }\n                vecY = { x: vecX.z * sin, y: cos, z: -vecX.x * sin };\n            }\n        }\n        else if (positionMode === 'ortho') {\n            // Labels will be rotated to be orthogonal to the axis\n            if (!axis.horiz) { // Y Axis\n                vecX = { x: Math.cos(beta), y: 0, z: Math.sin(beta) };\n            }\n            else { // X and Z Axis\n                var sina = Math.sin(alpha);\n                var cosa = Math.cos(alpha);\n                var sinb = Math.sin(beta);\n                var cosb = Math.cos(beta);\n                var vecZ = { x: sinb * cosa,\n                    y: -sina,\n                    z: -cosa * cosb };\n                vecY = {\n                    x: vecX.y * vecZ.z - vecX.z * vecZ.y,\n                    y: vecX.z * vecZ.x - vecX.x * vecZ.z,\n                    z: vecX.x * vecZ.y - vecX.y * vecZ.x\n                };\n                var scale = 1 / Math.sqrt(vecY.x * vecY.x + vecY.y * vecY.y + vecY.z * vecY.z);\n                if (reverseFlap) {\n                    scale = -scale;\n                }\n                vecY = {\n                    x: scale * vecY.x, y: scale * vecY.y, z: scale * vecY.z\n                };\n            }\n        }\n        else { // Position mode  == 'offset'\n            // Labels will be skewd to maintain vertical / horizontal offsets\n            // from axis\n            if (!axis.horiz) { // Y Axis\n                vecX = { x: Math.cos(beta), y: 0, z: Math.sin(beta) };\n            }\n            else { // X and Z Axis\n                vecY = {\n                    x: Math.sin(beta) * Math.sin(alpha),\n                    y: Math.cos(alpha),\n                    z: -Math.cos(beta) * Math.sin(alpha)\n                };\n            }\n        }\n        pos.x += offsetX * vecX.x + offsetY * vecY.x;\n        pos.y += offsetX * vecX.y + offsetY * vecY.y;\n        pos.z += offsetX * vecX.z + offsetY * vecY.z;\n        var projected = Axis3DComposition_perspective([pos],\n            axis.chart)[0];\n        if (skew) {\n            // Check if the label text would be mirrored\n            var isMirrored = Axis3DComposition_shapeArea(Axis3DComposition_perspective([\n                    pos,\n                    { x: pos.x + vecX.x,\n                y: pos.y + vecX.y,\n                z: pos.z + vecX.z },\n                    { x: pos.x + vecY.x,\n                y: pos.y + vecY.y,\n                z: pos.z + vecY.z }\n                ],\n                axis.chart)) < 0;\n            if (isMirrored) {\n                vecX = { x: -vecX.x, y: -vecX.y, z: -vecX.z };\n            }\n            var pointsProjected = Axis3DComposition_perspective([\n                    { x: pos.x,\n                y: pos.y,\n                z: pos.z },\n                    { x: pos.x + vecX.x,\n                y: pos.y + vecX.y,\n                z: pos.z + vecX.z },\n                    { x: pos.x + vecY.x,\n                y: pos.y + vecY.y,\n                z: pos.z + vecY.z }\n                ],\n                axis.chart);\n            projected.matrix = [\n                pointsProjected[1].x - pointsProjected[0].x,\n                pointsProjected[1].y - pointsProjected[0].y,\n                pointsProjected[2].x - pointsProjected[0].x,\n                pointsProjected[2].y - pointsProjected[0].y,\n                projected.x,\n                projected.y\n            ];\n            projected.matrix[4] -= projected.x * projected.matrix[0] +\n                projected.y * projected.matrix[2];\n            projected.matrix[5] -= projected.x * projected.matrix[1] +\n                projected.y * projected.matrix[3];\n        }\n        return projected;\n    };\n    /**\n     * @private\n     */\n    Axis3DAdditions.prototype.swapZ = function (p, insidePlotArea) {\n        var axis = this.axis;\n        if (axis.isZAxis) {\n            var plotLeft = insidePlotArea ? 0 : axis.chart.plotLeft;\n            return {\n                x: plotLeft + p.z,\n                y: p.y,\n                z: p.x - plotLeft\n            };\n        }\n        return p;\n    };\n    return Axis3DAdditions;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Axis3DComposition = (Axis3DAdditions);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"RendererRegistry\"],\"commonjs\":[\"highcharts\",\"RendererRegistry\"],\"commonjs2\":[\"highcharts\",\"RendererRegistry\"],\"root\":[\"Highcharts\",\"RendererRegistry\"]}\nvar highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_ = __webpack_require__(608);\nvar highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\"],\"commonjs\":[\"highcharts\",\"Series\"],\"commonjs2\":[\"highcharts\",\"Series\"],\"root\":[\"Highcharts\",\"Series\"]}\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_ = __webpack_require__(820);\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default = /*#__PURE__*/__webpack_require__.n(highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_);\n;// ./code/es5/es-modules/Core/Series/Series3D.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Extension to the Series object in 3D charts.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar Series3D_composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed;\n\nvar Series3D_perspective = Core_Math3D.perspective;\n\n\nvar Series3D_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, Series3D_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, Series3D_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, Series3D_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, Series3D_pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique;\n/* *\n *\n *  Class\n *\n * */\nvar Series3D = /** @class */ (function (_super) {\n    __extends(Series3D, _super);\n    function Series3D() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    Series3D.compose = function (SeriesClass) {\n        if (Series3D_pushUnique(Series3D_composed, 'Core.Series3D')) {\n            Series3D_addEvent(SeriesClass, 'afterTranslate', function () {\n                if (this.chart.is3d()) {\n                    this.translate3dPoints();\n                }\n            });\n            Series3D_extend(SeriesClass.prototype, {\n                translate3dPoints: Series3D.prototype.translate3dPoints\n            });\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Translate the plotX, plotY properties and add plotZ.\n     * @private\n     */\n    Series3D.prototype.translate3dPoints = function () {\n        var series = this,\n            seriesOptions = series.options,\n            chart = series.chart,\n            zAxis = Series3D_pick(series.zAxis,\n            chart.options.zAxis[0]),\n            rawPoints = [],\n            rawPointsX = [],\n            stack = seriesOptions.stacking ?\n                (isNumber(seriesOptions.stack) ? seriesOptions.stack : 0) :\n                series.index || 0;\n        var projectedPoint,\n            zValue;\n        series.zPadding = stack *\n            (seriesOptions.depth || 0 + (seriesOptions.groupZPadding || 1));\n        series.data.forEach(function (rawPoint) {\n            if (zAxis === null || zAxis === void 0 ? void 0 : zAxis.translate) {\n                zValue = zAxis.logarithmic && zAxis.val2lin ?\n                    zAxis.val2lin(rawPoint.z) :\n                    rawPoint.z; // #4562\n                rawPoint.plotZ = zAxis.translate(zValue);\n                rawPoint.isInside = rawPoint.isInside ?\n                    (zValue >= zAxis.min &&\n                        zValue <= zAxis.max) :\n                    false;\n            }\n            else {\n                rawPoint.plotZ = series.zPadding;\n            }\n            rawPoint.axisXpos = rawPoint.plotX;\n            rawPoint.axisYpos = rawPoint.plotY;\n            rawPoint.axisZpos = rawPoint.plotZ;\n            rawPoints.push({\n                x: rawPoint.plotX,\n                y: rawPoint.plotY,\n                z: rawPoint.plotZ\n            });\n            rawPointsX.push(rawPoint.plotX || 0);\n        });\n        series.rawPointsX = rawPointsX;\n        var projectedPoints = Series3D_perspective(rawPoints,\n            chart,\n            true);\n        series.data.forEach(function (rawPoint, i) {\n            projectedPoint = projectedPoints[i];\n            rawPoint.plotX = projectedPoint.x;\n            rawPoint.plotY = projectedPoint.y;\n            rawPoint.plotZ = projectedPoint.z;\n        });\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    Series3D.defaultOptions = Series3D_merge((highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default()).defaultOptions);\n    return Series3D;\n}((highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default())));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Series_Series3D = (Series3D);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"StackItem\"],\"commonjs\":[\"highcharts\",\"StackItem\"],\"commonjs2\":[\"highcharts\",\"StackItem\"],\"root\":[\"Highcharts\",\"StackItem\"]}\nvar highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_ = __webpack_require__(184);\nvar highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default = /*#__PURE__*/__webpack_require__.n(highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_);\n;// ./code/es5/es-modules/Core/Renderer/SVG/SVGElement3D.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Extensions to the SVGRenderer class to enable 3D shapes\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar SVGElement3D_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar SVGElement3D_color = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default()).parse;\n\nvar SVGElement = highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default().getRendererType().prototype.Element;\n\nvar defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, SVGElement3D_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Class\n *\n * */\nvar SVGElement3D = /** @class */ (function (_super) {\n    SVGElement3D_extends(SVGElement3D, _super);\n    function SVGElement3D() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        var _this = _super !== null && _super.apply(this,\n            arguments) || this;\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        _this.parts = ['front', 'top', 'side'];\n        _this.pathType = 'cuboid';\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * The init is used by base - renderer.Element\n     * @private\n     */\n    SVGElement3D.prototype.initArgs = function (args) {\n        var elem3d = this,\n            renderer = elem3d.renderer,\n            paths = renderer[elem3d.pathType + 'Path'](args),\n            zIndexes = paths.zIndexes;\n        // Build parts\n        for (var _i = 0, _a = elem3d.parts; _i < _a.length; _i++) {\n            var part = _a[_i];\n            var attribs = {\n                    'class': 'highcharts-3d-' + part,\n                    zIndex: zIndexes[part] || 0\n                };\n            if (renderer.styledMode) {\n                if (part === 'top') {\n                    attribs.filter = 'url(#highcharts-brighter)';\n                }\n                else if (part === 'side') {\n                    attribs.filter = 'url(#highcharts-darker)';\n                }\n            }\n            elem3d[part] = renderer.path(paths[part])\n                .attr(attribs)\n                .add(elem3d);\n        }\n        elem3d.attr({\n            'stroke-linejoin': 'round',\n            zIndex: zIndexes.group\n        });\n        // Store information if any side of element was rendered by force.\n        elem3d.forcedSides = paths.forcedSides;\n    };\n    /**\n     * Single property setter that applies options to each part\n     * @private\n     */\n    SVGElement3D.prototype.singleSetterForParts = function (prop, val, values, verb, duration, complete) {\n        var elem3d = this,\n            newAttr = {},\n            optionsToApply = [null,\n            null, (verb || 'attr'),\n            duration,\n            complete],\n            hasZIndexes = values === null || values === void 0 ? void 0 : values.zIndexes;\n        if (!values) {\n            newAttr[prop] = val;\n            optionsToApply[0] = newAttr;\n        }\n        else {\n            // It is needed to deal with the whole group zIndexing\n            // in case of graph rotation\n            if (hasZIndexes === null || hasZIndexes === void 0 ? void 0 : hasZIndexes.group) {\n                elem3d.attr({\n                    zIndex: hasZIndexes.group\n                });\n            }\n            for (var _i = 0, _a = Object.keys(values); _i < _a.length; _i++) {\n                var part = _a[_i];\n                newAttr[part] = {};\n                newAttr[part][prop] = values[part];\n                // Include zIndexes if provided\n                if (hasZIndexes) {\n                    newAttr[part].zIndex = values.zIndexes[part] || 0;\n                }\n            }\n            optionsToApply[1] = newAttr;\n        }\n        return this.processParts.apply(elem3d, optionsToApply);\n    };\n    /**\n     * Calls function for each part. Used for attr, animate and destroy.\n     * @private\n     */\n    SVGElement3D.prototype.processParts = function (props, partsProps, verb, duration, complete) {\n        var elem3d = this;\n        for (var _i = 0, _a = elem3d.parts; _i < _a.length; _i++) {\n            var part = _a[_i];\n            // If different props for different parts\n            if (partsProps) {\n                props = SVGElement3D_pick(partsProps[part], false);\n            }\n            // Only if something to set, but allow undefined\n            if (props !== false) {\n                elem3d[part][verb](props, duration, complete);\n            }\n        }\n        return elem3d;\n    };\n    /**\n     * Destroy all parts\n     * @private\n     */\n    SVGElement3D.prototype.destroy = function () {\n        this.processParts(null, null, 'destroy');\n        return _super.prototype.destroy.call(this);\n    };\n    // Following functions are SVGElement3DCuboid (= base)\n    SVGElement3D.prototype.attr = function (args, val, complete, continueAnimation) {\n        // Resolve setting attributes by string name\n        if (typeof args === 'string' && typeof val !== 'undefined') {\n            var key = args;\n            args = {};\n            args[key] = val;\n        }\n        if (args.shapeArgs || defined(args.x)) {\n            return this.singleSetterForParts('d', null, this.renderer[this.pathType + 'Path'](args.shapeArgs || args));\n        }\n        return _super.prototype.attr.call(this, args, void 0, complete, continueAnimation);\n    };\n    SVGElement3D.prototype.animate = function (args, duration, complete) {\n        if (defined(args.x) && defined(args.y)) {\n            var paths = this.renderer[this.pathType + 'Path'](args),\n                forcedSides = paths.forcedSides;\n            this.singleSetterForParts('d', null, paths, 'animate', duration, complete);\n            this.attr({\n                zIndex: paths.zIndexes.group\n            });\n            // If sides that are forced to render changed, recalculate colors.\n            if (forcedSides !== this.forcedSides) {\n                this.forcedSides = forcedSides;\n                if (!this.renderer.styledMode) {\n                    this.fillSetter(this.fill);\n                }\n            }\n        }\n        else {\n            _super.prototype.animate.call(this, args, duration, complete);\n        }\n        return this;\n    };\n    SVGElement3D.prototype.fillSetter = function (fill) {\n        var elem3d = this;\n        elem3d.forcedSides = elem3d.forcedSides || [];\n        elem3d.singleSetterForParts('fill', null, {\n            front: fill,\n            // Do not change color if side was forced to render.\n            top: SVGElement3D_color(fill).brighten(elem3d.forcedSides.indexOf('top') >= 0 ? 0 : 0.1).get(),\n            side: SVGElement3D_color(fill).brighten(elem3d.forcedSides.indexOf('side') >= 0 ? 0 : -0.1).get()\n        });\n        // Fill for animation getter (#6776)\n        elem3d.color = elem3d.fill = fill;\n        return elem3d;\n    };\n    SVGElement3D.types = {\n        base: SVGElement3D,\n        cuboid: SVGElement3D\n    };\n    return SVGElement3D;\n}(SVGElement));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var SVG_SVGElement3D = (SVGElement3D);\n\n;// ./code/es5/es-modules/Core/Renderer/SVG/SVGRenderer3D.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Extensions to the SVGRenderer class to enable 3D shapes\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar animObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).animObject;\n\nvar SVGRenderer3D_color = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default()).parse;\n\nvar charts = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).charts, SVGRenderer3D_deg2rad = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).deg2rad;\n\nvar SVGRenderer3D_perspective = Core_Math3D.perspective, SVGRenderer3D_shapeArea = Core_Math3D.shapeArea;\n\n\nvar SVGRenderer3D_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, SVGRenderer3D_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, SVGRenderer3D_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, SVGRenderer3D_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Constants\n *\n * */\nvar cos = Math.cos, sin = Math.sin, PI = Math.PI, dFactor = (4 * (Math.sqrt(2) - 1) / 3) / (PI / 2);\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Method to construct a curved path. Can 'wrap' around more then 180\n * degrees.\n * @private\n */\nfunction curveTo(cx, cy, rx, ry, start, end, dx, dy) {\n    var arcAngle = end - start;\n    var result = [];\n    if ((end > start) && (end - start > Math.PI / 2 + 0.0001)) {\n        result = result.concat(curveTo(cx, cy, rx, ry, start, start + (Math.PI / 2), dx, dy));\n        result = result.concat(curveTo(cx, cy, rx, ry, start + (Math.PI / 2), end, dx, dy));\n        return result;\n    }\n    if ((end < start) && (start - end > Math.PI / 2 + 0.0001)) {\n        result = result.concat(curveTo(cx, cy, rx, ry, start, start - (Math.PI / 2), dx, dy));\n        result = result.concat(curveTo(cx, cy, rx, ry, start - (Math.PI / 2), end, dx, dy));\n        return result;\n    }\n    return [[\n            'C',\n            cx + (rx * Math.cos(start)) -\n                ((rx * dFactor * arcAngle) * Math.sin(start)) + dx,\n            cy + (ry * Math.sin(start)) +\n                ((ry * dFactor * arcAngle) * Math.cos(start)) + dy,\n            cx + (rx * Math.cos(end)) +\n                ((rx * dFactor * arcAngle) * Math.sin(end)) + dx,\n            cy + (ry * Math.sin(end)) -\n                ((ry * dFactor * arcAngle) * Math.cos(end)) + dy,\n            cx + (rx * Math.cos(end)) + dx,\n            cy + (ry * Math.sin(end)) + dy\n        ]];\n}\n/* *\n *\n *  Composition\n *\n * */\nvar SVGRenderer3D;\n(function (SVGRenderer3D) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /** @private */\n    function compose(SVGRendererClass) {\n        var rendererProto = SVGRendererClass.prototype;\n        if (!rendererProto.element3d) {\n            SVGRenderer3D_extend(rendererProto, {\n                Element3D: SVG_SVGElement3D,\n                arc3d: arc3d,\n                arc3dPath: arc3dPath,\n                cuboid: cuboid,\n                cuboidPath: cuboidPath,\n                element3d: element3d,\n                face3d: face3d,\n                polyhedron: polyhedron,\n                toLinePath: toLinePath,\n                toLineSegments: toLineSegments\n            });\n        }\n    }\n    SVGRenderer3D.compose = compose;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /** @private */\n    function toLinePath(points, closed) {\n        var result = [];\n        // Put \"L x y\" for each point\n        for (var _i = 0, points_1 = points; _i < points_1.length; _i++) {\n            var point = points_1[_i];\n            result.push(['L', point.x, point.y]);\n        }\n        if (points.length) {\n            // Set the first element to M\n            result[0][0] = 'M';\n            // If it is a closed line, add Z\n            if (closed) {\n                result.push(['Z']);\n            }\n        }\n        return result;\n    }\n    /** @private */\n    function toLineSegments(points) {\n        var result = [];\n        var m = true;\n        for (var _i = 0, points_2 = points; _i < points_2.length; _i++) {\n            var point = points_2[_i];\n            result.push(m ? ['M', point.x, point.y] : ['L', point.x, point.y]);\n            m = !m;\n        }\n        return result;\n    }\n    /**\n     * A 3-D Face is defined by it's 3D vertexes, and is only visible if it's\n     * vertexes are counter-clockwise (Back-face culling). It is used as a\n     * polyhedron Element.\n     * @private\n     */\n    function face3d(args) {\n        var renderer = this,\n            elementProto = renderer.Element.prototype,\n            ret = renderer.createElement('path');\n        ret.vertexes = [];\n        ret.insidePlotArea = false;\n        ret.enabled = true;\n        /* eslint-disable no-invalid-this */\n        ret.attr = function (hash) {\n            if (typeof hash === 'object' &&\n                (SVGRenderer3D_defined(hash.enabled) ||\n                    SVGRenderer3D_defined(hash.vertexes) ||\n                    SVGRenderer3D_defined(hash.insidePlotArea))) {\n                this.enabled = SVGRenderer3D_pick(hash.enabled, this.enabled);\n                this.vertexes = SVGRenderer3D_pick(hash.vertexes, this.vertexes);\n                this.insidePlotArea = SVGRenderer3D_pick(hash.insidePlotArea, this.insidePlotArea);\n                delete hash.enabled;\n                delete hash.vertexes;\n                delete hash.insidePlotArea;\n                var chart = charts[renderer.chartIndex],\n                    vertexes2d = SVGRenderer3D_perspective(this.vertexes,\n                    chart,\n                    this.insidePlotArea),\n                    path = renderer.toLinePath(vertexes2d,\n                    true),\n                    area = SVGRenderer3D_shapeArea(vertexes2d);\n                hash.d = path;\n                hash.visibility = (this.enabled && area > 0) ?\n                    'inherit' : 'hidden';\n            }\n            return elementProto.attr.apply(this, arguments);\n        };\n        ret.animate = function (params) {\n            if (typeof params === 'object' &&\n                (SVGRenderer3D_defined(params.enabled) ||\n                    SVGRenderer3D_defined(params.vertexes) ||\n                    SVGRenderer3D_defined(params.insidePlotArea))) {\n                this.enabled = SVGRenderer3D_pick(params.enabled, this.enabled);\n                this.vertexes = SVGRenderer3D_pick(params.vertexes, this.vertexes);\n                this.insidePlotArea = SVGRenderer3D_pick(params.insidePlotArea, this.insidePlotArea);\n                delete params.enabled;\n                delete params.vertexes;\n                delete params.insidePlotArea;\n                var chart = charts[renderer.chartIndex],\n                    vertexes2d = SVGRenderer3D_perspective(this.vertexes,\n                    chart,\n                    this.insidePlotArea),\n                    path = renderer.toLinePath(vertexes2d,\n                    true),\n                    area = SVGRenderer3D_shapeArea(vertexes2d),\n                    visibility = (this.enabled && area > 0) ?\n                        'visible' : 'hidden';\n                params.d = path;\n                this.attr('visibility', visibility);\n            }\n            return elementProto.animate.apply(this, arguments);\n        };\n        /* eslint-enable no-invalid-this */\n        return ret.attr(args);\n    }\n    /**\n     * A Polyhedron is a handy way of defining a group of 3-D faces. It's only\n     * attribute is `faces`, an array of attributes of each one of it's Face3D\n     * instances.\n     * @private\n     */\n    function polyhedron(args) {\n        var renderer = this,\n            elementProto = renderer.Element.prototype,\n            result = renderer.g(),\n            destroy = result.destroy;\n        if (!this.styledMode) {\n            result.attr({\n                'stroke-linejoin': 'round'\n            });\n        }\n        result.faces = [];\n        // Destroy all children\n        result.destroy = function () {\n            for (var i = 0; i < result.faces.length; i++) {\n                result.faces[i].destroy();\n            }\n            return destroy.call(this);\n        };\n        result.attr = function (hash, val, complete, continueAnimation) {\n            if (typeof hash === 'object' && SVGRenderer3D_defined(hash.faces)) {\n                while (result.faces.length > hash.faces.length) {\n                    result.faces.pop().destroy();\n                }\n                while (result.faces.length < hash.faces.length) {\n                    result.faces.push(renderer.face3d().add(result));\n                }\n                for (var i = 0; i < hash.faces.length; i++) {\n                    if (renderer.styledMode) {\n                        delete hash.faces[i].fill;\n                    }\n                    result.faces[i].attr(hash.faces[i], null, complete, continueAnimation);\n                }\n                delete hash.faces;\n            }\n            return elementProto.attr.apply(this, arguments);\n        };\n        result.animate = function (params, duration, complete) {\n            if (params === null || params === void 0 ? void 0 : params.faces) {\n                while (result.faces.length > params.faces.length) {\n                    result.faces.pop().destroy();\n                }\n                while (result.faces.length < params.faces.length) {\n                    result.faces.push(renderer.face3d().add(result));\n                }\n                for (var i = 0; i < params.faces.length; i++) {\n                    result.faces[i].animate(params.faces[i], duration, complete);\n                }\n                delete params.faces;\n            }\n            return elementProto.animate.apply(this, arguments);\n        };\n        return result.attr(args);\n    }\n    /**\n     * Return result, generalization\n     * @private\n     * @requires highcharts-3d\n     */\n    function element3d(type, shapeArgs) {\n        var elem3d = new SVG_SVGElement3D.types[type](this, 'g');\n        elem3d.initArgs(shapeArgs);\n        return elem3d;\n    }\n    /**\n     * Generalized, so now use simply\n     * @private\n     */\n    function cuboid(shapeArgs) {\n        return this.element3d('cuboid', shapeArgs);\n    }\n    /**\n     * Generates a cuboid path and zIndexes\n     * @private\n     */\n    function cuboidPath(shapeArgs) {\n        var x = shapeArgs.x || 0, y = shapeArgs.y || 0, z = shapeArgs.z || 0, \n            // For side calculation (right/left)\n            // there is a need for height (and other shapeArgs arguments)\n            // to be at least 1px\n            h = shapeArgs.height || 0, w = shapeArgs.width || 0, d = shapeArgs.depth || 0, chart = charts[this.chartIndex], options3d = chart.options.chart.options3d, alpha = options3d.alpha, \n            // Priority for x axis is the biggest,\n            // because of x direction has biggest influence on zIndex\n            incrementX = 1000000, \n            // Y axis has the smallest priority in case of our charts\n            // (needs to be set because of stacking)\n            incrementY = 10, incrementZ = 100, forcedSides = [];\n        var shape,\n            zIndex = 0, \n            // The 8 corners of the cube\n            pArr = [{\n                    x: x,\n                    y: y,\n                    z: z\n                }, {\n                    x: x + w,\n                    y: y,\n                    z: z\n                }, {\n                    x: x + w,\n                    y: y + h,\n                    z: z\n                }, {\n                    x: x,\n                    y: y + h,\n                    z: z\n                }, {\n                    x: x,\n                    y: y + h,\n                    z: z + d\n                }, {\n                    x: x + w,\n                    y: y + h,\n                    z: z + d\n                }, {\n                    x: x + w,\n                    y: y,\n                    z: z + d\n                }, {\n                    x: x,\n                    y: y,\n                    z: z + d\n                }];\n        // Apply perspective\n        pArr = SVGRenderer3D_perspective(pArr, chart, shapeArgs.insidePlotArea);\n        /**\n         * Helper method to decide which side is visible\n         * @private\n         */\n        var mapSidePath = function (i) {\n                // Added support for 0 value in columns, where height is 0\n                // but the shape is rendered.\n                // Height is used from 1st to 6th element of pArr\n                if (h === 0 && i > 1 && i < 6) { // [2, 3, 4, 5]\n                    return {\n                        x: pArr[i].x,\n                        // When height is 0 instead of cuboid we render plane\n                        // so it is needed to add fake 10 height to imitate\n                        // cuboid for side calculation\n                        y: pArr[i].y + 10,\n                        z: pArr[i].z\n                    };\n            }\n            // It is needed to calculate dummy sides (front/back) for\n            // breaking points in case of x and depth values. If column has\n            // side, it means that x values of front and back side are\n            // different.\n            if (pArr[0].x === pArr[7].x && i >= 4) { // [4, 5, 6, 7]\n                return {\n                    x: pArr[i].x + 10,\n                    // When height is 0 instead of cuboid we render plane\n                    // so it is needed to add fake 10 height to imitate\n                    // cuboid for side calculation\n                    y: pArr[i].y,\n                    z: pArr[i].z\n                };\n            }\n            // Added dummy depth\n            if (d === 0 && i < 2 || i > 5) { // [0, 1, 6, 7]\n                return {\n                    x: pArr[i].x,\n                    // When height is 0 instead of cuboid we render plane\n                    // so it is needed to add fake 10 height to imitate\n                    // cuboid for side calculation\n                    y: pArr[i].y,\n                    z: pArr[i].z + 10\n                };\n            }\n            return pArr[i];\n        }, \n        /**\n         * Method creating the final side\n         * @private\n         */\n        mapPath = function (i) { return (pArr[i]); }, \n        /**\n         * First value - path with specific face\n         * Second value - added info about side for later calculations.\n         *                 Possible second values are 0 for path1, 1 for\n         *                 path2 and -1 for no path chosen.\n         * Third value - string containing information about current side of\n         *               cuboid for forcing side rendering.\n         * @private\n         */\n        pickShape = function (verticesIndex1, verticesIndex2, side) {\n            var // An array of vertices for cuboid face\n                face1 = verticesIndex1.map(mapPath),\n                face2 = verticesIndex2.map(mapPath), \n                // Dummy face is calculated the same way as standard face,\n                // but if cuboid height is 0 additional height is added so\n                // it is possible to use this vertices array for visible\n                // face calculation\n                dummyFace1 = verticesIndex1.map(mapSidePath),\n                dummyFace2 = verticesIndex2.map(mapSidePath);\n            var ret = [[], -1];\n            if (SVGRenderer3D_shapeArea(face1) < 0) {\n                ret = [face1, 0];\n            }\n            else if (SVGRenderer3D_shapeArea(face2) < 0) {\n                ret = [face2, 1];\n            }\n            else if (side) {\n                forcedSides.push(side);\n                if (SVGRenderer3D_shapeArea(dummyFace1) < 0) {\n                    ret = [face1, 0];\n                }\n                else if (SVGRenderer3D_shapeArea(dummyFace2) < 0) {\n                    ret = [face2, 1];\n                }\n                else {\n                    ret = [face1, 0]; // Force side calculation.\n                }\n            }\n            return ret;\n        };\n        // Front or back\n        var front = [3, 2, 1, 0],\n            back = [7, 6, 5, 4];\n        shape = pickShape(front, back, 'front');\n        var path1 = shape[0],\n            isFront = shape[1];\n        // Top or bottom\n        var top = [1, 6, 7, 0],\n            bottom = [4, 5, 2, 3];\n        shape = pickShape(top, bottom, 'top');\n        var path2 = shape[0],\n            isTop = shape[1];\n        // Side\n        var right = [1, 2, 5, 6],\n            left = [0, 7, 4, 3];\n        shape = pickShape(right, left, 'side');\n        var path3 = shape[0],\n            isRight = shape[1];\n        /* New block used for calculating zIndex. It is basing on X, Y and Z\n        position of specific columns. All zIndexes (for X, Y and Z values) are\n        added to the final zIndex, where every value has different priority. The\n        biggest priority is in X and Z directions, the lowest index is for\n        stacked columns (Y direction and the same X and Z positions). Big\n        differences between priorities is made because we need to ensure that\n        even for big changes in Y and Z parameters all columns will be drawn\n        correctly. */\n        if (isRight === 1) {\n            // It is needed to connect value with current chart width\n            // for big chart size.\n            zIndex += incrementX * (chart.plotWidth - x);\n        }\n        else if (!isRight) {\n            zIndex += incrementX * x;\n        }\n        zIndex += incrementY * (!isTop ||\n            // Numbers checked empirically\n            (alpha >= 0 && alpha <= 180 || alpha < 360 && alpha > 357.5) ?\n            chart.plotHeight - y : 10 + y);\n        if (isFront === 1) {\n            zIndex += incrementZ * (z);\n        }\n        else if (!isFront) {\n            zIndex += incrementZ * (1000 - z);\n        }\n        return {\n            front: this.toLinePath(path1, true),\n            top: this.toLinePath(path2, true),\n            side: this.toLinePath(path3, true),\n            zIndexes: {\n                group: Math.round(zIndex)\n            },\n            forcedSides: forcedSides,\n            // Additional info about zIndexes\n            isFront: isFront,\n            isTop: isTop\n        }; // #4774\n    }\n    /** @private */\n    function arc3d(attribs) {\n        var renderer = this, wrapper = renderer.g(), elementProto = renderer.Element.prototype, customAttribs = [\n                'alpha', 'beta',\n                'x', 'y', 'r', 'innerR', 'start', 'end', 'depth'\n            ];\n        /**\n         * Get custom attributes. Don't mutate the original object and return an\n         * object with only custom attr.\n         * @private\n         */\n        function extractCustom(params) {\n            var ca = {};\n            params = SVGRenderer3D_merge(params); // Don't mutate the original object\n            var key;\n            for (key in params) {\n                if (customAttribs.indexOf(key) !== -1) {\n                    ca[key] = params[key];\n                    delete params[key];\n                }\n            }\n            return Object.keys(ca).length ? [ca, params] : false;\n        }\n        attribs = SVGRenderer3D_merge(attribs);\n        attribs.alpha = (attribs.alpha || 0) * SVGRenderer3D_deg2rad;\n        attribs.beta = (attribs.beta || 0) * SVGRenderer3D_deg2rad;\n        // Create the different sub sections of the shape\n        wrapper.top = renderer.path();\n        wrapper.side1 = renderer.path();\n        wrapper.side2 = renderer.path();\n        wrapper.inn = renderer.path();\n        wrapper.out = renderer.path();\n        /* eslint-disable no-invalid-this */\n        // Add all faces\n        wrapper.onAdd = function () {\n            var parent = wrapper.parentGroup,\n                className = wrapper.attr('class');\n            wrapper.top.add(wrapper);\n            // These faces are added outside the wrapper group because the\n            // z-index relates to neighbour elements as well\n            for (var _i = 0, _a = ['out', 'inn', 'side1', 'side2']; _i < _a.length; _i++) {\n                var face = _a[_i];\n                wrapper[face]\n                    .attr({\n                    'class': className + ' highcharts-3d-side'\n                })\n                    .add(parent);\n            }\n        };\n        var _loop_1 = function (fn) {\n                wrapper[fn] = function () {\n                    var args = arguments;\n                for (var _i = 0, _a = ['top', 'out', 'inn', 'side1', 'side2']; _i < _a.length; _i++) {\n                    var face = _a[_i];\n                    wrapper[face][fn].apply(wrapper[face], args);\n                }\n            };\n        };\n        // Cascade to faces\n        for (var _i = 0, _a = ['addClass', 'removeClass']; _i < _a.length; _i++) {\n            var fn = _a[_i];\n            _loop_1(fn);\n        }\n        /**\n         * Compute the transformed paths and set them to the composite shapes\n         * @private\n         */\n        wrapper.setPaths = function (attribs) {\n            var paths = wrapper.renderer.arc3dPath(attribs),\n                zIndex = paths.zTop * 100;\n            wrapper.attribs = attribs;\n            wrapper.top.attr({ d: paths.top, zIndex: paths.zTop });\n            wrapper.inn.attr({ d: paths.inn, zIndex: paths.zInn });\n            wrapper.out.attr({ d: paths.out, zIndex: paths.zOut });\n            wrapper.side1.attr({ d: paths.side1, zIndex: paths.zSide1 });\n            wrapper.side2.attr({ d: paths.side2, zIndex: paths.zSide2 });\n            // Show all children\n            wrapper.zIndex = zIndex;\n            wrapper.attr({ zIndex: zIndex });\n            // Set the radial gradient center the first time\n            if (attribs.center) {\n                wrapper.top.setRadialReference(attribs.center);\n                delete attribs.center;\n            }\n        };\n        wrapper.setPaths(attribs);\n        /**\n         * Apply the fill to the top and a darker shade to the sides\n         * @private\n         */\n        wrapper.fillSetter = function (value) {\n            var darker = SVGRenderer3D_color(value).brighten(-0.1).get();\n            this.fill = value;\n            this.side1.attr({ fill: darker });\n            this.side2.attr({ fill: darker });\n            this.inn.attr({ fill: darker });\n            this.out.attr({ fill: darker });\n            this.top.attr({ fill: value });\n            return this;\n        };\n        // Apply the same value to all. These properties cascade down to the\n        // children when set to the composite arc3d.\n        for (var _b = 0, _c = ['opacity', 'translateX', 'translateY', 'visibility']; _b < _c.length; _b++) {\n            var setter = _c[_b];\n            wrapper[setter + 'Setter'] = function (value, key) {\n                wrapper[key] = value;\n                for (var _i = 0, _a = ['out', 'inn', 'side1', 'side2', 'top']; _i < _a.length; _i++) {\n                    var el = _a[_i];\n                    wrapper[el].attr(key, value);\n                }\n            };\n        }\n        // Override attr to remove shape attributes and use those to set child\n        // paths\n        wrapper.attr = function (params) {\n            if (typeof params === 'object') {\n                var paramArr = extractCustom(params);\n                if (paramArr) {\n                    var ca = paramArr[0];\n                    arguments[0] = paramArr[1];\n                    // Translate alpha and beta to rotation\n                    if (ca.alpha !== void 0) {\n                        ca.alpha *= SVGRenderer3D_deg2rad;\n                    }\n                    if (ca.beta !== void 0) {\n                        ca.beta *= SVGRenderer3D_deg2rad;\n                    }\n                    SVGRenderer3D_extend(wrapper.attribs, ca);\n                    if (wrapper.attribs) {\n                        wrapper.setPaths(wrapper.attribs);\n                    }\n                }\n            }\n            return elementProto.attr.apply(wrapper, arguments);\n        };\n        // Override the animate function by sucking out custom parameters\n        // related to the shapes directly, and update the shapes from the\n        // animation step.\n        wrapper.animate = function (params, animation, complete) {\n            var from = this.attribs,\n                randomProp = 'data-' +\n                    Math.random().toString(26).substring(2, 9);\n            // Attribute-line properties connected to 3D. These shouldn't have\n            // been in the attribs collection in the first place.\n            delete params.center;\n            delete params.z;\n            var anim = animObject(SVGRenderer3D_pick(animation,\n                this.renderer.globalAnimation));\n            if (anim.duration) {\n                var paramArr = extractCustom(params);\n                // Params need to have a property in order for the step to run\n                // (#5765, #7097, #7437)\n                wrapper[randomProp] = 0;\n                params[randomProp] = 1;\n                wrapper[randomProp + 'Setter'] = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop;\n                if (paramArr) {\n                    var to_1 = paramArr[0], // Custom attr\n                        interpolate_1 = function (key,\n                        pos) { return (from[key] + (SVGRenderer3D_pick(to_1[key],\n                        from[key]) -\n                            from[key]) * pos); };\n                    anim.step = function (a, fx) {\n                        if (fx.prop === randomProp) {\n                            fx.elem.setPaths(SVGRenderer3D_merge(from, {\n                                x: interpolate_1('x', fx.pos),\n                                y: interpolate_1('y', fx.pos),\n                                r: interpolate_1('r', fx.pos),\n                                innerR: interpolate_1('innerR', fx.pos),\n                                start: interpolate_1('start', fx.pos),\n                                end: interpolate_1('end', fx.pos),\n                                depth: interpolate_1('depth', fx.pos)\n                            }));\n                        }\n                    };\n                }\n                animation = anim; // Only when duration (#5572)\n            }\n            return elementProto.animate.call(this, params, animation, complete);\n        };\n        // Destroy all children\n        wrapper.destroy = function () {\n            this.top.destroy();\n            this.out.destroy();\n            this.inn.destroy();\n            this.side1.destroy();\n            this.side2.destroy();\n            return elementProto.destroy.call(this);\n        };\n        // Hide all children\n        wrapper.hide = function () {\n            this.top.hide();\n            this.out.hide();\n            this.inn.hide();\n            this.side1.hide();\n            this.side2.hide();\n        };\n        wrapper.show = function (inherit) {\n            this.top.show(inherit);\n            this.out.show(inherit);\n            this.inn.show(inherit);\n            this.side1.show(inherit);\n            this.side2.show(inherit);\n        };\n        /* eslint-enable no-invalid-this */\n        return wrapper;\n    }\n    /**\n     * Generate the paths required to draw a 3D arc.\n     * @private\n     */\n    function arc3dPath(shapeArgs) {\n        var cx = shapeArgs.x || 0, // X coordinate of the center\n            cy = shapeArgs.y || 0, // Y coordinate of the center\n            start = shapeArgs.start || 0, // Start angle\n            end = (shapeArgs.end || 0) - 0.00001, // End angle\n            r = shapeArgs.r || 0, // Radius\n            ir = shapeArgs.innerR || 0, // Inner radius\n            d = shapeArgs.depth || 0, // Depth\n            alpha = shapeArgs.alpha || 0, // Alpha rotation of the chart\n            beta = shapeArgs.beta || 0; // Beta rotation of the chart\n            // Derived Variables\n            var cs = Math.cos(start), // Cosinus of the start angle\n            ss = Math.sin(start), // Sinus of the start angle\n            ce = Math.cos(end), // Cosinus of the end angle\n            se = Math.sin(end), // Sinus of the end angle\n            rx = r * Math.cos(beta), // X-radius\n            ry = r * Math.cos(alpha), // Y-radius\n            irx = ir * Math.cos(beta), // X-radius (inner)\n            iry = ir * Math.cos(alpha), // Y-radius (inner)\n            dx = d * Math.sin(beta), // Distance between top and bottom in x\n            dy = d * Math.sin(alpha); // Distance between top and bottom in y\n            // TOP\n            var top = [\n                ['M',\n            cx + (rx * cs),\n            cy + (ry * ss)]\n            ];\n        top = top.concat(curveTo(cx, cy, rx, ry, start, end, 0, 0));\n        top.push([\n            'L', cx + (irx * ce), cy + (iry * se)\n        ]);\n        top = top.concat(curveTo(cx, cy, irx, iry, end, start, 0, 0));\n        top.push(['Z']);\n        // OUTSIDE\n        var b = (beta > 0 ? Math.PI / 2 : 0), a = (alpha > 0 ? 0 : Math.PI / 2);\n        var start2 = start > -b ? start : (end > -b ? -b : start),\n            end2 = end < PI - a ? end : (start < PI - a ? PI - a : end),\n            midEnd = 2 * PI - a;\n        // When slice goes over bottom middle, need to add both, left and right\n        // outer side. Additionally, when we cross right hand edge, create sharp\n        // edge. Outer shape/wall:\n        //\n        //            -------\n        //          /    ^    \\\n        //    4)   /   /   \\   \\  1)\n        //        /   /     \\   \\\n        //       /   /       \\   \\\n        // (c)=> ====         ==== <=(d)\n        //       \\   \\       /   /\n        //        \\   \\<=(a)/   /\n        //         \\   \\   /   / <=(b)\n        //    3)    \\    v    /  2)\n        //            -------\n        //\n        // (a) - inner side\n        // (b) - outer side\n        // (c) - left edge (sharp)\n        // (d) - right edge (sharp)\n        // 1..n - rendering order for startAngle = 0, when set to e.g 90, order\n        // changes clockwise (1->2, 2->3, n->1) and counterclockwise for\n        // negative startAngle\n        var out = [\n                ['M',\n            cx + (rx * cos(start2)),\n            cy + (ry * sin(start2))]\n            ];\n        out = out.concat(curveTo(cx, cy, rx, ry, start2, end2, 0, 0));\n        // When shape is wide, it can cross both, (c) and (d) edges, when using\n        // startAngle\n        if (end > midEnd && start < midEnd) {\n            // Go to outer side\n            out.push([\n                'L', cx + (rx * cos(end2)) + dx, cy + (ry * sin(end2)) + dy\n            ]);\n            // Curve to the right edge of the slice (d)\n            out = out.concat(curveTo(cx, cy, rx, ry, end2, midEnd, dx, dy));\n            // Go to the inner side\n            out.push([\n                'L', cx + (rx * cos(midEnd)), cy + (ry * sin(midEnd))\n            ]);\n            // Curve to the true end of the slice\n            out = out.concat(curveTo(cx, cy, rx, ry, midEnd, end, 0, 0));\n            // Go to the outer side\n            out.push([\n                'L', cx + (rx * cos(end)) + dx, cy + (ry * sin(end)) + dy\n            ]);\n            // Go back to middle (d)\n            out = out.concat(curveTo(cx, cy, rx, ry, end, midEnd, dx, dy));\n            out.push([\n                'L', cx + (rx * cos(midEnd)), cy + (ry * sin(midEnd))\n            ]);\n            // Go back to the left edge\n            out = out.concat(curveTo(cx, cy, rx, ry, midEnd, end2, 0, 0));\n            // But shape can cross also only (c) edge:\n        }\n        else if (end > PI - a && start < PI - a) {\n            // Go to outer side\n            out.push([\n                'L',\n                cx + (rx * Math.cos(end2)) + dx,\n                cy + (ry * Math.sin(end2)) + dy\n            ]);\n            // Curve to the true end of the slice\n            out = out.concat(curveTo(cx, cy, rx, ry, end2, end, dx, dy));\n            // Go to the inner side\n            out.push([\n                'L', cx + (rx * Math.cos(end)), cy + (ry * Math.sin(end))\n            ]);\n            // Go back to the artificial end2\n            out = out.concat(curveTo(cx, cy, rx, ry, end, end2, 0, 0));\n        }\n        out.push([\n            'L',\n            cx + (rx * Math.cos(end2)) + dx,\n            cy + (ry * Math.sin(end2)) + dy\n        ]);\n        out = out.concat(curveTo(cx, cy, rx, ry, end2, start2, dx, dy));\n        out.push(['Z']);\n        // INSIDE\n        var inn = [\n                ['M',\n            cx + (irx * cs),\n            cy + (iry * ss)]\n            ];\n        inn = inn.concat(curveTo(cx, cy, irx, iry, start, end, 0, 0));\n        inn.push([\n            'L',\n            cx + (irx * Math.cos(end)) + dx,\n            cy + (iry * Math.sin(end)) + dy\n        ]);\n        inn = inn.concat(curveTo(cx, cy, irx, iry, end, start, dx, dy));\n        inn.push(['Z']);\n        // SIDES\n        var side1 = [\n                ['M',\n            cx + (rx * cs),\n            cy + (ry * ss)],\n                ['L',\n            cx + (rx * cs) + dx,\n            cy + (ry * ss) + dy],\n                ['L',\n            cx + (irx * cs) + dx,\n            cy + (iry * ss) + dy],\n                ['L',\n            cx + (irx * cs),\n            cy + (iry * ss)],\n                ['Z']\n            ];\n        var side2 = [\n                ['M',\n            cx + (rx * ce),\n            cy + (ry * se)],\n                ['L',\n            cx + (rx * ce) + dx,\n            cy + (ry * se) + dy],\n                ['L',\n            cx + (irx * ce) + dx,\n            cy + (iry * se) + dy],\n                ['L',\n            cx + (irx * ce),\n            cy + (iry * se)],\n                ['Z']\n            ];\n        // Correction for changed position of vanishing point caused by alpha\n        // and beta rotations\n        var angleCorr = Math.atan2(dy, -dx);\n        var angleEnd = Math.abs(end + angleCorr),\n            angleStart = Math.abs(start + angleCorr),\n            angleMid = Math.abs((start + end) / 2 + angleCorr);\n        /**\n         * Set to 0-PI range\n         * @private\n         */\n        function toZeroPIRange(angle) {\n            angle = angle % (2 * Math.PI);\n            if (angle > Math.PI) {\n                angle = 2 * Math.PI - angle;\n            }\n            return angle;\n        }\n        angleEnd = toZeroPIRange(angleEnd);\n        angleStart = toZeroPIRange(angleStart);\n        angleMid = toZeroPIRange(angleMid);\n        // *1e5 is to compensate pInt in zIndexSetter\n        var incPrecision = 1e5,\n            a1 = angleMid * incPrecision,\n            a2 = angleStart * incPrecision,\n            a3 = angleEnd * incPrecision;\n        return {\n            top: top,\n            // Max angle is PI, so this is always higher\n            zTop: Math.PI * incPrecision + 1,\n            out: out,\n            zOut: Math.max(a1, a2, a3),\n            inn: inn,\n            zInn: Math.max(a1, a2, a3),\n            side1: side1,\n            // To keep below zOut and zInn in case of same values\n            zSide1: a3 * 0.99,\n            side2: side2,\n            zSide2: a2 * 0.99\n        };\n    }\n})(SVGRenderer3D || (SVGRenderer3D = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var SVG_SVGRenderer3D = (SVGRenderer3D);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Axis\"],\"commonjs\":[\"highcharts\",\"Axis\"],\"commonjs2\":[\"highcharts\",\"Axis\"],\"root\":[\"Highcharts\",\"Axis\"]}\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_ = __webpack_require__(532);\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default = /*#__PURE__*/__webpack_require__.n(highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_);\n;// ./code/es5/es-modules/Core/Axis/ZAxis.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar ZAxis_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar ZAxis_defaultOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defaultOptions;\n\nvar ZAxis_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, ZAxis_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, ZAxis_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, splat = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).splat;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction chartAddZAxis(options) {\n    return new ZAxis(this, options);\n}\n/**\n * Get the Z axis in addition to the default X and Y.\n * @private\n */\nfunction onChartAfterCreateAxes() {\n    var _this = this;\n    var zAxisOptions = this.options.zAxis = splat(this.options.zAxis || {});\n    if (!this.is3d()) {\n        return;\n    }\n    this.zAxis = [];\n    zAxisOptions.forEach(function (axisOptions) {\n        _this.addZAxis(axisOptions).setScale();\n    });\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * 3D axis for z coordinates.\n * @private\n */\nvar ZAxis = /** @class */ (function (_super) {\n    ZAxis_extends(ZAxis, _super);\n    function ZAxis() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        var _this = _super !== null && _super.apply(this,\n            arguments) || this;\n        _this.isZAxis = true;\n        return _this;\n    }\n    ZAxis.compose = function (ChartClass) {\n        var chartProto = ChartClass.prototype;\n        if (!chartProto.addZAxis) {\n            ZAxis_defaultOptions.zAxis = ZAxis_merge(ZAxis_defaultOptions.xAxis, {\n                offset: 0,\n                lineWidth: 0\n            });\n            chartProto.addZAxis = chartAddZAxis;\n            chartProto.collectionsWithInit.zAxis = [chartProto.addZAxis];\n            chartProto.collectionsWithUpdate.push('zAxis');\n            ZAxis_addEvent(ChartClass, 'afterCreateAxes', onChartAfterCreateAxes);\n        }\n    };\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    ZAxis.prototype.init = function (chart, userOptions) {\n        // #14793, this used to be set on the prototype\n        this.isZAxis = true;\n        _super.prototype.init.call(this, chart, userOptions, 'zAxis');\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    ZAxis.prototype.getSeriesExtremes = function () {\n        var _this = this;\n        this.hasVisibleSeries = false;\n        // Reset properties in case we're redrawing (#3353)\n        this.dataMin = this.dataMax = this.ignoreMinPadding = (this.ignoreMaxPadding = void 0);\n        if (this.stacking) {\n            this.stacking.buildStacks();\n        }\n        // Loop through this axis' series\n        this.series.forEach(function (series) {\n            if (series.reserveSpace()) {\n                var threshold = series.options.threshold;\n                _this.hasVisibleSeries = true;\n                // Validate threshold in logarithmic axes\n                if (_this.positiveValuesOnly && threshold <= 0) {\n                    threshold = void 0;\n                }\n                var zData = series.getColumn('z');\n                if (zData.length) {\n                    _this.dataMin = Math.min(ZAxis_pick(_this.dataMin, zData[0]), Math.min.apply(null, zData));\n                    _this.dataMax = Math.max(ZAxis_pick(_this.dataMax, zData[0]), Math.max.apply(null, zData));\n                }\n            }\n        });\n    };\n    /**\n     * @private\n     */\n    ZAxis.prototype.setAxisSize = function () {\n        var _a;\n        var chart = this.chart;\n        _super.prototype.setAxisSize.call(this);\n        this.width = this.len = ((_a = chart.options.chart.options3d) === null || _a === void 0 ? void 0 : _a.depth) || 0;\n        this.right = chart.chartWidth - this.width - this.left;\n    };\n    return ZAxis;\n}((highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default())));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Axis_ZAxis = (ZAxis);\n\n;// ./code/es5/es-modules/Series/Column3D/Column3DComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar Column3DComposition_composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed;\n\nvar Column3DComposition_perspective = Core_Math3D.perspective;\n\nvar Column3DComposition_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, Column3DComposition_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, Column3DComposition_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, Column3DComposition_pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique, Column3DComposition_wrap = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).wrap;\n/* *\n *\n *  Functions\n *\n * */\n/** @private */\nfunction columnSeriesTranslate3dShapes() {\n    var series = this,\n        chart = series.chart,\n        seriesOptions = series.options,\n        depth = seriesOptions.depth,\n        stack = seriesOptions.stacking ?\n            (seriesOptions.stack || 0) :\n            series.index; // #4743\n        var z = stack * (depth + (seriesOptions.groupZPadding || 1)),\n        borderCrisp = series.borderWidth % 2 ? 0.5 : 0,\n        point2dPos; // Position of point in 2D, used for 3D position calculation\n        if (chart.inverted && !series.yAxis.reversed) {\n            borderCrisp *= -1;\n    }\n    if (seriesOptions.grouping !== false) {\n        z = 0;\n    }\n    z += (seriesOptions.groupZPadding || 1);\n    for (var _i = 0, _a = series.points; _i < _a.length; _i++) {\n        var point = _a[_i];\n        // #7103 Reset outside3dPlot flag\n        point.outside3dPlot = null;\n        if (point.y !== null) {\n            var shapeArgs = Column3DComposition_extend({ x: 0, y: 0, width: 0, height: 0 }, point.shapeArgs || {}), \n                // Array for final shapeArgs calculation.\n                // We are checking two dimensions (x and y).\n                dimensions = [['x', 'width'], ['y', 'height']], tooltipPos = point.tooltipPos;\n            var borderlessBase = // Crisped rects can have +/- 0.5 pixels offset.\n                 void 0; // Crisped rects can have +/- 0.5 pixels offset.\n                // #3131 We need to check if column is inside plotArea.\n                for (var _b = 0, dimensions_1 = dimensions; _b < dimensions_1.length; _b++) {\n                    var d = dimensions_1[_b];\n                borderlessBase = shapeArgs[d[0]] - borderCrisp;\n                if (borderlessBase < 0) {\n                    // If borderLessBase is smaller than 0, it is needed to set\n                    // its value to 0 or 0.5 depending on borderWidth\n                    // borderWidth may be even or odd.\n                    shapeArgs[d[1]] += shapeArgs[d[0]] + borderCrisp;\n                    shapeArgs[d[0]] = -borderCrisp;\n                    borderlessBase = 0;\n                }\n                if ((borderlessBase + shapeArgs[d[1]] >\n                    series[d[0] + 'Axis'].len) &&\n                    // Do not change height/width of column if 0 (#6708)\n                    shapeArgs[d[1]] !== 0) {\n                    shapeArgs[d[1]] =\n                        series[d[0] + 'Axis'].len -\n                            shapeArgs[d[0]];\n                }\n                if (\n                // Do not remove columns with zero height/width.\n                shapeArgs[d[1]] !== 0 &&\n                    (shapeArgs[d[0]] >= series[d[0] + 'Axis'].len ||\n                        shapeArgs[d[0]] + shapeArgs[d[1]] <= borderCrisp)) {\n                    // Set args to 0 if column is outside the chart.\n                    for (var key in shapeArgs) { // eslint-disable-line guard-for-in\n                        // #13840\n                        shapeArgs[key] = key === 'y' ? -9999 : 0;\n                    }\n                    // #7103 outside3dPlot flag is set on Points which are\n                    // currently outside of plot.\n                    point.outside3dPlot = true;\n                }\n            }\n            // Change from 2d to 3d\n            if (point.shapeType === 'roundedRect') {\n                point.shapeType = 'cuboid';\n            }\n            point.shapeArgs = Column3DComposition_extend(shapeArgs, {\n                z: z,\n                depth: depth,\n                insidePlotArea: true\n            });\n            // Point's position in 2D\n            point2dPos = {\n                x: shapeArgs.x + shapeArgs.width / 2,\n                y: shapeArgs.y,\n                z: z + depth / 2 // The center of column in Z dimension\n            };\n            // Recalculate point positions for inverted graphs\n            if (chart.inverted) {\n                point2dPos.x = shapeArgs.height;\n                point2dPos.y = point.clientX || 0;\n            }\n            // Crosshair positions\n            point.axisXpos = point2dPos.x;\n            point.axisYpos = point2dPos.y;\n            point.axisZpos = point2dPos.z;\n            // Calculate and store point's position in 3D,\n            // using perspective method.\n            point.plot3d = Column3DComposition_perspective([point2dPos], chart, true, false)[0];\n            // Translate the tooltip position in 3d space\n            if (tooltipPos) {\n                var translatedTTPos = Column3DComposition_perspective([{\n                            x: tooltipPos[0],\n                            y: tooltipPos[1],\n                            z: z + depth / 2 // The center of column in Z dimension\n                        }], chart, true, false)[0];\n                point.tooltipPos = [translatedTTPos.x, translatedTTPos.y];\n            }\n        }\n    }\n    // Store for later use #4067\n    series.z = z;\n}\n/** @private */\nfunction Column3DComposition_compose(SeriesClass, StackItemClass) {\n    if (Column3DComposition_pushUnique(Column3DComposition_composed, 'Column3D')) {\n        var seriesProto = SeriesClass.prototype,\n            stackItemProto = StackItemClass.prototype,\n            _a = SeriesClass.types,\n            ColumnSeriesClass = _a.column,\n            ColumnRangeSeriesClass = _a.columnRange;\n        Column3DComposition_wrap(seriesProto, 'alignDataLabel', wrapSeriesAlignDataLabel);\n        Column3DComposition_wrap(seriesProto, 'justifyDataLabel', wrapSeriesJustifyDataLabel);\n        Column3DComposition_wrap(stackItemProto, 'getStackBox', wrapStackItemGetStackBox);\n        if (ColumnSeriesClass) {\n            var columnSeriesProto = ColumnSeriesClass.prototype,\n                columnPointProto = columnSeriesProto.pointClass.prototype;\n            columnSeriesProto.translate3dPoints = function () { return void 0; };\n            columnSeriesProto.translate3dShapes = columnSeriesTranslate3dShapes;\n            Column3DComposition_addEvent(columnSeriesProto, 'afterInit', onColumnSeriesAfterInit);\n            Column3DComposition_wrap(columnPointProto, 'hasNewShapeType', wrapColumnPointHasNewShapeType);\n            Column3DComposition_wrap(columnSeriesProto, 'animate', wrapColumnSeriesAnimate);\n            Column3DComposition_wrap(columnSeriesProto, 'plotGroup', wrapColumnSeriesPlotGroup);\n            Column3DComposition_wrap(columnSeriesProto, 'pointAttribs', wrapColumnSeriesPointAttribs);\n            Column3DComposition_wrap(columnSeriesProto, 'setState', wrapColumnSeriesSetState);\n            Column3DComposition_wrap(columnSeriesProto, 'setVisible', wrapColumnSeriesSetVisible);\n            Column3DComposition_wrap(columnSeriesProto, 'translate', wrapColumnSeriesTranslate);\n        }\n        if (ColumnRangeSeriesClass) {\n            var columnRangeSeriesProto = ColumnRangeSeriesClass.prototype,\n                columnRangePointProto = columnRangeSeriesProto.pointClass.prototype;\n            Column3DComposition_wrap(columnRangePointProto, 'hasNewShapeType', wrapColumnPointHasNewShapeType);\n            Column3DComposition_wrap(columnRangeSeriesProto, 'plotGroup', wrapColumnSeriesPlotGroup);\n            Column3DComposition_wrap(columnRangeSeriesProto, 'pointAttribs', wrapColumnSeriesPointAttribs);\n            Column3DComposition_wrap(columnRangeSeriesProto, 'setState', wrapColumnSeriesSetState);\n            Column3DComposition_wrap(columnRangeSeriesProto, 'setVisible', wrapColumnSeriesSetVisible);\n        }\n    }\n}\n/**\n * @private\n * @param {Highcharts.Chart} chart\n * Chart with stacks\n * @param {string} stacking\n * Stacking option\n */\nfunction retrieveStacks(chart, stacking) {\n    var series = chart.series,\n        stacks = { totalStacks: 0 };\n    var stackNumber,\n        i = 1;\n    series.forEach(function (s) {\n        stackNumber = Column3DComposition_pick(s.options.stack, (stacking ? 0 : series.length - 1 - s.index)); // #3841, #4532\n        if (!stacks[stackNumber]) {\n            stacks[stackNumber] = { series: [s], position: i };\n            i++;\n        }\n        else {\n            stacks[stackNumber].series.push(s);\n        }\n    });\n    stacks.totalStacks = i + 1;\n    return stacks;\n}\n/** @private */\nfunction onColumnSeriesAfterInit() {\n    if (this.chart.is3d()) {\n        var series = this,\n            seriesOptions = series.options,\n            grouping = seriesOptions.grouping,\n            stacking = seriesOptions.stacking,\n            reversedStacks = series.yAxis.options.reversedStacks;\n        var z = 0;\n        // @todo grouping === true ?\n        if (!(typeof grouping !== 'undefined' && !grouping)) {\n            var stacks = retrieveStacks(this.chart,\n                stacking),\n                stack = seriesOptions.stack || 0;\n            var i = // Position within the stack\n                 void 0; // Position within the stack\n                for (i = 0; i < stacks[stack].series.length; i++) {\n                    if (stacks[stack].series[i] === this) {\n                        break;\n                }\n            }\n            z = (10 * (stacks.totalStacks - stacks[stack].position)) +\n                (reversedStacks ? i : -i); // #4369\n            // In case when axis is reversed, columns are also reversed inside\n            // the group (#3737)\n            if (!this.xAxis.reversed) {\n                z = (stacks.totalStacks * 10) - z;\n            }\n        }\n        seriesOptions.depth = seriesOptions.depth || 25;\n        series.z = series.z || 0;\n        seriesOptions.zIndex = z;\n    }\n}\n/**\n * In 3D mode, simple checking for a new shape to animate is not enough.\n * Additionally check if graphic is a group of elements\n * @private\n */\nfunction wrapColumnPointHasNewShapeType(proceed) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    return this.series.chart.is3d() ?\n        this.graphic && this.graphic.element.nodeName !== 'g' :\n        proceed.apply(this, args);\n}\n/** @private */\nfunction wrapColumnSeriesAnimate(proceed) {\n    if (!this.chart.is3d()) {\n        proceed.apply(this, [].slice.call(arguments, 1));\n    }\n    else {\n        var args = arguments,\n            init = args[1],\n            yAxis = this.yAxis,\n            series = this,\n            reversed = this.yAxis.reversed;\n        if (init) {\n            for (var _i = 0, _a = series.points; _i < _a.length; _i++) {\n                var point = _a[_i];\n                if (point.y !== null) {\n                    point.height = point.shapeArgs.height;\n                    point.shapey = point.shapeArgs.y; // #2968\n                    point.shapeArgs.height = 1;\n                    if (!reversed) {\n                        if (point.stackY) {\n                            point.shapeArgs.y =\n                                point.plotY +\n                                    yAxis.translate(point.stackY);\n                        }\n                        else {\n                            point.shapeArgs.y =\n                                point.plotY +\n                                    (point.negative ?\n                                        -point.height :\n                                        point.height);\n                        }\n                    }\n                }\n            }\n        }\n        else { // Run the animation\n            for (var _b = 0, _c = series.points; _b < _c.length; _b++) {\n                var point = _c[_b];\n                if (point.y !== null) {\n                    point.shapeArgs.height = point.height;\n                    point.shapeArgs.y = point.shapey; // #2968\n                    // null value do not have a graphic\n                    if (point.graphic) {\n                        point.graphic[point.outside3dPlot ?\n                            'attr' :\n                            'animate'](point.shapeArgs, series.options.animation);\n                    }\n                }\n            }\n            // Redraw datalabels to the correct position\n            this.drawDataLabels();\n        }\n    }\n}\n/**\n * In case of 3d columns there is no sense to add these columns to a specific\n * series group. If a series is added to a group all columns will have the same\n * zIndex in comparison to another series.\n * @private\n */\nfunction wrapColumnSeriesPlotGroup(proceed, prop, _name, _visibility, _zIndex, parent) {\n    if (prop !== 'dataLabelsGroup' && prop !== 'markerGroup') {\n        if (this.chart.is3d()) {\n            if (this[prop]) {\n                delete this[prop];\n            }\n            if (parent) {\n                if (!this.chart.columnGroup) {\n                    this.chart.columnGroup =\n                        this.chart.renderer.g('columnGroup').add(parent);\n                }\n                this[prop] = this.chart.columnGroup;\n                this.chart.columnGroup.attr(this.getPlotBox());\n                this[prop].survive = true;\n                if (prop === 'group') {\n                    arguments[3] = 'visible';\n                    // For 3D column group and markerGroup should be visible\n                }\n            }\n        }\n    }\n    return proceed.apply(this, Array.prototype.slice.call(arguments, 1));\n}\n/** @private */\nfunction wrapColumnSeriesPointAttribs(proceed) {\n    var attr = proceed.apply(this,\n        [].slice.call(arguments, 1));\n    if (this.chart.is3d && this.chart.is3d()) {\n        // Set the fill color to the fill color to provide a smooth edge\n        attr.stroke = this.options.edgeColor || attr.fill;\n        attr['stroke-width'] = Column3DComposition_pick(this.options.edgeWidth, 1); // #4055\n    }\n    return attr;\n}\n/**\n * In 3D mode, all column-series are rendered in one main group. Because of that\n * we need to apply inactive state on all points.\n * @private\n */\nfunction wrapColumnSeriesSetState(proceed, state, inherit) {\n    var is3d = this.chart.is3d && this.chart.is3d();\n    if (is3d) {\n        this.options.inactiveOtherPoints = true;\n    }\n    proceed.call(this, state, inherit);\n    if (is3d) {\n        this.options.inactiveOtherPoints = false;\n    }\n}\n/**\n * When series is not added to group it is needed to change setVisible method to\n * allow correct Legend funcionality. This wrap is basing on pie chart series.\n * @private\n */\nfunction wrapColumnSeriesSetVisible(proceed, vis) {\n    var series = this;\n    if (series.chart.is3d()) {\n        for (var _i = 0, _a = series.points; _i < _a.length; _i++) {\n            var point = _a[_i];\n            point.visible = point.options.visible = vis =\n                typeof vis === 'undefined' ?\n                    !Column3DComposition_pick(series.visible, point.visible) : vis;\n            series.options.data[series.data.indexOf(point)] =\n                point.options;\n            if (point.graphic) {\n                point.graphic.attr({\n                    visibility: vis ? 'visible' : 'hidden'\n                });\n            }\n        }\n    }\n    proceed.apply(this, Array.prototype.slice.call(arguments, 1));\n}\n/** @private */\nfunction wrapColumnSeriesTranslate(proceed) {\n    proceed.apply(this, [].slice.call(arguments, 1));\n    // Do not do this if the chart is not 3D\n    if (this.chart.is3d()) {\n        this.translate3dShapes();\n    }\n}\n/** @private */\nfunction wrapSeriesAlignDataLabel(proceed, point, _dataLabel, options, alignTo) {\n    var chart = this.chart;\n    // In 3D we need to pass point.outsidePlot option to the justifyDataLabel\n    // method for disabling justifying dataLabels in columns outside plot\n    options.outside3dPlot = point.outside3dPlot;\n    // Only do this for 3D columns and it's derived series\n    if (chart.is3d() &&\n        this.is('column')) {\n        var series = this,\n            seriesOptions = series.options,\n            inside = Column3DComposition_pick(options.inside, !!series.options.stacking),\n            options3d = chart.options.chart.options3d,\n            xOffset = point.pointWidth / 2 || 0;\n        var dLPosition = {\n                x: alignTo.x + xOffset,\n                y: alignTo.y,\n                z: series.z + seriesOptions.depth / 2\n            };\n        if (chart.inverted) {\n            // Inside dataLabels are positioned according to above\n            // logic and there is no need to position them using\n            // non-3D algorighm (that use alignTo.width)\n            if (inside) {\n                alignTo.width = 0;\n                dLPosition.x += point.shapeArgs.height / 2;\n            }\n            // When chart is upside down\n            // (alpha angle between 180 and 360 degrees)\n            // it is needed to add column width to calculated value.\n            if (options3d.alpha >= 90 && options3d.alpha <= 270) {\n                dLPosition.y += point.shapeArgs.width;\n            }\n        }\n        // `dLPosition` is recalculated for 3D graphs\n        dLPosition = Column3DComposition_perspective([dLPosition], chart, true, false)[0];\n        alignTo.x = dLPosition.x - xOffset;\n        // #7103 If point is outside of plotArea, hide data label.\n        alignTo.y = point.outside3dPlot ? -9e9 : dLPosition.y;\n    }\n    proceed.apply(this, [].slice.call(arguments, 1));\n}\n/**\n * Don't use justifyDataLabel when point is outsidePlot.\n * @private\n */\nfunction wrapSeriesJustifyDataLabel(proceed) {\n    return (!(arguments[2].outside3dPlot) ?\n        proceed.apply(this, [].slice.call(arguments, 1)) :\n        false);\n}\n/**\n * Added stackLabels position calculation for 3D charts.\n * @private\n */\nfunction wrapStackItemGetStackBox(proceed, stackBoxProps) {\n    var stackBox = proceed.apply(this,\n        [].slice.call(arguments, 1));\n    // Only do this for 3D graph\n    var stackItem = this,\n        chart = this.axis.chart,\n        xWidth = stackBoxProps.width;\n    if (chart.is3d() && stackItem.base) {\n        // First element of stackItem.base is an index of base series.\n        var baseSeriesInd = +(stackItem.base).split(',')[0];\n        var columnSeries = chart.series[baseSeriesInd];\n        var options3d = chart.options.chart.options3d;\n        // Only do this if base series is a column or inherited type,\n        // use its barW, z and depth parameters\n        // for correct stackLabels position calculation\n        if (columnSeries &&\n            columnSeries.type === 'column') {\n            var dLPosition = {\n                    x: stackBox.x + (chart.inverted ? stackBox.height : xWidth / 2),\n                    y: stackBox.y,\n                    z: columnSeries.options.depth / 2\n                };\n            if (chart.inverted) {\n                // Do not use default offset calculation logic\n                // for 3D inverted stackLabels.\n                stackBox.width = 0;\n                // When chart is upside down\n                // (alpha angle between 180 and 360 degrees)\n                // it is needed to add column width to calculated value.\n                if (options3d.alpha >= 90 && options3d.alpha <= 270) {\n                    dLPosition.y += xWidth;\n                }\n            }\n            dLPosition = Column3DComposition_perspective([dLPosition], chart, true, false)[0];\n            stackBox.x = dLPosition.x - xWidth / 2;\n            stackBox.y = dLPosition.y;\n        }\n    }\n    return stackBox;\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar Column3DComposition = {\n    compose: Column3DComposition_compose\n};\n/* harmony default export */ var Column3D_Column3DComposition = (Column3DComposition);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Depth of the columns in a 3D column chart.\n *\n * @type      {number}\n * @default   25\n * @since     4.0\n * @product   highcharts\n * @requires  highcharts-3d\n * @apioption plotOptions.column.depth\n */\n/**\n * 3D columns only. The color of the edges. Similar to `borderColor`, except it\n * defaults to the same color as the column.\n *\n * @type      {Highcharts.ColorString}\n * @product   highcharts\n * @requires  highcharts-3d\n * @apioption plotOptions.column.edgeColor\n */\n/**\n * 3D columns only. The width of the colored edges.\n *\n * @type      {number}\n * @default   1\n * @product   highcharts\n * @requires  highcharts-3d\n * @apioption plotOptions.column.edgeWidth\n */\n/**\n * The spacing between columns on the Z Axis in a 3D chart.\n *\n * @type      {number}\n * @default   1\n * @since     4.0\n * @product   highcharts\n * @requires  highcharts-3d\n * @apioption plotOptions.column.groupZPadding\n */\n''; // Keeps doclets above in transpiled file\n\n;// ./code/es5/es-modules/Series/Pie3D/Pie3DPoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  3D pie series\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar Pie3DPoint_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar PiePoint = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.pie.prototype.pointClass;\n/* *\n *\n *  Class\n *\n * */\nvar Pie3DPoint = /** @class */ (function (_super) {\n    Pie3DPoint_extends(Pie3DPoint, _super);\n    function Pie3DPoint() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    Pie3DPoint.prototype.haloPath = function () {\n        var _a;\n        return ((_a = this.series) === null || _a === void 0 ? void 0 : _a.chart.is3d()) ?\n            [] : _super.prototype.haloPath.apply(this, arguments);\n    };\n    return Pie3DPoint;\n}(PiePoint));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Pie3D_Pie3DPoint = (Pie3DPoint);\n\n;// ./code/es5/es-modules/Series/Pie3D/Pie3DSeries.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  3D pie series\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar Pie3DSeries_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar Pie3DSeries_composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed, Pie3DSeries_deg2rad = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).deg2rad;\n\n\nvar PieSeries = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.pie;\n\nvar Pie3DSeries_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, Pie3DSeries_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, Pie3DSeries_pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique;\n/* *\n *\n *  Class\n *\n * */\nvar Pie3DSeries = /** @class */ (function (_super) {\n    Pie3DSeries_extends(Pie3DSeries, _super);\n    function Pie3DSeries() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    Pie3DSeries.compose = function (SeriesClass) {\n        if (Pie3DSeries_pushUnique(Pie3DSeries_composed, 'Pie3D')) {\n            SeriesClass.types.pie = Pie3DSeries;\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    Pie3DSeries.prototype.addPoint = function () {\n        _super.prototype.addPoint.apply(this, arguments);\n        if (this.chart.is3d()) {\n            // Destroy (and rebuild) everything!!!\n            this.update(this.userOptions, true); // #3845 pass the old options\n        }\n    };\n    /**\n     * @private\n     */\n    Pie3DSeries.prototype.animate = function (init) {\n        if (!this.chart.is3d()) {\n            _super.prototype.animate.apply(this, arguments);\n        }\n        else {\n            var center = this.center,\n                group = this.group,\n                markerGroup = this.markerGroup;\n            var animation = this.options.animation,\n                attribs = void 0;\n            if (animation === true) {\n                animation = {};\n            }\n            // Initialize the animation\n            if (init) {\n                // Scale down the group and place it in the center\n                group.oldtranslateX = Pie3DSeries_pick(group.oldtranslateX, group.translateX);\n                group.oldtranslateY = Pie3DSeries_pick(group.oldtranslateY, group.translateY);\n                attribs = {\n                    translateX: center[0],\n                    translateY: center[1],\n                    scaleX: 0.001, // #1499\n                    scaleY: 0.001\n                };\n                group.attr(attribs);\n                if (markerGroup) {\n                    markerGroup.attrSetters = group.attrSetters;\n                    markerGroup.attr(attribs);\n                }\n                // Run the animation\n            }\n            else {\n                attribs = {\n                    translateX: group.oldtranslateX,\n                    translateY: group.oldtranslateY,\n                    scaleX: 1,\n                    scaleY: 1\n                };\n                group.animate(attribs, animation);\n                if (markerGroup) {\n                    markerGroup.animate(attribs, animation);\n                }\n            }\n        }\n    };\n    /**\n     * @private\n     */\n    Pie3DSeries.prototype.getDataLabelPosition = function (point, distance) {\n        var labelPosition = _super.prototype.getDataLabelPosition.call(this,\n            point,\n            distance);\n        if (this.chart.is3d()) {\n            var options3d = this.chart.options.chart.options3d,\n                shapeArgs = point.shapeArgs,\n                r = shapeArgs.r, \n                // #3240 issue with datalabels for 0 and null values\n                a1 = ((shapeArgs.alpha || (options3d === null || options3d === void 0 ? void 0 : options3d.alpha)) *\n                    Pie3DSeries_deg2rad),\n                b1 = ((shapeArgs.beta || (options3d === null || options3d === void 0 ? void 0 : options3d.beta)) *\n                    Pie3DSeries_deg2rad),\n                a2 = (shapeArgs.start + shapeArgs.end) / 2,\n                connectorPosition = labelPosition.connectorPosition,\n                yOffset = (-r * (1 - Math.cos(a1)) * Math.sin(a2)),\n                xOffset = r * (Math.cos(b1) - 1) * Math.cos(a2);\n            // Apply perspective on label positions\n            for (var _a = 0, _b = [\n                labelPosition === null || labelPosition === void 0 ? void 0 : labelPosition.natural,\n                connectorPosition.breakAt,\n                connectorPosition.touchingSliceAt\n            ]; _a < _b.length; _a++) {\n                var coordinates = _b[_a];\n                coordinates.x += xOffset;\n                coordinates.y += yOffset;\n            }\n        }\n        return labelPosition;\n    };\n    /**\n     * @private\n     */\n    Pie3DSeries.prototype.pointAttribs = function (point) {\n        var attr = _super.prototype.pointAttribs.apply(this,\n            arguments),\n            options = this.options;\n        if (this.chart.is3d() && !this.chart.styledMode) {\n            attr.stroke = options.edgeColor || point.color || this.color;\n            attr['stroke-width'] = Pie3DSeries_pick(options.edgeWidth, 1);\n        }\n        return attr;\n    };\n    /**\n     * @private\n     */\n    Pie3DSeries.prototype.translate = function () {\n        _super.prototype.translate.apply(this, arguments);\n        // Do not do this if the chart is not 3D\n        if (!this.chart.is3d()) {\n            return;\n        }\n        var series = this,\n            seriesOptions = series.options,\n            depth = seriesOptions.depth || 0,\n            options3d = series.chart.options.chart.options3d,\n            alpha = options3d.alpha,\n            beta = options3d.beta;\n        var z = seriesOptions.stacking ?\n                (seriesOptions.stack || 0) * depth :\n                series._i * depth;\n        z += depth / 2;\n        if (seriesOptions.grouping !== false) {\n            z = 0;\n        }\n        for (var _a = 0, _b = series.points; _a < _b.length; _a++) {\n            var point = _b[_a];\n            var shapeArgs = point.shapeArgs;\n            point.shapeType = 'arc3d';\n            shapeArgs.z = z;\n            shapeArgs.depth = depth * 0.75;\n            shapeArgs.alpha = alpha;\n            shapeArgs.beta = beta;\n            shapeArgs.center = series.center;\n            var angle = (shapeArgs.end + shapeArgs.start) / 2;\n            point.slicedTranslation = {\n                translateX: Math.round(Math.cos(angle) *\n                    seriesOptions.slicedOffset *\n                    Math.cos(alpha * Pie3DSeries_deg2rad)),\n                translateY: Math.round(Math.sin(angle) *\n                    seriesOptions.slicedOffset *\n                    Math.cos(alpha * Pie3DSeries_deg2rad))\n            };\n        }\n    };\n    /**\n     * @private\n     */\n    Pie3DSeries.prototype.drawTracker = function () {\n        _super.prototype.drawTracker.apply(this, arguments);\n        // Do not do this if the chart is not 3D\n        if (!this.chart.is3d()) {\n            return;\n        }\n        for (var _a = 0, _b = this.points; _a < _b.length; _a++) {\n            var point = _b[_a];\n            if (point.graphic) {\n                for (var _c = 0, _d = ['out', 'inn', 'side1', 'side2']; _c < _d.length; _c++) {\n                    var face = _d[_c];\n                    if (point.graphic) {\n                        point.graphic[face].element.point = point;\n                    }\n                }\n            }\n        }\n    };\n    return Pie3DSeries;\n}(PieSeries));\nPie3DSeries_extend(Pie3DSeries.prototype, {\n    pointClass: Pie3D_Pie3DPoint\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Pie3D_Pie3DSeries = (Pie3DSeries);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The thickness of a 3D pie.\n *\n * @type      {number}\n * @default   0\n * @since     4.0\n * @product   highcharts\n * @requires  highcharts-3d\n * @apioption plotOptions.pie.depth\n */\n''; // Keeps doclets above after transpiledion\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\",\"types\",\"scatter\"],\"commonjs\":[\"highcharts\",\"Series\",\"types\",\"scatter\"],\"commonjs2\":[\"highcharts\",\"Series\",\"types\",\"scatter\"],\"root\":[\"Highcharts\",\"Series\",\"types\",\"scatter\"]}\nvar highcharts_Series_types_scatter_commonjs_highcharts_Series_types_scatter_commonjs2_highcharts_Series_types_scatter_root_Highcharts_Series_types_scatter_ = __webpack_require__(632);\nvar highcharts_Series_types_scatter_commonjs_highcharts_Series_types_scatter_commonjs2_highcharts_Series_types_scatter_root_Highcharts_Series_types_scatter_default = /*#__PURE__*/__webpack_require__.n(highcharts_Series_types_scatter_commonjs_highcharts_Series_types_scatter_commonjs2_highcharts_Series_types_scatter_root_Highcharts_Series_types_scatter_);\n;// ./code/es5/es-modules/Series/Scatter3D/Scatter3DPoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Scatter 3D series.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar Scatter3DPoint_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar ScatterPoint = (highcharts_Series_types_scatter_commonjs_highcharts_Series_types_scatter_commonjs2_highcharts_Series_types_scatter_root_Highcharts_Series_types_scatter_default()).prototype.pointClass;\n\nvar Scatter3DPoint_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined;\n/* *\n *\n *  Class\n *\n * */\nvar Scatter3DPoint = /** @class */ (function (_super) {\n    Scatter3DPoint_extends(Scatter3DPoint, _super);\n    function Scatter3DPoint() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    Scatter3DPoint.prototype.applyOptions = function () {\n        _super.prototype.applyOptions.apply(this, arguments);\n        if (!Scatter3DPoint_defined(this.z)) {\n            this.z = 0;\n        }\n        return this;\n    };\n    return Scatter3DPoint;\n}(ScatterPoint));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Scatter3D_Scatter3DPoint = (Scatter3DPoint);\n\n;// ./code/es5/es-modules/Series/Scatter3D/Scatter3DSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Scatter 3D series.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A 3D scatter plot uses x, y and z coordinates to display values for three\n * variables for a set of data.\n *\n * @sample {highcharts} highcharts/3d/scatter/\n *         Simple 3D scatter\n * @sample {highcharts} highcharts/demo/3d-scatter-draggable\n *         Draggable 3d scatter\n *\n * @extends      plotOptions.scatter\n * @excluding    boostThreshold, boostBlending, cluster, dragDrop,\n *               legendSymbolColor\n * @product      highcharts\n * @requires     highcharts-3d\n * @optionparent plotOptions.scatter3d\n */\nvar Scatter3DSeriesDefaults = {\n    tooltip: {\n        pointFormat: 'x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>z: <b>{point.z}</b><br/>'\n    }\n};\n/**\n * A `scatter3d` series. If the [type](#series.scatter3d.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * scatter3d](#plotOptions.scatter3d).\n *\n * @extends   series,plotOptions.scatter3d\n * @excluding boostThreshold, boostBlending\n * @product   highcharts\n * @requires  highcharts-3d\n * @apioption series.scatter3d\n */\n/**\n * An array of data points for the series. For the `scatter3d` series\n * type, points can be given in the following ways:\n *\n * 1.  An array of arrays with 3 values. In this case, the values correspond\n * to `x,y,z`. If the first value is a string, it is applied as the name\n * of the point, and the `x` value is inferred.\n *\n *  ```js\n *     data: [\n *         [0, 0, 1],\n *         [1, 8, 7],\n *         [2, 9, 2]\n *     ]\n *  ```\n *\n * 3.  An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of data\n * points exceeds the series'\n * [turboThreshold](#series.scatter3d.turboThreshold), this option is not\n * available.\n *\n *  ```js\n *     data: [{\n *         x: 1,\n *         y: 2,\n *         z: 24,\n *         name: \"Point2\",\n *         color: \"#00FF00\"\n *     }, {\n *         x: 1,\n *         y: 4,\n *         z: 12,\n *         name: \"Point1\",\n *         color: \"#FF00FF\"\n *     }]\n *  ```\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<Array<number>|*>}\n * @extends   series.scatter.data\n * @product   highcharts\n * @apioption series.scatter3d.data\n */\n/**\n * The z value for each data point.\n *\n * @type      {number}\n * @product   highcharts\n * @apioption series.scatter3d.data.z\n */\n''; // Detachs doclets above\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Scatter3D_Scatter3DSeriesDefaults = (Scatter3DSeriesDefaults);\n\n;// ./code/es5/es-modules/Series/Scatter3D/Scatter3DSeries.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  Scatter 3D series.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar Scatter3DSeries_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar Scatter3DSeries_pointCameraDistance = Core_Math3D.pointCameraDistance;\n\n\n\n\n\nvar Scatter3DSeries_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, Scatter3DSeries_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.scatter3d\n *\n * @augments Highcharts.Series\n */\nvar Scatter3DSeries = /** @class */ (function (_super) {\n    Scatter3DSeries_extends(Scatter3DSeries, _super);\n    function Scatter3DSeries() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    Scatter3DSeries.prototype.pointAttribs = function (point) {\n        var attribs = _super.prototype.pointAttribs.apply(this,\n            arguments);\n        if (this.chart.is3d() && point) {\n            attribs.zIndex =\n                Scatter3DSeries_pointCameraDistance(point, this.chart);\n        }\n        return attribs;\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    Scatter3DSeries.defaultOptions = Scatter3DSeries_merge((highcharts_Series_types_scatter_commonjs_highcharts_Series_types_scatter_commonjs2_highcharts_Series_types_scatter_root_Highcharts_Series_types_scatter_default()).defaultOptions, Scatter3D_Scatter3DSeriesDefaults);\n    return Scatter3DSeries;\n}((highcharts_Series_types_scatter_commonjs_highcharts_Series_types_scatter_commonjs2_highcharts_Series_types_scatter_root_Highcharts_Series_types_scatter_default())));\nScatter3DSeries_extend(Scatter3DSeries.prototype, {\n    axisTypes: ['xAxis', 'yAxis', 'zAxis'],\n    // Require direct touch rather than using the k-d-tree, because the\n    // k-d-tree currently doesn't take the xyz coordinate system into\n    // account (#4552)\n    directTouch: true,\n    parallelArrays: ['x', 'y', 'z'],\n    pointArrayMap: ['x', 'y', 'z'],\n    pointClass: Scatter3D_Scatter3DPoint\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('scatter3d', Scatter3DSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Scatter3D_Scatter3DSeries = ((/* unused pure expression or super */ null && (Scatter3DSeries)));\n\n;// ./code/es5/es-modules/masters/highcharts-3d.js\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n// Compositions\nArea3D_Area3DSeries.compose(G.Series.types.area);\nAxis3DComposition.compose(G.Axis, G.Tick);\nChart_Chart3D.compose(G.Chart, G.Fx);\nColumn3D_Column3DComposition.compose(G.Series, (highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default()));\nPie3D_Pie3DSeries.compose(G.Series);\nSeries_Series3D.compose(G.Series);\nSVG_SVGRenderer3D.compose(highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default().getRendererType());\nAxis_ZAxis.compose(G.Chart);\n/* harmony default export */ var highcharts_3d_src = (G);\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__608__", "__WEBPACK_EXTERNAL_MODULE__820__", "__WEBPACK_EXTERNAL_MODULE__184__", "__WEBPACK_EXTERNAL_MODULE__532__", "__WEBPACK_EXTERNAL_MODULE__632__", "extendStatics", "Chart3D", "SVGRenderer3D", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "highcharts_3d_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "deg2rad", "pick", "perspective", "points", "chart", "insidePlotArea", "useInvertedPersp", "options3d", "options", "inverted", "origin", "x", "plot<PERSON>id<PERSON>", "y", "plotHeight", "z", "depth", "vd", "viewDistance", "scale", "scale3d", "beta", "alpha", "angles", "cosA", "Math", "cos", "cosB", "sinA", "sin", "sinB", "plotLeft", "plotTop", "map", "point", "rotated", "coordinate", "perspective3D", "distance", "projection", "Number", "POSITIVE_INFINITY", "shapeArea", "vertexes", "i", "j", "area", "length", "coordinates", "cameraPosition", "sqrt", "pow", "plotX", "plotY", "plotZ", "color", "parse", "genericDefaultOptions", "defaultOptions", "addEvent", "isArray", "merge", "Chart3D_pick", "wrap", "onAddSeries", "e", "is3d", "type", "onAfterDrawChartBox", "chart3d", "renderer", "frame", "get3dFrame", "xm", "xp", "ym", "yp", "zp", "xmm", "left", "visible", "size", "xpp", "right", "ymm", "top", "ypp", "bottom", "zmm", "zm", "front", "zpp", "back", "verb", "hasRendered", "frame3d", "frameShapes", "polyhedron", "add", "zIndex", "frontFacing", "faces", "fill", "brighten", "enabled", "onAfterGetContainer", "styledMode", "name", "slope", "for<PERSON>ach", "cfg", "tagName", "attributes", "id", "children", "onAfterInit", "series", "s", "defaultSeriesType", "onAfterSetChartSize", "clipBox", "margin", "chartWidth", "chartHeight", "fitToPlot", "getScale", "onBeforeRedraw", "isDirtyBox", "onBeforeRender", "onInit", "Additions", "wrapIsInsidePlot", "proceed", "apply", "slice", "arguments", "wrapRenderSeries", "translate", "render", "wrapSetClassName", "container", "className", "axisLabelPosition", "compose", "ChartClass", "FxClass", "chartProto", "fxProto", "_a", "propsRequireDirtyBox", "push", "propsRequireUpdateSeries", "matrixSetter", "interpolated", "pos", "start", "end", "elem", "attr", "frameOptions", "faceOrientation", "bottomOrientation", "topOrientation", "leftOrientation", "rightOrientation", "frontOrientation", "backOrientation", "defaultShowBottom", "defaultShowTop", "defaultShowLeft", "defaultShowRight", "concat", "xAxis", "yAxis", "zAxis", "axis", "horiz", "opposite", "getFaceOptions", "sources", "defaultVisible", "faceAttrs", "val", "isVisible", "ret", "axes", "side", "isValidEdge", "face1", "face2", "y<PERSON><PERSON>", "xDir", "xBottomEdges", "xTopEdges", "zBottomEdges", "zTopEdges", "pickEdge", "edges", "mult", "projections", "Chart3D_perspective", "best", "corners", "plotRight", "plotBottom", "originX", "originY", "bbox3d", "minX", "MAX_VALUE", "maxX", "minY", "maxY", "corner", "min", "max", "abs", "Chart_Chart3D", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "composed", "lineProto", "seriesTypes", "line", "pushUnique", "Area3DSeries_wrap", "wrapAreaSeriesGetGraphPath", "svgPath", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "round", "get<PERSON><PERSON><PERSON>old", "threshold", "bottomPoints", "rawPointsX", "stacking", "yBottom", "zPadding", "Area3DSeries_perspective", "group", "markerGroup", "translateX", "translateY", "reversed", "bottomPath", "areaPath", "splice", "xMap", "graphPath", "Axis_Axis3DDefaults", "labels", "position3d", "skew3d", "title", "Tick3DComposition_composed", "Tick3DComposition_addEvent", "extend", "Tick3DComposition_pushUnique", "Tick3DComposition_wrap", "onTickAfterGetLabelPosition", "axis3D", "fix3dPosition", "wrapTickGetMarkPath", "path", "pArr", "toLineSegments", "TickClass", "Axis3DComposition_deg2rad", "Axis3DComposition_perspective3D", "Axis3DComposition_addEvent", "Axis3DComposition_merge", "Axis3DComposition_pick", "Axis3DComposition_wrap", "onAxisAfterSetOptions", "coll", "tickWidth", "gridLineWidth", "onAxisDrawCrosshair", "crosshairPos", "isXAxis", "axisXpos", "len", "axisYpos", "onAxisInit", "Axis3DAdditions", "wrapAxisGetLinePath", "wrapAxisGetPlotBandPath", "args", "from", "to", "fromPath", "getPlotLinePath", "value", "to<PERSON><PERSON>", "fromStartSeg", "fromEndSeg", "toStartSeg", "toEndSeg", "wrapAxisGetPlotLinePath", "isZAxis", "startSegment", "endSegment", "pathSegments", "swapZ", "Axis3DComposition_perspective", "wrapAxisGetSlotWidth", "tick", "_b", "gridGroup", "tickPositions", "ticks", "categories", "label", "firstGridLine", "element", "childNodes", "getBBox", "frame3DLeft", "origin_1", "index", "indexOf", "prevTick", "nextTick", "labelPos", "prevLabelPos", "nextLabelPos", "xy", "wrapAxisGetTitlePosition", "AxisClass", "Tick3DComposition", "keepProps", "includes", "axisProto", "isTitle", "vecX", "positionMode", "skew", "offsetX", "offsetY", "vecY", "reverseFlap", "sina", "cosa", "vecZ", "sinb", "projected", "Axis3DComposition_shapeArea", "pointsProjected", "matrix", "p", "highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_", "highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "__", "constructor", "create", "Series3D_composed", "Series3D_addEvent", "Series3D_extend", "isNumber", "Series3D_merge", "Series3D_pick", "Series3D_pushUnique", "Series3D", "_super", "SeriesClass", "translate3dPoints", "projectedPoint", "zValue", "seriesOptions", "rawPoints", "stack", "groupZPadding", "data", "rawPoint", "logarithmic", "val2lin", "isInside", "axisZpos", "projectedPoints", "Series3D_perspective", "highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_", "highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default", "SVGElement3D_extends", "TypeError", "String", "SVGElement3D_color", "SVGElement", "getRendererType", "Element", "defined", "SVGElement3D_pick", "SVGElement3D", "_this", "parts", "pathType", "initArgs", "elem3d", "paths", "zIndexes", "_i", "part", "attribs", "filter", "forcedSides", "singleSetterForParts", "values", "duration", "complete", "newAttr", "optionsToApply", "hasZIndexes", "keys", "processParts", "props", "partsProps", "destroy", "continueAnimation", "shapeArgs", "animate", "fillSetter", "types", "base", "cuboid", "animObject", "SVGRenderer3D_color", "charts", "SVGRenderer3D_deg2rad", "SVGRenderer3D_defined", "SVGRenderer3D_extend", "SVGRenderer3D_merge", "SVGRenderer3D_pick", "PI", "dFactor", "curveTo", "cx", "cy", "rx", "ry", "dx", "dy", "arcAngle", "result", "to<PERSON><PERSON><PERSON><PERSON>", "closed", "points_1", "m", "points_2", "face3d", "elementProto", "createElement", "hash", "chartIndex", "vertexes2d", "SVGRenderer3D_perspective", "SVGRenderer3D_shapeArea", "visibility", "params", "g", "pop", "element3d", "SVG_SVGElement3D", "cuboidPath", "shape", "h", "height", "w", "width", "mapSidePath", "mapPath", "pickShape", "verticesIndex1", "verticesIndex2", "dummyFace1", "dummyFace2", "path1", "isFront", "path2", "isTop", "path3", "isRight", "incrementX", "incrementY", "incrementZ", "arc3d", "wrapper", "customAttribs", "extractCustom", "ca", "side1", "side2", "inn", "out", "onAdd", "parent", "parentGroup", "_loop_1", "fn", "face", "setPaths", "arc3dPath", "zTop", "zInn", "zOut", "zSide1", "zSide2", "center", "setRadialReference", "darker", "_c", "setter", "paramArr", "animation", "randomProp", "random", "toString", "substring", "anim", "globalAnimation", "noop", "to_1", "interpolate_1", "step", "fx", "r", "innerR", "hide", "show", "inherit", "ir", "cs", "ss", "ce", "se", "irx", "iry", "start2", "end2", "midEnd", "angleCorr", "atan2", "angleEnd", "angleStart", "angleMid", "toZeroPIRange", "angle", "a1", "a2", "a3", "SVGRendererClass", "rendererProto", "Element3D", "SVG_SVGRenderer3D", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default", "ZAxis_extends", "ZAxis_defaultOptions", "ZAxis_addEvent", "ZAxis_merge", "ZAxis_pick", "splat", "chartAddZAxis", "ZAxis", "onChartAfterCreateAxes", "zAxisOptions", "axisOptions", "addZAxis", "setScale", "offset", "lineWidth", "collectionsWithInit", "collectionsWithUpdate", "init", "userOptions", "getSeriesExtremes", "hasVisibleSeries", "dataMin", "dataMax", "ignoreMinPadding", "ignoreMaxPadding", "buildStacks", "reserveSpace", "positive<PERSON><PERSON><PERSON><PERSON>nly", "zData", "getColumn", "setAxisSize", "Column3DComposition_composed", "Column3DComposition_addEvent", "Column3DComposition_extend", "Column3DComposition_pick", "Column3DComposition_pushUnique", "Column3DComposition_wrap", "columnSeriesTranslate3dShapes", "point2dPos", "borderCrisp", "borderWidth", "grouping", "outside3dPlot", "dimensions", "tooltipPos", "borderlessBase", "dimensions_1", "shapeType", "clientX", "plot3d", "Column3DComposition_perspective", "translatedTTPos", "onColumnSeriesAfterInit", "reversedStacks", "stacks", "stackNumber", "totalStacks", "position", "wrapColumnPointHasNewShapeType", "graphic", "nodeName", "wrapColumnSeriesAnimate", "shapey", "stackY", "negative", "drawDataLabels", "wrapColumnSeriesPlotGroup", "_name", "_visibility", "_zIndex", "columnGroup", "getPlotBox", "survive", "wrapColumnSeriesPointAttribs", "stroke", "edgeColor", "edgeWidth", "wrapColumnSeriesSetState", "state", "inactiveOtherPoints", "wrapColumnSeriesSetVisible", "vis", "wrapColumnSeriesTranslate", "translate3dShapes", "wrapSeriesAlignDataLabel", "_dataLabel", "alignTo", "is", "inside", "xOffset", "pointWidth", "dLPosition", "wrapSeriesJustifyDataLabel", "wrapStackItemGetStackBox", "stackBoxProps", "stackBox", "xWidth", "stackItem", "baseSeriesInd", "split", "columnSeries", "Pie3DPoint_extends", "Pie3DPoint", "haloPath", "pie", "pointClass", "Pie3DSeries_extends", "Pie3DSeries_composed", "Pie3DSeries_deg2rad", "PieSeries", "Pie3DSeries_extend", "Pie3DSeries_pick", "Pie3DSeries_pushUnique", "Pie3DSeries", "addPoint", "update", "oldtranslateX", "oldtranslateY", "scaleX", "scaleY", "attrSetters", "getDataLabelPosition", "labelPosition", "b1", "connectorPosition", "yOffset", "natural", "breakAt", "touchingSliceAt", "pointAttribs", "slicedTranslation", "slicedOffset", "drawTracker", "_d", "highcharts_Series_types_scatter_commonjs_highcharts_Series_types_scatter_commonjs2_highcharts_Series_types_scatter_root_Highcharts_Series_types_scatter_", "highcharts_Series_types_scatter_commonjs_highcharts_Series_types_scatter_commonjs2_highcharts_Series_types_scatter_root_Highcharts_Series_types_scatter_default", "Scatter3DPoint_extends", "ScatterPoint", "Scatter3DPoint_defined", "Scatter3DPoint", "applyOptions", "Scatter3D_Scatter3DSeriesDefaults", "tooltip", "pointFormat", "Scatter3DSeries_extends", "Scatter3DSeries_extend", "Scatter3DSeries_merge", "Scatter3DSeries", "Scatter3DSeries_pointCameraDistance", "axisTypes", "directTouch", "parallelArrays", "pointArrayMap", "registerSeriesType", "G", "Area3D_Area3DSeries", "AreaSeriesClass", "Series", "Axis3DComposition", "Axis", "Tick", "Chart", "Fx", "Column3D_Column3DComposition", "StackItemClass", "seriesProto", "stackItemProto", "ColumnSeriesClass", "column", "ColumnRangeSeriesClass", "columnRange", "columnSeriesProto", "columnPointProto", "columnRangeSeriesProto", "Pie3D_Pie3DSeries", "Series_Series3D", "Axis_ZAxis"], "mappings": "CASA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,cAAiB,CAAEA,QAAQ,cAAc,gBAAmB,CAAEA,QAAQ,cAAc,MAAS,CAAEA,QAAQ,cAAc,SAAY,CAAEA,QAAQ,cAAc,IAAO,CAAEA,QAAQ,cAAc,MAAS,CAAC,KAAQ,CAAC,OAAU,EACpT,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,2BAA4B,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,QAAQ,CAAE,CAAC,wBAAwB,iBAAiB,CAAE,CAAC,wBAAwB,mBAAmB,CAAE,CAAC,wBAAwB,SAAS,CAAE,CAAC,wBAAwB,YAAY,CAAE,CAAC,wBAAwB,OAAO,CAAE,CAAC,wBAAwB,SAAS,QAAQ,UAAU,CAAC,CAAEJ,GACzV,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,2BAA2B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,cAAiB,CAAEA,QAAQ,cAAc,gBAAmB,CAAEA,QAAQ,cAAc,MAAS,CAAEA,QAAQ,cAAc,SAAY,CAAEA,QAAQ,cAAc,IAAO,CAAEA,QAAQ,cAAc,MAAS,CAAC,KAAQ,CAAC,OAAU,EAEhVJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,gBAAmB,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAEA,EAAK,UAAa,CAAC,SAAY,CAAEA,EAAK,UAAa,CAAC,IAAO,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAC,KAAQ,CAAC,OAAU,CACzS,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,EAChS,OAAgB,AAAC,WACP,aACA,IAy8FFC,EA4IAA,EAwmCAA,EA4qBAA,EAkEAA,EAsQAA,EA2LAA,EAt8JJC,EA28FAC,EAj3GUC,EAAuB,CAE/B,IACC,SAASf,CAAM,EAEtBA,EAAOD,OAAO,CAAGU,CAEX,EAEA,IACC,SAAST,CAAM,EAEtBA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,SAASN,CAAM,EAEtBA,EAAOD,OAAO,CAAGW,CAEX,EAEA,IACC,SAASV,CAAM,EAEtBA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGY,CAEX,EAEA,IACC,SAASX,CAAM,EAEtBA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,SAASR,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGIY,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAapB,OAAO,CAG5B,IAAIC,EAASgB,CAAwB,CAACE,EAAS,CAAG,CAGjDnB,QAAS,CAAC,CACX,EAMA,OAHAgB,CAAmB,CAACG,EAAS,CAAClB,EAAQA,EAAOD,OAAO,CAAEkB,GAG/CjB,EAAOD,OAAO,AACtB,CAMCkB,EAAoBI,CAAC,CAAG,SAASrB,CAAM,EACtC,IAAIsB,EAAStB,GAAUA,EAAOuB,UAAU,CACvC,WAAa,OAAOvB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAiB,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASzB,CAAO,CAAE2B,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC7B,EAAS4B,IAC5EE,OAAOC,cAAc,CAAC/B,EAAS4B,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,EAAmB,CAClE,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAA+FzB,EAAoB,KACnH0B,EAAmH1B,EAAoBI,CAAC,CAACqB,GAazIE,EAAU,AAACH,IAA+EG,OAAO,CAEjGC,EAAO,AAACJ,IAA+EI,IAAI,CAiF/F,SAASC,EAAYC,CAAM,CAAEC,CAAK,CAAEC,CAAc,CAAEC,CAAgB,EAChE,IAAIC,EAAYH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAKzCE,EAAWR,EAAKK,EAChBD,EAAAA,GAAiBD,EAAMK,QAAQ,EAC/BC,EAAS,CACLC,EAAGP,EAAMQ,SAAS,CAAG,EACrBC,EAAGT,EAAMU,UAAU,CAAG,EACtBC,EAAGR,EAAUS,KAAK,CAAG,EACrBC,GAAIhB,EAAKM,EAAUS,KAAK,CAAE,GAAKf,EAAKM,EAAUW,YAAY,CAAE,EAChE,EACAC,EAAQf,EAAMgB,OAAO,EAAI,EACzBC,EAAOrB,EAAUO,EAAUc,IAAI,CAAIZ,CAAAA,EAAW,GAAK,CAAA,EACnDa,EAAQtB,EAAUO,EAAUe,KAAK,CAAIb,CAAAA,EAAW,GAAK,CAAA,EACrDc,EAAS,CACLC,KAAMC,KAAKC,GAAG,CAACJ,GACfK,KAAMF,KAAKC,GAAG,CAAC,CAACL,GAChBO,KAAMH,KAAKI,GAAG,CAACP,GACfQ,KAAML,KAAKI,GAAG,CAAC,CAACR,EACpB,EAMJ,OALKhB,IACDK,EAAOC,CAAC,EAAIP,EAAM2B,QAAQ,CAC1BrB,EAAOG,CAAC,EAAIT,EAAM4B,OAAO,EAGtB7B,EAAO8B,GAAG,CAAC,SAAUC,CAAK,EAC7B,IA7DUvB,EAAGE,EAAGE,EA6DZoB,GA7DMxB,EA6Da,AAACF,CAAAA,EAAWyB,EAAMrB,CAAC,CAAGqB,EAAMvB,CAAC,AAADA,EAAKD,EAAOC,CAAC,CA7DnDE,EA6DqD,AAACJ,CAAAA,EAAWyB,EAAMvB,CAAC,CAAGuB,EAAMrB,CAAC,AAADA,EAAKH,EAAOG,CAAC,CA7D3FE,EA6D6F,AAACmB,CAAAA,EAAMnB,CAAC,EAAI,CAAA,EAAKL,EAAOK,CAAC,CA5DnI,CACHJ,EAAGY,AA4DCA,EA5DMI,IAAI,CAAGhB,EAAIY,AA4DjBA,EA5DwBO,IAAI,CAAGf,EACnCF,EAAG,CAACU,AA2DAA,EA3DOK,IAAI,CAAGL,AA2DdA,EA3DqBO,IAAI,CAAGnB,EAAIY,AA2DhCA,EA3DuCC,IAAI,CAAGX,EAC9CU,AA0DAA,EA1DOI,IAAI,CAAGJ,AA0DdA,EA1DqBK,IAAI,CAAGb,EAChCA,EAAGQ,AAyDCA,EAzDMC,IAAI,CAAGD,AAyDbA,EAzDoBO,IAAI,CAAGnB,EAAIY,AAyD/BA,EAzDsCK,IAAI,CAAGf,EAC7CU,AAwDAA,EAxDOC,IAAI,CAAGD,AAwDdA,EAxDqBI,IAAI,CAAGZ,CACpC,GAyDQqB,EAAaC,EAAcF,EAC3BzB,EACAA,EAAOO,EAAE,EAKb,OAHAmB,EAAWzB,CAAC,CAAGyB,EAAWzB,CAAC,CAAGQ,EAAQT,EAAOC,CAAC,CAC9CyB,EAAWvB,CAAC,CAAGuB,EAAWvB,CAAC,CAAGM,EAAQT,EAAOG,CAAC,CAC9CuB,EAAWrB,CAAC,CAAGoB,EAAQpB,CAAC,CAAGI,EAAQT,EAAOK,CAAC,CACpC,CACHJ,EAAIF,EAAW2B,EAAWvB,CAAC,CAAGuB,EAAWzB,CAAC,CAC1CE,EAAIJ,EAAW2B,EAAWzB,CAAC,CAAGyB,EAAWvB,CAAC,CAC1CE,EAAGqB,EAAWrB,CAAC,AACnB,CACJ,EACJ,CAqBA,SAASsB,EAAcD,CAAU,CAAE1B,CAAM,CAAE4B,CAAQ,EAC/C,IAAIC,EAAa,AAAC,AAACD,EAAW,GACrBA,EAAWE,OAAOC,iBAAiB,CACpCH,EAAYF,CAAAA,EAAWrB,CAAC,CAAGL,EAAOK,CAAC,CAAGuB,CAAO,EAC7C,EACR,MAAO,CACH3B,EAAGyB,EAAWzB,CAAC,CAAG4B,EAClB1B,EAAGuB,EAAWvB,CAAC,CAAG0B,CACtB,CACJ,CAmDA,SAASG,EAAUC,CAAQ,EACvB,IACIC,EACAC,EAFAC,EAAO,EAGX,IAAKF,EAAI,EAAGA,EAAID,EAASI,MAAM,CAAEH,IAC7BC,EAAI,AAACD,CAAAA,EAAI,CAAA,EAAKD,EAASI,MAAM,CAC7BD,GAAQH,CAAQ,CAACC,EAAE,CAACjC,CAAC,CAAGgC,CAAQ,CAACE,EAAE,CAAChC,CAAC,CAAG8B,CAAQ,CAACE,EAAE,CAAClC,CAAC,CAAGgC,CAAQ,CAACC,EAAE,CAAC/B,CAAC,CAEzE,OAAOiC,EAAO,CAClB,CAoC6B,MA7E7B,SAA6BE,CAAW,CAAE5C,CAAK,EAC3C,IAAIG,EAAYH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CACzC0C,EAAiB,CACbtC,EAAGP,EAAMQ,SAAS,CAAG,EACrBC,EAAGT,EAAMU,UAAU,CAAG,EACtBC,EAAGd,EAAKM,EAAUS,KAAK,CAAE,GAAKf,EAAKM,EAAUW,YAAY,CAAE,GACvDX,EAAUS,KAAK,AACvB,EAQJ,OANeS,KAAKyB,IAAI,CAACzB,KAAK0B,GAAG,CAACF,EAAetC,CAAC,CAAGV,EAAK+C,EAAYI,KAAK,CACvEJ,EAAYrC,CAAC,EAAG,GACZc,KAAK0B,GAAG,CAACF,EAAepC,CAAC,CAAGZ,EAAK+C,EAAYK,KAAK,CACtDL,EAAYnC,CAAC,EAAG,GACZY,KAAK0B,GAAG,CAACF,EAAelC,CAAC,CAAGd,EAAK+C,EAAYM,KAAK,CACtDN,EAAYjC,CAAC,EAAG,GAExB,EA6EIwC,EAAQ,AAACxD,IAAuGyD,KAAK,CAErHC,EAAwB,AAAC5D,IAA+E6D,cAAc,CAItHC,EAAW,AAAC9D,IAA+E8D,QAAQ,CAAEC,EAAU,AAAC/D,IAA+E+D,OAAO,CAAEC,EAAQ,AAAChE,IAA+EgE,KAAK,CAAEC,EAAe,AAACjE,IAA+EI,IAAI,CAAE8D,EAAO,AAAClE,IAA+EkE,IAAI,EAO3e,AAAC,SAAU9F,CAAO,EAgPd,SAAS+F,EAAYC,CAAC,EACd,IAAI,CAACC,IAAI,IACLD,AAAmB,YAAnBA,EAAEzD,OAAO,CAAC2D,IAAI,EACdF,CAAAA,EAAEzD,OAAO,CAAC2D,IAAI,CAAG,WAAU,CAGvC,CAIA,SAASC,IACL,GAAI,IAAI,CAACC,OAAO,EACZ,IAAI,CAACH,IAAI,GAAI,CACb,IACII,EAAWlE,AADH,IAAI,CACKkE,QAAQ,CACzB/D,EAAYH,AAFJ,IAAI,CAEMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CACzCgE,EAAQnE,AAHA,IAAI,CAGEiE,OAAO,CAACG,UAAU,GAChCC,EAAKrE,AAJG,IAAI,CAID2B,QAAQ,CACnB2C,EAAKtE,AALG,IAAI,CAKD2B,QAAQ,CAAG3B,AALd,IAAI,CAKgBQ,SAAS,CACrC+D,EAAKvE,AANG,IAAI,CAMD4B,OAAO,CAClB4C,EAAKxE,AAPG,IAAI,CAOD4B,OAAO,CAAG5B,AAPb,IAAI,CAOeU,UAAU,CAErC+D,EAAKtE,EAAUS,KAAK,CACpB8D,EAAML,EAAMF,CAAAA,EAAMQ,IAAI,CAACC,OAAO,CAAGT,EAAMQ,IAAI,CAACE,IAAI,CAAG,CAAA,EACnDC,EAAMR,EAAMH,CAAAA,EAAMY,KAAK,CAACH,OAAO,CAAGT,EAAMY,KAAK,CAACF,IAAI,CAAG,CAAA,EACrDG,EAAMT,EAAMJ,CAAAA,EAAMc,GAAG,CAACL,OAAO,CAAGT,EAAMc,GAAG,CAACJ,IAAI,CAAG,CAAA,EACjDK,EAAMV,EAAML,CAAAA,EAAMgB,MAAM,CAACP,OAAO,CAAGT,EAAMgB,MAAM,CAACN,IAAI,CAAG,CAAA,EACvDO,EAAMC,AAND,EAMOlB,CAAAA,EAAMmB,KAAK,CAACV,OAAO,CAAGT,EAAMmB,KAAK,CAACT,IAAI,CAAG,CAAA,EACrDU,EAAMd,EAAMN,CAAAA,EAAMqB,IAAI,CAACZ,OAAO,CAAGT,EAAMqB,IAAI,CAACX,IAAI,CAAG,CAAA,EACnDY,EAAOzF,AAhBC,IAAI,CAgBC0F,WAAW,CAAG,UAAY,MAC3C1F,CAjBY,IAAI,CAiBViE,OAAO,CAAC0B,OAAO,CAAGxB,EACnBnE,AAlBO,IAAI,CAkBL4F,WAAW,EAClB5F,CAAAA,AAnBQ,IAAI,CAmBN4F,WAAW,CAAG,CAChBT,OAAQjB,EAAS2B,UAAU,GAAGC,GAAG,GACjCb,IAAKf,EAAS2B,UAAU,GAAGC,GAAG,GAC9BnB,KAAMT,EAAS2B,UAAU,GAAGC,GAAG,GAC/Bf,MAAOb,EAAS2B,UAAU,GAAGC,GAAG,GAChCN,KAAMtB,EAAS2B,UAAU,GAAGC,GAAG,GAC/BR,MAAOpB,EAAS2B,UAAU,GAAGC,GAAG,EACpC,CAAA,EAEJ9F,AA5BY,IAAI,CA4BV4F,WAAW,CAACT,MAAM,CAACM,EAAK,CAAC,CAC3B,MAAS,iDACTM,OAAQ5B,EAAMgB,MAAM,CAACa,WAAW,CAAG,KAAQ,IAC3CC,MAAO,CAAC,CACAC,KAAM/C,EAAMgB,EAAMgB,MAAM,CAAChC,KAAK,EAAEgD,QAAQ,CAAC,IAAKnH,GAAG,GACjDuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAGyE,CACP,EAAG,CACC7E,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAGyE,CACP,EAAG,CACC7E,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAG4E,CACP,EAAG,CACChF,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAG4E,CACP,EAAE,CACNa,QAASjC,EAAMgB,MAAM,CAACP,OAAO,AACjC,EACA,CACIsB,KAAM/C,EAAMgB,EAAMgB,MAAM,CAAChC,KAAK,EAAEgD,QAAQ,CAAC,IAAKnH,GAAG,GACjDuD,SAAU,CAAC,CACHhC,EAAG8D,EACH5D,EAAG+D,EACH7D,EAAG8D,CACP,EAAG,CACClE,EAAG+D,EACH7D,EAAG+D,EACH7D,EAAG8D,CACP,EAAG,CACClE,EAAG+D,EACH7D,EAAG+D,EACH7D,EAzDX,CA0DO,EAAG,CACCJ,EAAG8D,EACH5D,EAAG+D,EACH7D,EA7DX,CA8DO,EAAE,CACNyF,QAASjC,EAAMgB,MAAM,CAACP,OAAO,AACjC,EACA,CACIsB,KAAM/C,EAAMgB,EAAMgB,MAAM,CAAChC,KAAK,EAAEgD,QAAQ,CAAC,KAAMnH,GAAG,GAClDuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAGyE,CACP,EAAG,CACC7E,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAG4E,CACP,EAAG,CACChF,EAAG8D,EACH5D,EAAG+D,EACH7D,EAAG8D,CACP,EAAG,CACClE,EAAG8D,EACH5D,EAAG+D,EACH7D,EAlFX,CAmFO,EAAE,CACNyF,QAASjC,EAAMgB,MAAM,CAACP,OAAO,EAAI,CAACT,EAAMQ,IAAI,CAACC,OAAO,AACxD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMgB,MAAM,CAAChC,KAAK,EAAEgD,QAAQ,CAAC,KAAMnH,GAAG,GAClDuD,SAAU,CAAC,CACHhC,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAG4E,CACP,EAAG,CACChF,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAGyE,CACP,EAAG,CACC7E,EAAG+D,EACH7D,EAAG+D,EACH7D,EAnGX,CAoGO,EAAG,CACCJ,EAAG+D,EACH7D,EAAG+D,EACH7D,EAAG8D,CACP,EAAE,CACN2B,QAASjC,EAAMgB,MAAM,CAACP,OAAO,EAAI,CAACT,EAAMY,KAAK,CAACH,OAAO,AACzD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMgB,MAAM,CAAChC,KAAK,EAAEnE,GAAG,GACnCuD,SAAU,CAAC,CACHhC,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAGyE,CACP,EAAG,CACC7E,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAGyE,CACP,EAAG,CACC7E,EAAG8D,EACH5D,EAAG+D,EACH7D,EAxHX,CAyHO,EAAG,CACCJ,EAAG+D,EACH7D,EAAG+D,EACH7D,EA5HX,CA6HO,EAAE,CACNyF,QAASjC,EAAMgB,MAAM,CAACP,OAAO,EAAI,CAACT,EAAMmB,KAAK,CAACV,OAAO,AACzD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMgB,MAAM,CAAChC,KAAK,EAAEnE,GAAG,GACnCuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAG4E,CACP,EAAG,CACChF,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAG4E,CACP,EAAG,CACChF,EAAG+D,EACH7D,EAAG+D,EACH7D,EAAG8D,CACP,EAAG,CACClE,EAAG8D,EACH5D,EAAG+D,EACH7D,EAAG8D,CACP,EAAE,CACN2B,QAASjC,EAAMgB,MAAM,CAACP,OAAO,EAAI,CAACT,EAAMqB,IAAI,CAACZ,OAAO,AACxD,EAAE,AACV,GACA5E,AA9JY,IAAI,CA8JV4F,WAAW,CAACX,GAAG,CAACQ,EAAK,CAAC,CACxB,MAAS,8CACTM,OAAQ5B,EAAMc,GAAG,CAACe,WAAW,CAAG,KAAQ,IACxCC,MAAO,CAAC,CACAC,KAAM/C,EAAMgB,EAAMc,GAAG,CAAC9B,KAAK,EAAEgD,QAAQ,CAAC,IAAKnH,GAAG,GAC9CuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAG4E,CACP,EAAG,CACChF,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAG4E,CACP,EAAG,CACChF,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAGyE,CACP,EAAG,CACC7E,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAGyE,CACP,EAAE,CACNgB,QAASjC,EAAMc,GAAG,CAACL,OAAO,AAC9B,EACA,CACIsB,KAAM/C,EAAMgB,EAAMc,GAAG,CAAC9B,KAAK,EAAEgD,QAAQ,CAAC,IAAKnH,GAAG,GAC9CuD,SAAU,CAAC,CACHhC,EAAG8D,EACH5D,EAAG8D,EACH5D,EAnLX,CAoLO,EAAG,CACCJ,EAAG+D,EACH7D,EAAG8D,EACH5D,EAvLX,CAwLO,EAAG,CACCJ,EAAG+D,EACH7D,EAAG8D,EACH5D,EAAG8D,CACP,EAAG,CACClE,EAAG8D,EACH5D,EAAG8D,EACH5D,EAAG8D,CACP,EAAE,CACN2B,QAASjC,EAAMc,GAAG,CAACL,OAAO,AAC9B,EACA,CACIsB,KAAM/C,EAAMgB,EAAMc,GAAG,CAAC9B,KAAK,EAAEgD,QAAQ,CAAC,KAAMnH,GAAG,GAC/CuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAG4E,CACP,EAAG,CACChF,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAGyE,CACP,EAAG,CACC7E,EAAG8D,EACH5D,EAAG8D,EACH5D,EAhNX,CAiNO,EAAG,CACCJ,EAAG8D,EACH5D,EAAG8D,EACH5D,EAAG8D,CACP,EAAE,CACN2B,QAASjC,EAAMc,GAAG,CAACL,OAAO,EAAI,CAACT,EAAMQ,IAAI,CAACC,OAAO,AACrD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMc,GAAG,CAAC9B,KAAK,EAAEgD,QAAQ,CAAC,KAAMnH,GAAG,GAC/CuD,SAAU,CAAC,CACHhC,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAGyE,CACP,EAAG,CACC7E,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAG4E,CACP,EAAG,CACChF,EAAG+D,EACH7D,EAAG8D,EACH5D,EAAG8D,CACP,EAAG,CACClE,EAAG+D,EACH7D,EAAG8D,EACH5D,EAzOX,CA0OO,EAAE,CACNyF,QAASjC,EAAMc,GAAG,CAACL,OAAO,EAAI,CAACT,EAAMY,KAAK,CAACH,OAAO,AACtD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMc,GAAG,CAAC9B,KAAK,EAAEnE,GAAG,GAChCuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAGyE,CACP,EAAG,CACC7E,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAGyE,CACP,EAAG,CACC7E,EAAG+D,EACH7D,EAAG8D,EACH5D,EA1PX,CA2PO,EAAG,CACCJ,EAAG8D,EACH5D,EAAG8D,EACH5D,EA9PX,CA+PO,EAAE,CACNyF,QAASjC,EAAMc,GAAG,CAACL,OAAO,EAAI,CAACT,EAAMmB,KAAK,CAACV,OAAO,AACtD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMc,GAAG,CAAC9B,KAAK,EAAEnE,GAAG,GAChCuD,SAAU,CAAC,CACHhC,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAG4E,CACP,EAAG,CACChF,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAG4E,CACP,EAAG,CACChF,EAAG8D,EACH5D,EAAG8D,EACH5D,EAAG8D,CACP,EAAG,CACClE,EAAG+D,EACH7D,EAAG8D,EACH5D,EAAG8D,CACP,EAAE,CACN2B,QAASjC,EAAMc,GAAG,CAACL,OAAO,EAAI,CAACT,EAAMqB,IAAI,CAACZ,OAAO,AACrD,EAAE,AACV,GACA5E,AAhSY,IAAI,CAgSV4F,WAAW,CAACjB,IAAI,CAACc,EAAK,CAAC,CACzB,MAAS,+CACTM,OAAQ5B,EAAMQ,IAAI,CAACqB,WAAW,CAAG,KAAQ,IACzCC,MAAO,CAAC,CACAC,KAAM/C,EAAMgB,EAAMQ,IAAI,CAACxB,KAAK,EAAEgD,QAAQ,CAAC,IAAKnH,GAAG,GAC/CuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAGyE,CACP,EAAG,CACC7E,EAAG8D,EACH5D,EAAG+D,EACH7D,EApSX,CAqSO,EAAG,CACCJ,EAAG8D,EACH5D,EAAG+D,EACH7D,EAAG8D,CACP,EAAG,CACClE,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAG4E,CACP,EAAE,CACNa,QAASjC,EAAMQ,IAAI,CAACC,OAAO,EAAI,CAACT,EAAMgB,MAAM,CAACP,OAAO,AACxD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMQ,IAAI,CAACxB,KAAK,EAAEgD,QAAQ,CAAC,IAAKnH,GAAG,GAC/CuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAG4E,CACP,EAAG,CACChF,EAAG8D,EACH5D,EAAG8D,EACH5D,EAAG8D,CACP,EAAG,CACClE,EAAG8D,EACH5D,EAAG8D,EACH5D,EA7TX,CA8TO,EAAG,CACCJ,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAGyE,CACP,EAAE,CACNgB,QAASjC,EAAMQ,IAAI,CAACC,OAAO,EAAI,CAACT,EAAMc,GAAG,CAACL,OAAO,AACrD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMQ,IAAI,CAACxB,KAAK,EAAEgD,QAAQ,CAAC,KAAMnH,GAAG,GAChDuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAG4E,CACP,EAAG,CACChF,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAG4E,CACP,EAAG,CACChF,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAGyE,CACP,EAAG,CACC7E,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAGyE,CACP,EAAE,CACNgB,QAASjC,EAAMQ,IAAI,CAACC,OAAO,AAC/B,EACA,CACIsB,KAAM/C,EAAMgB,EAAMQ,IAAI,CAACxB,KAAK,EAAEgD,QAAQ,CAAC,KAAMnH,GAAG,GAChDuD,SAAU,CAAC,CACHhC,EAAG8D,EACH5D,EAAG8D,EACH5D,EAAG8D,CACP,EAAG,CACClE,EAAG8D,EACH5D,EAAG+D,EACH7D,EAAG8D,CACP,EAAG,CACClE,EAAG8D,EACH5D,EAAG+D,EACH7D,EAvWX,CAwWO,EAAG,CACCJ,EAAG8D,EACH5D,EAAG8D,EACH5D,EA3WX,CA4WO,EAAE,CACNyF,QAASjC,EAAMQ,IAAI,CAACC,OAAO,AAC/B,EACA,CACIsB,KAAM/C,EAAMgB,EAAMQ,IAAI,CAACxB,KAAK,EAAEnE,GAAG,GACjCuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAGyE,CACP,EAAG,CACC7E,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAGyE,CACP,EAAG,CACC7E,EAAG8D,EACH5D,EAAG8D,EACH5D,EA5XX,CA6XO,EAAG,CACCJ,EAAG8D,EACH5D,EAAG+D,EACH7D,EAhYX,CAiYO,EAAE,CACNyF,QAASjC,EAAMQ,IAAI,CAACC,OAAO,EAAI,CAACT,EAAMmB,KAAK,CAACV,OAAO,AACvD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMQ,IAAI,CAACxB,KAAK,EAAEnE,GAAG,GACjCuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAG4E,CACP,EAAG,CACChF,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAG4E,CACP,EAAG,CACChF,EAAG8D,EACH5D,EAAG+D,EACH7D,EAAG8D,CACP,EAAG,CACClE,EAAG8D,EACH5D,EAAG8D,EACH5D,EAAG8D,CACP,EAAE,CACN2B,QAASjC,EAAMQ,IAAI,CAACC,OAAO,EAAI,CAACT,EAAMqB,IAAI,CAACZ,OAAO,AACtD,EAAE,AACV,GACA5E,AAlaY,IAAI,CAkaV4F,WAAW,CAACb,KAAK,CAACU,EAAK,CAAC,CAC1B,MAAS,gDACTM,OAAQ5B,EAAMY,KAAK,CAACiB,WAAW,CAAG,KAAQ,IAC1CC,MAAO,CAAC,CACAC,KAAM/C,EAAMgB,EAAMY,KAAK,CAAC5B,KAAK,EAAEgD,QAAQ,CAAC,IAAKnH,GAAG,GAChDuD,SAAU,CAAC,CACHhC,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAG4E,CACP,EAAG,CACChF,EAAG+D,EACH7D,EAAG+D,EACH7D,EAAG8D,CACP,EAAG,CACClE,EAAG+D,EACH7D,EAAG+D,EACH7D,EA1aX,CA2aO,EAAG,CACCJ,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAGyE,CACP,EAAE,CACNgB,QAASjC,EAAMY,KAAK,CAACH,OAAO,EAAI,CAACT,EAAMgB,MAAM,CAACP,OAAO,AACzD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMY,KAAK,CAAC5B,KAAK,EAAEgD,QAAQ,CAAC,IAAKnH,GAAG,GAChDuD,SAAU,CAAC,CACHhC,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAGyE,CACP,EAAG,CACC7E,EAAG+D,EACH7D,EAAG8D,EACH5D,EA3bX,CA4bO,EAAG,CACCJ,EAAG+D,EACH7D,EAAG8D,EACH5D,EAAG8D,CACP,EAAG,CACClE,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAG4E,CACP,EAAE,CACNa,QAASjC,EAAMY,KAAK,CAACH,OAAO,EAAI,CAACT,EAAMc,GAAG,CAACL,OAAO,AACtD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMY,KAAK,CAAC5B,KAAK,EAAEgD,QAAQ,CAAC,KAAMnH,GAAG,GACjDuD,SAAU,CAAC,CACHhC,EAAG+D,EACH7D,EAAG8D,EACH5D,EA5cX,CA6cO,EAAG,CACCJ,EAAG+D,EACH7D,EAAG+D,EACH7D,EAhdX,CAidO,EAAG,CACCJ,EAAG+D,EACH7D,EAAG+D,EACH7D,EAAG8D,CACP,EAAG,CACClE,EAAG+D,EACH7D,EAAG8D,EACH5D,EAAG8D,CACP,EAAE,CACN2B,QAASjC,EAAMY,KAAK,CAACH,OAAO,AAChC,EACA,CACIsB,KAAM/C,EAAMgB,EAAMY,KAAK,CAAC5B,KAAK,EAAEgD,QAAQ,CAAC,KAAMnH,GAAG,GACjDuD,SAAU,CAAC,CACHhC,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAGyE,CACP,EAAG,CACC7E,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAGyE,CACP,EAAG,CACC7E,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAG4E,CACP,EAAG,CACChF,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAG4E,CACP,EAAE,CACNa,QAASjC,EAAMY,KAAK,CAACH,OAAO,AAChC,EACA,CACIsB,KAAM/C,EAAMgB,EAAMY,KAAK,CAAC5B,KAAK,EAAEnE,GAAG,GAClCuD,SAAU,CAAC,CACHhC,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAGyE,CACP,EAAG,CACC7E,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAGyE,CACP,EAAG,CACC7E,EAAG+D,EACH7D,EAAG+D,EACH7D,EA9fX,CA+fO,EAAG,CACCJ,EAAG+D,EACH7D,EAAG8D,EACH5D,EAlgBX,CAmgBO,EAAE,CACNyF,QAASjC,EAAMY,KAAK,CAACH,OAAO,EAAI,CAACT,EAAMmB,KAAK,CAACV,OAAO,AACxD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMY,KAAK,CAAC5B,KAAK,EAAEnE,GAAG,GAClCuD,SAAU,CAAC,CACHhC,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAG4E,CACP,EAAG,CACChF,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAG4E,CACP,EAAG,CACChF,EAAG+D,EACH7D,EAAG8D,EACH5D,EAAG8D,CACP,EAAG,CACClE,EAAG+D,EACH7D,EAAG+D,EACH7D,EAAG8D,CACP,EAAE,CACN2B,QAASjC,EAAMY,KAAK,CAACH,OAAO,EAAI,CAACT,EAAMqB,IAAI,CAACZ,OAAO,AACvD,EAAE,AACV,GACA5E,AApiBY,IAAI,CAoiBV4F,WAAW,CAACJ,IAAI,CAACC,EAAK,CAAC,CACzB,MAAS,+CACTM,OAAQ5B,EAAMqB,IAAI,CAACQ,WAAW,CAAG,KAAQ,IACzCC,MAAO,CAAC,CACAC,KAAM/C,EAAMgB,EAAMqB,IAAI,CAACrC,KAAK,EAAEgD,QAAQ,CAAC,IAAKnH,GAAG,GAC/CuD,SAAU,CAAC,CACHhC,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAG4E,CACP,EAAG,CACChF,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAG4E,CACP,EAAG,CACChF,EAAG8D,EACH5D,EAAG+D,EACH7D,EAAG8D,CACP,EAAG,CACClE,EAAG+D,EACH7D,EAAG+D,EACH7D,EAAG8D,CACP,EAAE,CACN2B,QAASjC,EAAMqB,IAAI,CAACZ,OAAO,EAAI,CAACT,EAAMgB,MAAM,CAACP,OAAO,AACxD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMqB,IAAI,CAACrC,KAAK,EAAEgD,QAAQ,CAAC,IAAKnH,GAAG,GAC/CuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAG4E,CACP,EAAG,CACChF,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAG4E,CACP,EAAG,CACChF,EAAG+D,EACH7D,EAAG8D,EACH5D,EAAG8D,CACP,EAAG,CACClE,EAAG8D,EACH5D,EAAG8D,EACH5D,EAAG8D,CACP,EAAE,CACN2B,QAASjC,EAAMqB,IAAI,CAACZ,OAAO,EAAI,CAACT,EAAMc,GAAG,CAACL,OAAO,AACrD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMqB,IAAI,CAACrC,KAAK,EAAEgD,QAAQ,CAAC,KAAMnH,GAAG,GAChDuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAG4E,CACP,EAAG,CACChF,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAG4E,CACP,EAAG,CACChF,EAAG8D,EACH5D,EAAG8D,EACH5D,EAAG8D,CACP,EAAG,CACClE,EAAG8D,EACH5D,EAAG+D,EACH7D,EAAG8D,CACP,EAAE,CACN2B,QAASjC,EAAMqB,IAAI,CAACZ,OAAO,EAAI,CAACT,EAAMQ,IAAI,CAACC,OAAO,AACtD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMqB,IAAI,CAACrC,KAAK,EAAEgD,QAAQ,CAAC,KAAMnH,GAAG,GAChDuD,SAAU,CAAC,CACHhC,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAG4E,CACP,EAAG,CACChF,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAG4E,CACP,EAAG,CACChF,EAAG+D,EACH7D,EAAG+D,EACH7D,EAAG8D,CACP,EAAG,CACClE,EAAG+D,EACH7D,EAAG8D,EACH5D,EAAG8D,CACP,EAAE,CACN2B,QAASjC,EAAMqB,IAAI,CAACZ,OAAO,EAAI,CAACT,EAAMY,KAAK,CAACH,OAAO,AACvD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMqB,IAAI,CAACrC,KAAK,EAAEnE,GAAG,GACjCuD,SAAU,CAAC,CACHhC,EAAG8D,EACH5D,EAAG8D,EACH5D,EAAG8D,CACP,EAAG,CACClE,EAAG+D,EACH7D,EAAG8D,EACH5D,EAAG8D,CACP,EAAG,CACClE,EAAG+D,EACH7D,EAAG+D,EACH7D,EAAG8D,CACP,EAAG,CACClE,EAAG8D,EACH5D,EAAG+D,EACH7D,EAAG8D,CACP,EAAE,CACN2B,QAASjC,EAAMqB,IAAI,CAACZ,OAAO,AAC/B,EACA,CACIsB,KAAM/C,EAAMgB,EAAMqB,IAAI,CAACrC,KAAK,EAAEnE,GAAG,GACjCuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAG4E,CACP,EAAG,CACChF,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAG4E,CACP,EAAG,CACChF,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAG4E,CACP,EAAG,CACChF,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAG4E,CACP,EAAE,CACNa,QAASjC,EAAMqB,IAAI,CAACZ,OAAO,AAC/B,EAAE,AACV,GACA5E,AAtqBY,IAAI,CAsqBV4F,WAAW,CAACN,KAAK,CAACG,EAAK,CAAC,CAC1B,MAAS,gDACTM,OAAQ5B,EAAMmB,KAAK,CAACU,WAAW,CAAG,KAAQ,IAC1CC,MAAO,CAAC,CACAC,KAAM/C,EAAMgB,EAAMmB,KAAK,CAACnC,KAAK,EAAEgD,QAAQ,CAAC,IAAKnH,GAAG,GAChDuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAGyE,CACP,EAAG,CACC7E,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAGyE,CACP,EAAG,CACC7E,EAAG+D,EACH7D,EAAG+D,EACH7D,EA9qBX,CA+qBO,EAAG,CACCJ,EAAG8D,EACH5D,EAAG+D,EACH7D,EAlrBX,CAmrBO,EAAE,CACNyF,QAASjC,EAAMmB,KAAK,CAACV,OAAO,EAAI,CAACT,EAAMgB,MAAM,CAACP,OAAO,AACzD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMmB,KAAK,CAACnC,KAAK,EAAEgD,QAAQ,CAAC,IAAKnH,GAAG,GAChDuD,SAAU,CAAC,CACHhC,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAGyE,CACP,EAAG,CACC7E,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAGyE,CACP,EAAG,CACC7E,EAAG8D,EACH5D,EAAG8D,EACH5D,EAnsBX,CAosBO,EAAG,CACCJ,EAAG+D,EACH7D,EAAG8D,EACH5D,EAvsBX,CAwsBO,EAAE,CACNyF,QAASjC,EAAMmB,KAAK,CAACV,OAAO,EAAI,CAACT,EAAMc,GAAG,CAACL,OAAO,AACtD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMmB,KAAK,CAACnC,KAAK,EAAEgD,QAAQ,CAAC,KAAMnH,GAAG,GACjDuD,SAAU,CAAC,CACHhC,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAGyE,CACP,EAAG,CACC7E,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAGyE,CACP,EAAG,CACC7E,EAAG8D,EACH5D,EAAG+D,EACH7D,EAxtBX,CAytBO,EAAG,CACCJ,EAAG8D,EACH5D,EAAG8D,EACH5D,EA5tBX,CA6tBO,EAAE,CACNyF,QAASjC,EAAMmB,KAAK,CAACV,OAAO,EAAI,CAACT,EAAMQ,IAAI,CAACC,OAAO,AACvD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMmB,KAAK,CAACnC,KAAK,EAAEgD,QAAQ,CAAC,KAAMnH,GAAG,GACjDuD,SAAU,CAAC,CACHhC,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAGyE,CACP,EAAG,CACC7E,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAGyE,CACP,EAAG,CACC7E,EAAG+D,EACH7D,EAAG8D,EACH5D,EA7uBX,CA8uBO,EAAG,CACCJ,EAAG+D,EACH7D,EAAG+D,EACH7D,EAjvBX,CAkvBO,EAAE,CACNyF,QAASjC,EAAMmB,KAAK,CAACV,OAAO,EAAI,CAACT,EAAMY,KAAK,CAACH,OAAO,AACxD,EACA,CACIsB,KAAM/C,EAAMgB,EAAMmB,KAAK,CAACnC,KAAK,EAAEnE,GAAG,GAClCuD,SAAU,CAAC,CACHhC,EAAG+D,EACH7D,EAAG8D,EACH5D,EA1vBX,CA2vBO,EAAG,CACCJ,EAAG8D,EACH5D,EAAG8D,EACH5D,EA9vBX,CA+vBO,EAAG,CACCJ,EAAG8D,EACH5D,EAAG+D,EACH7D,EAlwBX,CAmwBO,EAAG,CACCJ,EAAG+D,EACH7D,EAAG+D,EACH7D,EAtwBX,CAuwBO,EAAE,CACNyF,QAASjC,EAAMmB,KAAK,CAACV,OAAO,AAChC,EACA,CACIsB,KAAM/C,EAAMgB,EAAMmB,KAAK,CAACnC,KAAK,EAAEnE,GAAG,GAClCuD,SAAU,CAAC,CACHhC,EAAGuE,EACHrE,EAAGyE,EACHvE,EAAGyE,CACP,EAAG,CACC7E,EAAGmE,EACHjE,EAAGyE,EACHvE,EAAGyE,CACP,EAAG,CACC7E,EAAGmE,EACHjE,EAAGuE,EACHrE,EAAGyE,CACP,EAAG,CACC7E,EAAGuE,EACHrE,EAAGuE,EACHrE,EAAGyE,CACP,EAAE,CACNgB,QAASjC,EAAMmB,KAAK,CAACV,OAAO,AAChC,EAAE,AACV,EACJ,CACJ,CAKA,SAASyB,IACD,IAAI,CAACC,UAAU,EAEf,CAAC,CACOC,KAAM,SACNC,MAAO,EACX,EAAG,CACCD,KAAM,WACNC,MAAO,GACX,EAAE,CAACC,OAAO,CAAC,SAAUC,CAAG,EACxB,IAAI,CAACxC,QAAQ,CAACxF,UAAU,CAAC,CACrBiI,QAAS,SACTC,WAAY,CACRC,GAAI,cAAgBH,EAAIH,IAAI,AAChC,EACAO,SAAU,CAAC,CACHH,QAAS,sBACTG,SAAU,CAAC,CACHH,QAAS,UACTC,WAAY,CACR7C,KAAM,SACNyC,MAAOE,EAAIF,KAAK,AACpB,CACJ,EAAG,CACCG,QAAS,UACTC,WAAY,CACR7C,KAAM,SACNyC,MAAOE,EAAIF,KAAK,AACpB,CACJ,EAAG,CACCG,QAAS,UACTC,WAAY,CACR7C,KAAM,SACNyC,MAAOE,EAAIF,KAAK,AACpB,CACJ,EAAE,AACV,EAAE,AACV,EACJ,EAAG,IAAI,CAEf,CAMA,SAASO,IACL,IAAI3G,EAAU,IAAI,CAACA,OAAO,CACtB,IAAI,CAAC0D,IAAI,IACT,AAAC1D,CAAAA,EAAQ4G,MAAM,EAAI,EAAE,AAAD,EAAGP,OAAO,CAAC,SAAUQ,CAAC,EAIzB,YAHDA,CAAAA,EAAElD,IAAI,EACV3D,EAAQJ,KAAK,CAAC+D,IAAI,EAClB3D,EAAQJ,KAAK,CAACkH,iBAAiB,AAAD,GAElCD,CAAAA,EAAElD,IAAI,CAAG,WAAU,CAE3B,EAER,CAIA,SAASoD,IACL,IACIhH,EAAYH,AADJ,IAAI,CACMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAC7C,GAAIH,AAFQ,IAAI,CAENiE,OAAO,EACbjE,AAHQ,IAAI,CAGN8D,IAAI,GAAI,CAEV3D,IACAA,EAAUe,KAAK,CAAGf,EAAUe,KAAK,CAAG,IAC/Bf,CAAAA,EAAUe,KAAK,EAAI,EAAI,EAAI,GAAE,EAClCf,EAAUc,IAAI,CAAGd,EAAUc,IAAI,CAAG,IAC7Bd,CAAAA,EAAUc,IAAI,EAAI,EAAI,EAAI,GAAE,GAErC,IAAIZ,EAAWL,AAXP,IAAI,CAWSK,QAAQ,CAAE+G,EAAUpH,AAXjC,IAAI,CAWmCoH,OAAO,CAAEC,EAASrH,AAXzD,IAAI,CAW2DqH,MAAM,AAC7ED,CAAAA,CAAO,CAD4E/G,EAAW,IAAM,IAC1F,CAAG,CAAEgH,CAAAA,CAAM,CAAC,EAAE,EAAI,CAAA,EAC5BD,CAAO,CAFsG/G,EAAW,IAAM,IAEpH,CAAG,CAAEgH,CAAAA,CAAM,CAAC,EAAE,EAAI,CAAA,EAC5BD,CAAO,CAHgI/G,EAAW,SAAW,QAGnJ,CAAIL,AAdN,IAAI,CAcQsH,UAAU,CAAID,CAAAA,CAAM,CAAC,EAAE,EAAI,CAAA,EAAMA,CAAAA,CAAM,CAAC,EAAE,EAAI,CAAA,EAClED,CAAO,CAJmK/G,EAAW,QAAU,SAIrL,CAAIL,AAfN,IAAI,CAeQuH,WAAW,CAAIF,CAAAA,CAAM,CAAC,EAAE,EAAI,CAAA,EAAMA,CAAAA,CAAM,CAAC,EAAE,EAAI,CAAA,EAGnErH,AAlBQ,IAAI,CAkBNgB,OAAO,CAAG,EACY,CAAA,IAAxBb,EAAUqH,SAAS,EACnBxH,CAAAA,AApBI,IAAI,CAoBFgB,OAAO,CAAGhB,AApBZ,IAAI,CAoBciE,OAAO,CAACwD,QAAQ,CAACtH,EAAUS,KAAK,CAAA,EAK1DZ,AAzBQ,IAAI,CAyBNiE,OAAO,CAAC0B,OAAO,CAAG3F,AAzBhB,IAAI,CAyBkBiE,OAAO,CAACG,UAAU,EACpD,CACJ,CAIA,SAASsD,IACD,IAAI,CAAC5D,IAAI,IAET,CAAA,IAAI,CAAC6D,UAAU,CAAG,CAAA,CAAG,CAE7B,CAIA,SAASC,IACD,IAAI,CAAC3D,OAAO,EAAI,IAAI,CAACH,IAAI,IACzB,CAAA,IAAI,CAACG,OAAO,CAAC0B,OAAO,CAAG,IAAI,CAAC1B,OAAO,CAACG,UAAU,EAAC,CAEvD,CAIA,SAASyD,IACA,IAAI,CAAC5D,OAAO,EACb,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI6D,EAAU,IAAI,CAAA,CAEzC,CAIA,SAASC,EAAiBC,CAAO,EAC7B,OAAO,IAAI,CAAClE,IAAI,IAAMkE,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,GACvE,CAKA,SAASC,EAAiBJ,CAAO,EAC7B,IAAIhB,EACAxE,EAAI,IAAI,CAACwE,MAAM,CAACrE,MAAM,CAC1B,GAAI,IAAI,CAACmB,IAAI,GACT,KAAOtB,KAEHwE,AADAA,CAAAA,EAAS,IAAI,CAACA,MAAM,CAACxE,EAAE,AAAD,EACf6F,SAAS,GAChBrB,EAAOsB,MAAM,QAIjBN,EAAQ3I,IAAI,CAAC,IAAI,CAEzB,CAIA,SAASkJ,EAAiBP,CAAO,EAC7BA,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,IACzC,IAAI,CAACrE,IAAI,IACT,CAAA,IAAI,CAAC0E,SAAS,CAACC,SAAS,EAAI,sBAAqB,CAEzD,CAhrCA5K,EAAQyF,cAAc,CAAG,CACrBtD,MAAO,CAUHG,UAAW,CAOPiG,QAAS,CAAA,EAOTlF,MAAO,EAOPD,KAAM,EAONL,MAAO,IAQP4G,UAAW,CAAA,EAUX1G,aAAc,GASd4H,kBAAmB,KASnBvE,MAAO,CAIHS,QAAS,UAITC,KAAM,EA4CNM,OAAQ,CAAC,EAMTF,IAAK,CAAC,EAMNN,KAAM,CAAC,EAMPI,MAAO,CAAC,EAMRS,KAAM,CAAC,EAMPF,MAAO,CAAC,CACZ,CACJ,CACJ,CACJ,EA0DAzH,EAAQ8K,OAAO,CAjDf,SAAiBC,CAAU,CAAEC,CAAO,EAChC,IAAIC,EAAaF,EAAWzJ,SAAS,CACjC4J,EAAUF,EAAQ1J,SAAS,AAO/B2J,CAAAA,EAAWhF,IAAI,CAAG,WACd,IAAIkF,EACJ,MAAO,CAAC,CAAE,CAAA,AAAwC,OAAvCA,CAAAA,EAAK,IAAI,CAAC5I,OAAO,CAACJ,KAAK,CAACG,SAAS,AAAD,GAAe6I,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG5C,OAAO,AAAD,CAChG,EACA0C,EAAWG,oBAAoB,CAACC,IAAI,CAAC,mBACrCJ,EAAWK,wBAAwB,CAACD,IAAI,CAAC,mBAKzCH,EAAQK,YAAY,CAAG,WACnB,IAAIC,EACJ,GAAI,IAAI,CAACC,GAAG,CAAG,GACV9F,CAAAA,EAAQ,IAAI,CAAC+F,KAAK,GAAK/F,EAAQ,IAAI,CAACgG,GAAG,CAAA,EAAI,CAC5C,IAAID,EAAS,IAAI,CAACA,KAAK,EACf,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAE,CACtBC,EAAM,IAAI,CAACA,GAAG,EAAI,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAE,CACxCH,EAAe,EAAE,CACjB,IAAK,IAAI7G,EAAI,EAAGA,EAAI,EAAGA,IACnB6G,EAAaH,IAAI,CAAC,IAAI,CAACI,GAAG,CAAGE,CAAG,CAAChH,EAAE,CAAG,AAAC,CAAA,EAAI,IAAI,CAAC8G,GAAG,AAAD,EAAKC,CAAK,CAAC/G,EAAE,CAEvE,MAEI6G,EAAe,IAAI,CAACG,GAAG,CAE3B,IAAI,CAACC,IAAI,CAACC,IAAI,CAAC,IAAI,CAACxK,IAAI,CAAEmK,EAAc,KAAM,CAAA,EAClD,EACA5F,EAAM,CAAA,EAAMJ,EAAuBxF,EAAQyF,cAAc,EACzDC,EAASqF,EAAY,OAAQf,GAC7BtE,EAASqF,EAAY,YAAahF,GAClCL,EAASqF,EAAY,oBAAqB5E,GAC1CT,EAASqF,EAAY,oBAAqBvC,GAC1C9C,EAASqF,EAAY,YAAa7B,GAClCxD,EAASqF,EAAY,oBAAqBzB,GAC1C5D,EAASqF,EAAY,eAAgBlB,GACrCnE,EAASqF,EAAY,eAAgBhB,GACrCjE,EAAKmF,EAAY,eAAgBf,GACjCpE,EAAKmF,EAAY,eAAgBV,GACjCzE,EAAKmF,EAAY,eAAgBP,EACrC,EA49BA,IAAIT,EAA2B,WAMvB,SAASA,EAAU9H,CAAK,EACpB,IAAI,CAACA,KAAK,CAAGA,CACrB,CAwaA,OAlaA8H,EAAU3I,SAAS,CAACiF,UAAU,CAAG,WAC7B,IAAIpE,EAAQ,IAAI,CAACA,KAAK,CAClBG,EAAYH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CACzCwJ,EAAexJ,EAAUgE,KAAK,CAC9BE,EAAKrE,EAAM2B,QAAQ,CACnB2C,EAAKtE,EAAM2B,QAAQ,CAAG3B,EAAMQ,SAAS,CACrC+D,EAAKvE,EAAM4B,OAAO,CAClB4C,EAAKxE,EAAM4B,OAAO,CAAG5B,EAAMU,UAAU,CAErC+D,EAAKtE,EAAUS,KAAK,CACpBgJ,EAAkB,SAAUrH,CAAQ,EAChC,IA1wCkBtC,EA0wCdyC,EAzwCbJ,EAAUxC,EAywC8ByC,EACnCvC,EA3wCsBC,KAAAA,WA6wCtB,AAAIyC,EAAO,GACA,EAEPA,EAAO,IACA,GAEJ,CACX,EAAGmH,EAAoBD,EAAgB,CACnC,CAAErJ,EAAG8D,EAAI5D,EAAG+D,EAAI7D,EAAG8D,CAAG,EACtB,CAAElE,EAAG+D,EAAI7D,EAAG+D,EAAI7D,EAAG8D,CAAG,EACtB,CAAElE,EAAG+D,EAAI7D,EAAG+D,EAAI7D,EAhBX,CAgBiB,EACtB,CAAEJ,EAAG8D,EAAI5D,EAAG+D,EAAI7D,EAjBX,CAiBiB,EACzB,EAAGmJ,EAAiBF,EAAgB,CACjC,CAAErJ,EAAG8D,EAAI5D,EAAG8D,EAAI5D,EAnBX,CAmBiB,EACtB,CAAEJ,EAAG+D,EAAI7D,EAAG8D,EAAI5D,EApBX,CAoBiB,EACtB,CAAEJ,EAAG+D,EAAI7D,EAAG8D,EAAI5D,EAAG8D,CAAG,EACtB,CAAElE,EAAG8D,EAAI5D,EAAG8D,EAAI5D,EAAG8D,CAAG,EACzB,EAAGsF,EAAkBH,EAAgB,CAClC,CAAErJ,EAAG8D,EAAI5D,EAAG8D,EAAI5D,EAxBX,CAwBiB,EACtB,CAAEJ,EAAG8D,EAAI5D,EAAG8D,EAAI5D,EAAG8D,CAAG,EACtB,CAAElE,EAAG8D,EAAI5D,EAAG+D,EAAI7D,EAAG8D,CAAG,EACtB,CAAElE,EAAG8D,EAAI5D,EAAG+D,EAAI7D,EA3BX,CA2BiB,EACzB,EAAGqJ,EAAmBJ,EAAgB,CACnC,CAAErJ,EAAG+D,EAAI7D,EAAG8D,EAAI5D,EAAG8D,CAAG,EACtB,CAAElE,EAAG+D,EAAI7D,EAAG8D,EAAI5D,EA9BX,CA8BiB,EACtB,CAAEJ,EAAG+D,EAAI7D,EAAG+D,EAAI7D,EA/BX,CA+BiB,EACtB,CAAEJ,EAAG+D,EAAI7D,EAAG+D,EAAI7D,EAAG8D,CAAG,EACzB,EAAGwF,EAAmBL,EAAgB,CACnC,CAAErJ,EAAG8D,EAAI5D,EAAG+D,EAAI7D,EAlCX,CAkCiB,EACtB,CAAEJ,EAAG+D,EAAI7D,EAAG+D,EAAI7D,EAnCX,CAmCiB,EACtB,CAAEJ,EAAG+D,EAAI7D,EAAG8D,EAAI5D,EApCX,CAoCiB,EACtB,CAAEJ,EAAG8D,EAAI5D,EAAG8D,EAAI5D,EArCX,CAqCiB,EACzB,EAAGuJ,EAAkBN,EAAgB,CAClC,CAAErJ,EAAG8D,EAAI5D,EAAG8D,EAAI5D,EAAG8D,CAAG,EACtB,CAAElE,EAAG+D,EAAI7D,EAAG8D,EAAI5D,EAAG8D,CAAG,EACtB,CAAElE,EAAG+D,EAAI7D,EAAG+D,EAAI7D,EAAG8D,CAAG,EACtB,CAAElE,EAAG8D,EAAI5D,EAAG+D,EAAI7D,EAAG8D,CAAG,EACzB,EACG0F,EAAoB,CAAA,EACpBC,EAAiB,CAAA,EACjBC,EAAkB,CAAA,EAClBC,EAAmB,CAAA,EAIvB,EAAE,CACGC,MAAM,CAACvK,EAAMwK,KAAK,CAAExK,EAAMyK,KAAK,CAAEzK,EAAM0K,KAAK,EAC5CjE,OAAO,CAAC,SAAUkE,CAAI,EACnBA,IACIA,EAAKC,KAAK,CACND,EAAKE,QAAQ,CACbT,EAAiB,CAAA,EAGjBD,EAAoB,CAAA,EAIpBQ,EAAKE,QAAQ,CACbP,EAAmB,CAAA,EAGnBD,EAAkB,CAAA,EAIlC,GACA,IAAIS,EAAiB,SAAUC,CAAO,CAAEnB,CAAe,CAAEoB,CAAc,EAEnE,IAAK,IADGC,EAAY,CAAC,OAAQ,QAAS,UAAU,CAAE7K,EAAU,CAAC,EACpDoC,EAAI,EAAGA,EAAIyI,EAAUtI,MAAM,CAAEH,IAElC,IAAK,IADDkH,EAAOuB,CAAS,CAACzI,EAAE,CACdC,EAAI,EAAGA,EAAIsI,EAAQpI,MAAM,CAAEF,IAChC,GAAI,AAAsB,UAAtB,OAAOsI,CAAO,CAACtI,EAAE,CAAe,CAChC,IAAIyI,EAAMH,CAAO,CAACtI,EAAE,CAACiH,EAAK,CAC1B,GAAI,MAAOwB,EAAqC,CAC5C9K,CAAO,CAACsJ,EAAK,CAAGwB,EAChB,KACJ,CACJ,CAVJ,IAaAC,EAAYH,EAOhB,MANI5K,AAAoB,CAAA,IAApBA,EAAQwE,OAAO,EAAaxE,AAAoB,CAAA,IAApBA,EAAQwE,OAAO,CAC3CuG,EAAY/K,EAAQwE,OAAO,CAEF,SAApBxE,EAAQwE,OAAO,EACpBuG,CAAAA,EAAYvB,EAAkB,CAAA,EAE3B,CACH/E,KAAMnB,EAAatD,EAAQyE,IAAI,CAAE,GACjC1B,MAAOO,EAAatD,EAAQ+C,KAAK,CAAE,QACnC6C,YAAa4D,EAAkB,EAC/BhF,QAASuG,CACb,CACJ,EAGIC,EAAM,CACFC,KAAM,CAAC,EAOPlG,OAAQ2F,EAAe,CAACnB,EAAaxE,MAAM,CAAEwE,EAAa1E,GAAG,CAAE0E,EAAa,CAAEE,EAAmBM,GACjGlF,IAAK6F,EAAe,CAACnB,EAAa1E,GAAG,CAAE0E,EAAaxE,MAAM,CAAEwE,EAAa,CAAEG,EAAgBM,GAC3FzF,KAAMmG,EAAe,CACjBnB,EAAahF,IAAI,CACjBgF,EAAa5E,KAAK,CAClB4E,EAAa2B,IAAI,CACjB3B,EACH,CAAEI,EAAiBM,GACpBtF,MAAO+F,EAAe,CAClBnB,EAAa5E,KAAK,CAClB4E,EAAahF,IAAI,CACjBgF,EAAa2B,IAAI,CACjB3B,EACH,CAAEK,EAAkBM,GACrB9E,KAAMsF,EAAe,CAACnB,EAAanE,IAAI,CAAEmE,EAAarE,KAAK,CAAEqE,EAAa,CAAEO,EAlFpC,CAAA,GAmFxC5E,MAAOwF,EAAe,CAACnB,EAAarE,KAAK,CAAEqE,EAAanE,IAAI,CAAEmE,EAAa,CAAEM,EAnF9D,CAAA,EAoFnB,EAKJ,GAAI9J,AAAgC,SAAhCA,EAAUuI,iBAAiB,CAAa,CACxC,IAAI6C,EAAc,SAAUC,CAAK,CAC7BC,CAAK,EACD,OAAQ,AAACD,EAAM5G,OAAO,GAAK6G,EAAM7G,OAAO,EACnC4G,EAAM5G,OAAO,EACV6G,EAAM7G,OAAO,EACZ4G,EAAMxF,WAAW,GAAKyF,EAAMzF,WAAW,AACxD,EACI0F,EAAS,EAAE,CACXH,EAAYH,EAAIzG,IAAI,CAAEyG,EAAI9F,KAAK,GAC/BoG,EAAOxC,IAAI,CAAC,CACRzI,EAAG,AAAC8D,CAAAA,EAAKC,CAAC,EAAK,EACfjE,EAAG8D,EACH1D,EAjJH,EAkJGgL,KAAM,CAAEpL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC7B,GAEA4K,EAAYH,EAAIzG,IAAI,CAAEyG,EAAI5F,IAAI,GAC9BkG,EAAOxC,IAAI,CAAC,CACRzI,EAAG,AAAC8D,CAAAA,EAAKC,CAAC,EAAK,EACfjE,EAAG8D,EACH1D,EAAG8D,EACHkH,KAAM,CAAEpL,EAAG,EAAGE,EAAG,EAAGE,EAAG,EAAG,CAC9B,GAEA4K,EAAYH,EAAIrG,KAAK,CAAEqG,EAAI9F,KAAK,GAChCoG,EAAOxC,IAAI,CAAC,CACRzI,EAAG,AAAC8D,CAAAA,EAAKC,CAAC,EAAK,EACfjE,EAAG+D,EACH3D,EAjKH,EAkKGgL,KAAM,CAAEpL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC7B,GAEA4K,EAAYH,EAAIrG,KAAK,CAAEqG,EAAI5F,IAAI,GAC/BkG,EAAOxC,IAAI,CAAC,CACRzI,EAAG,AAAC8D,CAAAA,EAAKC,CAAC,EAAK,EACfjE,EAAG+D,EACH3D,EAAG8D,EACHkH,KAAM,CAAEpL,EAAG,GAAIE,EAAG,EAAGE,EAAG,CAAE,CAC9B,GAEJ,IAAIiL,EAAe,EAAE,CACjBL,EAAYH,EAAIjG,MAAM,CAAEiG,EAAI9F,KAAK,GACjCsG,EAAa1C,IAAI,CAAC,CACd3I,EAAG,AAAC8D,CAAAA,EAAKC,CAAC,EAAK,EACf7D,EAAG+D,EACH7D,EAlLH,EAmLGgL,KAAM,CAAEpL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC7B,GAEA4K,EAAYH,EAAIjG,MAAM,CAAEiG,EAAI5F,IAAI,GAChCoG,EAAa1C,IAAI,CAAC,CACd3I,EAAG,AAAC8D,CAAAA,EAAKC,CAAC,EAAK,EACf7D,EAAG+D,EACH7D,EAAG8D,EACHkH,KAAM,CAAEpL,EAAG,GAAIE,EAAG,EAAGE,EAAG,CAAE,CAC9B,GAEJ,IAAIkL,EAAY,EAAE,CACdN,EAAYH,EAAInG,GAAG,CAAEmG,EAAI9F,KAAK,GAC9BuG,EAAU3C,IAAI,CAAC,CACX3I,EAAG,AAAC8D,CAAAA,EAAKC,CAAC,EAAK,EACf7D,EAAG8D,EACH5D,EAnMH,EAoMGgL,KAAM,CAAEpL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC7B,GAEA4K,EAAYH,EAAInG,GAAG,CAAEmG,EAAI5F,IAAI,GAC7BqG,EAAU3C,IAAI,CAAC,CACX3I,EAAG,AAAC8D,CAAAA,EAAKC,CAAC,EAAK,EACf7D,EAAG8D,EACH5D,EAAG8D,EACHkH,KAAM,CAAEpL,EAAG,GAAIE,EAAG,EAAGE,EAAG,CAAE,CAC9B,GAEJ,IAAImL,EAAe,EAAE,CACjBP,EAAYH,EAAIjG,MAAM,CAAEiG,EAAIzG,IAAI,GAChCmH,EAAa5C,IAAI,CAAC,CACdvI,EAAG,AAAC0E,CAAAA,AAlNP,EAkNYZ,CAAC,EAAK,EACfhE,EAAG+D,EACHjE,EAAG8D,EACHsH,KAAM,CAAEpL,EAAG,EAAGE,EAAG,EAAGE,EAAG,EAAG,CAC9B,GAEA4K,EAAYH,EAAIjG,MAAM,CAAEiG,EAAIrG,KAAK,GACjC+G,EAAa5C,IAAI,CAAC,CACdvI,EAAG,AAAC0E,CAAAA,AA1NP,EA0NYZ,CAAC,EAAK,EACfhE,EAAG+D,EACHjE,EAAG+D,EACHqH,KAAM,CAAEpL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC7B,GAEJ,IAAIoL,EAAY,EAAE,CACdR,EAAYH,EAAInG,GAAG,CAAEmG,EAAIzG,IAAI,GAC7BoH,EAAU7C,IAAI,CAAC,CACXvI,EAAG,AAAC0E,CAAAA,AAnOP,EAmOYZ,CAAC,EAAK,EACfhE,EAAG8D,EACHhE,EAAG8D,EACHsH,KAAM,CAAEpL,EAAG,EAAGE,EAAG,EAAGE,EAAG,EAAG,CAC9B,GAEA4K,EAAYH,EAAInG,GAAG,CAAEmG,EAAIrG,KAAK,GAC9BgH,EAAU7C,IAAI,CAAC,CACXvI,EAAG,AAAC0E,CAAAA,AA3OP,EA2OYZ,CAAC,EAAK,EACfhE,EAAG8D,EACHhE,EAAG+D,EACHqH,KAAM,CAAEpL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC7B,GAEJ,IAAIqL,EAAW,SAAUC,CAAK,CAC1BtB,CAAI,CACJuB,CAAI,EACA,GAAID,AAAiB,IAAjBA,EAAMtJ,MAAM,CACZ,OAAO,KAEf,GAAIsJ,AAAiB,IAAjBA,EAAMtJ,MAAM,CACZ,OAAOsJ,CAAK,CAAC,EAAE,CAMnB,IAAK,IAJDE,EAAcC,AAx/CrBtM,EAw/CyCmM,EAClCjM,EACA,CAAA,GACAqM,EAAO,EACF7J,EAAI,EAAGA,EAAI2J,EAAYxJ,MAAM,CAAEH,IAChC0J,EAAOC,CAAW,CAAC3J,EAAE,CAACmI,EAAK,CAC3BuB,EAAOC,CAAW,CAACE,EAAK,CAAC1B,EAAK,CAC9B0B,EAAO7J,EAED0J,EAAOC,CAAW,CAAC3J,EAAE,CAACmI,EAAK,EACjCuB,EAAOC,CAAW,CAACE,EAAK,CAAC1B,EAAK,EAC7BwB,CAAW,CAAC3J,EAAE,CAAC7B,CAAC,CAAGwL,CAAW,CAACE,EAAK,CAAC1L,CAAC,EACvC0L,CAAAA,EAAO7J,CAAAA,EAGf,OAAOyJ,CAAK,CAACI,EAAK,AACtB,CACAjB,CAAAA,EAAIC,IAAI,CAAG,CACP5K,EAAG,CACC,KAAQuL,EAASN,EAAQ,IAAK,IAC9B,MAASM,EAASN,EAAQ,IAAK,EACnC,EACAnL,EAAG,CACC,IAAOyL,EAASH,EAAW,IAAK,IAChC,OAAUG,EAASJ,EAAc,IAAK,EAC1C,EACAjL,EAAG,CACC,IAAOqL,EAASD,EAAW,IAAK,IAChC,OAAUC,EAASF,EAAc,IAAK,EAC1C,CACJ,CACJ,MAEIV,EAAIC,IAAI,CAAG,CACP5K,EAAG,CACC,KAAQ,CACJF,EAAG8D,EAAI1D,EA9Rd,EA8RqBgL,KAAM,CAAEpL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC3C,EACA,MAAS,CACLJ,EAAG+D,EAAI3D,EAjSd,EAiSqBgL,KAAM,CAAEpL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC3C,CACJ,EACAJ,EAAG,CACC,IAAO,CACHE,EAAG8D,EAAI5D,EAtSd,EAsSqBgL,KAAM,CAAEpL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC3C,EACA,OAAU,CACNF,EAAG+D,EACH7D,EA1SP,EA2SOgL,KAAM,CAAEpL,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,CAC7B,CACJ,EACAA,EAAG,CACC,IAAO,CACHJ,EAAG8J,EAAkB/F,EAAKD,EAC1B5D,EAAG8D,EACHoH,KAAMtB,EACF,CAAE9J,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,EACnB,CAAEJ,EAAG,EAAGE,EAAG,EAAGE,EAAG,EAAG,CAC5B,EACA,OAAU,CACNJ,EAAG8J,EAAkB/F,EAAKD,EAC1B5D,EAAG+D,EACHmH,KAAMtB,EACF,CAAE9J,EAAG,EAAGE,EAAG,EAAGE,EAAG,CAAE,EACnB,CAAEJ,EAAG,EAAGE,EAAG,EAAGE,EAAG,EAAG,CAC5B,CACJ,CACJ,EAEJ,OAAOyK,CACX,EAsBAtD,EAAU3I,SAAS,CAACsI,QAAQ,CAAG,SAAU7G,CAAK,EAC1C,IAMI0L,EANAtM,EAAQ,IAAI,CAACA,KAAK,CAAE2B,EAAW3B,EAAM2B,QAAQ,CAAE4K,EAAYvM,EAAMQ,SAAS,CAAGmB,EAAUC,EAAU5B,EAAM4B,OAAO,CAAE4K,EAAaxM,EAAMU,UAAU,CAAGkB,EAAS6K,EAAU9K,EAAW3B,EAAMQ,SAAS,CAAG,EAAGkM,EAAU9K,EAAU5B,EAAMU,UAAU,CAAG,EAAGiM,EAAS,CAClPC,KAAMxK,OAAOyK,SAAS,CACtBC,KAAM,CAAC1K,OAAOyK,SAAS,CACvBE,KAAM3K,OAAOyK,SAAS,CACtBG,KAAM,CAAC5K,OAAOyK,SAAS,AAC3B,EAEA9L,EAAQ,EAyDZ,OAvDAuL,EAAU,CAAC,CACH/L,EAAGoB,EACHlB,EAAGmB,EACHjB,EAAG,CACP,EAAG,CACCJ,EAAGoB,EACHlB,EAAGmB,EACHjB,EAAGC,CACP,EAAE,CAEN,CAAC,EAAG,EAAE,CAAC6F,OAAO,CAAC,SAAUjE,CAAC,EACtB8J,EAAQpD,IAAI,CAAC,CACT3I,EAAGgM,EACH9L,EAAG6L,CAAO,CAAC9J,EAAE,CAAC/B,CAAC,CACfE,EAAG2L,CAAO,CAAC9J,EAAE,CAAC7B,CAAC,AACnB,EACJ,GAEA,CAAC,EAAG,EAAG,EAAG,EAAE,CAAC8F,OAAO,CAAC,SAAUjE,CAAC,EAC5B8J,EAAQpD,IAAI,CAAC,CACT3I,EAAG+L,CAAO,CAAC9J,EAAE,CAACjC,CAAC,CACfE,EAAG+L,EACH7L,EAAG2L,CAAO,CAAC9J,EAAE,CAAC7B,CAAC,AACnB,EACJ,GAIA2L,AAFAA,CAAAA,EAAUF,AAznDLtM,EAynDyBwM,EAAStM,EAAO,CAAA,EAAK,EAE3CyG,OAAO,CAAC,SAAUwG,CAAM,EAC5BN,EAAOC,IAAI,CAAGvL,KAAK6L,GAAG,CAACP,EAAOC,IAAI,CAAEK,EAAO1M,CAAC,EAC5CoM,EAAOG,IAAI,CAAGzL,KAAK8L,GAAG,CAACR,EAAOG,IAAI,CAAEG,EAAO1M,CAAC,EAC5CoM,EAAOI,IAAI,CAAG1L,KAAK6L,GAAG,CAACP,EAAOI,IAAI,CAAEE,EAAOxM,CAAC,EAC5CkM,EAAOK,IAAI,CAAG3L,KAAK8L,GAAG,CAACR,EAAOK,IAAI,CAAEC,EAAOxM,CAAC,CAChD,GAEIkB,EAAWgL,EAAOC,IAAI,EACtB7L,CAAAA,EAAQM,KAAK6L,GAAG,CAACnM,EAAO,EAAIM,KAAK+L,GAAG,CAAC,AAACzL,CAAAA,EAAW8K,CAAM,EAAME,CAAAA,EAAOC,IAAI,CAAGH,CAAM,GAAM,EAAC,EAGxFF,EAAYI,EAAOG,IAAI,EACvB/L,CAAAA,EAAQM,KAAK6L,GAAG,CAACnM,EAAO,AAACwL,CAAAA,EAAYE,CAAM,EAAME,CAAAA,EAAOG,IAAI,CAAGL,CAAM,EAAE,EAGvE7K,EAAU+K,EAAOI,IAAI,GAEjBhM,EADA4L,EAAOI,IAAI,CAAG,EACN1L,KAAK6L,GAAG,CAACnM,EAAO,AAACa,CAAAA,EAAU8K,CAAM,EAAM,CAAA,CAACC,EAAOI,IAAI,CAAGnL,EAAU8K,CAAM,GAGtErL,KAAK6L,GAAG,CAACnM,EAAO,EAAI,AAACa,CAAAA,EAAU8K,CAAM,EAAMC,CAAAA,EAAOI,IAAI,CAAGL,CAAM,EAAK,IAIhFF,EAAaG,EAAOK,IAAI,EACxBjM,CAAAA,EAAQM,KAAK6L,GAAG,CAACnM,EAAOM,KAAK+L,GAAG,CAAC,AAACZ,CAAAA,EAAaE,CAAM,EAAMC,CAAAA,EAAOK,IAAI,CAAGN,CAAM,GAAG,EAE/E3L,CACX,EACO+G,CACX,GACAjK,CAAAA,EAAQiK,SAAS,CAAGA,CACxB,EAAGjK,GAAYA,CAAAA,EAAU,CAAC,CAAA,GAMG,IAAIwP,EAAiBxP,EAwC9CyP,EAAmIrP,EAAoB,KACvJsP,EAAuJtP,EAAoBI,CAAC,CAACiP,GAa7KE,EAAW,AAAC/N,IAA+E+N,QAAQ,CAInGC,EAAY,AAACF,IAA2IG,WAAW,CAACC,IAAI,CAACxO,SAAS,CAElLyO,EAAa,AAACnO,IAA+EmO,UAAU,CAAEC,EAAoB,AAACpO,IAA+EkE,IAAI,CAiBrN,SAASmK,EAA2B9F,CAAO,EACvC,IACI+F,EAAU/F,EAAQC,KAAK,CADd,IAAI,CAEb,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,IAE7B,GAAI,CAACnB,AAJQ,IAAI,CAILhH,KAAK,CAAC8D,IAAI,GAClB,OAAOiK,EAEX,IAAIC,EAAeP,EAAUO,YAAY,CACrC5N,EAAU4G,AARD,IAAI,CAQI5G,OAAO,CACxB6N,EAAsB5M,KAAK6M,KAAK,CAChClH,AAVS,IAAI,CAUNyD,KAAK,CAAC0D,YAAY,CAAC/N,EAAQgO,SAAS,GAC3CC,EAAe,EAAE,CACrB,GAAIrH,AAZS,IAAI,CAYNsH,UAAU,CACjB,IAAK,IAAI9L,EAAI,EAAGA,EAAIwE,AAbX,IAAI,CAacjH,MAAM,CAAC4C,MAAM,CAAEH,IACtC6L,EAAanF,IAAI,CAAC,CACd3I,EAAGyG,AAfF,IAAI,CAeKsH,UAAU,CAAC9L,EAAE,CACvB/B,EAAGL,EAAQmO,QAAQ,CACfvH,AAjBH,IAAI,CAiBMjH,MAAM,CAACyC,EAAE,CAACgM,OAAO,CAAGP,EAC/BtN,EAAGqG,AAlBF,IAAI,CAkBKyH,QAAQ,AACtB,GAGR,IAAItO,EAAY6G,AAtBH,IAAI,CAsBMhH,KAAK,CAACI,OAAO,CAACJ,KAAK,CAACG,SAAS,CACpDkO,EAAeK,AAtwDF5O,EAswD2BuO,EAAcrH,AAvBzC,IAAI,CAuB4ChH,KAAK,CAAE,CAAA,GAAM6B,GAAG,CAAC,SAAUC,CAAK,EAAI,MAAQ,CAAEkB,MAAOlB,EAAMvB,CAAC,CAAE0C,MAAOnB,EAAMrB,CAAC,CAAEyC,MAAOpB,EAAMnB,CAAC,AAAC,CAAI,GAC1JqG,AAxBS,IAAI,CAwBN2H,KAAK,EAAIxO,GAAaA,EAAUS,KAAK,EAAIT,EAAUc,IAAI,GAE1D+F,AA1BK,IAAI,CA0BF4H,WAAW,GAClB5H,AA3BK,IAAI,CA2BF4H,WAAW,CAAC9I,GAAG,CAACkB,AA3BlB,IAAI,CA2BqB2H,KAAK,EACnC3H,AA5BK,IAAI,CA4BF4H,WAAW,CAAClF,IAAI,CAAC,CACpBmF,WAAY,EACZC,WAAY,CAChB,IAEJ9H,AAjCS,IAAI,CAiCN2H,KAAK,CAACjF,IAAI,CAAC,CACd3D,OAAQ1E,KAAK8L,GAAG,CAAC,EAAG,AAAChN,EAAUc,IAAI,CAAG,KAAOd,EAAUc,IAAI,CAAG,GAC1Dd,EAAUS,KAAK,CAAGS,KAAK6M,KAAK,CAAClH,AAnC5B,IAAI,CAmC+ByH,QAAQ,EAAI,GAChDpN,KAAK6M,KAAK,CAAClH,AApCV,IAAI,CAoCayH,QAAQ,EAAI,GACtC,IAEJJ,EAAaU,QAAQ,CAAG,CAAA,EACxB,IAAIC,EAAahB,EAAa3O,IAAI,CAxCrB,IAAI,CAyCbgP,EACA,CAAA,EACA,CAAA,GAIJ,GAHIW,CAAU,CAAC,EAAE,EAAIA,AAAqB,MAArBA,CAAU,CAAC,EAAE,CAAC,EAAE,EACjCA,CAAAA,CAAU,CAAC,EAAE,CAAG,CAAC,IAAKA,CAAU,CAAC,EAAE,CAAC,EAAE,CAAEA,CAAU,CAAC,EAAE,CAAC,EAAE,CAAC,AAAD,EAExDhI,AA/CS,IAAI,CA+CNiI,QAAQ,CAAE,CAEjB,IAAIA,EAAWjI,AAjDN,IAAI,CAiDSiI,QAAQ,CAACC,MAAM,CAAC,EAClClI,AAlDK,IAAI,CAkDFiI,QAAQ,CAACtM,MAAM,CAAG,GAAG4H,MAAM,CAACyE,EAEvCC,CAAAA,EAASE,IAAI,CAAGnI,AApDP,IAAI,CAoDUiI,QAAQ,CAACE,IAAI,CACpCnI,AArDS,IAAI,CAqDNiI,QAAQ,CAAGA,CACtB,CAEA,OADAjI,AAvDa,IAAI,CAuDVoI,SAAS,CAAGrB,EACZA,CACX,CA6I6B,IAAIsB,EA7GZ,CACjBC,OAAQ,CA8BJC,WAAY,SAiBZC,OAAQ,CAAA,CACZ,EACAC,MAAO,CAgCHF,WAAY,KAmBZC,OAAQ,IACZ,CACJ,EAsBIE,EAA6B,AAACjQ,IAA+E+N,QAAQ,CAErHmC,EAA6B,AAAClQ,IAA+E8D,QAAQ,CAAEqM,EAAS,AAACnQ,IAA+EmQ,MAAM,CAAEC,EAA+B,AAACpQ,IAA+EmO,UAAU,CAAEkC,EAAyB,AAACrQ,IAA+EkE,IAAI,CAkBpc,SAASoM,EAA4BlM,CAAC,EAClC,IAAImM,EAAS,IAAI,CAACrF,IAAI,CAACqF,MAAM,CACzBA,GACAJ,EAAO/L,EAAEyF,GAAG,CAAE0G,EAAOC,aAAa,CAACpM,EAAEyF,GAAG,EAEhD,CAIA,SAAS4G,EAAoBlI,CAAO,EAChC,IAAIgI,EAAS,IAAI,CAACrF,IAAI,CAACqF,MAAM,CACzBG,EAAOnI,EAAQC,KAAK,CAAC,IAAI,CACzB,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,IAC7B,GAAI6H,EAAQ,CACR,IAAIzG,EAAQ4G,CAAI,CAAC,EAAE,CACf3G,EAAM2G,CAAI,CAAC,EAAE,CACjB,GAAI5G,AAAa,MAAbA,CAAK,CAAC,EAAE,EAAYC,AAAW,MAAXA,CAAG,CAAC,EAAE,CAAU,CACpC,IAAI4G,EAAO,CACHJ,EAAOC,aAAa,CAAC,CAAE1P,EAAGgJ,CAAK,CAAC,EAAE,CACtC9I,EAAG8I,CAAK,CAAC,EAAE,CACX5I,EAAG,CAAE,GACDqP,EAAOC,aAAa,CAAC,CAAE1P,EAAGiJ,CAAG,CAAC,EAAE,CACpC/I,EAAG+I,CAAG,CAAC,EAAE,CACT7I,EAAG,CAAE,GACJ,CACL,OAAO,IAAI,CAACgK,IAAI,CAAC3K,KAAK,CAACkE,QAAQ,CAACmM,cAAc,CAACD,EACnD,CACJ,CACA,OAAOD,CACX,CAS6B,OA/C7B,SAAmCG,CAAS,EACpCT,EAA6BH,EAA4B,iBACzDC,EAA2BW,EAAW,wBAAyBP,GAC/DD,EAAuBQ,EAAUnR,SAAS,CAAE,cAAe+Q,GAEnE,EA2DI5M,GAAiB,AAAC7D,IAA+E6D,cAAc,CAE/GiN,GAA4B,AAAC9Q,IAA+EG,OAAO,CAE1D4Q,GAnhE1CvO,EAshEfwO,GAA6B,AAAChR,IAA+E8D,QAAQ,CAAEmN,GAA0B,AAACjR,IAA+EgE,KAAK,CAAEkN,GAAyB,AAAClR,IAA+EI,IAAI,CAAE+Q,GAAyB,AAACnR,IAA+EkE,IAAI,CASxc,SAASkN,KAEL,IADI7H,EAEAhJ,EAAQ2K,AADD,IAAI,CACE3K,KAAK,CAClBI,EAAUuK,AAFH,IAAI,CAEIvK,OAAO,CACrB,CAAA,AAAsB,OAArB4I,CAAAA,EAAKhJ,EAAM8D,IAAI,AAAD,GAAekF,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG3J,IAAI,CAACW,EAAK,GAAM2K,AAAc,cAAdA,AAHpE,IAAI,CAGqEmG,IAAI,GACpF1Q,EAAQ2Q,SAAS,CAAGJ,GAAuBvQ,EAAQ2Q,SAAS,CAAE,GAC9D3Q,EAAQ4Q,aAAa,CAAGL,GAAuBvQ,EAAQ4Q,aAAa,CAAE,GAE9E,CAIA,SAASC,GAAoBpN,CAAC,EAEtB8G,AADO,IAAI,CACN3K,KAAK,CAAC8D,IAAI,IACf6G,AAAc,cAAdA,AAFO,IAAI,CAENmG,IAAI,EACLjN,EAAE/B,KAAK,EACP+B,CAAAA,EAAE/B,KAAK,CAACoP,YAAY,CAAGvG,AAJpB,IAAI,CAIqBwG,OAAO,CAC/BtN,EAAE/B,KAAK,CAACsP,QAAQ,CAChBzG,AAND,IAAI,CAME0G,GAAG,CAAGxN,EAAE/B,KAAK,CAACwP,QAAQ,AAAD,CAG1C,CAIA,SAASC,KAEA5G,AADM,IAAI,CACLqF,MAAM,EACZrF,CAAAA,AAFO,IAAI,CAENqF,MAAM,CAAG,IAAIwB,GAFX,IAAI,CAE2B,CAE9C,CAKA,SAASC,GAAoBzJ,CAAO,SAGhC,AAAI,AAAC2C,AAFM,IAAI,CAEL3K,KAAK,CAAC8D,IAAI,IAAM6G,AAAc,cAAdA,AAFf,IAAI,CAEgBmG,IAAI,CAG5B,EAAE,CAFE9I,EAAQC,KAAK,CAHb,IAAI,CAGgB,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,GAG5D,CAIA,SAASuJ,GAAwB1J,CAAO,EAEpC,GAAI,CAAC,IAAI,CAAChI,KAAK,CAAC8D,IAAI,IAAM,AAAc,cAAd,IAAI,CAACgN,IAAI,CAC/B,OAAO9I,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,IAExD,IAAIwJ,EAAOxJ,UACPyJ,EAAOD,CAAI,CAAC,EAAE,CACdE,EAAKF,CAAI,CAAC,EAAE,CACZxB,EAAO,EAAE,CACT2B,EAAW,IAAI,CAACC,eAAe,CAAC,CAAEC,MAAOJ,CAAK,GAC9CK,EAAS,IAAI,CAACF,eAAe,CAAC,CAAEC,MAAOH,CAAG,GAC9C,GAAIC,GAAYG,EACZ,IAAK,IAAIzP,EAAI,EAAGA,EAAIsP,EAASnP,MAAM,CAAEH,GAAK,EAAG,CACzC,IAAI0P,EAAeJ,CAAQ,CAACtP,EAAE,CAC1B2P,EAAaL,CAAQ,CAACtP,EAAI,EAAE,CAC5B4P,EAAaH,CAAM,CAACzP,EAAE,CACtB6P,EAAWJ,CAAM,CAACzP,EAAI,EAAE,AACJ,CAAA,MAApB0P,CAAY,CAAC,EAAE,EACfC,AAAkB,MAAlBA,CAAU,CAAC,EAAE,EACbC,AAAkB,MAAlBA,CAAU,CAAC,EAAE,EACbC,AAAgB,MAAhBA,CAAQ,CAAC,EAAE,EACXlC,EAAKjH,IAAI,CAACgJ,EAAcC,EAAYE,EAEpC,CAAC,IAAKD,CAAU,CAAC,EAAE,CAAEA,CAAU,CAAC,EAAE,CAAC,CAAE,CAAC,IAAI,CAElD,CAEJ,OAAOjC,CACX,CAIA,SAASmC,GAAwBtK,CAAO,EACpC,IACIgI,EAASrF,AADF,IAAI,CACGqF,MAAM,CACpBhQ,EAAQ2K,AAFD,IAAI,CAEE3K,KAAK,CAClBmQ,EAAOnI,EAAQC,KAAK,CAHb,IAAI,CAIX,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,IAE7B,GAAkB,cAAdwC,AANO,IAAI,CAMNmG,IAAI,EACT,CAAC9Q,EAAMiE,OAAO,EACd,CAACjE,EAAM8D,IAAI,IAGXqM,AAAS,OAATA,EAFA,OAAOA,EAKX,IAKIC,EALAjQ,EAAYH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CACzC3B,EAAImM,AAfG,IAAI,CAeF4H,OAAO,CAAGvS,EAAMQ,SAAS,CAAGL,EAAUS,KAAK,CACpDuD,EAAQnE,EAAMiE,OAAO,CAAC0B,OAAO,CAC7B6M,EAAerC,CAAI,CAAC,EAAE,CACtBsC,EAAatC,CAAI,CAAC,EAAE,CAEpBuC,EAAe,EAAE,CAoDrB,MAnDwB,MAApBF,CAAY,CAAC,EAAE,EAAYC,AAAkB,MAAlBA,CAAU,CAAC,EAAE,GACxCrC,EAAO,CACHJ,EAAO2C,KAAK,CAAC,CAAEpS,EAAGiS,CAAY,CAAC,EAAE,CAAE/R,EAAG+R,CAAY,CAAC,EAAE,CAAE7R,EAAG,CAAE,GAC5DqP,EAAO2C,KAAK,CAAC,CAAEpS,EAAGiS,CAAY,CAAC,EAAE,CAAE/R,EAAG+R,CAAY,CAAC,EAAE,CAAE7R,EAAGnC,CAAE,GAC5DwR,EAAO2C,KAAK,CAAC,CAAEpS,EAAGkS,CAAU,CAAC,EAAE,CAAEhS,EAAGgS,CAAU,CAAC,EAAE,CAAE9R,EAAG,CAAE,GACxDqP,EAAO2C,KAAK,CAAC,CAAEpS,EAAGkS,CAAU,CAAC,EAAE,CAAEhS,EAAGgS,CAAU,CAAC,EAAE,CAAE9R,EAAGnC,CAAE,GAC3D,CACI,IAAI,CAACoM,KAAK,EAcN,IAAI,CAAC2H,OAAO,EACbpO,EAAMQ,IAAI,CAACC,OAAO,EAClB8N,EAAaxJ,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,EAElCjM,EAAMY,KAAK,CAACH,OAAO,EACnB8N,EAAaxJ,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,IAUlCjM,EAAMmB,KAAK,CAACV,OAAO,EACnB8N,EAAaxJ,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,EAElCjM,EAAMqB,IAAI,CAACZ,OAAO,EAClB8N,EAAaxJ,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,GAElCjM,EAAMc,GAAG,CAACL,OAAO,EACjB8N,EAAaxJ,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,EAElCjM,EAAMgB,MAAM,CAACP,OAAO,EACpB8N,EAAaxJ,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,IAtClCjM,EAAMmB,KAAK,CAACV,OAAO,EACnB8N,EAAaxJ,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,EAElCjM,EAAMqB,IAAI,CAACZ,OAAO,EAClB8N,EAAaxJ,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,EAElCjM,EAAMQ,IAAI,CAACC,OAAO,EAClB8N,EAAaxJ,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,EAElCjM,EAAMY,KAAK,CAACH,OAAO,EACnB8N,EAAaxJ,IAAI,CAACkH,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,GA+B1CsC,EAAeE,AAvrEN9S,EAurEoC4S,EAAc,IAAI,CAAC1S,KAAK,CAAE,CAAA,IAEpEA,EAAMkE,QAAQ,CAACmM,cAAc,CAACqC,EACzC,CAMA,SAASG,GAAqB7K,CAAO,CAAE8K,CAAI,EAGvC,IAFI9J,EACA+J,EAEA/S,EAAQ2K,AADD,IAAI,CACE3K,KAAK,CAClBgT,EAAYrI,AAFL,IAAI,CAEMqI,SAAS,CAC1BC,EAAgBtI,AAHT,IAAI,CAGUsI,aAAa,CAClCC,EAAQvI,AAJD,IAAI,CAIEuI,KAAK,CACtB,GAAIvI,AALO,IAAI,CAKNwI,UAAU,EACfnT,EAAM4F,WAAW,EACjB5F,EAAM8D,IAAI,IACVkP,GACAF,GACAA,EAAKM,KAAK,CAAE,CACZ,IAAIC,EAAiBL,EAAUM,OAAO,CAACC,UAAU,CAAC,EAAE,CAACC,OAAO,GACxDC,EAAczT,EAAM4F,WAAW,CAACjB,IAAI,CAAC6O,OAAO,GAC5CrT,EAAYH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CACzCuT,EAAW,CACPnT,EAAGP,EAAMQ,SAAS,CAAG,EACrBC,EAAGT,EAAMU,UAAU,CAAG,EACtBC,EAAGR,EAAUS,KAAK,CAAG,EACrBC,GAAK8P,GAAuBxQ,EAAUS,KAAK,CAAE,GACzC+P,GAAuBxQ,EAAUW,YAAY,CAAE,EACvD,EACA6S,EAAQV,EAAcW,OAAO,CAACd,EAAKxJ,GAAG,EACtCuK,EAAWX,CAAK,CAACD,CAAa,CAACU,EAAQ,EAAE,CAAC,CAC1CG,EAAWZ,CAAK,CAACD,CAAa,CAACU,EAAQ,EAAE,CAAC,CAC1CI,EAAW,KAAK,EAChBC,EAAe,KAAK,EACpBC,EAAe,KAAK,EA8BxB,MA3BI,CAAA,AAA8E,OAA7EjL,CAAAA,EAAK6K,MAAAA,EAA2C,KAAK,EAAIA,EAAST,KAAK,AAAD,GAAepK,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGkL,EAAE,AAAD,GACnHF,CAAAA,EAAexD,GAAgC,CAC3CjQ,EAAGsT,EAAST,KAAK,CAACc,EAAE,CAAC3T,CAAC,CACtBE,EAAGoT,EAAST,KAAK,CAACc,EAAE,CAACzT,CAAC,CACtBE,EAAG,IACP,EAAG+S,EAAUA,EAAS7S,EAAE,CAAA,EAIxB,CAAA,AAA8E,OAA7EkS,CAAAA,EAAKe,MAAAA,EAA2C,KAAK,EAAIA,EAASV,KAAK,AAAD,GAAeL,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGmB,EAAE,AAAD,GACnHD,CAAAA,EAAezD,GAAgC,CAC3CjQ,EAAGuT,EAASV,KAAK,CAACc,EAAE,CAAC3T,CAAC,CACtBE,EAAGqT,EAASV,KAAK,CAACc,EAAE,CAACzT,CAAC,CACtBE,EAAG,IACP,EAAG+S,EAAUA,EAAS7S,EAAE,CAAA,EAO5BkT,EAAWvD,GALXuD,EAAW,CACPxT,EAAGuS,EAAKM,KAAK,CAACc,EAAE,CAAC3T,CAAC,CAClBE,EAAGqS,EAAKM,KAAK,CAACc,EAAE,CAACzT,CAAC,CAClBE,EAAG,IACP,EACqD+S,EAAUA,EAAS7S,EAAE,EAMnEQ,KAAK+L,GAAG,CAAC4G,EACZD,EAASxT,CAAC,CAAGyT,EAAazT,CAAC,CAAG0T,EAC9BA,EAAa1T,CAAC,CAAGwT,EAASxT,CAAC,CAC3B8S,EAAc9S,CAAC,CAAGkT,EAAYlT,CAAC,CACvC,CACA,OAAOyH,EAAQC,KAAK,CA7DT,IAAI,CA6DY,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,GACxD,CAIA,SAASgM,GAAyBnM,CAAO,EACrC,IAAIsB,EAAMtB,EAAQC,KAAK,CAAC,IAAI,CACxB,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,IAC7B,OAAO,IAAI,CAAC6H,MAAM,CACd,IAAI,CAACA,MAAM,CAACC,aAAa,CAAC3G,EAAK,CAAA,GAC/BA,CACR,CAWA,IAAIkI,GAAiC,WASjC,SAASA,EAAgB7G,CAAI,EACzB,IAAI,CAACA,IAAI,CAAGA,CAChB,CAuQA,OA7PA6G,EAAgB7I,OAAO,CAAG,SAAUyL,CAAS,CAAE9D,CAAS,EAEpD,GADA+D,GAA0B/D,GACtB,CAAC8D,EAAUE,SAAS,CAACC,QAAQ,CAAC,UAAW,CACzC7D,GAAwB,CAAA,EAAMpN,GAAekH,KAAK,CAAE6E,GACpD+E,EAAUE,SAAS,CAACpL,IAAI,CAAC,UACzBuH,GAA2B2D,EAAW,OAAQ7C,IAC9Cd,GAA2B2D,EAAW,kBAAmBvD,IACzDJ,GAA2B2D,EAAW,gBAAiBnD,IACvD,IAAIuD,EAAYJ,EAAUjV,SAAS,CACnCyR,GAAuB4D,EAAW,cAAe/C,IACjDb,GAAuB4D,EAAW,kBAAmB9C,IACrDd,GAAuB4D,EAAW,kBAAmBlC,IACrD1B,GAAuB4D,EAAW,eAAgB3B,IAClDjC,GAAuB4D,EAAW,mBAAoBL,GAC1D,CACJ,EAiBA3C,EAAgBrS,SAAS,CAAC8Q,aAAa,CAAG,SAAU3G,CAAG,CAAEmL,CAAO,EAE5D,IAAI9J,EAAOqF,AADE,IAAI,CACCrF,IAAI,CAClB3K,EAAQ2K,EAAK3K,KAAK,CAEtB,GAAI2K,AAAc,cAAdA,EAAKmG,IAAI,EACT,CAAC9Q,EAAMiE,OAAO,EACd,CAACjE,EAAM8D,IAAI,GACX,OAAOwF,EAEX,IAaIoL,EAbAxT,EAAQqP,GAA4BvQ,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAACe,KAAK,CACvED,EAAOsP,GAA4BvQ,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAACc,IAAI,CACrE0T,EAAehE,GAAuB8D,GAAW9J,EAAKvK,OAAO,CAACqP,KAAK,CAACF,UAAU,CAC9E5E,EAAKvK,OAAO,CAACkP,MAAM,CAACC,UAAU,EAC9BqF,EAAOjE,GAAuB8D,GAAW9J,EAAKvK,OAAO,CAACqP,KAAK,CAACD,MAAM,CAClE7E,EAAKvK,OAAO,CAACkP,MAAM,CAACE,MAAM,EAC1BrL,EAAQnE,EAAMiE,OAAO,CAAC0B,OAAO,CAC7BhE,EAAW3B,EAAM2B,QAAQ,CACzB4K,EAAYvM,EAAMQ,SAAS,CAAGmB,EAC9BC,EAAU5B,EAAM4B,OAAO,CACvB4K,EAAaxM,EAAMU,UAAU,CAAGkB,EAChCiT,EAAU,EACVC,EAAU,EAEVC,EAAO,CAAExU,EAAG,EACZE,EAAG,EACHE,EAAG,CAAE,EAGLqU,EAAc,CAAA,EAElB,GADA1L,EAAMqB,EAAKqF,MAAM,CAAC2C,KAAK,CAAC,CAAEpS,EAAG+I,EAAI/I,CAAC,CAAEE,EAAG6I,EAAI7I,CAAC,CAAEE,EAAG,CAAE,GAC/CgK,EAAK4H,OAAO,EACZ,GAAI5H,EAAKE,QAAQ,CAAE,CACf,GAAI1G,AAAqB,OAArBA,EAAMkH,IAAI,CAAC1K,CAAC,CAACsE,GAAG,CAChB,MAAO,CAAC,EAEZ6P,EAAUxL,EAAI7I,CAAC,CAAGmB,EAClB0H,EAAI/I,CAAC,CAAG4D,EAAMkH,IAAI,CAAC1K,CAAC,CAACsE,GAAG,CAAC1E,CAAC,CAC1B+I,EAAI7I,CAAC,CAAG0D,EAAMkH,IAAI,CAAC1K,CAAC,CAACsE,GAAG,CAACxE,CAAC,CAC1BiU,EAAOvQ,EAAMkH,IAAI,CAAC1K,CAAC,CAACsE,GAAG,CAAC0G,IAAI,CAC5BqJ,EAAc,CAAC7Q,EAAMc,GAAG,CAACe,WAAW,AACxC,KACK,CACD,GAAI7B,AAAwB,OAAxBA,EAAMkH,IAAI,CAAC1K,CAAC,CAACwE,MAAM,CACnB,MAAO,CAAC,EAEZ2P,EAAUxL,EAAI7I,CAAC,CAAG+L,EAClBlD,EAAI/I,CAAC,CAAG4D,EAAMkH,IAAI,CAAC1K,CAAC,CAACwE,MAAM,CAAC5E,CAAC,CAC7B+I,EAAI7I,CAAC,CAAG0D,EAAMkH,IAAI,CAAC1K,CAAC,CAACwE,MAAM,CAAC1E,CAAC,CAC7BiU,EAAOvQ,EAAMkH,IAAI,CAAC1K,CAAC,CAACwE,MAAM,CAACwG,IAAI,CAC/BqJ,EAAc,CAAC7Q,EAAMgB,MAAM,CAACa,WAAW,AAC3C,OAEC,GAAI2E,EAAKC,KAAK,EACf,GAAID,EAAKE,QAAQ,CAAE,CACf,GAAI1G,AAAqB,OAArBA,EAAMkH,IAAI,CAAC9K,CAAC,CAAC0E,GAAG,CAChB,MAAO,CAAC,EAEZ6P,EAAUxL,EAAI7I,CAAC,CAAGmB,EAClB0H,EAAI7I,CAAC,CAAG0D,EAAMkH,IAAI,CAAC9K,CAAC,CAAC0E,GAAG,CAACxE,CAAC,CAC1B6I,EAAI3I,CAAC,CAAGwD,EAAMkH,IAAI,CAAC9K,CAAC,CAAC0E,GAAG,CAACtE,CAAC,CAC1B+T,EAAOvQ,EAAMkH,IAAI,CAAC9K,CAAC,CAAC0E,GAAG,CAAC0G,IAAI,CAC5BqJ,EAAc,CAAC7Q,EAAMc,GAAG,CAACe,WAAW,AACxC,KACK,CACD,GAAI7B,AAAwB,OAAxBA,EAAMkH,IAAI,CAAC9K,CAAC,CAAC4E,MAAM,CACnB,MAAO,CAAC,EAEZ2P,EAAUxL,EAAI7I,CAAC,CAAG+L,EAClBlD,EAAI7I,CAAC,CAAG0D,EAAMkH,IAAI,CAAC9K,CAAC,CAAC4E,MAAM,CAAC1E,CAAC,CAC7B6I,EAAI3I,CAAC,CAAGwD,EAAMkH,IAAI,CAAC9K,CAAC,CAAC4E,MAAM,CAACxE,CAAC,CAC7B+T,EAAOvQ,EAAMkH,IAAI,CAAC9K,CAAC,CAAC4E,MAAM,CAACwG,IAAI,CAC/BqJ,EAAc,CAAC7Q,EAAMgB,MAAM,CAACa,WAAW,AAC3C,OAGA,GAAI2E,EAAKE,QAAQ,CAAE,CACf,GAAI1G,AAAuB,OAAvBA,EAAMkH,IAAI,CAAC5K,CAAC,CAACsE,KAAK,CAClB,MAAO,CAAC,EAEZ8P,EAAUvL,EAAI/I,CAAC,CAAGgM,EAClBjD,EAAI/I,CAAC,CAAG4D,EAAMkH,IAAI,CAAC5K,CAAC,CAACsE,KAAK,CAACxE,CAAC,CAC5B+I,EAAI3I,CAAC,CAAGwD,EAAMkH,IAAI,CAAC5K,CAAC,CAACsE,KAAK,CAACpE,CAAC,CAG5B+T,EAAO,CAAEnU,EAAGmU,AAFZA,CAAAA,EAAOvQ,EAAMkH,IAAI,CAAC5K,CAAC,CAACsE,KAAK,CAAC4G,IAAI,AAAD,EAEZhL,CAAC,CAAEF,EAAGiU,EAAKjU,CAAC,CAAEE,EAAG,CAAC+T,EAAKnU,CAAC,AAAC,CAC9C,KACK,CACD,GAAI4D,AAAsB,OAAtBA,EAAMkH,IAAI,CAAC5K,CAAC,CAACkE,IAAI,CACjB,MAAO,CAAC,EAEZkQ,EAAUvL,EAAI/I,CAAC,CAAGoB,EAClB2H,EAAI/I,CAAC,CAAG4D,EAAMkH,IAAI,CAAC5K,CAAC,CAACkE,IAAI,CAACpE,CAAC,CAC3B+I,EAAI3I,CAAC,CAAGwD,EAAMkH,IAAI,CAAC5K,CAAC,CAACkE,IAAI,CAAChE,CAAC,CAC3B+T,EAAOvQ,EAAMkH,IAAI,CAAC5K,CAAC,CAACkE,IAAI,CAACgH,IAAI,AACjC,CAEJ,GAAIgJ,AAAiB,UAAjBA,QAIC,GAAIA,AAAiB,SAAjBA,GAEL,GAAKhK,EAAKC,KAAK,CAGV,CACD,IAAInJ,EAAMJ,KAAKI,GAAG,CAACP,GACfI,EAAMD,KAAKC,GAAG,CAACJ,EACfyJ,CAAAA,EAAKE,QAAQ,EACbpJ,CAAAA,EAAM,CAACA,CAAE,EAETuT,GACAvT,CAAAA,EAAM,CAACA,CAAE,EAEbsT,EAAO,CAAExU,EAAGmU,EAAK/T,CAAC,CAAGc,EAAKhB,EAAGa,EAAKX,EAAG,CAAC+T,EAAKnU,CAAC,CAAGkB,CAAI,CACvD,MAZIiT,EAAO,CAAEnU,EAAGc,KAAKC,GAAG,CAACL,GAAOR,EAAG,EAAGE,EAAGU,KAAKI,GAAG,CAACR,EAAM,OAcvD,GAAI0T,AAAiB,UAAjBA,GAEL,GAAKhK,EAAKC,KAAK,CAGV,CACD,IAAIqK,EAAO5T,KAAKI,GAAG,CAACP,GAChBgU,EAAO7T,KAAKC,GAAG,CAACJ,GAGhBiU,EAAO,CAAE5U,EAAG6U,AAFL/T,KAAKI,GAAG,CAACR,GAEGiU,EACnBzU,EAAG,CAACwU,EACJtU,EAAG,CAACuU,EAHG7T,KAAKC,GAAG,CAACL,EAGA,EAMhBF,EAAQ,EAAIM,KAAKyB,IAAI,CAACiS,AAL1BA,CAAAA,EAAO,CACHxU,EAAGmU,EAAKjU,CAAC,CAAG0U,EAAKxU,CAAC,CAAG+T,EAAK/T,CAAC,CAAGwU,EAAK1U,CAAC,CACpCA,EAAGiU,EAAK/T,CAAC,CAAGwU,EAAK5U,CAAC,CAAGmU,EAAKnU,CAAC,CAAG4U,EAAKxU,CAAC,CACpCA,EAAG+T,EAAKnU,CAAC,CAAG4U,EAAK1U,CAAC,CAAGiU,EAAKjU,CAAC,CAAG0U,EAAK5U,CAAC,AACxC,CAAA,EAC+BA,CAAC,CAAGwU,EAAKxU,CAAC,CAAGwU,EAAKtU,CAAC,CAAGsU,EAAKtU,CAAC,CAAGsU,EAAKpU,CAAC,CAAGoU,EAAKpU,CAAC,EACzEqU,GACAjU,CAAAA,EAAQ,CAACA,CAAI,EAEjBgU,EAAO,CACHxU,EAAGQ,EAAQgU,EAAKxU,CAAC,CAAEE,EAAGM,EAAQgU,EAAKtU,CAAC,CAAEE,EAAGI,EAAQgU,EAAKpU,CAAC,AAC3D,CACJ,MAtBI+T,EAAO,CAAEnU,EAAGc,KAAKC,GAAG,CAACL,GAAOR,EAAG,EAAGE,EAAGU,KAAKI,GAAG,CAACR,EAAM,OA2BnD0J,EAAKC,KAAK,CAIXmK,EAAO,CACHxU,EAAGc,KAAKI,GAAG,CAACR,GAAQI,KAAKI,GAAG,CAACP,GAC7BT,EAAGY,KAAKC,GAAG,CAACJ,GACZP,EAAG,CAACU,KAAKC,GAAG,CAACL,GAAQI,KAAKI,GAAG,CAACP,EAClC,EAPAwT,EAAO,CAAEnU,EAAGc,KAAKC,GAAG,CAACL,GAAOR,EAAG,EAAGE,EAAGU,KAAKI,GAAG,CAACR,EAAM,CAU5DqI,CAAAA,EAAI/I,CAAC,EAAIsU,EAAUH,EAAKnU,CAAC,CAAGuU,EAAUC,EAAKxU,CAAC,CAC5C+I,EAAI7I,CAAC,EAAIoU,EAAUH,EAAKjU,CAAC,CAAGqU,EAAUC,EAAKtU,CAAC,CAC5C6I,EAAI3I,CAAC,EAAIkU,EAAUH,EAAK/T,CAAC,CAAGmU,EAAUC,EAAKpU,CAAC,CAC5C,IAAI0U,EAAYzC,AA7+EP9S,EA6+EqC,CAACwJ,EAAI,CAC/CqB,EAAK3K,KAAK,CAAC,CAAC,EAAE,CAClB,GAAI4U,EAAM,CAWa,EATFU,AA9+EdhT,EA8+E0CsQ,AAj/ExC9S,EAi/EsE,CACnEwJ,EACA,CAAE/I,EAAG+I,EAAI/I,CAAC,CAAGmU,EAAKnU,CAAC,CACvBE,EAAG6I,EAAI7I,CAAC,CAAGiU,EAAKjU,CAAC,CACjBE,EAAG2I,EAAI3I,CAAC,CAAG+T,EAAK/T,CAAC,AAAC,EACd,CAAEJ,EAAG+I,EAAI/I,CAAC,CAAGwU,EAAKxU,CAAC,CACvBE,EAAG6I,EAAI7I,CAAC,CAAGsU,EAAKtU,CAAC,CACjBE,EAAG2I,EAAI3I,CAAC,CAAGoU,EAAKpU,CAAC,AAAC,EACjB,CACDgK,EAAK3K,KAAK,IAEV0U,CAAAA,EAAO,CAAEnU,EAAG,CAACmU,EAAKnU,CAAC,CAAEE,EAAG,CAACiU,EAAKjU,CAAC,CAAEE,EAAG,CAAC+T,EAAK/T,CAAC,AAAC,CAAA,EAEhD,IAAI4U,EAAkB3C,AA9/EjB9S,EA8/E+C,CAC5C,CAAES,EAAG+I,EAAI/I,CAAC,CACdE,EAAG6I,EAAI7I,CAAC,CACRE,EAAG2I,EAAI3I,CAAC,AAAC,EACL,CAAEJ,EAAG+I,EAAI/I,CAAC,CAAGmU,EAAKnU,CAAC,CACvBE,EAAG6I,EAAI7I,CAAC,CAAGiU,EAAKjU,CAAC,CACjBE,EAAG2I,EAAI3I,CAAC,CAAG+T,EAAK/T,CAAC,AAAC,EACd,CAAEJ,EAAG+I,EAAI/I,CAAC,CAAGwU,EAAKxU,CAAC,CACvBE,EAAG6I,EAAI7I,CAAC,CAAGsU,EAAKtU,CAAC,CACjBE,EAAG2I,EAAI3I,CAAC,CAAGoU,EAAKpU,CAAC,AAAC,EACjB,CACDgK,EAAK3K,KAAK,CACdqV,CAAAA,EAAUG,MAAM,CAAG,CACfD,CAAe,CAAC,EAAE,CAAChV,CAAC,CAAGgV,CAAe,CAAC,EAAE,CAAChV,CAAC,CAC3CgV,CAAe,CAAC,EAAE,CAAC9U,CAAC,CAAG8U,CAAe,CAAC,EAAE,CAAC9U,CAAC,CAC3C8U,CAAe,CAAC,EAAE,CAAChV,CAAC,CAAGgV,CAAe,CAAC,EAAE,CAAChV,CAAC,CAC3CgV,CAAe,CAAC,EAAE,CAAC9U,CAAC,CAAG8U,CAAe,CAAC,EAAE,CAAC9U,CAAC,CAC3C4U,EAAU9U,CAAC,CACX8U,EAAU5U,CAAC,CACd,CACD4U,EAAUG,MAAM,CAAC,EAAE,EAAIH,EAAU9U,CAAC,CAAG8U,EAAUG,MAAM,CAAC,EAAE,CACpDH,EAAU5U,CAAC,CAAG4U,EAAUG,MAAM,CAAC,EAAE,CACrCH,EAAUG,MAAM,CAAC,EAAE,EAAIH,EAAU9U,CAAC,CAAG8U,EAAUG,MAAM,CAAC,EAAE,CACpDH,EAAU5U,CAAC,CAAG4U,EAAUG,MAAM,CAAC,EAAE,AACzC,CACA,OAAOH,CACX,EAIA7D,EAAgBrS,SAAS,CAACwT,KAAK,CAAG,SAAU8C,CAAC,CAAExV,CAAc,EACzD,IAAI0K,EAAO,IAAI,CAACA,IAAI,CACpB,GAAIA,EAAK4H,OAAO,CAAE,CACd,IAAI5Q,EAAW1B,EAAiB,EAAI0K,EAAK3K,KAAK,CAAC2B,QAAQ,CACvD,MAAO,CACHpB,EAAGoB,EAAW8T,EAAE9U,CAAC,CACjBF,EAAGgV,EAAEhV,CAAC,CACNE,EAAG8U,EAAElV,CAAC,CAAGoB,CACb,CACJ,CACA,OAAO8T,CACX,EACOjE,CACX,IASIkE,GAA2IzX,EAAoB,KAC/J0X,GAA+J1X,EAAoBI,CAAC,CAACqX,IAErLE,GAAmG3X,EAAoB,KACvH4X,GAAuH5X,EAAoBI,CAAC,CAACuX,IAc7IE,IACIlY,EAAgB,SAAUY,CAAC,CAC3BuX,CAAC,EAMD,MAAOnY,AALHA,CAAAA,EAAgBiB,OAAOmX,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU1X,CAAC,CAC1DuX,CAAC,EAAIvX,EAAEyX,SAAS,CAAGF,CAAG,GACd,SAAUvX,CAAC,CACnBuX,CAAC,EAAI,IAAK,IAAIN,KAAKM,EAAOA,EAAE3W,cAAc,CAACqW,IAAIjX,CAAAA,CAAC,CAACiX,EAAE,CAAGM,CAAC,CAACN,EAAE,AAAD,CAAG,CAAA,EACvCjX,EAAGuX,EAC5B,EACO,SAAUvX,CAAC,CAAEuX,CAAC,EAEjB,SAASI,IAAO,IAAI,CAACC,WAAW,CAAG5X,CAAG,CADtCZ,EAAcY,EAAGuX,GAEjBvX,EAAEW,SAAS,CAAG4W,AAAM,OAANA,EAAalX,OAAOwX,MAAM,CAACN,GAAMI,CAAAA,EAAGhX,SAAS,CAAG4W,EAAE5W,SAAS,CAAE,IAAIgX,CAAG,CACtF,GAGAG,GAAoB,AAAC7W,IAA+E+N,QAAQ,CAK5G+I,GAAoB,AAAC9W,IAA+E8D,QAAQ,CAAEiT,GAAkB,AAAC/W,IAA+EmQ,MAAM,CAAE6G,GAAW,AAAChX,IAA+EgX,QAAQ,CAAEC,GAAiB,AAACjX,IAA+EgE,KAAK,CAAEkT,GAAgB,AAAClX,IAA+EI,IAAI,CAAE+W,GAAsB,AAACnX,IAA+EmO,UAAU,CAM3nBiJ,GAA0B,SAAUC,CAAM,EAE1C,SAASD,IACL,OAAOC,AAAW,OAAXA,GAAmBA,EAAO7O,KAAK,CAAC,IAAI,CAAEE,YAAc,IAAI,AACnE,CAmFA,OAtFA2N,GAAUe,EAAUC,GASpBD,EAASlO,OAAO,CAAG,SAAUoO,CAAW,EAChCH,GAAoBN,GAAmB,mBACvCC,GAAkBQ,EAAa,iBAAkB,WACzC,IAAI,CAAC/W,KAAK,CAAC8D,IAAI,IACf,IAAI,CAACkT,iBAAiB,EAE9B,GACAR,GAAgBO,EAAY5X,SAAS,CAAE,CACnC6X,kBAAmBH,EAAS1X,SAAS,CAAC6X,iBAAiB,AAC3D,GAER,EAUAH,EAAS1X,SAAS,CAAC6X,iBAAiB,CAAG,WACnC,IAUIC,EACAC,EAXAlQ,EAAS,IAAI,CACbmQ,EAAgBnQ,EAAO5G,OAAO,CAC9BJ,EAAQgH,EAAOhH,KAAK,CACpB0K,EAAQiM,GAAc3P,EAAO0D,KAAK,CAClC1K,EAAMI,OAAO,CAACsK,KAAK,CAAC,EAAE,EACtB0M,EAAY,EAAE,CACd9I,EAAa,EAAE,CACf+I,EAAQF,EAAc5I,QAAQ,CACzBkI,GAASU,EAAcE,KAAK,EAAIF,EAAcE,KAAK,CAAG,EACvDrQ,EAAO2M,KAAK,EAAI,CAGxB3M,CAAAA,EAAOyH,QAAQ,CAAG4I,EACbF,CAAAA,EAAcvW,KAAK,EAAI,EAAKuW,CAAAA,EAAcG,aAAa,EAAI,CAAA,CAAC,EACjEtQ,EAAOuQ,IAAI,CAAC9Q,OAAO,CAAC,SAAU+Q,CAAQ,EAC9B9M,CAAAA,MAAAA,EAAqC,KAAK,EAAIA,EAAMrC,SAAS,AAAD,GAC5D6O,EAASxM,EAAM+M,WAAW,EAAI/M,EAAMgN,OAAO,CACvChN,EAAMgN,OAAO,CAACF,EAAS7W,CAAC,EACxB6W,EAAS7W,CAAC,CACd6W,EAAStU,KAAK,CAAGwH,EAAMrC,SAAS,CAAC6O,GACjCM,EAASG,QAAQ,CAAGH,EAAAA,EAASG,QAAQ,EAChCT,GAAUxM,EAAMwC,GAAG,EAChBgK,GAAUxM,EAAMyC,GAAG,EAI3BqK,EAAStU,KAAK,CAAG8D,EAAOyH,QAAQ,CAEpC+I,EAASpG,QAAQ,CAAGoG,EAASxU,KAAK,CAClCwU,EAASlG,QAAQ,CAAGkG,EAASvU,KAAK,CAClCuU,EAASI,QAAQ,CAAGJ,EAAStU,KAAK,CAClCkU,EAAUlO,IAAI,CAAC,CACX3I,EAAGiX,EAASxU,KAAK,CACjBvC,EAAG+W,EAASvU,KAAK,CACjBtC,EAAG6W,EAAStU,KAAK,AACrB,GACAoL,EAAWpF,IAAI,CAACsO,EAASxU,KAAK,EAAI,EACtC,GACAgE,EAAOsH,UAAU,CAAGA,EACpB,IAAIuJ,EAAkBC,AAvqFbhY,EAuqFkCsX,EACvCpX,EACA,CAAA,GACJgH,EAAOuQ,IAAI,CAAC9Q,OAAO,CAAC,SAAU+Q,CAAQ,CAAEhV,CAAC,EAErCgV,EAASxU,KAAK,CAAGiU,AADjBA,CAAAA,EAAiBY,CAAe,CAACrV,EAAE,AAAD,EACFjC,CAAC,CACjCiX,EAASvU,KAAK,CAAGgU,EAAexW,CAAC,CACjC+W,EAAStU,KAAK,CAAG+T,EAAetW,CAAC,AACrC,EACJ,EAMAkW,EAASvT,cAAc,CAAGoT,GAAe,AAACb,KAA2GvS,cAAc,EAC5JuT,CACX,EAAGhB,MASCkC,GAA+G9Z,EAAoB,KACnI+Z,GAAmI/Z,EAAoBI,CAAC,CAAC0Z,IAczJE,IACIra,EAAgB,SAAUY,CAAC,CAC3BuX,CAAC,EAOD,MAAOnY,AANHA,CAAAA,EAAgBiB,OAAOmX,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU1X,CAAC,CAC1DuX,CAAC,EAAIvX,EAAEyX,SAAS,CAAGF,CAAG,GACd,SAAUvX,CAAC,CACnBuX,CAAC,EAAI,IAAK,IAAIN,KAAKM,EAAOlX,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC0W,EAC/DN,IAAIjX,CAAAA,CAAC,CAACiX,EAAE,CAAGM,CAAC,CAACN,EAAE,AAAD,CAAG,CAAA,EACIjX,EAAGuX,EAC5B,EACO,SAAUvX,CAAC,CAAEuX,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAImC,UAAU,uBAAyBC,OAAOpC,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAG5X,CAAG,CADtCZ,EAAcY,EAAGuX,GAEjBvX,EAAEW,SAAS,CAAG4W,AAAM,OAANA,EAAalX,OAAOwX,MAAM,CAACN,GAAMI,CAAAA,EAAGhX,SAAS,CAAG4W,EAAE5W,SAAS,CAAE,IAAIgX,CAAG,CACtF,GAGAiC,GAAqB,AAACzY,IAAuGyD,KAAK,CAElIiV,GAAa1C,KAAkJ2C,eAAe,GAAGnZ,SAAS,CAACoZ,OAAO,CAElMC,GAAU,AAAC/Y,IAA+E+Y,OAAO,CAAEC,GAAoB,AAAChZ,IAA+EI,IAAI,CAM3M6Y,GAA8B,SAAU5B,CAAM,EAE9C,SAAS4B,IAML,IAAIC,EAAQ7B,AAAW,OAAXA,GAAmBA,EAAO7O,KAAK,CAAC,IAAI,CAC5CE,YAAc,IAAI,CAQtB,OAFAwQ,EAAMC,KAAK,CAAG,CAAC,QAAS,MAAO,OAAO,CACtCD,EAAME,QAAQ,CAAG,SACVF,CACX,CA4JA,OA7KAV,GAAqBS,EAAc5B,GA2BnC4B,EAAavZ,SAAS,CAAC2Z,QAAQ,CAAG,SAAUnH,CAAI,EAM5C,IAAK,IAJDzN,EAAW6U,AADF,IAAI,CACK7U,QAAQ,CAC1B8U,EAAQ9U,CAAQ,CAAC6U,AAFR,IAAI,CAEWF,QAAQ,CAAG,OAAO,CAAClH,GAC3CsH,EAAWD,EAAMC,QAAQ,CAEpBC,EAAK,EAAGlQ,EAAK+P,AALT,IAAI,CAKYH,KAAK,CAAEM,EAAKlQ,EAAGrG,MAAM,CAAEuW,IAAM,CACtD,IAAIC,EAAOnQ,CAAE,CAACkQ,EAAG,CACbE,EAAU,CACN,MAAS,iBAAmBD,EAC5BpT,OAAQkT,CAAQ,CAACE,EAAK,EAAI,CAC9B,CACAjV,CAAAA,EAASoC,UAAU,GACf6S,AAAS,QAATA,EACAC,EAAQC,MAAM,CAAG,4BAEH,SAATF,GACLC,CAAAA,EAAQC,MAAM,CAAG,yBAAwB,GAGjDN,AAnBS,IAAI,AAmBP,CAACI,EAAK,CAAGjV,EAASiM,IAAI,CAAC6I,CAAK,CAACG,EAAK,EACnCzP,IAAI,CAAC0P,GACLtT,GAAG,CArBC,IAAI,CAsBjB,CACAiT,AAvBa,IAAI,CAuBVrP,IAAI,CAAC,CACR,kBAAmB,QACnB3D,OAAQkT,EAAStK,KAAK,AAC1B,GAEAoK,AA5Ba,IAAI,CA4BVO,WAAW,CAAGN,EAAMM,WAAW,AAC1C,EAKAZ,EAAavZ,SAAS,CAACoa,oBAAoB,CAAG,SAAUra,CAAI,CAAEgM,CAAG,CAAEsO,CAAM,CAAE/T,CAAI,CAAEgU,CAAQ,CAAEC,CAAQ,EAC/F,IACIC,EAAU,CAAC,EACXC,EAAiB,CAAC,KAClB,KAAOnU,GAAQ,OACfgU,EACAC,EAAS,CACTG,EAAcL,MAAAA,EAAuC,KAAK,EAAIA,EAAOP,QAAQ,CACjF,GAAKO,EAIA,CAGGK,CAAAA,MAAAA,EAAiD,KAAK,EAAIA,EAAYlL,KAAK,AAAD,GAC1EoK,AAfK,IAAI,CAeFrP,IAAI,CAAC,CACR3D,OAAQ8T,EAAYlL,KAAK,AAC7B,GAEJ,IAAK,IAAIuK,EAAK,EAAGlQ,EAAKnK,OAAOib,IAAI,CAACN,GAASN,EAAKlQ,EAAGrG,MAAM,CAAEuW,IAAM,CAC7D,IAAIC,EAAOnQ,CAAE,CAACkQ,EAAG,AACjBS,CAAAA,CAAO,CAACR,EAAK,CAAG,CAAC,EACjBQ,CAAO,CAACR,EAAK,CAACja,EAAK,CAAGsa,CAAM,CAACL,EAAK,CAE9BU,GACAF,CAAAA,CAAO,CAACR,EAAK,CAACpT,MAAM,CAAGyT,EAAOP,QAAQ,CAACE,EAAK,EAAI,CAAA,CAExD,CACAS,CAAc,CAAC,EAAE,CAAGD,CACxB,MArBIA,CAAO,CAACza,EAAK,CAAGgM,EAChB0O,CAAc,CAAC,EAAE,CAAGD,EAqBxB,OAAO,IAAI,CAACI,YAAY,CAAC9R,KAAK,CA9BjB,IAAI,CA8BsB2R,EAC3C,EAKAlB,EAAavZ,SAAS,CAAC4a,YAAY,CAAG,SAAUC,CAAK,CAAEC,CAAU,CAAExU,CAAI,CAAEgU,CAAQ,CAAEC,CAAQ,EAEvF,IAAK,IAAIR,EAAK,EAAGlQ,EAAK+P,AADT,IAAI,CACYH,KAAK,CAAEM,EAAKlQ,EAAGrG,MAAM,CAAEuW,IAAM,CACtD,IAAIC,EAAOnQ,CAAE,CAACkQ,EAAG,CAEbe,GACAD,CAAAA,EAAQvB,GAAkBwB,CAAU,CAACd,EAAK,CAAE,CAAA,EAAK,EAGvC,CAAA,IAAVa,GACAjB,AATK,IAAI,AASH,CAACI,EAAK,CAAC1T,EAAK,CAACuU,EAAOP,EAAUC,EAE5C,CACA,OAZa,IAAI,AAarB,EAKAhB,EAAavZ,SAAS,CAAC+a,OAAO,CAAG,WAE7B,OADA,IAAI,CAACH,YAAY,CAAC,KAAM,KAAM,WACvBjD,EAAO3X,SAAS,CAAC+a,OAAO,CAAC7a,IAAI,CAAC,IAAI,CAC7C,EAEAqZ,EAAavZ,SAAS,CAACuK,IAAI,CAAG,SAAUiI,CAAI,CAAEzG,CAAG,CAAEwO,CAAQ,CAAES,CAAiB,EAE1E,GAAI,AAAgB,UAAhB,OAAOxI,GAAqB,AAAe,KAAA,IAARzG,EAAqB,CACxD,IAAIvM,EAAMgT,CAEVA,CADAA,CAAAA,EAAO,CAAC,CAAA,CACJ,CAAChT,EAAI,CAAGuM,CAChB,QACA,AAAIyG,EAAKyI,SAAS,EAAI5B,GAAQ7G,EAAKpR,CAAC,EACzB,IAAI,CAACgZ,oBAAoB,CAAC,IAAK,KAAM,IAAI,CAACrV,QAAQ,CAAC,IAAI,CAAC2U,QAAQ,CAAG,OAAO,CAAClH,EAAKyI,SAAS,EAAIzI,IAEjGmF,EAAO3X,SAAS,CAACuK,IAAI,CAACrK,IAAI,CAAC,IAAI,CAAEsS,EAAM,KAAK,EAAG+H,EAAUS,EACpE,EACAzB,EAAavZ,SAAS,CAACkb,OAAO,CAAG,SAAU1I,CAAI,CAAE8H,CAAQ,CAAEC,CAAQ,EAC/D,GAAIlB,GAAQ7G,EAAKpR,CAAC,GAAKiY,GAAQ7G,EAAKlR,CAAC,EAAG,CACpC,IAAIuY,EAAQ,IAAI,CAAC9U,QAAQ,CAAC,IAAI,CAAC2U,QAAQ,CAAG,OAAO,CAAClH,GAC9C2H,EAAcN,EAAMM,WAAW,CACnC,IAAI,CAACC,oBAAoB,CAAC,IAAK,KAAMP,EAAO,UAAWS,EAAUC,GACjE,IAAI,CAAChQ,IAAI,CAAC,CACN3D,OAAQiT,EAAMC,QAAQ,CAACtK,KAAK,AAChC,GAEI2K,IAAgB,IAAI,CAACA,WAAW,GAChC,IAAI,CAACA,WAAW,CAAGA,EACd,IAAI,CAACpV,QAAQ,CAACoC,UAAU,EACzB,IAAI,CAACgU,UAAU,CAAC,IAAI,CAACpU,IAAI,EAGrC,MAEI4Q,EAAO3X,SAAS,CAACkb,OAAO,CAAChb,IAAI,CAAC,IAAI,CAAEsS,EAAM8H,EAAUC,GAExD,OAAO,IAAI,AACf,EACAhB,EAAavZ,SAAS,CAACmb,UAAU,CAAG,SAAUpU,CAAI,EAW9C,OATA6S,AADa,IAAI,CACVO,WAAW,CAAGP,AADR,IAAI,CACWO,WAAW,EAAI,EAAE,CAC7CP,AAFa,IAAI,CAEVQ,oBAAoB,CAAC,OAAQ,KAAM,CACtCjU,MAAOY,EAEPjB,IAAKmT,GAAmBlS,GAAMC,QAAQ,CAAC4S,AAL9B,IAAI,CAKiCO,WAAW,CAAC1F,OAAO,CAAC,QAAU,EAAI,EAAI,IAAK5U,GAAG,GAC5FsM,KAAM8M,GAAmBlS,GAAMC,QAAQ,CAAC4S,AAN/B,IAAI,CAMkCO,WAAW,CAAC1F,OAAO,CAAC,SAAW,EAAI,EAAI,KAAM5U,GAAG,EACnG,GAEA+Z,AATa,IAAI,CASV5V,KAAK,CAAG4V,AATF,IAAI,CASK7S,IAAI,CAAGA,EAThB,IAAI,AAWrB,EACAwS,EAAa6B,KAAK,CAAG,CACjBC,KAAM9B,EACN+B,OAAQ/B,CACZ,EACOA,CACX,EAAEL,IAsBEqC,GAAa,AAACjb,IAA+Eib,UAAU,CAEvGC,GAAsB,AAAChb,IAAuGyD,KAAK,CAEnIwX,GAAS,AAACnb,IAA+Emb,MAAM,CAAEC,GAAwB,AAACpb,IAA+EG,OAAO,CAKhNkb,GAAwB,AAACrb,IAA+E+Y,OAAO,CAAEuC,GAAuB,AAACtb,IAA+EmQ,MAAM,CAAEoL,GAAsB,AAACvb,IAA+EgE,KAAK,CAAEwX,GAAqB,AAACxb,IAA+EI,IAAI,CAMtbyB,GAAMD,KAAKC,GAAG,CAAEG,GAAMJ,KAAKI,GAAG,CAAEyZ,GAAK7Z,KAAK6Z,EAAE,CAAEC,GAAU,AAAC,EAAK9Z,CAAAA,KAAKyB,IAAI,CAAC,GAAK,CAAA,EAAK,EAAMoY,CAAAA,GAAK,CAAA,EAWjG,SAASE,GAAQC,CAAE,CAAEC,CAAE,CAAEC,CAAE,CAAEC,CAAE,CAAEjS,CAAK,CAAEC,CAAG,CAAEiS,CAAE,CAAEC,CAAE,EAC/C,IAAIC,EAAWnS,EAAMD,EACjBqS,EAAS,EAAE,QACf,AAAI,AAACpS,EAAMD,GAAWC,EAAMD,EAAQlI,KAAK6Z,EAAE,CAAG,EAAI,KAE9CU,EAASA,AADTA,CAAAA,EAASA,EAAOrR,MAAM,CAAC6Q,GAAQC,EAAIC,EAAIC,EAAIC,EAAIjS,EAAOA,EAASlI,KAAK6Z,EAAE,CAAG,EAAIO,EAAIC,GAAG,EACpEnR,MAAM,CAAC6Q,GAAQC,EAAIC,EAAIC,EAAIC,EAAIjS,EAASlI,KAAK6Z,EAAE,CAAG,EAAI1R,EAAKiS,EAAIC,IAG/E,AAAClS,EAAMD,GAAWA,EAAQC,EAAMnI,KAAK6Z,EAAE,CAAG,EAAI,KAE9CU,EAASA,AADTA,CAAAA,EAASA,EAAOrR,MAAM,CAAC6Q,GAAQC,EAAIC,EAAIC,EAAIC,EAAIjS,EAAOA,EAASlI,KAAK6Z,EAAE,CAAG,EAAIO,EAAIC,GAAG,EACpEnR,MAAM,CAAC6Q,GAAQC,EAAIC,EAAIC,EAAIC,EAAIjS,EAASlI,KAAK6Z,EAAE,CAAG,EAAI1R,EAAKiS,EAAIC,IAG5E,CAAC,CACA,IACAL,EAAME,EAAKla,KAAKC,GAAG,CAACiI,GACf,AAACgS,EAAKJ,GAAUQ,EAAYta,KAAKI,GAAG,CAAC8H,GAAUkS,EACpDH,EAAME,EAAKna,KAAKI,GAAG,CAAC8H,GACf,AAACiS,EAAKL,GAAUQ,EAAYta,KAAKC,GAAG,CAACiI,GAAUmS,EACpDL,EAAME,EAAKla,KAAKC,GAAG,CAACkI,GACf,AAAC+R,EAAKJ,GAAUQ,EAAYta,KAAKI,GAAG,CAAC+H,GAAQiS,EAClDH,EAAME,EAAKna,KAAKI,GAAG,CAAC+H,GACf,AAACgS,EAAKL,GAAUQ,EAAYta,KAAKC,GAAG,CAACkI,GAAQkS,EAClDL,EAAME,EAAKla,KAAKC,GAAG,CAACkI,GAAQiS,EAC5BH,EAAME,EAAKna,KAAKI,GAAG,CAAC+H,GAAQkS,EAC/B,CAAC,AACV,EAOA,AAAC,SAAU5d,CAAa,EAoCpB,SAAS+d,EAAW9b,CAAM,CAAE+b,CAAM,EAG9B,IAAK,IAFDF,EAAS,EAAE,CAEN1C,EAAK,EAAsBA,EAAK6C,AAAbhc,EAAsB4C,MAAM,CAAEuW,IAAM,CAC5D,IAAIpX,EAAQia,AADYhc,CACJ,CAACmZ,EAAG,CACxB0C,EAAO1S,IAAI,CAAC,CAAC,IAAKpH,EAAMvB,CAAC,CAAEuB,EAAMrB,CAAC,CAAC,CACvC,CASA,OARIV,EAAO4C,MAAM,GAEbiZ,CAAM,CAAC,EAAE,CAAC,EAAE,CAAG,IAEXE,GACAF,EAAO1S,IAAI,CAAC,CAAC,IAAI,GAGlB0S,CACX,CAEA,SAASvL,EAAetQ,CAAM,EAG1B,IAAK,IAFD6b,EAAS,EAAE,CACXI,EAAI,CAAA,EACC9C,EAAK,EAAsBA,EAAK+C,AAAblc,EAAsB4C,MAAM,CAAEuW,IAAM,CAC5D,IAAIpX,EAAQma,AADYlc,CACJ,CAACmZ,EAAG,CACxB0C,EAAO1S,IAAI,CAAC8S,EAAI,CAAC,IAAKla,EAAMvB,CAAC,CAAEuB,EAAMrB,CAAC,CAAC,CAAG,CAAC,IAAKqB,EAAMvB,CAAC,CAAEuB,EAAMrB,CAAC,CAAC,EACjEub,EAAI,CAACA,CACT,CACA,OAAOJ,CACX,CAOA,SAASM,EAAOvK,CAAI,EAChB,IAAIzN,EAAW,IAAI,CACfiY,EAAejY,EAASqU,OAAO,CAACpZ,SAAS,CACzCiM,EAAMlH,EAASkY,aAAa,CAAC,QAuDjC,OAtDAhR,EAAI7I,QAAQ,CAAG,EAAE,CACjB6I,EAAInL,cAAc,CAAG,CAAA,EACrBmL,EAAIhF,OAAO,CAAG,CAAA,EAEdgF,EAAI1B,IAAI,CAAG,SAAU2S,CAAI,EACrB,GAAI,AAAgB,UAAhB,OAAOA,GACNvB,CAAAA,GAAsBuB,EAAKjW,OAAO,GAC/B0U,GAAsBuB,EAAK9Z,QAAQ,GACnCuY,GAAsBuB,EAAKpc,cAAc,CAAA,EAAI,CACjD,IAAI,CAACmG,OAAO,CAAG6U,GAAmBoB,EAAKjW,OAAO,CAAE,IAAI,CAACA,OAAO,EAC5D,IAAI,CAAC7D,QAAQ,CAAG0Y,GAAmBoB,EAAK9Z,QAAQ,CAAE,IAAI,CAACA,QAAQ,EAC/D,IAAI,CAACtC,cAAc,CAAGgb,GAAmBoB,EAAKpc,cAAc,CAAE,IAAI,CAACA,cAAc,EACjF,OAAOoc,EAAKjW,OAAO,CACnB,OAAOiW,EAAK9Z,QAAQ,CACpB,OAAO8Z,EAAKpc,cAAc,CAC1B,IAAID,EAAQ4a,EAAM,CAAC1W,EAASoY,UAAU,CAAC,CACnCC,EAAaC,AAxkGhB1c,EAwkG0C,IAAI,CAACyC,QAAQ,CACpDvC,EACA,IAAI,CAACC,cAAc,EACnBkQ,EAAOjM,EAAS2X,UAAU,CAACU,EAC3B,CAAA,GACA7Z,EAAO+Z,AA1kGZna,EA0kGoCia,EACnCF,CAAAA,EAAK7d,CAAC,CAAG2R,EACTkM,EAAKK,UAAU,CAAG,AAAC,IAAI,CAACtW,OAAO,EAAI1D,EAAO,EACtC,UAAY,QACpB,CACA,OAAOyZ,EAAazS,IAAI,CAACzB,KAAK,CAAC,IAAI,CAAEE,UACzC,EACAiD,EAAIiP,OAAO,CAAG,SAAUsC,CAAM,EAC1B,GAAI,AAAkB,UAAlB,OAAOA,GACN7B,CAAAA,GAAsB6B,EAAOvW,OAAO,GACjC0U,GAAsB6B,EAAOpa,QAAQ,GACrCuY,GAAsB6B,EAAO1c,cAAc,CAAA,EAAI,CACnD,IAAI,CAACmG,OAAO,CAAG6U,GAAmB0B,EAAOvW,OAAO,CAAE,IAAI,CAACA,OAAO,EAC9D,IAAI,CAAC7D,QAAQ,CAAG0Y,GAAmB0B,EAAOpa,QAAQ,CAAE,IAAI,CAACA,QAAQ,EACjE,IAAI,CAACtC,cAAc,CAAGgb,GAAmB0B,EAAO1c,cAAc,CAAE,IAAI,CAACA,cAAc,EACnF,OAAO0c,EAAOvW,OAAO,CACrB,OAAOuW,EAAOpa,QAAQ,CACtB,OAAOoa,EAAO1c,cAAc,CAC5B,IAAID,EAAQ4a,EAAM,CAAC1W,EAASoY,UAAU,CAAC,CACnCC,EAAaC,AAhmGhB1c,EAgmG0C,IAAI,CAACyC,QAAQ,CACpDvC,EACA,IAAI,CAACC,cAAc,EACnBkQ,EAAOjM,EAAS2X,UAAU,CAACU,EAC3B,CAAA,GACA7Z,EAAO+Z,AAlmGZna,EAkmGoCia,GAC/BG,EAAa,AAAC,IAAI,CAACtW,OAAO,EAAI1D,EAAO,EACjC,UAAY,QACpBia,CAAAA,EAAOne,CAAC,CAAG2R,EACX,IAAI,CAACzG,IAAI,CAAC,aAAcgT,EAC5B,CACA,OAAOP,EAAa9B,OAAO,CAACpS,KAAK,CAAC,IAAI,CAAEE,UAC5C,EAEOiD,EAAI1B,IAAI,CAACiI,EACpB,CAOA,SAAS9L,EAAW8L,CAAI,EACpB,IAAIzN,EAAW,IAAI,CACfiY,EAAejY,EAASqU,OAAO,CAACpZ,SAAS,CACzCyc,EAAS1X,EAAS0Y,CAAC,GACnB1C,EAAU0B,EAAO1B,OAAO,CA+C5B,OA9CK,IAAI,CAAC5T,UAAU,EAChBsV,EAAOlS,IAAI,CAAC,CACR,kBAAmB,OACvB,GAEJkS,EAAO3V,KAAK,CAAG,EAAE,CAEjB2V,EAAO1B,OAAO,CAAG,WACb,IAAK,IAAI1X,EAAI,EAAGA,EAAIoZ,EAAO3V,KAAK,CAACtD,MAAM,CAAEH,IACrCoZ,EAAO3V,KAAK,CAACzD,EAAE,CAAC0X,OAAO,GAE3B,OAAOA,EAAQ7a,IAAI,CAAC,IAAI,CAC5B,EACAuc,EAAOlS,IAAI,CAAG,SAAU2S,CAAI,CAAEnR,CAAG,CAAEwO,CAAQ,CAAES,CAAiB,EAC1D,GAAI,AAAgB,UAAhB,OAAOkC,GAAqBvB,GAAsBuB,EAAKpW,KAAK,EAAG,CAC/D,KAAO2V,EAAO3V,KAAK,CAACtD,MAAM,CAAG0Z,EAAKpW,KAAK,CAACtD,MAAM,EAC1CiZ,EAAO3V,KAAK,CAAC4W,GAAG,GAAG3C,OAAO,GAE9B,KAAO0B,EAAO3V,KAAK,CAACtD,MAAM,CAAG0Z,EAAKpW,KAAK,CAACtD,MAAM,EAC1CiZ,EAAO3V,KAAK,CAACiD,IAAI,CAAChF,EAASgY,MAAM,GAAGpW,GAAG,CAAC8V,IAE5C,IAAK,IAAIpZ,EAAI,EAAGA,EAAI6Z,EAAKpW,KAAK,CAACtD,MAAM,CAAEH,IAC/B0B,EAASoC,UAAU,EACnB,OAAO+V,EAAKpW,KAAK,CAACzD,EAAE,CAAC0D,IAAI,CAE7B0V,EAAO3V,KAAK,CAACzD,EAAE,CAACkH,IAAI,CAAC2S,EAAKpW,KAAK,CAACzD,EAAE,CAAE,KAAMkX,EAAUS,EAExD,QAAOkC,EAAKpW,KAAK,AACrB,CACA,OAAOkW,EAAazS,IAAI,CAACzB,KAAK,CAAC,IAAI,CAAEE,UACzC,EACAyT,EAAOvB,OAAO,CAAG,SAAUsC,CAAM,CAAElD,CAAQ,CAAEC,CAAQ,EACjD,GAAIiD,MAAAA,EAAuC,KAAK,EAAIA,EAAO1W,KAAK,CAAE,CAC9D,KAAO2V,EAAO3V,KAAK,CAACtD,MAAM,CAAGga,EAAO1W,KAAK,CAACtD,MAAM,EAC5CiZ,EAAO3V,KAAK,CAAC4W,GAAG,GAAG3C,OAAO,GAE9B,KAAO0B,EAAO3V,KAAK,CAACtD,MAAM,CAAGga,EAAO1W,KAAK,CAACtD,MAAM,EAC5CiZ,EAAO3V,KAAK,CAACiD,IAAI,CAAChF,EAASgY,MAAM,GAAGpW,GAAG,CAAC8V,IAE5C,IAAK,IAAIpZ,EAAI,EAAGA,EAAIma,EAAO1W,KAAK,CAACtD,MAAM,CAAEH,IACrCoZ,EAAO3V,KAAK,CAACzD,EAAE,CAAC6X,OAAO,CAACsC,EAAO1W,KAAK,CAACzD,EAAE,CAAEiX,EAAUC,EAEvD,QAAOiD,EAAO1W,KAAK,AACvB,CACA,OAAOkW,EAAa9B,OAAO,CAACpS,KAAK,CAAC,IAAI,CAAEE,UAC5C,EACOyT,EAAOlS,IAAI,CAACiI,EACvB,CAMA,SAASmL,EAAU/Y,CAAI,CAAEqW,CAAS,EAC9B,IAAIrB,EAAS,IAAIgE,AA9Q4BrE,GA8QX6B,KAAK,CAACxW,EAAK,CAAC,IAAI,CAAE,KAEpD,OADAgV,EAAOD,QAAQ,CAACsB,GACTrB,CACX,CAKA,SAAS0B,EAAOL,CAAS,EACrB,OAAO,IAAI,CAAC0C,SAAS,CAAC,SAAU1C,EACpC,CAKA,SAAS4C,EAAW5C,CAAS,EACzB,IAWI6C,EAXA1c,EAAI6Z,EAAU7Z,CAAC,EAAI,EAAGE,EAAI2Z,EAAU3Z,CAAC,EAAI,EAAGE,EAAIyZ,EAAUzZ,CAAC,EAAI,EAI/Duc,EAAI9C,EAAU+C,MAAM,EAAI,EAAGC,EAAIhD,EAAUiD,KAAK,EAAI,EAAG7e,EAAI4b,EAAUxZ,KAAK,EAAI,EAAGZ,EAAQ4a,EAAM,CAAC,IAAI,CAAC0B,UAAU,CAAC,CAA6Cpb,EAAQf,AAAvCH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAAoBe,KAAK,CAM/IoY,EAAc,EAAE,CAEnDvT,EAAS,EAETqK,EAAO,CAAC,CACA7P,EAAGA,EACHE,EAAGA,EACHE,EAAGA,CACP,EAAG,CACCJ,EAAGA,EAAI6c,EACP3c,EAAGA,EACHE,EAAGA,CACP,EAAG,CACCJ,EAAGA,EAAI6c,EACP3c,EAAGA,EAAIyc,EACPvc,EAAGA,CACP,EAAG,CACCJ,EAAGA,EACHE,EAAGA,EAAIyc,EACPvc,EAAGA,CACP,EAAG,CACCJ,EAAGA,EACHE,EAAGA,EAAIyc,EACPvc,EAAGA,EAAInC,CACX,EAAG,CACC+B,EAAGA,EAAI6c,EACP3c,EAAGA,EAAIyc,EACPvc,EAAGA,EAAInC,CACX,EAAG,CACC+B,EAAGA,EAAI6c,EACP3c,EAAGA,EACHE,EAAGA,EAAInC,CACX,EAAG,CACC+B,EAAGA,EACHE,EAAGA,EACHE,EAAGA,EAAInC,CACX,EAAE,CAEV4R,EAAOoM,AAjvGE1c,EAivGwBsQ,EAAMpQ,EAAOoa,EAAUna,cAAc,EAKtE,IAAIqd,EAAc,SAAU9a,CAAC,SAIrB,AAAI0a,AAAM,IAANA,GAAW1a,EAAI,GAAKA,EAAI,EACjB,CACHjC,EAAG6P,CAAI,CAAC5N,EAAE,CAACjC,CAAC,CAIZE,EAAG2P,CAAI,CAAC5N,EAAE,CAAC/B,CAAC,CAAG,GACfE,EAAGyP,CAAI,CAAC5N,EAAE,CAAC7B,CAAC,AAChB,EAMJyP,CAAI,CAAC,EAAE,CAAC7P,CAAC,GAAK6P,CAAI,CAAC,EAAE,CAAC7P,CAAC,EAAIiC,GAAK,EACzB,CACHjC,EAAG6P,CAAI,CAAC5N,EAAE,CAACjC,CAAC,CAAG,GAIfE,EAAG2P,CAAI,CAAC5N,EAAE,CAAC/B,CAAC,CACZE,EAAGyP,CAAI,CAAC5N,EAAE,CAAC7B,CAAC,AAChB,EAGAnC,AAAM,IAANA,GAAWgE,EAAI,GAAKA,EAAI,EACjB,CACHjC,EAAG6P,CAAI,CAAC5N,EAAE,CAACjC,CAAC,CAIZE,EAAG2P,CAAI,CAAC5N,EAAE,CAAC/B,CAAC,CACZE,EAAGyP,CAAI,CAAC5N,EAAE,CAAC7B,CAAC,CAAG,EACnB,EAEGyP,CAAI,CAAC5N,EAAE,AAClB,EAKA+a,EAAU,SAAU/a,CAAC,EAAI,OAAQ4N,CAAI,CAAC5N,EAAE,AAAG,EAU3Cgb,EAAY,SAAUC,CAAc,CAAEC,CAAc,CAAEpS,CAAI,EACtD,IACIE,EAAQiS,EAAe5b,GAAG,CAAC0b,GAC3B9R,EAAQiS,EAAe7b,GAAG,CAAC0b,GAK3BI,EAAaF,EAAe5b,GAAG,CAACyb,GAChCM,EAAaF,EAAe7b,GAAG,CAACyb,GAChClS,EAAM,CAAC,EAAE,CAAE,GAAG,CAmBlB,OAlBIqR,AAAiC,EAAjCA,AArzGDna,EAqzGyBkJ,GACxBJ,EAAM,CAACI,EAAO,EAAE,CAEXiR,AAAiC,EAAjCA,AAxzGNna,EAwzG8BmJ,GAC7BL,EAAM,CAACK,EAAO,EAAE,CAEXH,IACLgO,EAAYpQ,IAAI,CAACoC,GAEbF,EADAqR,AAAsC,EAAtCA,AA7zGLna,EA6zG6Bqb,GAClB,CAACnS,EAAO,EAAE,CAEXiR,AAAsC,EAAtCA,AAh0GVna,EAg0GkCsb,GACvB,CAACnS,EAAO,EAAE,CAGV,CAACD,EAAO,EAAE,EAGjBJ,CACX,EAKIyS,EAAQZ,AADZA,CAAAA,EAAQO,EAFI,CAAC,EAAG,EAAG,EAAG,EAAE,CACb,CAAC,EAAG,EAAG,EAAG,EAAE,CACQ,QAAO,CACrB,CAAC,EAAE,CAChBM,EAAUb,CAAK,CAAC,EAAE,CAKlBc,EAAQd,AADZA,CAAAA,EAAQO,EAFE,CAAC,EAAG,EAAG,EAAG,EAAE,CACT,CAAC,EAAG,EAAG,EAAG,EAAE,CACM,MAAK,CACnB,CAAC,EAAE,CAChBQ,EAAQf,CAAK,CAAC,EAAE,CAKhBgB,EAAQhB,AADZA,CAAAA,EAAQO,EAFI,CAAC,EAAG,EAAG,EAAG,EAAE,CACb,CAAC,EAAG,EAAG,EAAG,EAAE,CACQ,OAAM,CACpB,CAAC,EAAE,CAChBU,EAAUjB,CAAK,CAAC,EAAE,CA2BtB,OAlBIiB,AAAY,IAAZA,EAGAnY,GAAUoY,AAjKG,IAiKWne,CAAAA,EAAMQ,SAAS,CAAGD,CAAAA,EAEpC2d,GACNnY,CAAAA,GAAUoY,AApKG,IAoKU5d,CAAAA,EAE3BwF,GAAUqY,AAnKO,GAmKO,CAAA,CAACJ,GAEpB9c,GAAS,GAAKA,GAAS,KAAOA,EAAQ,KAAOA,EAAQ,MACtDlB,EAAMU,UAAU,CAAGD,EAAI,GAAKA,CAAAA,EAC5Bqd,AAAY,IAAZA,EACA/X,GAAUsY,AAxKoB,IAwKN1d,EAElBmd,GACN/X,CAAAA,GAAUsY,AA3KoB,IA2KN,CAAA,IAAO1d,CAAAA,CAAC,EAE7B,CACH2E,MAAO,IAAI,CAACuW,UAAU,CAACgC,EAAO,CAAA,GAC9B5Y,IAAK,IAAI,CAAC4W,UAAU,CAACkC,EAAO,CAAA,GAC5BzS,KAAM,IAAI,CAACuQ,UAAU,CAACoC,EAAO,CAAA,GAC7BhF,SAAU,CACNtK,MAAOtN,KAAK6M,KAAK,CAACnI,EACtB,EACAuT,YAAaA,EAEbwE,QAASA,EACTE,MAAOA,CACX,CACJ,CAEA,SAASM,EAAMlF,CAAO,EAClB,IAAqBmF,EAAUra,AAAhB,IAAI,CAAqB0Y,CAAC,GAAIT,EAAejY,AAA7C,IAAI,CAAkDqU,OAAO,CAACpZ,SAAS,CAAEqf,EAAgB,CAChG,QAAS,OACT,IAAK,IAAK,IAAK,SAAU,QAAS,MAAO,QAC5C,CAML,SAASC,EAAc9B,CAAM,EACzB,IAEIhe,EAFA+f,EAAK,CAAC,EAGV,IAAK/f,KAFLge,EAAS3B,GAAoB2B,GAGU,KAA/B6B,EAAc5K,OAAO,CAACjV,KACtB+f,CAAE,CAAC/f,EAAI,CAAGge,CAAM,CAAChe,EAAI,CACrB,OAAOge,CAAM,CAAChe,EAAI,EAG1B,MAAOE,EAAAA,OAAOib,IAAI,CAAC4E,GAAI/b,MAAM,EAAG,CAAC+b,EAAI/B,EAAO,AAChD,CAEAvD,AADAA,CAAAA,EAAU4B,GAAoB5B,EAAO,EAC7BlY,KAAK,CAAG,AAACkY,CAAAA,EAAQlY,KAAK,EAAI,CAAA,EAAK2Z,GACvCzB,EAAQnY,IAAI,CAAG,AAACmY,CAAAA,EAAQnY,IAAI,EAAI,CAAA,EAAK4Z,GAErC0D,EAAQtZ,GAAG,CAAGf,AAzBC,IAAI,CAyBIiM,IAAI,GAC3BoO,EAAQI,KAAK,CAAGza,AA1BD,IAAI,CA0BMiM,IAAI,GAC7BoO,EAAQK,KAAK,CAAG1a,AA3BD,IAAI,CA2BMiM,IAAI,GAC7BoO,EAAQM,GAAG,CAAG3a,AA5BC,IAAI,CA4BIiM,IAAI,GAC3BoO,EAAQO,GAAG,CAAG5a,AA7BC,IAAI,CA6BIiM,IAAI,GAG3BoO,EAAQQ,KAAK,CAAG,WACZ,IAAIC,EAAST,EAAQU,WAAW,CAC5BxW,EAAY8V,EAAQ7U,IAAI,CAAC,SAC7B6U,EAAQtZ,GAAG,CAACa,GAAG,CAACyY,GAGhB,IAAK,IAAIrF,EAAK,EAAGlQ,EAAK,CAAC,MAAO,MAAO,QAAS,QAAQ,CAAEkQ,EAAKlQ,EAAGrG,MAAM,CAAEuW,IAEpEqF,CAAO,CADIvV,CAAE,CAACkQ,EAAG,CACJ,CACRxP,IAAI,CAAC,CACN,MAASjB,EAAY,qBACzB,GACK3C,GAAG,CAACkZ,EAEjB,EAWA,IAAK,IAVDE,EAAU,SAAUC,CAAE,EAClBZ,CAAO,CAACY,EAAG,CAAG,WAEd,IAAK,IADGxN,EAAOxJ,UACN+Q,EAAK,EAAGlQ,EAAK,CAAC,MAAO,MAAO,MAAO,QAAS,QAAQ,CAAEkQ,EAAKlQ,EAAGrG,MAAM,CAAEuW,IAAM,CACjF,IAAIkG,EAAOpW,CAAE,CAACkQ,EAAG,CACjBqF,CAAO,CAACa,EAAK,CAACD,EAAG,CAAClX,KAAK,CAACsW,CAAO,CAACa,EAAK,CAAEzN,EAC3C,CACJ,CACJ,EAESuH,EAAK,EAAGlQ,EAAK,CAAC,WAAY,cAAc,CAAEkQ,EAAKlQ,EAAGrG,MAAM,CAAEuW,IAE/DgG,EADSlW,CAAE,CAACkQ,EAAG,CAOnBqF,CAAAA,EAAQc,QAAQ,CAAG,SAAUjG,CAAO,EAChC,IAAIJ,EAAQuF,EAAQra,QAAQ,CAACob,SAAS,CAAClG,GACnCrT,EAASiT,AAAa,IAAbA,EAAMuG,IAAI,AACvBhB,CAAAA,EAAQnF,OAAO,CAAGA,EAClBmF,EAAQtZ,GAAG,CAACyE,IAAI,CAAC,CAAElL,EAAGwa,EAAM/T,GAAG,CAAEc,OAAQiT,EAAMuG,IAAI,AAAC,GACpDhB,EAAQM,GAAG,CAACnV,IAAI,CAAC,CAAElL,EAAGwa,EAAM6F,GAAG,CAAE9Y,OAAQiT,EAAMwG,IAAI,AAAC,GACpDjB,EAAQO,GAAG,CAACpV,IAAI,CAAC,CAAElL,EAAGwa,EAAM8F,GAAG,CAAE/Y,OAAQiT,EAAMyG,IAAI,AAAC,GACpDlB,EAAQI,KAAK,CAACjV,IAAI,CAAC,CAAElL,EAAGwa,EAAM2F,KAAK,CAAE5Y,OAAQiT,EAAM0G,MAAM,AAAC,GAC1DnB,EAAQK,KAAK,CAAClV,IAAI,CAAC,CAAElL,EAAGwa,EAAM4F,KAAK,CAAE7Y,OAAQiT,EAAM2G,MAAM,AAAC,GAE1DpB,EAAQxY,MAAM,CAAGA,EACjBwY,EAAQ7U,IAAI,CAAC,CAAE3D,OAAQA,CAAO,GAE1BqT,EAAQwG,MAAM,GACdrB,EAAQtZ,GAAG,CAAC4a,kBAAkB,CAACzG,EAAQwG,MAAM,EAC7C,OAAOxG,EAAQwG,MAAM,CAE7B,EACArB,EAAQc,QAAQ,CAACjG,GAKjBmF,EAAQjE,UAAU,CAAG,SAAUtI,CAAK,EAChC,IAAI8N,EAASnF,GAAoB3I,GAAO7L,QAAQ,CAAC,KAAMnH,GAAG,GAO1D,OANA,IAAI,CAACkH,IAAI,CAAG8L,EACZ,IAAI,CAAC2M,KAAK,CAACjV,IAAI,CAAC,CAAExD,KAAM4Z,CAAO,GAC/B,IAAI,CAAClB,KAAK,CAAClV,IAAI,CAAC,CAAExD,KAAM4Z,CAAO,GAC/B,IAAI,CAACjB,GAAG,CAACnV,IAAI,CAAC,CAAExD,KAAM4Z,CAAO,GAC7B,IAAI,CAAChB,GAAG,CAACpV,IAAI,CAAC,CAAExD,KAAM4Z,CAAO,GAC7B,IAAI,CAAC7a,GAAG,CAACyE,IAAI,CAAC,CAAExD,KAAM8L,CAAM,GACrB,IAAI,AACf,EAGA,IAAK,IAAIe,EAAK,EAAGgN,EAAK,CAAC,UAAW,aAAc,aAAc,aAAa,CAAEhN,EAAKgN,EAAGpd,MAAM,CAAEoQ,IAEzFwL,CAAO,CAACyB,AADKD,CAAE,CAAChN,EAAG,CACF,SAAS,CAAG,SAAUf,CAAK,CAAErT,CAAG,EAC7C4f,CAAO,CAAC5f,EAAI,CAAGqT,EACf,IAAK,IAAIkH,EAAK,EAAGlQ,EAAK,CAAC,MAAO,MAAO,QAAS,QAAS,MAAM,CAAEkQ,EAAKlQ,EAAGrG,MAAM,CAAEuW,IAE3EqF,CAAO,CADEvV,CAAE,CAACkQ,EAAG,CACJ,CAACxP,IAAI,CAAC/K,EAAKqT,EAE9B,EA8FJ,OA1FAuM,EAAQ7U,IAAI,CAAG,SAAUiT,CAAM,EAC3B,GAAI,AAAkB,UAAlB,OAAOA,EAAqB,CAC5B,IAAIsD,EAAWxB,EAAc9B,GAC7B,GAAIsD,EAAU,CACV,IAAIvB,EAAKuB,CAAQ,CAAC,EAAE,AACpB9X,CAAAA,SAAS,CAAC,EAAE,CAAG8X,CAAQ,CAAC,EAAE,CAET,KAAK,IAAlBvB,EAAGxd,KAAK,EACRwd,CAAAA,EAAGxd,KAAK,EAAI2Z,EAAoB,EAEpB,KAAK,IAAjB6D,EAAGzd,IAAI,EACPyd,CAAAA,EAAGzd,IAAI,EAAI4Z,EAAoB,EAEnCE,GAAqBwD,EAAQnF,OAAO,CAAEsF,GAClCH,EAAQnF,OAAO,EACfmF,EAAQc,QAAQ,CAACd,EAAQnF,OAAO,CAExC,CACJ,CACA,OAAO+C,EAAazS,IAAI,CAACzB,KAAK,CAACsW,EAASpW,UAC5C,EAIAoW,EAAQlE,OAAO,CAAG,SAAUsC,CAAM,CAAEuD,CAAS,CAAExG,CAAQ,EACnD,IAAI9H,EAAO,IAAI,CAACwH,OAAO,CACnB+G,EAAa,QACT9e,KAAK+e,MAAM,GAAGC,QAAQ,CAAC,IAAIC,SAAS,CAAC,EAAG,EAGhD,QAAO3D,EAAOiD,MAAM,CACpB,OAAOjD,EAAOhc,CAAC,CACf,IAAI4f,EAAO7F,GAAWO,GAAmBiF,EACrC,IAAI,CAAChc,QAAQ,CAACsc,eAAe,GACjC,GAAID,EAAK9G,QAAQ,CAAE,CACf,IAAIwG,EAAWxB,EAAc9B,GAM7B,GAHA4B,CAAO,CAAC4B,EAAW,CAAG,EACtBxD,CAAM,CAACwD,EAAW,CAAG,EACrB5B,CAAO,CAAC4B,EAAa,SAAS,CAAG,AAAC1gB,IAA+EghB,IAAI,CACjHR,EAAU,CACV,IAAIS,EAAOT,CAAQ,CAAC,EAAE,CAClBU,EAAgB,SAAUhiB,CAAG,CAC7B2K,CAAG,EAAI,OAAQsI,CAAI,CAACjT,EAAI,CAAG,AAACsc,CAAAA,GAAmByF,CAAI,CAAC/hB,EAAI,CACxDiT,CAAI,CAACjT,EAAI,EACLiT,CAAI,CAACjT,EAAI,AAAD,EAAK2K,CAAM,CAC3BiX,CAAAA,EAAKK,IAAI,CAAG,SAAUniB,CAAC,CAAEoiB,CAAE,EACnBA,EAAG3hB,IAAI,GAAKihB,GACZU,EAAGpX,IAAI,CAAC4V,QAAQ,CAACrE,GAAoBpJ,EAAM,CACvCrR,EAAGogB,EAAc,IAAKE,EAAGvX,GAAG,EAC5B7I,EAAGkgB,EAAc,IAAKE,EAAGvX,GAAG,EAC5BwX,EAAGH,EAAc,IAAKE,EAAGvX,GAAG,EAC5ByX,OAAQJ,EAAc,SAAUE,EAAGvX,GAAG,EACtCC,MAAOoX,EAAc,QAASE,EAAGvX,GAAG,EACpCE,IAAKmX,EAAc,MAAOE,EAAGvX,GAAG,EAChC1I,MAAO+f,EAAc,QAASE,EAAGvX,GAAG,CACxC,GAER,CACJ,CACA4W,EAAYK,CAChB,CACA,OAAOpE,EAAa9B,OAAO,CAAChb,IAAI,CAAC,IAAI,CAAEsd,EAAQuD,EAAWxG,EAC9D,EAEA6E,EAAQrE,OAAO,CAAG,WAMd,OALA,IAAI,CAACjV,GAAG,CAACiV,OAAO,GAChB,IAAI,CAAC4E,GAAG,CAAC5E,OAAO,GAChB,IAAI,CAAC2E,GAAG,CAAC3E,OAAO,GAChB,IAAI,CAACyE,KAAK,CAACzE,OAAO,GAClB,IAAI,CAAC0E,KAAK,CAAC1E,OAAO,GACXiC,EAAajC,OAAO,CAAC7a,IAAI,CAAC,IAAI,CACzC,EAEAkf,EAAQyC,IAAI,CAAG,WACX,IAAI,CAAC/b,GAAG,CAAC+b,IAAI,GACb,IAAI,CAAClC,GAAG,CAACkC,IAAI,GACb,IAAI,CAACnC,GAAG,CAACmC,IAAI,GACb,IAAI,CAACrC,KAAK,CAACqC,IAAI,GACf,IAAI,CAACpC,KAAK,CAACoC,IAAI,EACnB,EACAzC,EAAQ0C,IAAI,CAAG,SAAUC,CAAO,EAC5B,IAAI,CAACjc,GAAG,CAACgc,IAAI,CAACC,GACd,IAAI,CAACpC,GAAG,CAACmC,IAAI,CAACC,GACd,IAAI,CAACrC,GAAG,CAACoC,IAAI,CAACC,GACd,IAAI,CAACvC,KAAK,CAACsC,IAAI,CAACC,GAChB,IAAI,CAACtC,KAAK,CAACqC,IAAI,CAACC,EACpB,EAEO3C,CACX,CAKA,SAASe,EAAUlF,CAAS,EACxB,IAAIiB,EAAKjB,EAAU7Z,CAAC,EAAI,EACpB+a,EAAKlB,EAAU3Z,CAAC,EAAI,EACpB8I,EAAQ6Q,EAAU7Q,KAAK,EAAI,EAC3BC,EAAM,AAAC4Q,CAAAA,EAAU5Q,GAAG,EAAI,CAAA,EAAK,KAC7BsX,EAAI1G,EAAU0G,CAAC,EAAI,EACnBK,EAAK/G,EAAU2G,MAAM,EAAI,EACzBviB,EAAI4b,EAAUxZ,KAAK,EAAI,EACvBM,EAAQkZ,EAAUlZ,KAAK,EAAI,EAC3BD,EAAOmZ,EAAUnZ,IAAI,EAAI,EAErBmgB,EAAK/f,KAAKC,GAAG,CAACiI,GAClB8X,EAAKhgB,KAAKI,GAAG,CAAC8H,GACd+X,EAAKjgB,KAAKC,GAAG,CAACkI,GACd+X,EAAKlgB,KAAKI,GAAG,CAAC+H,GACd+R,EAAKuF,EAAIzf,KAAKC,GAAG,CAACL,GAClBua,EAAKsF,EAAIzf,KAAKC,GAAG,CAACJ,GAClBsgB,EAAML,EAAK9f,KAAKC,GAAG,CAACL,GACpBwgB,EAAMN,EAAK9f,KAAKC,GAAG,CAACJ,GACpBua,EAAKjd,EAAI6C,KAAKI,GAAG,CAACR,GAClBya,EAAKld,EAAI6C,KAAKI,GAAG,CAACP,GAEd+D,EAAM,CACN,CAAC,IACLoW,EAAME,EAAK6F,EACX9F,EAAME,EAAK6F,EAAI,CACd,CAELpc,AADAA,CAAAA,EAAMA,EAAIsF,MAAM,CAAC6Q,GAAQC,EAAIC,EAAIC,EAAIC,EAAIjS,EAAOC,EAAK,EAAG,GAAE,EACtDN,IAAI,CAAC,CACL,IAAKmS,EAAMmG,EAAMF,EAAKhG,EAAMmG,EAAMF,EACrC,EAEDtc,AADAA,CAAAA,EAAMA,EAAIsF,MAAM,CAAC6Q,GAAQC,EAAIC,EAAIkG,EAAKC,EAAKjY,EAAKD,EAAO,EAAG,GAAE,EACxDL,IAAI,CAAC,CAAC,IAAI,EAEd,IAAI6M,EAAK9U,EAAO,EAAII,KAAK6Z,EAAE,CAAG,EAAI,EAAIzc,EAAKyC,EAAQ,EAAI,EAAIG,KAAK6Z,EAAE,CAAG,EACjEwG,EAASnY,EAAQ,CAACwM,EAAIxM,EAASC,EAAM,CAACuM,EAAI,CAACA,EAAIxM,EAC/CoY,EAAOnY,EAAM0R,GAAKzc,EAAI+K,EAAOD,EAAQ2R,GAAKzc,EAAIyc,GAAKzc,EAAI+K,EACvDoY,EAAS,EAAI1G,GAAKzc,EAwBlBqgB,EAAM,CACF,CAAC,IACLzD,EAAME,EAAKja,GAAIogB,GACfpG,EAAME,EAAK/Z,GAAIigB,GAAS,CACvB,CACL5C,EAAMA,EAAIvU,MAAM,CAAC6Q,GAAQC,EAAIC,EAAIC,EAAIC,EAAIkG,EAAQC,EAAM,EAAG,IAGtDnY,EAAMoY,GAAUrY,EAAQqY,GAExB9C,EAAI5V,IAAI,CAAC,CACL,IAAKmS,EAAME,EAAKja,GAAIqgB,GAASlG,EAAIH,EAAME,EAAK/Z,GAAIkgB,GAASjG,EAC5D,EAIDoD,AAFAA,CAAAA,EAAMA,EAAIvU,MAAM,CAAC6Q,GAAQC,EAAIC,EAAIC,EAAIC,EAAImG,EAAMC,EAAQnG,EAAIC,GAAG,EAE1DxS,IAAI,CAAC,CACL,IAAKmS,EAAME,EAAKja,GAAIsgB,GAAUtG,EAAME,EAAK/Z,GAAImgB,GAChD,EAID9C,AAFAA,CAAAA,EAAMA,EAAIvU,MAAM,CAAC6Q,GAAQC,EAAIC,EAAIC,EAAIC,EAAIoG,EAAQpY,EAAK,EAAG,GAAE,EAEvDN,IAAI,CAAC,CACL,IAAKmS,EAAME,EAAKja,GAAIkI,GAAQiS,EAAIH,EAAME,EAAK/Z,GAAI+H,GAAQkS,EAC1D,EAGDoD,AADAA,CAAAA,EAAMA,EAAIvU,MAAM,CAAC6Q,GAAQC,EAAIC,EAAIC,EAAIC,EAAIhS,EAAKoY,EAAQnG,EAAIC,GAAG,EACzDxS,IAAI,CAAC,CACL,IAAKmS,EAAME,EAAKja,GAAIsgB,GAAUtG,EAAME,EAAK/Z,GAAImgB,GAChD,EAED9C,EAAMA,EAAIvU,MAAM,CAAC6Q,GAAQC,EAAIC,EAAIC,EAAIC,EAAIoG,EAAQD,EAAM,EAAG,KAGrDnY,EAAM0R,GAAKzc,GAAK8K,EAAQ2R,GAAKzc,IAElCqgB,EAAI5V,IAAI,CAAC,CACL,IACAmS,EAAME,EAAKla,KAAKC,GAAG,CAACqgB,GAASlG,EAC7BH,EAAME,EAAKna,KAAKI,GAAG,CAACkgB,GAASjG,EAChC,EAIDoD,AAFAA,CAAAA,EAAMA,EAAIvU,MAAM,CAAC6Q,GAAQC,EAAIC,EAAIC,EAAIC,EAAImG,EAAMnY,EAAKiS,EAAIC,GAAG,EAEvDxS,IAAI,CAAC,CACL,IAAKmS,EAAME,EAAKla,KAAKC,GAAG,CAACkI,GAAO8R,EAAME,EAAKna,KAAKI,GAAG,CAAC+H,GACvD,EAEDsV,EAAMA,EAAIvU,MAAM,CAAC6Q,GAAQC,EAAIC,EAAIC,EAAIC,EAAIhS,EAAKmY,EAAM,EAAG,KAE3D7C,EAAI5V,IAAI,CAAC,CACL,IACAmS,EAAME,EAAKla,KAAKC,GAAG,CAACqgB,GAASlG,EAC7BH,EAAME,EAAKna,KAAKI,GAAG,CAACkgB,GAASjG,EAChC,EAEDoD,AADAA,CAAAA,EAAMA,EAAIvU,MAAM,CAAC6Q,GAAQC,EAAIC,EAAIC,EAAIC,EAAImG,EAAMD,EAAQjG,EAAIC,GAAG,EAC1DxS,IAAI,CAAC,CAAC,IAAI,EAEd,IAAI2V,EAAM,CACF,CAAC,IACLxD,EAAMmG,EAAMJ,EACZ9F,EAAMmG,EAAMJ,EAAI,CACf,CAELxC,AADAA,CAAAA,EAAMA,EAAItU,MAAM,CAAC6Q,GAAQC,EAAIC,EAAIkG,EAAKC,EAAKlY,EAAOC,EAAK,EAAG,GAAE,EACxDN,IAAI,CAAC,CACL,IACAmS,EAAMmG,EAAMngB,KAAKC,GAAG,CAACkI,GAAQiS,EAC7BH,EAAMmG,EAAMpgB,KAAKI,GAAG,CAAC+H,GAAQkS,EAChC,EAEDmD,AADAA,CAAAA,EAAMA,EAAItU,MAAM,CAAC6Q,GAAQC,EAAIC,EAAIkG,EAAKC,EAAKjY,EAAKD,EAAOkS,EAAIC,GAAG,EAC1DxS,IAAI,CAAC,CAAC,IAAI,EAEd,IAAIyV,EAAQ,CACJ,CAAC,IACLtD,EAAME,EAAK6F,EACX9F,EAAME,EAAK6F,EAAI,CACX,CAAC,IACLhG,EAAME,EAAK6F,EAAM3F,EACjBH,EAAME,EAAK6F,EAAM3F,EAAG,CAChB,CAAC,IACLL,EAAMmG,EAAMJ,EAAM3F,EAClBH,EAAMmG,EAAMJ,EAAM3F,EAAG,CACjB,CAAC,IACLL,EAAMmG,EAAMJ,EACZ9F,EAAMmG,EAAMJ,EAAI,CACZ,CAAC,IAAI,CACR,CACDzC,EAAQ,CACJ,CAAC,IACLvD,EAAME,EAAK+F,EACXhG,EAAME,EAAK+F,EAAI,CACX,CAAC,IACLlG,EAAME,EAAK+F,EAAM7F,EACjBH,EAAME,EAAK+F,EAAM7F,EAAG,CAChB,CAAC,IACLL,EAAMmG,EAAMF,EAAM7F,EAClBH,EAAMmG,EAAMF,EAAM7F,EAAG,CACjB,CAAC,IACLL,EAAMmG,EAAMF,EACZhG,EAAMmG,EAAMF,EAAI,CACZ,CAAC,IAAI,CACR,CAGDM,EAAYxgB,KAAKygB,KAAK,CAACpG,EAAI,CAACD,GAC5BsG,EAAW1gB,KAAK+L,GAAG,CAAC5D,EAAMqY,GAC1BG,EAAa3gB,KAAK+L,GAAG,CAAC7D,EAAQsY,GAC9BI,EAAW5gB,KAAK+L,GAAG,CAAC,AAAC7D,CAAAA,EAAQC,CAAE,EAAK,EAAIqY,GAK5C,SAASK,EAAcC,CAAK,EAKxB,MAJAA,CAAAA,GAAiB,EAAI9gB,KAAK6Z,EAAE,EAChB7Z,KAAK6Z,EAAE,EACfiH,CAAAA,EAAQ,EAAI9gB,KAAK6Z,EAAE,CAAGiH,CAAI,EAEvBA,CACX,CACAJ,EAAWG,EAAcH,GACzBC,EAAaE,EAAcF,GAG3B,IACII,EAAKH,AADU,IAFnBA,CAAAA,EAAWC,EAAcD,EAAQ,EAI7BI,EAAKL,AAFU,IAEVA,EACLM,EAAKP,AAHU,IAGVA,EACT,MAAO,CACH9c,IAAKA,EAELsa,KAAMle,AAPS,IAOTA,KAAK6Z,EAAE,CAAkB,EAC/B4D,IAAKA,EACLW,KAAMpe,KAAK8L,GAAG,CAACiV,EAAIC,EAAIC,GACvBzD,IAAKA,EACLW,KAAMne,KAAK8L,GAAG,CAACiV,EAAIC,EAAIC,GACvB3D,MAAOA,EAEPe,OAAQ4C,AAAK,IAALA,EACR1D,MAAOA,EACPe,OAAQ0C,AAAK,IAALA,CACZ,CACJ,CAtxBAvkB,EAAc6K,OAAO,CAjBrB,SAAiB4Z,CAAgB,EAC7B,IAAIC,EAAgBD,EAAiBpjB,SAAS,AACzCqjB,CAAAA,EAAc1F,SAAS,EACxB/B,GAAqByH,EAAe,CAChCC,UA3FqC/J,GA4FrC4F,MAAOA,EACPgB,UAAWA,EACX7E,OAAQA,EACRuC,WAAYA,EACZF,UAAWA,EACXZ,OAAQA,EACRrW,WAAYA,EACZgW,WAAYA,EACZxL,eAAgBA,CACpB,EAER,CAwxBJ,EAAGvS,GAAkBA,CAAAA,EAAgB,CAAC,CAAA,GAMT,IAAI4kB,GAAqB5kB,EAGlD6kB,GAA2F1kB,EAAoB,KAC/G2kB,GAA+G3kB,EAAoBI,CAAC,CAACskB,IAYrIE,IACIjlB,EAAgB,SAAUY,CAAC,CAC3BuX,CAAC,EAOD,MAAOnY,AANHA,CAAAA,EAAgBiB,OAAOmX,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU1X,CAAC,CAC1DuX,CAAC,EAAIvX,EAAEyX,SAAS,CAAGF,CAAG,GACd,SAAUvX,CAAC,CACnBuX,CAAC,EAAI,IAAK,IAAIN,KAAKM,EAAOlX,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC0W,EAC/DN,IAAIjX,CAAAA,CAAC,CAACiX,EAAE,CAAGM,CAAC,CAACN,EAAE,AAAD,CAAG,CAAA,EACIjX,EAAGuX,EAC5B,EACO,SAAUvX,CAAC,CAAEuX,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAImC,UAAU,uBAAyBC,OAAOpC,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAG5X,CAAG,CADtCZ,EAAcY,EAAGuX,GAEjBvX,EAAEW,SAAS,CAAG4W,AAAM,OAANA,EAAalX,OAAOwX,MAAM,CAACN,GAAMI,CAAAA,EAAGhX,SAAS,CAAG4W,EAAE5W,SAAS,CAAE,IAAIgX,CAAG,CACtF,GAIA2M,GAAuB,AAACrjB,IAA+E6D,cAAc,CAErHyf,GAAiB,AAACtjB,IAA+E8D,QAAQ,CAAEyf,GAAc,AAACvjB,IAA+EgE,KAAK,CAAEwf,GAAa,AAACxjB,IAA+EI,IAAI,CAAEqjB,GAAQ,AAACzjB,IAA+EyjB,KAAK,CASpZ,SAASC,GAAc/iB,CAAO,EAC1B,OAAO,IAAIgjB,GAAM,IAAI,CAAEhjB,EAC3B,CAKA,SAASijB,KACL,IAAI1K,EAAQ,IAAI,CACZ2K,EAAe,IAAI,CAACljB,OAAO,CAACsK,KAAK,CAAGwY,GAAM,IAAI,CAAC9iB,OAAO,CAACsK,KAAK,EAAI,CAAC,GAChE,IAAI,CAAC5G,IAAI,KAGd,IAAI,CAAC4G,KAAK,CAAG,EAAE,CACf4Y,EAAa7c,OAAO,CAAC,SAAU8c,CAAW,EACtC5K,EAAM6K,QAAQ,CAACD,GAAaE,QAAQ,EACxC,GACJ,CAUA,IAAIL,GAAuB,SAAUtM,CAAM,EAEvC,SAASsM,IAML,IAAIzK,EAAQ7B,AAAW,OAAXA,GAAmBA,EAAO7O,KAAK,CAAC,IAAI,CAC5CE,YAAc,IAAI,CAEtB,OADAwQ,EAAMpG,OAAO,CAAG,CAAA,EACToG,CACX,CAgEA,OA3EAkK,GAAcO,EAAOtM,GAYrBsM,EAAMza,OAAO,CAAG,SAAUC,CAAU,EAChC,IAAIE,EAAaF,EAAWzJ,SAAS,AAChC2J,CAAAA,EAAW0a,QAAQ,GACpBV,GAAqBpY,KAAK,CAAGsY,GAAYF,GAAqBtY,KAAK,CAAE,CACjEkZ,OAAQ,EACRC,UAAW,CACf,GACA7a,EAAW0a,QAAQ,CAAGL,GACtBra,EAAW8a,mBAAmB,CAAClZ,KAAK,CAAG,CAAC5B,EAAW0a,QAAQ,CAAC,CAC5D1a,EAAW+a,qBAAqB,CAAC3a,IAAI,CAAC,SACtC6Z,GAAena,EAAY,kBAAmBya,IAEtD,EAMAD,EAAMjkB,SAAS,CAAC2kB,IAAI,CAAG,SAAU9jB,CAAK,CAAE+jB,CAAW,EAE/C,IAAI,CAACxR,OAAO,CAAG,CAAA,EACfuE,EAAO3X,SAAS,CAAC2kB,IAAI,CAACzkB,IAAI,CAAC,IAAI,CAAEW,EAAO+jB,EAAa,QACzD,EAMAX,EAAMjkB,SAAS,CAAC6kB,iBAAiB,CAAG,WAChC,IAAIrL,EAAQ,IAAI,AAChB,CAAA,IAAI,CAACsL,gBAAgB,CAAG,CAAA,EAExB,IAAI,CAACC,OAAO,CAAG,IAAI,CAACC,OAAO,CAAG,IAAI,CAACC,gBAAgB,CAAI,IAAI,CAACC,gBAAgB,CAAG,KAAK,EAChF,IAAI,CAAC9V,QAAQ,EACb,IAAI,CAACA,QAAQ,CAAC+V,WAAW,GAG7B,IAAI,CAACtd,MAAM,CAACP,OAAO,CAAC,SAAUO,CAAM,EAChC,GAAIA,EAAOud,YAAY,GAAI,CACvB,IAAInW,EAAYpH,EAAO5G,OAAO,CAACgO,SAAS,AACxCuK,CAAAA,EAAMsL,gBAAgB,CAAG,CAAA,EAErBtL,EAAM6L,kBAAkB,EAAIpW,GAAa,GACzCA,CAAAA,EAAY,KAAK,CAAA,EAErB,IAAIqW,EAAQzd,EAAO0d,SAAS,CAAC,IACzBD,CAAAA,EAAM9hB,MAAM,GACZgW,EAAMuL,OAAO,CAAG7iB,KAAK6L,GAAG,CAAC+V,GAAWtK,EAAMuL,OAAO,CAAEO,CAAK,CAAC,EAAE,EAAGpjB,KAAK6L,GAAG,CAACjF,KAAK,CAAC,KAAMwc,IACnF9L,EAAMwL,OAAO,CAAG9iB,KAAK8L,GAAG,CAAC8V,GAAWtK,EAAMwL,OAAO,CAAEM,CAAK,CAAC,EAAE,EAAGpjB,KAAK8L,GAAG,CAAClF,KAAK,CAAC,KAAMwc,IAE3F,CACJ,EACJ,EAIArB,EAAMjkB,SAAS,CAACwlB,WAAW,CAAG,WAE1B,IADI3b,EACAhJ,EAAQ,IAAI,CAACA,KAAK,CACtB8W,EAAO3X,SAAS,CAACwlB,WAAW,CAACtlB,IAAI,CAAC,IAAI,EACtC,IAAI,CAACge,KAAK,CAAG,IAAI,CAAChM,GAAG,CAAG,AAAC,CAAA,AAAyC,OAAxCrI,CAAAA,EAAKhJ,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,AAAD,GAAe6I,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGpI,KAAK,AAAD,GAAM,EAChH,IAAI,CAACmE,KAAK,CAAG/E,EAAMsH,UAAU,CAAG,IAAI,CAAC+V,KAAK,CAAG,IAAI,CAAC1Y,IAAI,AAC1D,EACOye,CACX,EAAGR,MAoBCgC,GAA+B,AAACnlB,IAA+E+N,QAAQ,CAIvHqX,GAA+B,AAACplB,IAA+E8D,QAAQ,CAAEuhB,GAA6B,AAACrlB,IAA+EmQ,MAAM,CAAEmV,GAA2B,AAACtlB,IAA+EI,IAAI,CAAEmlB,GAAiC,AAACvlB,IAA+EmO,UAAU,CAAEqX,GAA2B,AAACxlB,IAA+EkE,IAAI,CAO/kB,SAASuhB,KACL,IASIC,EARAnlB,EAAQgH,AADC,IAAI,CACEhH,KAAK,CACpBmX,EAAgBnQ,AAFP,IAAI,CAEU5G,OAAO,CAC9BQ,EAAQuW,EAAcvW,KAAK,CAIvBD,EAAI0W,AAHAF,CAAAA,EAAc5I,QAAQ,CACzB4I,EAAcE,KAAK,EAAI,EACxBrQ,AANK,IAAI,CAMF2M,KAAK,AAAD,EACE/S,CAAAA,EAASuW,CAAAA,EAAcG,aAAa,EAAI,CAAA,CAAC,EAC1D8N,EAAcpe,AARL,IAAI,CAQQqe,WAAW,CAAG,EAAI,GAAM,CAEzCrlB,CAAAA,EAAMK,QAAQ,EAAI,CAAC2G,AAVd,IAAI,CAUiByD,KAAK,CAACsE,QAAQ,EACxCqW,CAAAA,GAAe,EAAC,EAEO,CAAA,IAA3BjO,EAAcmO,QAAQ,EACtB3kB,CAAAA,EAAI,CAAA,EAERA,GAAMwW,EAAcG,aAAa,EAAI,EACrC,IAAK,IAAI4B,EAAK,EAAGlQ,EAAKhC,AAjBT,IAAI,CAiBYjH,MAAM,CAAEmZ,EAAKlQ,EAAGrG,MAAM,CAAEuW,IAAM,CACvD,IAAIpX,EAAQkH,CAAE,CAACkQ,EAAG,CAGlB,GADApX,EAAMyjB,aAAa,CAAG,KAClBzjB,AAAY,OAAZA,EAAMrB,CAAC,CAAW,CAQd,IAAK,IAPL2Z,EAAY0K,GAA2B,CAAEvkB,EAAG,EAAGE,EAAG,EAAG4c,MAAO,EAAGF,OAAQ,CAAE,EAAGrb,EAAMsY,SAAS,EAAI,CAAC,GAGhGoL,EAAa,CAAC,CAAC,IAAK,QAAQ,CAAE,CAAC,IAAK,SAAS,CAAC,CAAEC,EAAa3jB,EAAM2jB,UAAU,CAC7EC,EACC,KAAK,EAEG3S,EAAK,EAA8BA,EAAK4S,AAAjBH,EAA8B7iB,MAAM,CAAEoQ,IAAM,CACxE,IAAIvU,EAAImnB,AADoBH,CACR,CAACzS,EAAG,CAkB5B,GAjBA2S,CAAAA,EAAiBtL,CAAS,CAAC5b,CAAC,CAAC,EAAE,CAAC,CAAG4mB,CAAU,EACxB,IAIjBhL,CAAS,CAAC5b,CAAC,CAAC,EAAE,CAAC,EAAI4b,CAAS,CAAC5b,CAAC,CAAC,EAAE,CAAC,CAAG4mB,EACrChL,CAAS,CAAC5b,CAAC,CAAC,EAAE,CAAC,CAAG,CAAC4mB,EACnBM,EAAiB,GAEhBA,EAAiBtL,CAAS,CAAC5b,CAAC,CAAC,EAAE,CAAC,CACjCwI,AAzCH,IAAI,AAyCK,CAACxI,CAAC,CAAC,EAAE,CAAG,OAAO,CAAC6S,GAAG,EAEzB+I,AAAoB,IAApBA,CAAS,CAAC5b,CAAC,CAAC,EAAE,CAAC,EACf4b,CAAAA,CAAS,CAAC5b,CAAC,CAAC,EAAE,CAAC,CACXwI,AA7CP,IAAI,AA6CS,CAACxI,CAAC,CAAC,EAAE,CAAG,OAAO,CAAC6S,GAAG,CACrB+I,CAAS,CAAC5b,CAAC,CAAC,EAAE,CAAC,AAAD,EAI1B4b,AAAoB,IAApBA,CAAS,CAAC5b,CAAC,CAAC,EAAE,CAAC,EACV4b,CAAAA,CAAS,CAAC5b,CAAC,CAAC,EAAE,CAAC,EAAIwI,AAnDvB,IAAI,AAmDyB,CAACxI,CAAC,CAAC,EAAE,CAAG,OAAO,CAAC6S,GAAG,EACzC+I,CAAS,CAAC5b,CAAC,CAAC,EAAE,CAAC,CAAG4b,CAAS,CAAC5b,CAAC,CAAC,EAAE,CAAC,EAAI4mB,CAAU,EAAI,CAEvD,IAAK,IAAIzmB,KAAOyb,EAEZA,CAAS,CAACzb,EAAI,CAAGA,AAAQ,MAARA,EAAc,MAAQ,CAI3CmD,CAAAA,EAAMyjB,aAAa,CAAG,CAAA,CAC1B,CACJ,CA6BA,GA3BwB,gBAApBzjB,EAAM8jB,SAAS,EACf9jB,CAAAA,EAAM8jB,SAAS,CAAG,QAAO,EAE7B9jB,EAAMsY,SAAS,CAAG0K,GAA2B1K,EAAW,CACpDzZ,EAAGA,EACHC,MAAOA,EACPX,eAAgB,CAAA,CACpB,GAEAklB,EAAa,CACT5kB,EAAG6Z,EAAU7Z,CAAC,CAAG6Z,EAAUiD,KAAK,CAAG,EACnC5c,EAAG2Z,EAAU3Z,CAAC,CACdE,EAAGA,EAAIC,EAAQ,CACnB,EAEIZ,EAAMK,QAAQ,GACd8kB,EAAW5kB,CAAC,CAAG6Z,EAAU+C,MAAM,CAC/BgI,EAAW1kB,CAAC,CAAGqB,EAAM+jB,OAAO,EAAI,GAGpC/jB,EAAMsP,QAAQ,CAAG+T,EAAW5kB,CAAC,CAC7BuB,EAAMwP,QAAQ,CAAG6T,EAAW1kB,CAAC,CAC7BqB,EAAM8V,QAAQ,CAAGuN,EAAWxkB,CAAC,CAG7BmB,EAAMgkB,MAAM,CAAGC,AAzjIVjmB,EAyjI0C,CAACqlB,EAAW,CAAEnlB,EAAO,CAAA,EAAM,CAAA,EAAM,CAAC,EAAE,CAE/EylB,EAAY,CACZ,IAAIO,EAAkBD,AA5jIrBjmB,EA4jIqD,CAAC,CAC3CS,EAAGklB,CAAU,CAAC,EAAE,CAChBhlB,EAAGglB,CAAU,CAAC,EAAE,CAChB9kB,EAAGA,EAAIC,EAAQ,CACnB,EAAE,CAAEZ,EAAO,CAAA,EAAM,CAAA,EAAM,CAAC,EAAE,AAClC8B,CAAAA,EAAM2jB,UAAU,CAAG,CAACO,EAAgBzlB,CAAC,CAAEylB,EAAgBvlB,CAAC,CAAC,AAC7D,CACJ,CACJ,CAEAuG,AAtGa,IAAI,CAsGVrG,CAAC,CAAGA,CACf,CA+DA,SAASslB,KACL,GAAI,IAAI,CAACjmB,KAAK,CAAC8D,IAAI,GAAI,CACnB,IACIqT,EAAgBnQ,AADP,IAAI,CACU5G,OAAO,CAC9BklB,EAAWnO,EAAcmO,QAAQ,CACjC/W,EAAW4I,EAAc5I,QAAQ,CACjC2X,EAAiBlf,AAJR,IAAI,CAIWyD,KAAK,CAACrK,OAAO,CAAC8lB,cAAc,CACpDvlB,EAAI,EAER,GAAI,CAAE,CAAA,AAAoB,KAAA,IAAb2kB,GAA4B,CAACA,CAAO,EAAI,CACjD,IA5BJte,EACAmf,EACAC,EACA5jB,EAyBQ2jB,GA5BRnf,EAAShH,AA4BuB,IAAI,CAACA,KAAK,CA5B3BgH,MAAM,CACrBmf,EAAS,CAAEE,YAAa,CAAE,EAE1B7jB,EAAI,EACRwE,EAAOP,OAAO,CAAC,SAAUQ,CAAC,EAEjBkf,CAAM,CADXC,EAAcrB,GAAyB9d,EAAE7G,OAAO,CAACiX,KAAK,CAAG9I,AAwBjDA,EAxB4D,EAAIvH,EAAOrE,MAAM,CAAG,EAAIsE,EAAE0M,KAAK,EAC3E,CAKpBwS,CAAM,CAACC,EAAY,CAACpf,MAAM,CAACkC,IAAI,CAACjC,IAJhCkf,CAAM,CAACC,EAAY,CAAG,CAAEpf,OAAQ,CAACC,EAAE,CAAEqf,SAAU9jB,CAAE,EACjDA,IAKR,GACA2jB,EAAOE,WAAW,CAAG7jB,EAAI,EAClB2jB,GAeK9O,EAAQF,EAAcE,KAAK,EAAI,EAC/B7U,EACC,KAAK,EACN,IAAKA,EAAI,EAAGA,EAAI2jB,CAAM,CAAC9O,EAAM,CAACrQ,MAAM,CAACrE,MAAM,EACnCwjB,CAAM,CAAC9O,EAAM,CAACrQ,MAAM,CAACxE,EAAE,GAAK,IAAI,CADKA,KAKjD7B,EAAI,AAAC,GAAMwlB,CAAAA,EAAOE,WAAW,CAAGF,CAAM,CAAC9O,EAAM,CAACiP,QAAQ,AAAD,EAChDJ,CAAAA,EAAiB1jB,EAAI,CAACA,CAAAA,EAGtB,IAAI,CAACgI,KAAK,CAACuE,QAAQ,EACpBpO,CAAAA,EAAI,AAAsB,GAArBwlB,EAAOE,WAAW,CAAS1lB,CAAAA,CAExC,CACAwW,EAAcvW,KAAK,CAAGuW,EAAcvW,KAAK,EAAI,GAC7CoG,AA3Ba,IAAI,CA2BVrG,CAAC,CAAGqG,AA3BE,IAAI,CA2BCrG,CAAC,EAAI,EACvBwW,EAAcpR,MAAM,CAAGpF,CAC3B,CACJ,CAMA,SAAS4lB,GAA+Bve,CAAO,EAE3C,IAAK,IADD2J,EAAO,EAAE,CACJuH,EAAK,EAAGA,EAAK/Q,UAAUxF,MAAM,CAAEuW,IACpCvH,CAAI,CAACuH,EAAK,EAAE,CAAG/Q,SAAS,CAAC+Q,EAAG,CAEhC,OAAO,IAAI,CAAClS,MAAM,CAAChH,KAAK,CAAC8D,IAAI,GACzB,IAAI,CAAC0iB,OAAO,EAAI,AAAkC,MAAlC,IAAI,CAACA,OAAO,CAAClT,OAAO,CAACmT,QAAQ,CAC7Cze,EAAQC,KAAK,CAAC,IAAI,CAAE0J,EAC5B,CAEA,SAAS+U,GAAwB1e,CAAO,EACpC,GAAK,IAAI,CAAChI,KAAK,CAAC8D,IAAI,GAGf,CACD,IAAI6N,EAAOxJ,UACP2b,EAAOnS,CAAI,CAAC,EAAE,CACdlH,EAAQ,IAAI,CAACA,KAAK,CAElBsE,EAAW,IAAI,CAACtE,KAAK,CAACsE,QAAQ,CAClC,GAAI+U,EACA,IAAK,IAAI5K,EAAK,EAAGlQ,EAAKhC,AAHb,IAAI,CAGgBjH,MAAM,CAAEmZ,EAAKlQ,EAAGrG,MAAM,CAAEuW,IAAM,CACvD,IAAIpX,EAAQkH,CAAE,CAACkQ,EAAG,AACF,QAAZpX,EAAMrB,CAAC,GACPqB,EAAMqb,MAAM,CAAGrb,EAAMsY,SAAS,CAAC+C,MAAM,CACrCrb,EAAM6kB,MAAM,CAAG7kB,EAAMsY,SAAS,CAAC3Z,CAAC,CAChCqB,EAAMsY,SAAS,CAAC+C,MAAM,CAAG,EACpBpO,IACGjN,EAAM8kB,MAAM,CACZ9kB,EAAMsY,SAAS,CAAC3Z,CAAC,CACbqB,EAAMmB,KAAK,CACPwH,EAAMpC,SAAS,CAACvG,EAAM8kB,MAAM,EAGpC9kB,EAAMsY,SAAS,CAAC3Z,CAAC,CACbqB,EAAMmB,KAAK,CACNnB,CAAAA,EAAM+kB,QAAQ,CACX,CAAC/kB,EAAMqb,MAAM,CACbrb,EAAMqb,MAAM,AAAD,GAIvC,KAEC,CACD,IAAK,IAAIpK,EAAK,EAAGgN,EAAK/Y,AA3Bb,IAAI,CA2BgBjH,MAAM,CAAEgT,EAAKgN,EAAGpd,MAAM,CAAEoQ,IAAM,CACvD,IAAIjR,EAAQie,CAAE,CAAChN,EAAG,AACF,QAAZjR,EAAMrB,CAAC,GACPqB,EAAMsY,SAAS,CAAC+C,MAAM,CAAGrb,EAAMqb,MAAM,CACrCrb,EAAMsY,SAAS,CAAC3Z,CAAC,CAAGqB,EAAM6kB,MAAM,CAE5B7kB,EAAM0kB,OAAO,EACb1kB,EAAM0kB,OAAO,CAAC1kB,EAAMyjB,aAAa,CAC7B,OACA,UAAU,CAACzjB,EAAMsY,SAAS,CAAEpT,AApCnC,IAAI,CAoCsC5G,OAAO,CAAC8f,SAAS,EAGpE,CAEA,IAAI,CAAC4G,cAAc,EACvB,CACJ,MAjDI9e,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,GAkDrD,CAOA,SAAS4e,GAA0B/e,CAAO,CAAE9I,CAAI,CAAE8nB,CAAK,CAAEC,CAAW,CAAEC,CAAO,CAAElI,CAAM,EAqBjF,MApBa,oBAAT9f,GAA8BA,AAAS,gBAATA,GAC1B,IAAI,CAACc,KAAK,CAAC8D,IAAI,KACX,IAAI,CAAC5E,EAAK,EACV,OAAO,IAAI,CAACA,EAAK,CAEjB8f,IACK,IAAI,CAAChf,KAAK,CAACmnB,WAAW,EACvB,CAAA,IAAI,CAACnnB,KAAK,CAACmnB,WAAW,CAClB,IAAI,CAACnnB,KAAK,CAACkE,QAAQ,CAAC0Y,CAAC,CAAC,eAAe9W,GAAG,CAACkZ,EAAM,EAEvD,IAAI,CAAC9f,EAAK,CAAG,IAAI,CAACc,KAAK,CAACmnB,WAAW,CACnC,IAAI,CAACnnB,KAAK,CAACmnB,WAAW,CAACzd,IAAI,CAAC,IAAI,CAAC0d,UAAU,IAC3C,IAAI,CAACloB,EAAK,CAACmoB,OAAO,CAAG,CAAA,EACR,UAATnoB,GACAiJ,CAAAA,SAAS,CAAC,EAAE,CAAG,SAAQ,IAMhCH,EAAQC,KAAK,CAAC,IAAI,CAAEiO,MAAM/W,SAAS,CAAC+I,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,GACrE,CAEA,SAASmf,GAA6Btf,CAAO,EACzC,IAAI0B,EAAO1B,EAAQC,KAAK,CAAC,IAAI,CACzB,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,IAM7B,OALI,IAAI,CAACnI,KAAK,CAAC8D,IAAI,EAAI,IAAI,CAAC9D,KAAK,CAAC8D,IAAI,KAElC4F,EAAK6d,MAAM,CAAG,IAAI,CAACnnB,OAAO,CAAConB,SAAS,EAAI9d,EAAKxD,IAAI,CACjDwD,CAAI,CAAC,eAAe,CAAGqb,GAAyB,IAAI,CAAC3kB,OAAO,CAACqnB,SAAS,CAAE,IAErE/d,CACX,CAMA,SAASge,GAAyB1f,CAAO,CAAE2f,CAAK,CAAEzG,CAAO,EACrD,IAAIpd,EAAO,IAAI,CAAC9D,KAAK,CAAC8D,IAAI,EAAI,IAAI,CAAC9D,KAAK,CAAC8D,IAAI,GACzCA,GACA,CAAA,IAAI,CAAC1D,OAAO,CAACwnB,mBAAmB,CAAG,CAAA,CAAG,EAE1C5f,EAAQ3I,IAAI,CAAC,IAAI,CAAEsoB,EAAOzG,GACtBpd,GACA,CAAA,IAAI,CAAC1D,OAAO,CAACwnB,mBAAmB,CAAG,CAAA,CAAI,CAE/C,CAMA,SAASC,GAA2B7f,CAAO,CAAE8f,CAAG,EAE5C,GAAI9gB,AADS,IAAI,CACNhH,KAAK,CAAC8D,IAAI,GACjB,IAAK,IAAIoV,EAAK,EAAGlQ,EAAKhC,AAFb,IAAI,CAEgBjH,MAAM,CAAEmZ,EAAKlQ,EAAGrG,MAAM,CAAEuW,IAAM,CACvD,IAAIpX,EAAQkH,CAAE,CAACkQ,EAAG,AAClBpX,CAAAA,EAAM8C,OAAO,CAAG9C,EAAM1B,OAAO,CAACwE,OAAO,CAAGkjB,EACpC,AAAe,KAAA,IAARA,EACH,CAAC/C,GAAyB/d,AAN7B,IAAI,CAMgCpC,OAAO,CAAE9C,EAAM8C,OAAO,EAAIkjB,EACnE9gB,AAPK,IAAI,CAOF5G,OAAO,CAACmX,IAAI,CAACvQ,AAPf,IAAI,CAOkBuQ,IAAI,CAAC3D,OAAO,CAAC9R,GAAO,CAC3CA,EAAM1B,OAAO,CACb0B,EAAM0kB,OAAO,EACb1kB,EAAM0kB,OAAO,CAAC9c,IAAI,CAAC,CACfgT,WAAYoL,EAAM,UAAY,QAClC,EAER,CAEJ9f,EAAQC,KAAK,CAAC,IAAI,CAAEiO,MAAM/W,SAAS,CAAC+I,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,GAC9D,CAEA,SAAS4f,GAA0B/f,CAAO,EACtCA,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,IAEzC,IAAI,CAACnI,KAAK,CAAC8D,IAAI,IACf,IAAI,CAACkkB,iBAAiB,EAE9B,CAEA,SAASC,GAAyBjgB,CAAO,CAAElG,CAAK,CAAEomB,CAAU,CAAE9nB,CAAO,CAAE+nB,CAAO,EAC1E,IAAInoB,EAAQ,IAAI,CAACA,KAAK,CAKtB,GAFAI,EAAQmlB,aAAa,CAAGzjB,EAAMyjB,aAAa,CAEvCvlB,EAAM8D,IAAI,IACV,IAAI,CAACskB,EAAE,CAAC,UAAW,CACnB,IACIjR,EAAgBnQ,AADP,IAAI,CACU5G,OAAO,CAC9BioB,EAAStD,GAAyB3kB,EAAQioB,MAAM,CAAE,CAAC,CAACrhB,AAF3C,IAAI,CAE8C5G,OAAO,CAACmO,QAAQ,EAC3EpO,EAAYH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CACzCmoB,EAAUxmB,EAAMymB,UAAU,CAAG,GAAK,EAClCC,EAAa,CACTjoB,EAAG4nB,EAAQ5nB,CAAC,CAAG+nB,EACf7nB,EAAG0nB,EAAQ1nB,CAAC,CACZE,EAAGqG,AARE,IAAI,CAQCrG,CAAC,CAAGwW,EAAcvW,KAAK,CAAG,CACxC,CACAZ,CAAAA,EAAMK,QAAQ,GAIVgoB,IACAF,EAAQ9K,KAAK,CAAG,EAChBmL,EAAWjoB,CAAC,EAAIuB,EAAMsY,SAAS,CAAC+C,MAAM,CAAG,GAKzChd,EAAUe,KAAK,EAAI,IAAMf,EAAUe,KAAK,EAAI,KAC5CsnB,CAAAA,EAAW/nB,CAAC,EAAIqB,EAAMsY,SAAS,CAACiD,KAAK,AAAD,GAK5C8K,EAAQ5nB,CAAC,CAAGioB,AADZA,CAAAA,EAAazC,AAr2IJjmB,EAq2IoC,CAAC0oB,EAAW,CAAExoB,EAAO,CAAA,EAAM,CAAA,EAAM,CAAC,EAAE,AAAD,EACzDO,CAAC,CAAG+nB,EAE3BH,EAAQ1nB,CAAC,CAAGqB,EAAMyjB,aAAa,CAAG,KAAOiD,EAAW/nB,CAAC,AACzD,CACAuH,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,GACjD,CAKA,SAASsgB,GAA2BzgB,CAAO,EACvC,MAAQ,CAAEG,SAAS,CAAC,EAAE,CAACod,aAAa,EAChCvd,EAAQC,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,GAErD,CAKA,SAASugB,GAAyB1gB,CAAO,CAAE2gB,CAAa,EACpD,IAAIC,EAAW5gB,EAAQC,KAAK,CAAC,IAAI,CAC7B,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,IAGzBnI,EAAQ,IAAI,CAAC2K,IAAI,CAAC3K,KAAK,CACvB6oB,EAASF,EAActL,KAAK,CAChC,GAAIrd,EAAM8D,IAAI,IAAMglB,AAHJ,IAAI,CAGUtO,IAAI,CAAE,CAEhC,IAAIuO,EAAgB,CAAC,AAACD,AALV,IAAI,CAKgBtO,IAAI,CAAEwO,KAAK,CAAC,IAAI,CAAC,EAAE,CAC/CC,EAAejpB,EAAMgH,MAAM,CAAC+hB,EAAc,CAC1C5oB,EAAYH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAI7C,GAAI8oB,GACAA,AAAsB,WAAtBA,EAAallB,IAAI,CAAe,CAChC,IAAIykB,EAAa,CACTjoB,EAAGqoB,EAASroB,CAAC,CAAIP,CAAAA,EAAMK,QAAQ,CAAGuoB,EAASzL,MAAM,CAAG0L,EAAS,CAAA,EAC7DpoB,EAAGmoB,EAASnoB,CAAC,CACbE,EAAGsoB,EAAa7oB,OAAO,CAACQ,KAAK,CAAG,CACpC,CACAZ,CAAAA,EAAMK,QAAQ,GAGduoB,EAASvL,KAAK,CAAG,EAIbld,EAAUe,KAAK,EAAI,IAAMf,EAAUe,KAAK,EAAI,KAC5CsnB,CAAAA,EAAW/nB,CAAC,EAAIooB,CAAK,GAI7BD,EAASroB,CAAC,CAAGioB,AADbA,CAAAA,EAAazC,AA15IRjmB,EA05IwC,CAAC0oB,EAAW,CAAExoB,EAAO,CAAA,EAAM,CAAA,EAAM,CAAC,EAAE,AAAD,EACxDO,CAAC,CAAGsoB,EAAS,EACrCD,EAASnoB,CAAC,CAAG+nB,EAAW/nB,CAAC,AAC7B,CACJ,CACA,OAAOmoB,CACX,CAoEA,IAAIM,IACItrB,EAAgB,SAAUY,CAAC,CAC3BuX,CAAC,EAOD,MAAOnY,AANHA,CAAAA,EAAgBiB,OAAOmX,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU1X,CAAC,CAC1DuX,CAAC,EAAIvX,EAAEyX,SAAS,CAAGF,CAAG,GACd,SAAUvX,CAAC,CACnBuX,CAAC,EAAI,IAAK,IAAIN,KAAKM,EAAOlX,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC0W,EAC/DN,IAAIjX,CAAAA,CAAC,CAACiX,EAAE,CAAGM,CAAC,CAACN,EAAE,AAAD,CAAG,CAAA,EACIjX,EAAGuX,EAC5B,EACO,SAAUvX,CAAC,CAAEuX,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAImC,UAAU,uBAAyBC,OAAOpC,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAG5X,CAAG,CADtCZ,EAAcY,EAAGuX,GAEjBvX,EAAEW,SAAS,CAAG4W,AAAM,OAANA,EAAalX,OAAOwX,MAAM,CAACN,GAAMI,CAAAA,EAAGhX,SAAS,CAAG4W,EAAE5W,SAAS,CAAE,IAAIgX,CAAG,CACtF,GASAgT,GAA4B,SAAUrS,CAAM,EAE5C,SAASqS,IACL,OAAOrS,AAAW,OAAXA,GAAmBA,EAAO7O,KAAK,CAAC,IAAI,CAAEE,YAAc,IAAI,AACnE,CAcA,OAjBA+gB,GAAmBC,EAAYrS,GAY/BqS,EAAWhqB,SAAS,CAACiqB,QAAQ,CAAG,WAC5B,IAAIpgB,EACJ,MAAO,AAAC,CAAA,AAAuB,OAAtBA,CAAAA,EAAK,IAAI,CAAChC,MAAM,AAAD,GAAegC,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGhJ,KAAK,CAAC8D,IAAI,EAAC,EAC1E,EAAE,CAAGgT,EAAO3X,SAAS,CAACiqB,QAAQ,CAACnhB,KAAK,CAAC,IAAI,CAAEE,UACnD,EACOghB,CACX,EAzBe,AAAC5b,IAA2IG,WAAW,CAAC2b,GAAG,CAAClqB,SAAS,CAACmqB,UAAU,EA8C3LC,IACI3rB,EAAgB,SAAUY,CAAC,CAC3BuX,CAAC,EAOD,MAAOnY,AANHA,CAAAA,EAAgBiB,OAAOmX,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU1X,CAAC,CAC1DuX,CAAC,EAAIvX,EAAEyX,SAAS,CAAGF,CAAG,GACd,SAAUvX,CAAC,CACnBuX,CAAC,EAAI,IAAK,IAAIN,KAAKM,EAAOlX,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC0W,EAC/DN,IAAIjX,CAAAA,CAAC,CAACiX,EAAE,CAAGM,CAAC,CAACN,EAAE,AAAD,CAAG,CAAA,EACIjX,EAAGuX,EAC5B,EACO,SAAUvX,CAAC,CAAEuX,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAImC,UAAU,uBAAyBC,OAAOpC,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAG5X,CAAG,CADtCZ,EAAcY,EAAGuX,GAEjBvX,EAAEW,SAAS,CAAG4W,AAAM,OAANA,EAAalX,OAAOwX,MAAM,CAACN,GAAMI,CAAAA,EAAGhX,SAAS,CAAG4W,EAAE5W,SAAS,CAAE,IAAIgX,CAAG,CACtF,GAGAqT,GAAuB,AAAC/pB,IAA+E+N,QAAQ,CAAEic,GAAsB,AAAChqB,IAA+EG,OAAO,CAG9N8pB,GAAY,AAACnc,IAA2IG,WAAW,CAAC2b,GAAG,CAEvKM,GAAqB,AAAClqB,IAA+EmQ,MAAM,CAAEga,GAAmB,AAACnqB,IAA+EI,IAAI,CAAEgqB,GAAyB,AAACpqB,IAA+EmO,UAAU,CAMzUkc,GAA6B,SAAUhT,CAAM,EAE7C,SAASgT,IACL,OAAOhT,AAAW,OAAXA,GAAmBA,EAAO7O,KAAK,CAAC,IAAI,CAAEE,YAAc,IAAI,AACnE,CAuLA,OA1LAohB,GAAoBO,EAAahT,GASjCgT,EAAYnhB,OAAO,CAAG,SAAUoO,CAAW,EACnC8S,GAAuBL,GAAsB,UAC7CzS,CAAAA,EAAYwD,KAAK,CAAC8O,GAAG,CAAGS,CAAU,CAE1C,EASAA,EAAY3qB,SAAS,CAAC4qB,QAAQ,CAAG,WAC7BjT,EAAO3X,SAAS,CAAC4qB,QAAQ,CAAC9hB,KAAK,CAAC,IAAI,CAAEE,WAClC,IAAI,CAACnI,KAAK,CAAC8D,IAAI,IAEf,IAAI,CAACkmB,MAAM,CAAC,IAAI,CAACjG,WAAW,CAAE,CAAA,EAEtC,EAIA+F,EAAY3qB,SAAS,CAACkb,OAAO,CAAG,SAAUyJ,CAAI,EAC1C,GAAK,IAAI,CAAC9jB,KAAK,CAAC8D,IAAI,GAGf,CACD,IAAI8b,EAAS,IAAI,CAACA,MAAM,CACpBjR,EAAQ,IAAI,CAACA,KAAK,CAClBC,EAAc,IAAI,CAACA,WAAW,CAC9BsR,EAAY,IAAI,CAAC9f,OAAO,CAAC8f,SAAS,CAClC9G,EAAU,KAAK,CACD,EAAA,IAAd8G,GACAA,CAAAA,EAAY,CAAC,CAAA,EAGb4D,GAEAnV,EAAMsb,aAAa,CAAGL,GAAiBjb,EAAMsb,aAAa,CAAEtb,EAAME,UAAU,EAC5EF,EAAMub,aAAa,CAAGN,GAAiBjb,EAAMub,aAAa,CAAEvb,EAAMG,UAAU,EAC5EsK,EAAU,CACNvK,WAAY+Q,CAAM,CAAC,EAAE,CACrB9Q,WAAY8Q,CAAM,CAAC,EAAE,CACrBuK,OAAQ,KACRC,OAAQ,IACZ,EACAzb,EAAMjF,IAAI,CAAC0P,GACPxK,IACAA,EAAYyb,WAAW,CAAG1b,EAAM0b,WAAW,CAC3Czb,EAAYlF,IAAI,CAAC0P,MAKrBA,EAAU,CACNvK,WAAYF,EAAMsb,aAAa,CAC/Bnb,WAAYH,EAAMub,aAAa,CAC/BC,OAAQ,EACRC,OAAQ,CACZ,EACAzb,EAAM0L,OAAO,CAACjB,EAAS8G,GACnBtR,GACAA,EAAYyL,OAAO,CAACjB,EAAS8G,GAGzC,MAzCIpJ,EAAO3X,SAAS,CAACkb,OAAO,CAACpS,KAAK,CAAC,IAAI,CAAEE,UA0C7C,EAIA2hB,EAAY3qB,SAAS,CAACmrB,oBAAoB,CAAG,SAAUxoB,CAAK,CAAEI,CAAQ,EAClE,IAAIqoB,EAAgBzT,EAAO3X,SAAS,CAACmrB,oBAAoB,CAACjrB,IAAI,CAAC,IAAI,CAC/DyC,EACAI,GACJ,GAAI,IAAI,CAAClC,KAAK,CAAC8D,IAAI,GAcf,IAAK,IAbD3D,EAAY,IAAI,CAACH,KAAK,CAACI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAC9Cia,EAAYtY,EAAMsY,SAAS,CAC3B0G,EAAI1G,EAAU0G,CAAC,CAEfsB,EAAM,AAAChI,CAAAA,EAAUlZ,KAAK,EAAKf,CAAAA,MAAAA,EAA6C,KAAK,EAAIA,EAAUe,KAAK,AAAD,CAAC,EAC5FuoB,GACJe,EAAM,AAACpQ,CAAAA,EAAUnZ,IAAI,EAAKd,CAAAA,MAAAA,EAA6C,KAAK,EAAIA,EAAUc,IAAI,AAAD,CAAC,EAC1FwoB,GACJpH,EAAK,AAACjI,CAAAA,EAAU7Q,KAAK,CAAG6Q,EAAU5Q,GAAG,AAAD,EAAK,EACzCihB,EAAoBF,EAAcE,iBAAiB,CACnDC,EAAW,CAAC5J,EAAK,CAAA,EAAIzf,KAAKC,GAAG,CAAC8gB,EAAE,EAAK/gB,KAAKI,GAAG,CAAC4gB,GAC9CiG,EAAUxH,EAAKzf,CAAAA,KAAKC,GAAG,CAACkpB,GAAM,CAAA,EAAKnpB,KAAKC,GAAG,CAAC+gB,GAEvCrZ,EAAK,EAAG+J,EAAK,CAClBwX,MAAAA,EAAqD,KAAK,EAAIA,EAAcI,OAAO,CACnFF,EAAkBG,OAAO,CACzBH,EAAkBI,eAAe,CACpC,CAAE7hB,EAAK+J,EAAGpQ,MAAM,CAAEqG,IAAM,CACrB,IAAIpG,EAAcmQ,CAAE,CAAC/J,EAAG,AACxBpG,CAAAA,EAAYrC,CAAC,EAAI+nB,EACjB1lB,EAAYnC,CAAC,EAAIiqB,CACrB,CAEJ,OAAOH,CACX,EAIAT,EAAY3qB,SAAS,CAAC2rB,YAAY,CAAG,SAAUhpB,CAAK,EAChD,IAAI4H,EAAOoN,EAAO3X,SAAS,CAAC2rB,YAAY,CAAC7iB,KAAK,CAAC,IAAI,CAC/CE,WACA/H,EAAU,IAAI,CAACA,OAAO,CAK1B,OAJI,IAAI,CAACJ,KAAK,CAAC8D,IAAI,IAAM,CAAC,IAAI,CAAC9D,KAAK,CAACsG,UAAU,GAC3CoD,EAAK6d,MAAM,CAAGnnB,EAAQonB,SAAS,EAAI1lB,EAAMqB,KAAK,EAAI,IAAI,CAACA,KAAK,CAC5DuG,CAAI,CAAC,eAAe,CAAGkgB,GAAiBxpB,EAAQqnB,SAAS,CAAE,IAExD/d,CACX,EAIAogB,EAAY3qB,SAAS,CAACkJ,SAAS,CAAG,WAG9B,GAFAyO,EAAO3X,SAAS,CAACkJ,SAAS,CAACJ,KAAK,CAAC,IAAI,CAAEE,WAElC,IAAI,CAACnI,KAAK,CAAC8D,IAAI,IAGpB,IACIqT,EAAgBnQ,AADP,IAAI,CACU5G,OAAO,CAC9BQ,EAAQuW,EAAcvW,KAAK,EAAI,EAC/BT,EAAY6G,AAHH,IAAI,CAGMhH,KAAK,CAACI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAChDe,EAAQf,EAAUe,KAAK,CACvBD,EAAOd,EAAUc,IAAI,CACrBN,EAAIwW,EAAc5I,QAAQ,CACtB,AAAC4I,CAAAA,EAAcE,KAAK,EAAI,CAAA,EAAKzW,EAC7BoG,AARK,IAAI,CAQFkS,EAAE,CAAGtY,EACpBD,GAAKC,EAAQ,EACkB,CAAA,IAA3BuW,EAAcmO,QAAQ,EACtB3kB,CAAAA,EAAI,CAAA,EAER,IAAK,IAAIqI,EAAK,EAAG+J,EAAK/L,AAbT,IAAI,CAaYjH,MAAM,CAAEiJ,EAAK+J,EAAGpQ,MAAM,CAAEqG,IAAM,CACvD,IAAIlH,EAAQiR,CAAE,CAAC/J,EAAG,CACdoR,EAAYtY,EAAMsY,SAAS,AAC/BtY,CAAAA,EAAM8jB,SAAS,CAAG,QAClBxL,EAAUzZ,CAAC,CAAGA,EACdyZ,EAAUxZ,KAAK,CAAGA,AAAQ,IAARA,EAClBwZ,EAAUlZ,KAAK,CAAGA,EAClBkZ,EAAUnZ,IAAI,CAAGA,EACjBmZ,EAAUwF,MAAM,CAAG5Y,AArBV,IAAI,CAqBa4Y,MAAM,CAChC,IAAIuC,EAAQ,AAAC/H,CAAAA,EAAU5Q,GAAG,CAAG4Q,EAAU7Q,KAAK,AAAD,EAAK,CAChDzH,CAAAA,EAAMipB,iBAAiB,CAAG,CACtBlc,WAAYxN,KAAK6M,KAAK,CAAC7M,KAAKC,GAAG,CAAC6gB,GAC5BhL,EAAc6T,YAAY,CAC1B3pB,KAAKC,GAAG,CAACJ,EAAQuoB,KACrB3a,WAAYzN,KAAK6M,KAAK,CAAC7M,KAAKI,GAAG,CAAC0gB,GAC5BhL,EAAc6T,YAAY,CAC1B3pB,KAAKC,GAAG,CAACJ,EAAQuoB,IACzB,CACJ,EACJ,EAIAK,EAAY3qB,SAAS,CAAC8rB,WAAW,CAAG,WAGhC,GAFAnU,EAAO3X,SAAS,CAAC8rB,WAAW,CAAChjB,KAAK,CAAC,IAAI,CAAEE,WAEpC,IAAI,CAACnI,KAAK,CAAC8D,IAAI,GAGpB,IAAK,IAAIkF,EAAK,EAAG+J,EAAK,IAAI,CAAChT,MAAM,CAAEiJ,EAAK+J,EAAGpQ,MAAM,CAAEqG,IAAM,CACrD,IAAIlH,EAAQiR,CAAE,CAAC/J,EAAG,CAClB,GAAIlH,EAAM0kB,OAAO,CACb,IAAK,IAAIzG,EAAK,EAAGmL,EAAK,CAAC,MAAO,MAAO,QAAS,QAAQ,CAAEnL,EAAKmL,EAAGvoB,MAAM,CAAEod,IAAM,CAC1E,IAAIX,EAAO8L,CAAE,CAACnL,EAAG,AACbje,CAAAA,EAAM0kB,OAAO,EACb1kB,CAAAA,EAAM0kB,OAAO,CAACpH,EAAK,CAAC9L,OAAO,CAACxR,KAAK,CAAGA,CAAI,CAEhD,CAER,CACJ,EACOgoB,CACX,EAAEJ,IACFC,GAAmBG,GAAY3qB,SAAS,CAAE,CACtCmqB,WA5OiDH,EA6OrD,GAyBA,IAAIgC,GAA2JltB,EAAoB,KAC/KmtB,GAA+KntB,EAAoBI,CAAC,CAAC8sB,IAcrME,IACIztB,EAAgB,SAAUY,CAAC,CAC3BuX,CAAC,EAOD,MAAOnY,AANHA,CAAAA,EAAgBiB,OAAOmX,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU1X,CAAC,CAC1DuX,CAAC,EAAIvX,EAAEyX,SAAS,CAAGF,CAAG,GACd,SAAUvX,CAAC,CACnBuX,CAAC,EAAI,IAAK,IAAIN,KAAKM,EAAOlX,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC0W,EAC/DN,IAAIjX,CAAAA,CAAC,CAACiX,EAAE,CAAGM,CAAC,CAACN,EAAE,AAAD,CAAG,CAAA,EACIjX,EAAGuX,EAC5B,EACO,SAAUvX,CAAC,CAAEuX,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAImC,UAAU,uBAAyBC,OAAOpC,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAG5X,CAAG,CADtCZ,EAAcY,EAAGuX,GAEjBvX,EAAEW,SAAS,CAAG4W,AAAM,OAANA,EAAalX,OAAOwX,MAAM,CAACN,GAAMI,CAAAA,EAAGhX,SAAS,CAAG4W,EAAE5W,SAAS,CAAE,IAAIgX,CAAG,CACtF,GAGAmV,GAAe,AAACF,KAAmKjsB,SAAS,CAACmqB,UAAU,CAEvMiC,GAAyB,AAAC9rB,IAA+E+Y,OAAO,CAMhHgT,GAAgC,SAAU1U,CAAM,EAEhD,SAAS0U,IACL,OAAO1U,AAAW,OAAXA,GAAmBA,EAAO7O,KAAK,CAAC,IAAI,CAAEE,YAAc,IAAI,AACnE,CAaA,OAhBAkjB,GAAuBG,EAAgB1U,GASvC0U,EAAersB,SAAS,CAACssB,YAAY,CAAG,WAKpC,OAJA3U,EAAO3X,SAAS,CAACssB,YAAY,CAACxjB,KAAK,CAAC,IAAI,CAAEE,WACrCojB,GAAuB,IAAI,CAAC5qB,CAAC,GAC9B,CAAA,IAAI,CAACA,CAAC,CAAG,CAAA,EAEN,IAAI,AACf,EACO6qB,CACX,EAAEF,IA8H+BI,GApFH,CAC1BC,QAAS,CACLC,YAAa,0EACjB,CACJ,EA+FIC,IACIjuB,EAAgB,SAAUY,CAAC,CAC3BuX,CAAC,EAOD,MAAOnY,AANHA,CAAAA,EAAgBiB,OAAOmX,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU1X,CAAC,CAC1DuX,CAAC,EAAIvX,EAAEyX,SAAS,CAAGF,CAAG,GACd,SAAUvX,CAAC,CACnBuX,CAAC,EAAI,IAAK,IAAIN,KAAKM,EAAOlX,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC0W,EAC/DN,IAAIjX,CAAAA,CAAC,CAACiX,EAAE,CAAGM,CAAC,CAACN,EAAE,AAAD,CAAG,CAAA,EACIjX,EAAGuX,EAC5B,EACO,SAAUvX,CAAC,CAAEuX,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAImC,UAAU,uBAAyBC,OAAOpC,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAG5X,CAAG,CADtCZ,EAAcY,EAAGuX,GAEjBvX,EAAEW,SAAS,CAAG4W,AAAM,OAANA,EAAalX,OAAOwX,MAAM,CAACN,GAAMI,CAAAA,EAAGhX,SAAS,CAAG4W,EAAE5W,SAAS,CAAE,IAAIgX,CAAG,CACtF,GASA2V,GAAyB,AAACrsB,IAA+EmQ,MAAM,CAAEmc,GAAwB,AAACtsB,IAA+EgE,KAAK,CAa9NuoB,GAAiC,SAAUlV,CAAM,EAEjD,SAASkV,IACL,OAAOlV,AAAW,OAAXA,GAAmBA,EAAO7O,KAAK,CAAC,IAAI,CAAEE,YAAc,IAAI,AACnE,CAqBA,OAxBA0jB,GAAwBG,EAAiBlV,GASzCkV,EAAgB7sB,SAAS,CAAC2rB,YAAY,CAAG,SAAUhpB,CAAK,EACpD,IAAIsX,EAAUtC,EAAO3X,SAAS,CAAC2rB,YAAY,CAAC7iB,KAAK,CAAC,IAAI,CAClDE,WAKJ,OAJI,IAAI,CAACnI,KAAK,CAAC8D,IAAI,IAAMhC,GACrBsX,CAAAA,EAAQrT,MAAM,CACVkmB,EAAoCnqB,EAAO,IAAI,CAAC9B,KAAK,CAAA,EAEtDoZ,CACX,EAMA4S,EAAgB1oB,cAAc,CAAGyoB,GAAsB,AAACX,KAAmK9nB,cAAc,CAAEooB,IACpOM,CACX,EAAGZ,MACHU,GAAuBE,GAAgB7sB,SAAS,CAAE,CAC9C+sB,UAAW,CAAC,QAAS,QAAS,QAAQ,CAItCC,YAAa,CAAA,EACbC,eAAgB,CAAC,IAAK,IAAK,IAAI,CAC/BC,cAAe,CAAC,IAAK,IAAK,IAAI,CAC9B/C,WAjNyDkC,EAkN7D,GACAje,IAA0I+e,kBAAkB,CAAC,YAAaN,IAuB1K,IAAIO,GAAK9sB,IAET+sB,AA9xGmB,CAAA,CACf7jB,QAzEJ,SAAiB8jB,CAAe,EACxB7e,EAAWJ,EAAU,iBACrBK,EAAkB4e,EAAgBttB,SAAS,CAAE,eAAgB2O,EAErE,CAsEA,CAAA,EA4xGoBnF,OAAO,CAAC4jB,GAAEG,MAAM,CAACnS,KAAK,CAAC7X,IAAI,EAC/CiqB,AA9hFsDnb,GA8hFpC7I,OAAO,CAAC4jB,GAAEK,IAAI,CAAEL,GAAEM,IAAI,EACxCxf,EAAc1E,OAAO,CAAC4jB,GAAEO,KAAK,CAAEP,GAAEQ,EAAE,EACnCC,AAzqB0B,CAAA,CACtBrkB,QA9VJ,SAAqCoO,CAAW,CAAEkW,CAAc,EAC5D,GAAIjI,GAA+BJ,GAA8B,YAAa,CAC1E,IAAIsI,EAAcnW,EAAY5X,SAAS,CACnCguB,EAAiBF,EAAe9tB,SAAS,CACzC6J,EAAK+N,EAAYwD,KAAK,CACtB6S,EAAoBpkB,EAAGqkB,MAAM,CAC7BC,EAAyBtkB,EAAGukB,WAAW,CAI3C,GAHAtI,GAAyBiI,EAAa,iBAAkBjF,IACxDhD,GAAyBiI,EAAa,mBAAoBzE,IAC1DxD,GAAyBkI,EAAgB,cAAezE,IACpD0E,EAAmB,CACnB,IAAII,EAAoBJ,EAAkBjuB,SAAS,CAC/CsuB,EAAmBD,EAAkBlE,UAAU,CAACnqB,SAAS,AAC7DquB,CAAAA,EAAkBxW,iBAAiB,CAAG,WAA6B,EACnEwW,EAAkBxF,iBAAiB,CAAG9C,GACtCL,GAA6B2I,EAAmB,YAAavH,IAC7DhB,GAAyBwI,EAAkB,kBAAmBlH,IAC9DtB,GAAyBuI,EAAmB,UAAW9G,IACvDzB,GAAyBuI,EAAmB,YAAazG,IACzD9B,GAAyBuI,EAAmB,eAAgBlG,IAC5DrC,GAAyBuI,EAAmB,WAAY9F,IACxDzC,GAAyBuI,EAAmB,aAAc3F,IAC1D5C,GAAyBuI,EAAmB,YAAazF,GAC7D,CACA,GAAIuF,EAAwB,CACxB,IAAII,EAAyBJ,EAAuBnuB,SAAS,CAE7D8lB,GAD4ByI,EAAuBpE,UAAU,CAACnqB,SAAS,CACvB,kBAAmBonB,IACnEtB,GAAyByI,EAAwB,YAAa3G,IAC9D9B,GAAyByI,EAAwB,eAAgBpG,IACjErC,GAAyByI,EAAwB,WAAYhG,IAC7DzC,GAAyByI,EAAwB,aAAc7F,GACnE,CACJ,CACJ,CA6TA,CAAA,EAuqB6Blf,OAAO,CAAC4jB,GAAEG,MAAM,CAAG1U,MAChD2V,AAtUsD7D,GAsUpCnhB,OAAO,CAAC4jB,GAAEG,MAAM,EAClCkB,AAn5EoD/W,GAm5EpClO,OAAO,CAAC4jB,GAAEG,MAAM,EAChChK,GAAkB/Z,OAAO,CAACgN,KAAkJ2C,eAAe,IAC3LuV,AA7oC+CzK,GA6oCpCza,OAAO,CAAC4jB,GAAEO,KAAK,EACG,IAAIvtB,GAAqBgtB,GAG5C,OADYjtB,EAAoB,OAAU,AAE3C,GAET"}