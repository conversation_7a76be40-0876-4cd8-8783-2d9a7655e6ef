{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/themes/sand-signika\n * @requires highcharts\n *\n * (c) 2009-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/themes/sand-signika\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/themes/sand-signika\"] = factory(root[\"_Highcharts\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ sand_signika_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/Themes/SandSignika.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  Sand-Signika theme for Highcharts JS\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { setOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { addEvent, createElement } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Theme\n *\n * */\nvar SandSignikaTheme;\n(function (SandSignikaTheme) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    SandSignikaTheme.options = {\n        colors: [\n            '#f45b5b', '#8085e9', '#8d4654', '#7798BF', '#aaeeee',\n            '#ff0066', '#eeaaee', '#55BF3B', '#DF5353', '#7798BF', '#aaeeee'\n        ],\n        chart: {\n            backgroundColor: null,\n            style: {\n                fontFamily: 'Signika, serif'\n            }\n        },\n        title: {\n            style: {\n                color: 'black',\n                fontSize: '16px',\n                fontWeight: 'bold'\n            }\n        },\n        subtitle: {\n            style: {\n                color: 'black'\n            }\n        },\n        tooltip: {\n            borderWidth: 0\n        },\n        legend: {\n            backgroundColor: '#E0E0E8',\n            itemStyle: {\n                fontWeight: 'bold',\n                fontSize: '13px'\n            }\n        },\n        xAxis: {\n            labels: {\n                style: {\n                    color: '#6e6e70'\n                }\n            }\n        },\n        yAxis: {\n            labels: {\n                style: {\n                    color: '#6e6e70'\n                }\n            }\n        },\n        plotOptions: {\n            series: {\n                shadow: true\n            },\n            candlestick: {\n                lineColor: '#404048'\n            },\n            map: {\n                shadow: false\n            }\n        },\n        // Highcharts Stock specific\n        navigator: {\n            xAxis: {\n                gridLineColor: '#D0D0D8'\n            }\n        },\n        rangeSelector: {\n            buttonTheme: {\n                fill: 'white',\n                stroke: '#C0C0C8',\n                'stroke-width': 1,\n                states: {\n                    select: {\n                        fill: '#D0D0D8'\n                    }\n                }\n            }\n        },\n        scrollbar: {\n            trackBorderColor: '#C0C0C8'\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Apply the theme.\n     */\n    function apply() {\n        // Load the fonts\n        createElement('link', {\n            href: 'https://fonts.googleapis.com/css?family=Signika:400,700',\n            rel: 'stylesheet',\n            type: 'text/css'\n        }, null, document.getElementsByTagName('head')[0]);\n        // Add the background image to the container\n        addEvent((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).Chart, 'afterGetContainer', function () {\n            // eslint-disable-next-line no-invalid-this\n            this.container.style.background =\n                'url(https://www.highcharts.com/samples/graphics/sand.png)';\n        });\n        // Apply the theme\n        setOptions(SandSignikaTheme.options);\n    }\n    SandSignikaTheme.apply = apply;\n})(SandSignikaTheme || (SandSignikaTheme = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const SandSignika = (SandSignikaTheme);\n\n;// ./code/es-modules/masters/themes/sand-signika.js\n\n\n\n\n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).theme = SandSignika.options;\nSandSignika.apply();\n/* harmony default export */ const sand_signika_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "SandSignikaTheme", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "sand_signika_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "setOptions", "addEvent", "createElement", "options", "colors", "chart", "backgroundColor", "style", "fontFamily", "title", "color", "fontSize", "fontWeight", "subtitle", "tooltip", "borderWidth", "legend", "itemStyle", "xAxis", "labels", "yAxis", "plotOptions", "series", "shadow", "candlestick", "lineColor", "map", "navigator", "gridLineColor", "rangeSelector", "buttonTheme", "fill", "stroke", "states", "select", "scrollbar", "trackBorderColor", "apply", "href", "rel", "type", "document", "getElementsByTagName", "Chart", "container", "background", "SandSignika", "theme"], "mappings": "CASA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,EACrC,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,iCAAkC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAM,GACnG,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,iCAAiC,CAAGD,EAAQD,EAAK,WAAc,EAEvEA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,AAACC,GACnC,AAAC,CAAA,KACP,aACA,IAmGNC,EAnGUC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAerH,GAAM,CAAEE,WAAAA,CAAU,CAAE,CAAID,IAGlB,CAAEE,SAAAA,CAAQ,CAAEC,cAAAA,CAAa,CAAE,CAAIH,KAOrC,AAAC,SAAU3B,CAAgB,EAMvBA,EAAiB+B,OAAO,CAAG,CACvBC,OAAQ,CACJ,UAAW,UAAW,UAAW,UAAW,UAC5C,UAAW,UAAW,UAAW,UAAW,UAAW,UAC1D,CACDC,MAAO,CACHC,gBAAiB,KACjBC,MAAO,CACHC,WAAY,gBAChB,CACJ,EACAC,MAAO,CACHF,MAAO,CACHG,MAAO,QACPC,SAAU,OACVC,WAAY,MAChB,CACJ,EACAC,SAAU,CACNN,MAAO,CACHG,MAAO,OACX,CACJ,EACAI,QAAS,CACLC,YAAa,CACjB,EACAC,OAAQ,CACJV,gBAAiB,UACjBW,UAAW,CACPL,WAAY,OACZD,SAAU,MACd,CACJ,EACAO,MAAO,CACHC,OAAQ,CACJZ,MAAO,CACHG,MAAO,SACX,CACJ,CACJ,EACAU,MAAO,CACHD,OAAQ,CACJZ,MAAO,CACHG,MAAO,SACX,CACJ,CACJ,EACAW,YAAa,CACTC,OAAQ,CACJC,OAAQ,CAAA,CACZ,EACAC,YAAa,CACTC,UAAW,SACf,EACAC,IAAK,CACDH,OAAQ,CAAA,CACZ,CACJ,EAEAI,UAAW,CACPT,MAAO,CACHU,cAAe,SACnB,CACJ,EACAC,cAAe,CACXC,YAAa,CACTC,KAAM,QACNC,OAAQ,UACR,eAAgB,EAChBC,OAAQ,CACJC,OAAQ,CACJH,KAAM,SACV,CACJ,CACJ,CACJ,EACAI,UAAW,CACPC,iBAAkB,SACtB,CACJ,EAyBAhE,EAAiBiE,KAAK,CAhBtB,WAEInC,EAAc,OAAQ,CAClBoC,KAAM,0DACNC,IAAK,aACLC,KAAM,UACV,EAAG,KAAMC,SAASC,oBAAoB,CAAC,OAAO,CAAC,EAAE,EAEjDzC,EAAS,AAACF,IAA+E4C,KAAK,CAAE,oBAAqB,WAEjH,IAAI,CAACC,SAAS,CAACrC,KAAK,CAACsC,UAAU,CAC3B,2DACR,GAEA7C,EAAW5B,EAAiB+B,OAAO,CACvC,CAEJ,EAAG/B,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,GAMf,IAAM0E,EAAe1E,CAOlD,CAAC2B,IAA+EgD,KAAK,CAAGD,EAAY3C,OAAO,CAC3G2C,EAAYT,KAAK,GACY,IAAMxC,EAAqBE,IAG9C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}