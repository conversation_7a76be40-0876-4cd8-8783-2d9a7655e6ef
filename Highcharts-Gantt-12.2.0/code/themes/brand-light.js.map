{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/themes/brand-light\n * @requires highcharts\n *\n * (c) 2009-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/themes/brand-light\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/themes/brand-light\"] = factory(root[\"_Highcharts\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ brand_light_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/Themes/BrandLight.js\n/* *\n *\n *   (c) 2010-2025 Highsoft AS\n *\n *  Author: Nancy Dillon\n *\n *  License: www.highcharts.com/license\n *\n *  Light theme based on Highcharts brand system\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { setOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { createElement } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Theme\n *\n * */\nvar BrandLightTheme;\n(function (BrandLightTheme) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    BrandLightTheme.options = {\n        colors: [\n            '#8087E8', '#A3EDBA', '#F19E53', '#6699A1',\n            '#E1D369', '#87B4E7', '#DA6D85', '#BBBAC5'\n        ],\n        chart: {\n            backgroundColor: '#f0f0f0',\n            style: {\n                fontFamily: 'IBM Plex Sans, sans-serif'\n            }\n        },\n        title: {\n            style: {\n                fontSize: '22px',\n                fontWeight: '500',\n                color: '#2F2B38'\n            }\n        },\n        subtitle: {\n            style: {\n                fontSize: '16px',\n                fontWeight: '400',\n                color: '#2F2B38'\n            }\n        },\n        tooltip: {\n            borderWidth: 0,\n            backgroundColor: '#46465C',\n            style: {\n                color: '#f0f0f0'\n            },\n            shadow: true\n        },\n        legend: {\n            backgroundColor: '#f0f0f0',\n            borderColor: '#BBBAC5',\n            borderWidth: 1,\n            borderRadius: 2,\n            itemStyle: {\n                fontWeight: '400',\n                fontSize: '12px',\n                color: '#2F2B38'\n            },\n            itemHoverStyle: {\n                fontWeight: '700',\n                color: '#46465C'\n            }\n        },\n        navigation: {\n            buttonOptions: {\n                symbolStroke: '#2F2B38',\n                theme: {\n                    fill: '#fff',\n                    states: {\n                        hover: {\n                            stroke: '#46465C',\n                            fill: '#fff'\n                        },\n                        select: {\n                            stroke: '#46465C',\n                            fill: '#fff'\n                        }\n                    }\n                }\n            }\n        },\n        credits: {\n            style: {\n                color: '#46465C'\n            }\n        },\n        drilldown: {\n            activeAxisLabelStyle: {\n                color: '#2F2B38'\n            },\n            activeDataLabelStyle: {\n                color: '#2F2B38'\n            },\n            drillUpButton: {\n                theme: {\n                    fill: '#2F2B38',\n                    style: {\n                        color: '#fff'\n                    }\n                }\n            }\n        },\n        xAxis: {\n            gridLineColor: '#ccc',\n            labels: {\n                style: {\n                    color: '#46465C',\n                    fontSize: '12px'\n                }\n            },\n            lineColor: '#ccc',\n            minorGridLineColor: '#ebebeb',\n            tickColor: '#ccc',\n            title: {\n                style: {\n                    color: '#2F2B38'\n                }\n            }\n        },\n        yAxis: {\n            gridLineColor: '#ccc',\n            labels: {\n                style: {\n                    color: '#46465C',\n                    fontSize: '12px'\n                }\n            },\n            lineColor: '#ccc',\n            minorGridLineColor: '#ebebeb',\n            tickColor: '#ccc',\n            tickWidth: 1,\n            title: {\n                style: {\n                    color: '#2F2B38',\n                    fontWeight: '300'\n                }\n            }\n        },\n        // Scroll charts\n        rangeSelector: {\n            buttonTheme: {\n                fill: '#fff',\n                style: {\n                    color: '#46465C',\n                    stroke: 'transparent'\n                },\n                states: {\n                    hover: {\n                        fill: '#fff',\n                        style: {\n                            color: '#46465C'\n                        },\n                        'stroke-width': 1,\n                        stroke: '#46465C'\n                    },\n                    select: {\n                        fill: '#fff',\n                        style: {\n                            color: '#46465C'\n                        },\n                        'stroke-width': 1,\n                        stroke: '#46465C'\n                    }\n                }\n            },\n            inputBoxBorderColor: '#BBBAC5',\n            inputStyle: {\n                backgroundColor: '#fff',\n                color: '#46465C'\n            },\n            labelStyle: {\n                color: '#46465C'\n            }\n        },\n        scrollbar: {\n            barBackgroundColor: '#BBBAC5',\n            barBorderColor: '#808083',\n            buttonArrowColor: '#fff',\n            buttonBackgroundColor: '#BBBAC5',\n            buttonBorderColor: '#46465C',\n            rifleColor: '#FFF',\n            trackBackgroundColor: '#dedede',\n            trackBorderColor: '#BBBAC5'\n        },\n        plotOptions: {\n            series: {\n                borderWidth: 1,\n                borderColor: '#BBBAC5',\n                dataLabels: {\n                    color: '#46465C',\n                    style: {\n                        fontSize: '13px'\n                    }\n                },\n                marker: {\n                    lineColor: '#46465C'\n                }\n            },\n            boxplot: {\n                fillColor: '#505053'\n            },\n            candlestick: {\n                lineColor: null,\n                upColor: '#DA6D85',\n                upLineColor: '#DA6D85'\n            },\n            errorbar: {\n                color: 'white'\n            },\n            map: {\n                borderColor: 'rgba(200, 200, 200, 0.3)',\n                nullColor: 'rgba(200, 200, 200, 0.3)'\n            }\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Apply the theme.\n     */\n    function apply() {\n        // Load the fonts\n        createElement('link', {\n            href: 'https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@200;300;400;600;700',\n            rel: 'stylesheet',\n            type: 'text/css'\n        }, null, document.getElementsByTagName('head')[0]);\n        // Apply the theme\n        setOptions(BrandLightTheme.options);\n    }\n    BrandLightTheme.apply = apply;\n})(BrandLightTheme || (BrandLightTheme = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const BrandLight = (BrandLightTheme);\n\n;// ./code/es-modules/masters/themes/brand-light.js\n\n\n\n\n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).theme = BrandLight.options;\nBrandLight.apply();\n/* harmony default export */ const brand_light_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "BrandLightTheme", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "brand_light_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "setOptions", "createElement", "options", "colors", "chart", "backgroundColor", "style", "fontFamily", "title", "fontSize", "fontWeight", "color", "subtitle", "tooltip", "borderWidth", "shadow", "legend", "borderColor", "borderRadius", "itemStyle", "itemHoverStyle", "navigation", "buttonOptions", "symbolStroke", "theme", "fill", "states", "hover", "stroke", "select", "credits", "drilldown", "activeAxisLabelStyle", "activeDataLabelStyle", "drillUpButton", "xAxis", "gridLineColor", "labels", "lineColor", "minorGridLineColor", "tickColor", "yAxis", "tickWidth", "rangeSelector", "buttonTheme", "inputBoxBorderColor", "inputStyle", "labelStyle", "scrollbar", "barBackgroundColor", "barBorderColor", "buttonArrowColor", "buttonBackgroundColor", "buttonBorderColor", "rifleColor", "trackBackgroundColor", "trackBorderColor", "plotOptions", "series", "dataLabels", "marker", "boxplot", "fillColor", "candlestick", "upColor", "upLineColor", "errorbar", "map", "nullColor", "apply", "href", "rel", "type", "document", "getElementsByTagName", "BrandLight"], "mappings": "CASA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,EACrC,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,gCAAiC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAM,GAClG,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,gCAAgC,CAAGD,EAAQD,EAAK,WAAc,EAEtEA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,AAACC,GACnC,AAAC,CAAA,KACP,aACA,IAoGNC,EApGUC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAiBrH,GAAM,CAAEE,WAAAA,CAAU,CAAE,CAAID,IAElB,CAAEE,cAAAA,CAAa,CAAE,CAAIF,KAO3B,AAAC,SAAU3B,CAAe,EAMtBA,EAAgB8B,OAAO,CAAG,CACtBC,OAAQ,CACJ,UAAW,UAAW,UAAW,UACjC,UAAW,UAAW,UAAW,UACpC,CACDC,MAAO,CACHC,gBAAiB,UACjBC,MAAO,CACHC,WAAY,2BAChB,CACJ,EACAC,MAAO,CACHF,MAAO,CACHG,SAAU,OACVC,WAAY,MACZC,MAAO,SACX,CACJ,EACAC,SAAU,CACNN,MAAO,CACHG,SAAU,OACVC,WAAY,MACZC,MAAO,SACX,CACJ,EACAE,QAAS,CACLC,YAAa,EACbT,gBAAiB,UACjBC,MAAO,CACHK,MAAO,SACX,EACAI,OAAQ,CAAA,CACZ,EACAC,OAAQ,CACJX,gBAAiB,UACjBY,YAAa,UACbH,YAAa,EACbI,aAAc,EACdC,UAAW,CACPT,WAAY,MACZD,SAAU,OACVE,MAAO,SACX,EACAS,eAAgB,CACZV,WAAY,MACZC,MAAO,SACX,CACJ,EACAU,WAAY,CACRC,cAAe,CACXC,aAAc,UACdC,MAAO,CACHC,KAAM,OACNC,OAAQ,CACJC,MAAO,CACHC,OAAQ,UACRH,KAAM,MACV,EACAI,OAAQ,CACJD,OAAQ,UACRH,KAAM,MACV,CACJ,CACJ,CACJ,CACJ,EACAK,QAAS,CACLxB,MAAO,CACHK,MAAO,SACX,CACJ,EACAoB,UAAW,CACPC,qBAAsB,CAClBrB,MAAO,SACX,EACAsB,qBAAsB,CAClBtB,MAAO,SACX,EACAuB,cAAe,CACXV,MAAO,CACHC,KAAM,UACNnB,MAAO,CACHK,MAAO,MACX,CACJ,CACJ,CACJ,EACAwB,MAAO,CACHC,cAAe,OACfC,OAAQ,CACJ/B,MAAO,CACHK,MAAO,UACPF,SAAU,MACd,CACJ,EACA6B,UAAW,OACXC,mBAAoB,UACpBC,UAAW,OACXhC,MAAO,CACHF,MAAO,CACHK,MAAO,SACX,CACJ,CACJ,EACA8B,MAAO,CACHL,cAAe,OACfC,OAAQ,CACJ/B,MAAO,CACHK,MAAO,UACPF,SAAU,MACd,CACJ,EACA6B,UAAW,OACXC,mBAAoB,UACpBC,UAAW,OACXE,UAAW,EACXlC,MAAO,CACHF,MAAO,CACHK,MAAO,UACPD,WAAY,KAChB,CACJ,CACJ,EAEAiC,cAAe,CACXC,YAAa,CACTnB,KAAM,OACNnB,MAAO,CACHK,MAAO,UACPiB,OAAQ,aACZ,EACAF,OAAQ,CACJC,MAAO,CACHF,KAAM,OACNnB,MAAO,CACHK,MAAO,SACX,EACA,eAAgB,EAChBiB,OAAQ,SACZ,EACAC,OAAQ,CACJJ,KAAM,OACNnB,MAAO,CACHK,MAAO,SACX,EACA,eAAgB,EAChBiB,OAAQ,SACZ,CACJ,CACJ,EACAiB,oBAAqB,UACrBC,WAAY,CACRzC,gBAAiB,OACjBM,MAAO,SACX,EACAoC,WAAY,CACRpC,MAAO,SACX,CACJ,EACAqC,UAAW,CACPC,mBAAoB,UACpBC,eAAgB,UAChBC,iBAAkB,OAClBC,sBAAuB,UACvBC,kBAAmB,UACnBC,WAAY,OACZC,qBAAsB,UACtBC,iBAAkB,SACtB,EACAC,YAAa,CACTC,OAAQ,CACJ5C,YAAa,EACbG,YAAa,UACb0C,WAAY,CACRhD,MAAO,UACPL,MAAO,CACHG,SAAU,MACd,CACJ,EACAmD,OAAQ,CACJtB,UAAW,SACf,CACJ,EACAuB,QAAS,CACLC,UAAW,SACf,EACAC,YAAa,CACTzB,UAAW,KACX0B,QAAS,UACTC,YAAa,SACjB,EACAC,SAAU,CACNvD,MAAO,OACX,EACAwD,IAAK,CACDlD,YAAa,2BACbmD,UAAW,0BACf,CACJ,CACJ,EAmBAhG,EAAgBiG,KAAK,CAVrB,WAEIpE,EAAc,OAAQ,CAClBqE,KAAM,kFACNC,IAAK,aACLC,KAAM,UACV,EAAG,KAAMC,SAASC,oBAAoB,CAAC,OAAO,CAAC,EAAE,EAEjD1E,EAAW5B,EAAgB8B,OAAO,CACtC,CAEJ,EAAG9B,GAAoBA,CAAAA,EAAkB,CAAC,CAAA,GAMb,IAAMuG,EAAcvG,CAOjD,CAAC2B,IAA+EyB,KAAK,CAAGmD,EAAWzE,OAAO,CAC1GyE,EAAWN,KAAK,GACa,IAAMxE,EAAoBE,IAG7C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}