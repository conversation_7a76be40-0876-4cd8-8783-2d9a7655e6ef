{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/themes/brand-dark\n * @requires highcharts\n *\n * (c) 2009-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/themes/brand-dark\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/themes/brand-dark\"] = factory(root[\"_Highcharts\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ brand_dark_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/Themes/BrandDark.js\n/* *\n *\n *   (c) 2010-2025 Highsoft AS\n *\n *  Author: Nancy Dillon\n *\n *  License: www.highcharts.com/license\n *\n *  Dark theme based on Highcharts brand system\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { setOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { createElement } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Theme\n *\n * */\nvar BrandDarkTheme;\n(function (BrandDarkTheme) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    BrandDarkTheme.options = {\n        colors: [\n            '#8087E8', '#A3EDBA', '#F19E53', '#6699A1',\n            '#E1D369', '#87B4E7', '#DA6D85', '#BBBAC5'\n        ],\n        chart: {\n            backgroundColor: {\n                linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },\n                stops: [\n                    [0, '#1f1836'],\n                    [1, '#45445d']\n                ]\n            },\n            style: {\n                fontFamily: 'IBM Plex Sans, sans-serif'\n            }\n        },\n        title: {\n            style: {\n                fontSize: '22px',\n                fontWeight: '500',\n                color: '#fff'\n            }\n        },\n        subtitle: {\n            style: {\n                fontSize: '16px',\n                fontWeight: '400',\n                color: '#fff'\n            }\n        },\n        credits: {\n            style: {\n                color: '#f0f0f0'\n            }\n        },\n        caption: {\n            style: {\n                color: '#f0f0f0'\n            }\n        },\n        tooltip: {\n            borderWidth: 0,\n            backgroundColor: '#f0f0f0',\n            shadow: true\n        },\n        legend: {\n            backgroundColor: 'transparent',\n            itemStyle: {\n                fontWeight: '400',\n                fontSize: '12px',\n                color: '#fff'\n            },\n            itemHoverStyle: {\n                fontWeight: '700',\n                color: '#fff'\n            }\n        },\n        plotOptions: {\n            series: {\n                dataLabels: {\n                    color: '#46465C',\n                    style: {\n                        fontSize: '13px'\n                    }\n                },\n                marker: {\n                    lineColor: '#333'\n                }\n            },\n            boxplot: {\n                fillColor: '#505053'\n            },\n            candlestick: {\n                lineColor: null,\n                upColor: '#DA6D85',\n                upLineColor: '#DA6D85'\n            },\n            errorbar: {\n                color: 'white'\n            },\n            dumbbell: {\n                lowColor: '#f0f0f0'\n            },\n            map: {\n                borderColor: '#909090',\n                nullColor: '#78758C'\n            }\n        },\n        drilldown: {\n            activeAxisLabelStyle: {\n                color: '#F0F0F3'\n            },\n            activeDataLabelStyle: {\n                color: '#F0F0F3'\n            },\n            drillUpButton: {\n                theme: {\n                    fill: '#fff'\n                }\n            }\n        },\n        xAxis: {\n            gridLineColor: '#707073',\n            labels: {\n                style: {\n                    color: '#fff',\n                    fontSize: '12px'\n                }\n            },\n            lineColor: '#707073',\n            minorGridLineColor: '#505053',\n            tickColor: '#707073',\n            title: {\n                style: {\n                    color: '#fff'\n                }\n            }\n        },\n        yAxis: {\n            gridLineColor: '#707073',\n            labels: {\n                style: {\n                    color: '#fff',\n                    fontSize: '12px'\n                }\n            },\n            lineColor: '#707073',\n            minorGridLineColor: '#505053',\n            tickColor: '#707073',\n            tickWidth: 1,\n            title: {\n                style: {\n                    color: '#fff',\n                    fontWeight: '300'\n                }\n            }\n        },\n        colorAxis: {\n            gridLineColor: '#45445d',\n            labels: {\n                style: {\n                    color: '#fff',\n                    fontSize: '12px'\n                }\n            },\n            minColor: '#342f95',\n            maxColor: '#2caffe',\n            tickColor: '#45445d'\n        },\n        mapNavigation: {\n            enabled: true,\n            buttonOptions: {\n                theme: {\n                    fill: '#46465C',\n                    'stroke-width': 1,\n                    stroke: '#BBBAC5',\n                    r: 2,\n                    style: {\n                        color: '#fff'\n                    },\n                    states: {\n                        hover: {\n                            fill: '#000',\n                            'stroke-width': 1,\n                            stroke: '#f0f0f0',\n                            style: {\n                                color: '#fff'\n                            }\n                        },\n                        select: {\n                            fill: '#000',\n                            'stroke-width': 1,\n                            stroke: '#f0f0f0',\n                            style: {\n                                color: '#fff'\n                            }\n                        }\n                    }\n                }\n            }\n        },\n        // Scroll charts\n        rangeSelector: {\n            buttonTheme: {\n                fill: '#46465C',\n                stroke: '#BBBAC5',\n                'stroke-width': 1,\n                style: {\n                    color: '#fff'\n                },\n                states: {\n                    hover: {\n                        fill: '#1f1836',\n                        style: {\n                            color: '#fff'\n                        },\n                        'stroke-width': 1,\n                        stroke: 'white'\n                    },\n                    select: {\n                        fill: '#1f1836',\n                        style: {\n                            color: '#fff'\n                        },\n                        'stroke-width': 1,\n                        stroke: 'white'\n                    }\n                }\n            },\n            inputBoxBorderColor: '#BBBAC5',\n            inputStyle: {\n                backgroundColor: '#2F2B38',\n                color: '#fff'\n            },\n            labelStyle: {\n                color: '#fff'\n            }\n        },\n        navigator: {\n            handles: {\n                backgroundColor: '#BBBAC5',\n                borderColor: '#2F2B38'\n            },\n            outlineColor: '#CCC',\n            maskFill: 'rgba(255,255,255,0.1)',\n            series: {\n                color: '#A3EDBA',\n                lineColor: '#A3EDBA'\n            },\n            xAxis: {\n                gridLineColor: '#505053'\n            }\n        },\n        scrollbar: {\n            barBackgroundColor: '#BBBAC5',\n            barBorderColor: '#808083',\n            buttonArrowColor: '#2F2B38',\n            buttonBackgroundColor: '#BBBAC5',\n            buttonBorderColor: '#2F2B38',\n            rifleColor: '#2F2B38',\n            trackBackgroundColor: '#78758C',\n            trackBorderColor: '#2F2B38'\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Apply the theme.\n     */\n    function apply() {\n        // Load the fonts\n        createElement('link', {\n            href: 'https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@200;300;400;600;700',\n            rel: 'stylesheet',\n            type: 'text/css'\n        }, null, document.getElementsByTagName('head')[0]);\n        // Apply the theme\n        setOptions(BrandDarkTheme.options);\n    }\n    BrandDarkTheme.apply = apply;\n})(BrandDarkTheme || (BrandDarkTheme = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const BrandDark = (BrandDarkTheme);\n\n;// ./code/es-modules/masters/themes/brand-dark.js\n\n\n\n\n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).theme = BrandDark.options;\nBrandDark.apply();\n/* harmony default export */ const brand_dark_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "BrandDarkTheme", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "brand_dark_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "setOptions", "createElement", "options", "colors", "chart", "backgroundColor", "linearGradient", "x1", "y1", "x2", "y2", "stops", "style", "fontFamily", "title", "fontSize", "fontWeight", "color", "subtitle", "credits", "caption", "tooltip", "borderWidth", "shadow", "legend", "itemStyle", "itemHoverStyle", "plotOptions", "series", "dataLabels", "marker", "lineColor", "boxplot", "fillColor", "candlestick", "upColor", "upLineColor", "errorbar", "dumbbell", "lowColor", "map", "borderColor", "nullColor", "drilldown", "activeAxisLabelStyle", "activeDataLabelStyle", "drillUpButton", "theme", "fill", "xAxis", "gridLineColor", "labels", "minorGridLineColor", "tickColor", "yAxis", "tickWidth", "colorAxis", "minColor", "maxColor", "mapNavigation", "enabled", "buttonOptions", "stroke", "r", "states", "hover", "select", "rangeSelector", "buttonTheme", "inputBoxBorderColor", "inputStyle", "labelStyle", "navigator", "handles", "outlineColor", "maskFill", "scrollbar", "barBackgroundColor", "barBorderColor", "buttonArrowColor", "buttonBackgroundColor", "buttonBorderColor", "rifleColor", "trackBackgroundColor", "trackBorderColor", "apply", "href", "rel", "type", "document", "getElementsByTagName", "BrandDark"], "mappings": "CASA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,EACrC,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,+BAAgC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAM,GACjG,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,+BAA+B,CAAGD,EAAQD,EAAK,WAAc,EAErEA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,AAACC,GACnC,AAAC,CAAA,KACP,aACA,IAoGNC,EApGUC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAiBrH,GAAM,CAAEE,WAAAA,CAAU,CAAE,CAAID,IAElB,CAAEE,cAAAA,CAAa,CAAE,CAAIF,KAO3B,AAAC,SAAU3B,CAAc,EAMrBA,EAAe8B,OAAO,CAAG,CACrBC,OAAQ,CACJ,UAAW,UAAW,UAAW,UACjC,UAAW,UAAW,UAAW,UACpC,CACDC,MAAO,CACHC,gBAAiB,CACbC,eAAgB,CAAEC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGC,GAAI,CAAE,EAC7CC,MAAO,CACH,CAAC,EAAG,UAAU,CACd,CAAC,EAAG,UAAU,CACjB,AACL,EACAC,MAAO,CACHC,WAAY,2BAChB,CACJ,EACAC,MAAO,CACHF,MAAO,CACHG,SAAU,OACVC,WAAY,MACZC,MAAO,MACX,CACJ,EACAC,SAAU,CACNN,MAAO,CACHG,SAAU,OACVC,WAAY,MACZC,MAAO,MACX,CACJ,EACAE,QAAS,CACLP,MAAO,CACHK,MAAO,SACX,CACJ,EACAG,QAAS,CACLR,MAAO,CACHK,MAAO,SACX,CACJ,EACAI,QAAS,CACLC,YAAa,EACbjB,gBAAiB,UACjBkB,OAAQ,CAAA,CACZ,EACAC,OAAQ,CACJnB,gBAAiB,cACjBoB,UAAW,CACPT,WAAY,MACZD,SAAU,OACVE,MAAO,MACX,EACAS,eAAgB,CACZV,WAAY,MACZC,MAAO,MACX,CACJ,EACAU,YAAa,CACTC,OAAQ,CACJC,WAAY,CACRZ,MAAO,UACPL,MAAO,CACHG,SAAU,MACd,CACJ,EACAe,OAAQ,CACJC,UAAW,MACf,CACJ,EACAC,QAAS,CACLC,UAAW,SACf,EACAC,YAAa,CACTH,UAAW,KACXI,QAAS,UACTC,YAAa,SACjB,EACAC,SAAU,CACNpB,MAAO,OACX,EACAqB,SAAU,CACNC,SAAU,SACd,EACAC,IAAK,CACDC,YAAa,UACbC,UAAW,SACf,CACJ,EACAC,UAAW,CACPC,qBAAsB,CAClB3B,MAAO,SACX,EACA4B,qBAAsB,CAClB5B,MAAO,SACX,EACA6B,cAAe,CACXC,MAAO,CACHC,KAAM,MACV,CACJ,CACJ,EACAC,MAAO,CACHC,cAAe,UACfC,OAAQ,CACJvC,MAAO,CACHK,MAAO,OACPF,SAAU,MACd,CACJ,EACAgB,UAAW,UACXqB,mBAAoB,UACpBC,UAAW,UACXvC,MAAO,CACHF,MAAO,CACHK,MAAO,MACX,CACJ,CACJ,EACAqC,MAAO,CACHJ,cAAe,UACfC,OAAQ,CACJvC,MAAO,CACHK,MAAO,OACPF,SAAU,MACd,CACJ,EACAgB,UAAW,UACXqB,mBAAoB,UACpBC,UAAW,UACXE,UAAW,EACXzC,MAAO,CACHF,MAAO,CACHK,MAAO,OACPD,WAAY,KAChB,CACJ,CACJ,EACAwC,UAAW,CACPN,cAAe,UACfC,OAAQ,CACJvC,MAAO,CACHK,MAAO,OACPF,SAAU,MACd,CACJ,EACA0C,SAAU,UACVC,SAAU,UACVL,UAAW,SACf,EACAM,cAAe,CACXC,QAAS,CAAA,EACTC,cAAe,CACXd,MAAO,CACHC,KAAM,UACN,eAAgB,EAChBc,OAAQ,UACRC,EAAG,EACHnD,MAAO,CACHK,MAAO,MACX,EACA+C,OAAQ,CACJC,MAAO,CACHjB,KAAM,OACN,eAAgB,EAChBc,OAAQ,UACRlD,MAAO,CACHK,MAAO,MACX,CACJ,EACAiD,OAAQ,CACJlB,KAAM,OACN,eAAgB,EAChBc,OAAQ,UACRlD,MAAO,CACHK,MAAO,MACX,CACJ,CACJ,CACJ,CACJ,CACJ,EAEAkD,cAAe,CACXC,YAAa,CACTpB,KAAM,UACNc,OAAQ,UACR,eAAgB,EAChBlD,MAAO,CACHK,MAAO,MACX,EACA+C,OAAQ,CACJC,MAAO,CACHjB,KAAM,UACNpC,MAAO,CACHK,MAAO,MACX,EACA,eAAgB,EAChB6C,OAAQ,OACZ,EACAI,OAAQ,CACJlB,KAAM,UACNpC,MAAO,CACHK,MAAO,MACX,EACA,eAAgB,EAChB6C,OAAQ,OACZ,CACJ,CACJ,EACAO,oBAAqB,UACrBC,WAAY,CACRjE,gBAAiB,UACjBY,MAAO,MACX,EACAsD,WAAY,CACRtD,MAAO,MACX,CACJ,EACAuD,UAAW,CACPC,QAAS,CACLpE,gBAAiB,UACjBoC,YAAa,SACjB,EACAiC,aAAc,OACdC,SAAU,wBACV/C,OAAQ,CACJX,MAAO,UACPc,UAAW,SACf,EACAkB,MAAO,CACHC,cAAe,SACnB,CACJ,EACA0B,UAAW,CACPC,mBAAoB,UACpBC,eAAgB,UAChBC,iBAAkB,UAClBC,sBAAuB,UACvBC,kBAAmB,UACnBC,WAAY,UACZC,qBAAsB,UACtBC,iBAAkB,SACtB,CACJ,EAmBAhH,EAAeiH,KAAK,CAVpB,WAEIpF,EAAc,OAAQ,CAClBqF,KAAM,kFACNC,IAAK,aACLC,KAAM,UACV,EAAG,KAAMC,SAASC,oBAAoB,CAAC,OAAO,CAAC,EAAE,EAEjD1F,EAAW5B,EAAe8B,OAAO,CACrC,CAEJ,EAAG9B,GAAmBA,CAAAA,EAAiB,CAAC,CAAA,GAMX,IAAMuH,EAAavH,CAOhD,CAAC2B,IAA+EgD,KAAK,CAAG4C,EAAUzF,OAAO,CACzGyF,EAAUN,KAAK,GACc,IAAMxF,EAAmBE,IAG5C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}