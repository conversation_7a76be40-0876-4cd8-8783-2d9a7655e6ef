!function(e,n){"object"==typeof exports&&"object"==typeof module?module.exports=n(e._Highcharts):"function"==typeof define&&define.amd?define("highcharts/i18n/fr-FR",["highcharts/highcharts"],function(e){return n(e)}):"object"==typeof exports?exports["highcharts/i18n/fr-FR"]=n(e._Highcharts):e.Highcharts=n(e.Highcharts)}("undefined"==typeof window?this:window,e=>(()=>{"use strict";var n={944:n=>{n.exports=e}},i={};function r(e){var s=i[e];if(void 0!==s)return s.exports;var t=i[e]={exports:{}};return n[e](t,t.exports,r),t.exports}r.n=e=>{var n=e&&e.__esModule?()=>e.default:()=>e;return r.d(n,{a:n}),n},r.d=(e,n)=>{for(var i in n)r.o(n,i)&&!r.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:n[i]})},r.o=(e,n)=>Object.prototype.hasOwnProperty.call(e,n);var s={};r.d(s,{default:()=>l});var t=r(944),a=r.n(t);let{setOptions:o}=a();o({lang:{locale:"fr-FR",chartTitle:"Titre du graphique",pieSliceName:"Part",seriesName:"S\xe9rie {add index 1}",yAxisTitle:"Valeurs",rangeSelector:{allText:"Tout",allTitle:"Voir tout",monthText:"{count}m",monthTitle:"Voir {count} mois",yearText:"{count}a",yearTitle:"Voir {count} {#eq count 1}an{else}ans{/eq}",ytdText:"ACD",ytdTitle:"Voir depuis le d\xe9but de l'ann\xe9e"},viewFullscreen:"Voir en plein \xe9cran",stockOpen:"Ouverture",stockHigh:"Haut",stockLow:"Bas",stockClose:"Cl\xf4ture",weekFrom:"semaine \xe0 partir de",exitFullscreen:"Quitter le plein \xe9cran",printChart:"Imprimer le graphique",downloadPNG:"T\xe9l\xe9charger l'image PNG",downloadJPEG:"T\xe9l\xe9charger l'image JPEG",downloadPDF:"T\xe9l\xe9charger le document PDF",downloadSVG:"T\xe9l\xe9charger l'image vectorielle SVG",contextButtonTitle:"Menu contextuel du graphique",loading:"Chargement...",numericSymbols:["k","M","G","T","P","E"],resetZoom:"R\xe9initialiser le zoom",resetZoomTitle:"R\xe9initialiser le niveau de zoom 1:1",rangeSelectorZoom:"Zoom",rangeSelectorFrom:"",rangeSelectorTo:"→",zoomIn:"Zoomer",zoomOut:"D\xe9zoomer",downloadCSV:"T\xe9l\xe9charger CSV",downloadXLS:"T\xe9l\xe9charger XLS",exportData:{annotationHeader:"Annotations",categoryHeader:"Cat\xe9gorie",categoryDatetimeHeader:"DateTime"},viewData:"Voir le tableau de donn\xe9es",hideData:"Cacher le tableau de donn\xe9es",exportInProgress:"Exportation en cours...",accessibility:{defaultChartTitle:"Graphique",chartContainerLabel:"{title}. Graphique interactif Highcharts.",svgContainerLabel:"Graphique interactif",drillUpButton:"{buttonText}",credits:"Cr\xe9dits du graphique : {creditsStr}",thousandsSep:",",svgContainerTitle:"",graphicContainerLabel:"",screenReaderSection:{beforeRegionLabel:"",afterRegionLabel:"",annotations:{heading:"R\xe9sum\xe9 des annotations du graphique",descriptionSinglePoint:"{annotationText}. En relation avec {annotationPoint}",descriptionMultiplePoints:"{annotationText}. En relation avec {annotationPoint}{#each additionalAnnotationPoints}, \xe9galement li\xe9 \xe0 {this}{/each}",descriptionNoPoints:"{annotationText}"},endOfChartMarker:"Fin du graphique interactif."},sonification:{playAsSoundButtonText:"Jouer en tant que son, {chartTitle}",playAsSoundClickAnnouncement:"Jouer"},legend:{legendLabelNoTitle:"Changer la visibilit\xe9 de la s\xe9rie, {chartTitle}",legendLabel:"L\xe9gende du graphique : {legendTitle}",legendItem:"Montrer {itemName}"},zoom:{mapZoomIn:"Zoomer sur le graphique",mapZoomOut:"D\xe9zoomer le graphique",resetZoomButton:"R\xe9initialiser le zoom"},rangeSelector:{dropdownLabel:"{rangeTitle}",minInputLabel:"S\xe9lectionner la date de d\xe9but.",maxInputLabel:"S\xe9lectionner la date de fin.",clickButtonAnnouncement:"Affichage de {axisRangeDescription}"},navigator:{handleLabel:"{#eq handleIx 0}D\xe9but, pourcentage{else}Fin, pourcentage{/eq}",groupLabel:"Zoom de l'axe",changeAnnouncement:"{axisRangeDescription}"},table:{viewAsDataTableButtonText:"Voir en tant que tableau de donn\xe9es, {chartTitle}",tableSummary:"Repr\xe9sentation en tableau du graphique."},announceNewData:{newDataAnnounce:"Donn\xe9es mises \xe0 jour pour le graphique {chartTitle}",newSeriesAnnounceSingle:"Nouvelle s\xe9rie de donn\xe9es : {seriesDesc}",newPointAnnounceSingle:"Nouveau point de donn\xe9es : {pointDesc}",newSeriesAnnounceMultiple:"Nouvelles s\xe9ries de donn\xe9es dans le graphique {chartTitle} : {seriesDesc}",newPointAnnounceMultiple:"Nouveau point de donn\xe9es dans le graphique {chartTitle} : {pointDesc}"},seriesTypeDescriptions:{boxplot:"Les graphiques en bo\xeete sont typiquement utilis\xe9s pour afficher des groupes de donn\xe9es statistiques. Chaque point de donn\xe9es dans le graphique peut avoir jusqu'\xe0 5 valeurs : minimum, premier quartile, m\xe9diane, troisi\xe8me quartile et maximum.",arearange:"Les graphiques en aire montrent une plage entre une valeur inf\xe9rieure et une valeur sup\xe9rieure pour chaque point.",areasplinerange:"Ces graphiques sont des graphiques en ligne affichant une plage entre une valeur inf\xe9rieure et une valeur sup\xe9rieure pour chaque point.",bubble:"Les graphiques \xe0 bulles sont des graphiques de dispersion o\xf9 chaque point de donn\xe9es a \xe9galement une valeur de taille.",columnrange:"Les graphiques en colonnes montrent une plage entre une valeur inf\xe9rieure et une valeur sup\xe9rieure pour chaque point.",errorbar:"Les s\xe9ries de barres d'erreur sont utilis\xe9es pour afficher la variabilit\xe9 des donn\xe9es.",funnel:"Les graphiques en entonnoir sont utilis\xe9s pour afficher la r\xe9duction des donn\xe9es en \xe9tapes.",pyramid:"Les graphiques en pyramide se composent d'une seule pyramide dont les hauteurs des \xe9l\xe9ments correspondent \xe0 chaque valeur de point.",waterfall:"Un graphique en cascade est un graphique en colonnes o\xf9 chaque colonne contribue \xe0 une valeur finale totale."},chartTypes:{emptyChart:"Graphique vide",mapTypeDescription:"Carte de {mapTitle} avec {numSeries} s\xe9ries de donn\xe9es.",unknownMap:"Carte de r\xe9gion non sp\xe9cifi\xe9e avec {numSeries} s\xe9ries de donn\xe9es.",combinationChart:"Graphique combin\xe9 avec {numSeries} s\xe9ries de donn\xe9es.",defaultSingle:"Graphique avec {numPoints} donn\xe9e{#eq numPoints 1}{else}s{/eq}.",defaultMultiple:"Graphique avec {numSeries} s\xe9ries de donn\xe9es.",splineSingle:"Graphique en ligne avec {numPoints} donn\xe9e{#eq numPoints 1}{else}s{/eq}.",splineMultiple:"Graphique en ligne avec {numSeries} lignes.",lineSingle:"Graphique en ligne avec {numPoints} donn\xe9e{#eq numPoints 1}{else}s{/eq}.",lineMultiple:"Graphique en ligne avec {numSeries} lignes.",columnSingle:"Graphique en barres avec {numPoints} barre{#eq numPoints 1}{else}s{/eq}.",columnMultiple:"Graphique en barres avec {numSeries} s\xe9ries de donn\xe9es.",barSingle:"Graphique en barres avec {numPoints} barre{#eq numPoints 1}{else}s{/eq}.",barMultiple:"Graphique en barres avec {numSeries} s\xe9ries de donn\xe9es.",pieSingle:"Graphique en camembert avec {numPoints} part{#eq numPoints 1}{else}s{/eq}.",pieMultiple:"Graphique en camembert avec {numSeries} camemberts.",scatterSingle:"Graphique de dispersion avec {numPoints} point{#eq numPoints 1}{else}s{/eq}.",scatterMultiple:"Graphique de dispersion avec {numSeries} s\xe9ries de donn\xe9es.",boxplotSingle:"Graphique en bo\xeete avec {numPoints} bo\xeete{#eq numPoints 1}{else}s{/eq}.",boxplotMultiple:"Graphique en bo\xeete avec {numSeries} s\xe9ries de donn\xe9es.",bubbleSingle:"Graphique \xe0 bulles avec {numPoints} bulle{#eq numPoints 1}{else}s{/eq}.",bubbleMultiple:"Graphique \xe0 bulles avec {numSeries} s\xe9ries de donn\xe9es."},axis:{xAxisDescriptionSingular:"Le graphique a 1 axe X affichant {names[0]}. {ranges[0]}",xAxisDescriptionPlural:"Le graphique a {numAxes} axes X affichant {#each names}{#unless @first},{/unless}{#if @last} et{/if} {this}{/each}.",yAxisDescriptionSingular:"Le graphique a 1 axe Y affichant {names[0]}. {ranges[0]}",yAxisDescriptionPlural:"Le graphique a {numAxes} axes Y affichant {#each names}{#unless @first},{/unless}{#if @last} et{/if} {this}{/each}.",timeRangeDays:"Plage de donn\xe9es : {range} jours.",timeRangeHours:"Plage de donn\xe9es : {range} heures.",timeRangeMinutes:"Plage de donn\xe9es : {range} minutes.",timeRangeSeconds:"Plage de donn\xe9es : {range} secondes.",rangeFromTo:"Les donn\xe9es vont de {rangeFrom} \xe0 {rangeTo}.",rangeCategories:"Plage de donn\xe9es : {numCategories} cat\xe9gories."},exporting:{chartMenuLabel:"Menu du graphique",menuButtonLabel:"Voir le menu du graphique, {chartTitle}"},series:{summary:{default:"{series.name}, s\xe9rie {seriesNumber} sur {chart.series.length} avec {series.points.length} point{#eq series.points.length 1}{else}s{/eq}.",defaultCombination:"{series.name}, s\xe9rie {seriesNumber} sur {chart.series.length} avec {series.points.length} point{#eq series.points.length 1}{else}s{/eq}.",line:"{series.name}, ligne {seriesNumber} sur {chart.series.length} avec {series.points.length} point{#eq series.points.length 1}{else}s{/eq}.",lineCombination:"{series.name}, s\xe9rie {seriesNumber} sur {chart.series.length}. Ligne avec {series.points.length} point{#eq series.points.length 1}{else}s{/eq}.",spline:"{series.name}, ligne {seriesNumber} sur {chart.series.length} avec {series.points.length} point{#eq series.points.length 1}{else}s{/eq}.",splineCombination:"{series.name}, s\xe9rie {seriesNumber} sur {chart.series.length}. Ligne avec {series.points.length} point{#eq series.points.length 1}{else}s{/eq}.",column:"{series.name}, s\xe9rie de barres {seriesNumber} sur {chart.series.length} avec {series.points.length} barre{#eq series.points.length 1}{else}s{/eq}.",columnCombination:"{series.name}, s\xe9rie {seriesNumber} sur {chart.series.length}. S\xe9rie de barres avec {series.points.length} barre{#eq series.points.length 1}{else}s{/eq}.",bar:"{series.name}, s\xe9rie de barres {seriesNumber} sur {chart.series.length} avec {series.points.length} barre{#eq series.points.length 1}{else}s{/eq}.",barCombination:"{series.name}, s\xe9rie {seriesNumber} sur {chart.series.length}. S\xe9rie de barres avec {series.points.length} barre{#eq series.points.length 1}{else}s{/eq}.",pie:"{series.name}, tarte {seriesNumber} sur {chart.series.length} avec {series.points.length} part{#eq series.points.length 1}{else}s{/eq}.",pieCombination:"{series.name}, s\xe9rie {seriesNumber} sur {chart.series.length}. Tarte avec {series.points.length} part{#eq series.points.length 1}{else}s{/eq}.",scatter:"{series.name}, graphique de dispersion {seriesNumber} sur {chart.series.length} avec {series.points.length} point{#eq series.points.length 1}{else}s{/eq}.",scatterCombination:"{series.name}, s\xe9rie {seriesNumber} sur {chart.series.length}, graphique de dispersion avec {series.points.length} point{#eq series.points.length 1}{else}s{/eq}.",boxplot:"{series.name}, graphique en bo\xeete {seriesNumber} sur {chart.series.length} avec {series.points.length} bo\xeete{#eq series.points.length 1}{else}s{/eq}.",boxplotCombination:"{series.name}, s\xe9rie {seriesNumber} sur {chart.series.length}. Graphique en bo\xeete avec {series.points.length} bo\xeete{#eq series.points.length 1}{else}s{/eq}.",bubble:"{series.name}, s\xe9rie de bulles {seriesNumber} sur {chart.series.length} avec {series.points.length} bulle{#eq series.points.length 1}{else}s{/eq}.",bubbleCombination:"{series.name}, s\xe9rie {seriesNumber} sur {chart.series.length}. S\xe9rie de bulles avec {series.points.length} bulle{#eq series.points.length 1}{else}s{/eq}.",map:"{series.name}, carte {seriesNumber} sur {chart.series.length} avec {series.points.length} zone{#eq series.points.length 1}{else}s{/eq}.",mapCombination:"{series.name}, s\xe9rie {seriesNumber} sur {chart.series.length}. Carte avec {series.points.length} zone{#eq series.points.length 1}{else}s{/eq}.",mapline:"{series.name}, ligne {seriesNumber} sur {chart.series.length} avec {series.points.length} donn\xe9e{#eq series.points.length 1}{else}s{/eq}.",maplineCombination:"{series.name}, s\xe9rie {seriesNumber} sur {chart.series.length}. Ligne avec {series.points.length} donn\xe9e{#eq series.points.length 1}{else}s{/eq}.",mapbubble:"{series.name}, s\xe9rie de bulles {seriesNumber} sur {chart.series.length} avec {series.points.length} bulle{#eq series.points.length 1}{else}s{/eq}.",mapbubbleCombination:"{series.name}, s\xe9rie {seriesNumber} sur {chart.series.length}. S\xe9rie de bulles avec {series.points.length} bulle{#eq series.points.length 1}{else}s{/eq}."},description:"{description}",xAxisDescription:"Axe X, {name}",yAxisDescription:"Axe Y, {name}",nullPointValue:"Aucune valeur",pointAnnotationsDescription:"{#each annotations}Annotation : {this}{/each}"}},navigation:{popup:{simpleShapes:"Formes simples",lines:"Lignes",circle:"Cercle",ellipse:"Ellipse",rectangle:"Rectangle",label:"\xc9tiquette",shapeOptions:"Options de forme",typeOptions:"D\xe9tails",fill:"Remplissage",format:"Texte",strokeWidth:"Largeur de ligne",stroke:"Couleur de ligne",title:"Titre",name:"Nom",labelOptions:"Options d'\xe9tiquette",labels:"\xc9tiquettes",backgroundColor:"Couleur d'arri\xe8re-plan",backgroundColors:"Couleurs d'arri\xe8re-plan",borderColor:"Couleur de bordure",borderRadius:"Rayon de bordure",borderWidth:"Largeur de bordure",style:"Style",padding:"Marge",fontSize:"Taille de police",color:"Couleur",height:"Hauteur",shapes:"Options de forme",segment:"Segment",arrowSegment:"Segment fl\xe9ch\xe9",ray:"Rayon",arrowRay:"Rayon fl\xe9ch\xe9",line:"Ligne",arrowInfinityLine:"Ligne fl\xe9ch\xe9e",horizontalLine:"Ligne horizontale",verticalLine:"Ligne verticale",crooked3:"Ligne courb\xe9e 3",crooked5:"Ligne courb\xe9e 5",elliott3:"Ligne Elliott 3",elliott5:"Ligne Elliott 5",verticalCounter:"Compteur vertical",verticalLabel:"\xc9tiquette verticale",verticalArrow:"Fl\xe8che verticale",fibonacci:"Fibonacci",fibonacciTimeZones:"Zones temporelles Fibonacci",pitchfork:"Fourchette",parallelChannel:"Canal parall\xe8le",infinityLine:"Ligne infinie",measure:"Mesure",measureXY:"Mesurer XY",measureX:"Mesurer X",measureY:"Mesurer Y",timeCycles:"Cycles temporels",flags:"Drapeaux",addButton:"Ajouter",saveButton:"Enregistrer",editButton:"Modifier",removeButton:"Retirer",series:"S\xe9ries",volume:"Volume",connector:"Connecteur",innerBackground:"Arri\xe8re-plan int\xe9rieur",outerBackground:"Arri\xe8re-plan ext\xe9rieur",crosshairX:"R\xe9ticule X",crosshairY:"R\xe9ticule Y",tunnel:"Tunnel",background:"Arri\xe8re-plan",noFilterMatch:"Aucune correspondance",searchIndicators:"Rechercher des indicateurs",clearFilter:"✕ effacer le filtre",index:"Index",period:"P\xe9riode",periods:"P\xe9riodes",standardDeviation:"\xc9cart type",periodTenkan:"P\xe9riode Tenkan",periodSenkouSpanB:"P\xe9riode Senkou Span B",periodATR:"P\xe9riode ATR",multiplierATR:"Multiplicateur ATR",shortPeriod:"P\xe9riode courte",longPeriod:"P\xe9riode longue",signalPeriod:"P\xe9riode de signal",decimals:"D\xe9cimales",algorithm:"Algorithme",topBand:"Bande sup\xe9rieure",bottomBand:"Bande inf\xe9rieure",initialAccelerationFactor:"Facteur d'acc\xe9l\xe9ration initial",maxAccelerationFactor:"Facteur d'acc\xe9l\xe9ration maximal",increment:"Incr\xe9ment",multiplier:"Multiplicateur",ranges:"Plages",highIndex:"Index \xe9lev\xe9",lowIndex:"Index bas",deviation:"D\xe9viation",xAxisUnit:"Unit\xe9 de l'axe X",factor:"Facteur",fastAvgPeriod:"P\xe9riode moyenne rapide",slowAvgPeriod:"P\xe9riode moyenne lente",average:"Moyenne",indicatorAliases:{abands:["Bandes d'acc\xe9l\xe9ration"],bb:["Bandes de Bollinger"],dema:["Moyenne mobile exponentielle double"],ema:["Moyenne mobile exponentielle"],ikh:["Ichimoku Kinko Hyo"],keltnerchannels:["Canaux de Keltner"],linearRegression:["R\xe9gression lin\xe9aire"],pivotpoints:["Points de pivot"],pc:["Canal de prix"],priceenvelopes:["Enveloppes de prix"],psar:["Parabolic SAR"],sma:["Moyenne mobile simple"],supertrend:["Super tendance"],tema:["Moyenne mobile exponentielle triple"],vbp:["Volume par prix"],vwap:["Moyenne pond\xe9r\xe9e par le volume"],wma:["Moyenne mobile pond\xe9r\xe9e"],zigzag:["Zig Zag"],apo:["Indicateur de prix absolu"],ad:["Accumulation/Distribution"],aroon:["Aroon"],aroonoscillator:["Oscillateur Aroon"],atr:["Average True Range"],ao:["Oscillateur impressionnant"],cci:["Indice de canal des marchandises"],chaikin:["Chaikin"],cmf:["Flux mon\xe9taire de Chaikin"],cmo:["Oscillateur de momentum de Chande"],disparityindex:["Indice de disparit\xe9"],dmi:["Indice de mouvement directionnel"],dpo:["Oscillateur de prix d\xe9tendu"],klinger:["Oscillateur Klinger"],linearRegressionAngle:["Angle de r\xe9gression lin\xe9aire"],linearRegressionIntercept:["Interception de r\xe9gression lin\xe9aire"],linearRegressionSlope:["Pente de r\xe9gression lin\xe9aire"],macd:["Convergence et divergence de moyenne mobile"],mfi:["Indice de flux mon\xe9taire"],momentum:["Momentum"],natr:["Average True Range normalis\xe9"],obv:["Volume \xe9quilibr\xe9"],ppo:["Oscillateur de prix en pourcentage"],roc:["Taux de changement"],rsi:["Indice de force relative"],slowstochastic:["Stochastique lent"],stochastic:["Stochastique"],trix:["TRIX"],williamsr:["Williams %R"]}}},mainBreadcrumb:"Principal",downloadMIDI:"T\xe9l\xe9charger MIDI",playAsSound:"Jouer comme un son",stockTools:{gui:{simpleShapes:"Formes simples",lines:"Lignes",crookedLines:"Lignes courb\xe9es",measure:"Mesure",advanced:"Avanc\xe9",toggleAnnotations:"Basculer les annotations",verticalLabels:"\xc9tiquettes verticales",flags:"Drapeaux",zoomChange:"Changement de zoom",typeChange:"Changement de type",saveChart:"Enregistrer le graphique",indicators:"Indicateurs",currentPriceIndicator:"Indicateurs du prix actuel",zoomX:"Zoom X",zoomY:"Zoom Y",zoomXY:"Zoom XY",fullScreen:"Plein \xe9cran",typeOHLC:"OHLC",typeLine:"Ligne",typeCandlestick:"Chandelier",typeHLC:"HLC",typeHollowCandlestick:"Chandelier creux",typeHeikinAshi:"Heikin Ashi",circle:"Cercle",ellipse:"Ellipse",label:"\xc9tiquette",rectangle:"Rectangle",flagCirclepin:"Drapeau cercle",flagDiamondpin:"Drapeau losange",flagSquarepin:"Drapeau carr\xe9",flagSimplepin:"Drapeau simple",measureXY:"Mesurer XY",measureX:"Mesurer X",measureY:"Mesurer Y",segment:"Segment",arrowSegment:"Segment fl\xe9ch\xe9",ray:"Rayon",arrowRay:"Rayon fl\xe9ch\xe9",line:"Ligne",arrowInfinityLine:"Ligne fl\xe9ch\xe9e",horizontalLine:"Ligne horizontale",verticalLine:"Ligne verticale",infinityLine:"Ligne infinie",crooked3:"Ligne courb\xe9e 3",crooked5:"Ligne courb\xe9e 5",elliott3:"Ligne Elliott 3",elliott5:"Ligne Elliott 5",verticalCounter:"Compteur vertical",verticalLabel:"\xc9tiquette verticale",verticalArrow:"Fl\xe8che verticale",fibonacci:"Fibonacci",fibonacciTimeZones:"Zones temporelles Fibonacci",pitchfork:"Fourche",parallelChannel:"Canal parall\xe8le",timeCycles:"Cycles temporels"}},noData:"Aucune donn\xe9e \xe0 afficher"}});let l=a();return s.default})());