{"locale": "nb-NO", "chartTitle": "Diagramtittel", "pieSliceName": "Skive", "seriesName": "Serie {add index 1}", "yAxisTitle": "<PERSON><PERSON>", "rangeSelector": {"allText": "Alle", "allTitle": "Vis alle", "monthText": "{count}m", "monthTitle": "Vis {count} {#eq count 1}måned{else}måneder{/eq}", "yearText": "{count}år", "yearTitle": "Vis {count} {#eq count 1}år{else}år{/eq}", "ytdText": "YTD", "ytdTitle": "Vis år til dato"}, "stockOpen": "<PERSON><PERSON><PERSON>", "stockHigh": "Hø<PERSON>", "stockLow": "Lav", "stockClose": "Slutt", "viewFullscreen": "<PERSON><PERSON> i fullskjerm", "weekFrom": "uka fra", "exitFullscreen": "Avslutt fullskjerm", "printChart": "Skriv ut diagram", "downloadPNG": "Last ned P<PERSON>", "downloadJPEG": "Last ned <PERSON>", "downloadPDF": "Last ned PDF", "downloadSVG": "Last ned SVG", "contextButtonTitle": "Diagramkontekstmeny", "loading": "Laster...", "numericSymbols": ["k", "M", "G", "T", "P", "E"], "resetZoom": "Tilbakestill zoom", "resetZoomTitle": "Tilbakestill zoomnivå 1:1", "rangeSelectorZoom": "Zoom", "rangeSelectorFrom": "", "rangeSelectorTo": "→", "zoomIn": "Zoom inn", "zoomOut": "Zoom ut", "downloadCSV": "Last ned CSV", "downloadXLS": "Last ned X<PERSON>", "exportData": {"annotationHeader": "Merknader", "categoryHeader": "<PERSON><PERSON><PERSON>", "categoryDatetimeHeader": "<PERSON>to og tid"}, "viewData": "Vis datatabell", "hideData": "Skjul datatabell", "exportInProgress": "Eksporterer...", "accessibility": {"defaultChartTitle": "Diagram", "chartContainerLabel": "{title}. Highcharts interaktivt diagram.", "svgContainerLabel": "Interaktivt diagram", "drillUpButton": "{buttonText}", "credits": "Diagramkreditter: {creditsStr}", "thousandsSep": ",", "svgContainerTitle": "", "graphicContainerLabel": "", "screenReaderSection": {"beforeRegionLabel": "", "afterRegionLabel": "", "annotations": {"heading": "Sammendrag av diagramannotasjoner", "descriptionSinglePoint": "{annotationText}. Relatert til {annotationPoint}", "descriptionMultiplePoints": "{annotationText}. Relatert til {annotationPoint}{#each additionalAnnotationPoints}, også relatert til {this}{/each}", "descriptionNoPoints": "{annotationText}"}, "endOfChartMarker": "Slutt på interaktivt diagram."}, "sonification": {"playAsSoundButtonText": "Spill som lyd, {chartTitle}", "playAsSoundClickAnnouncement": "Spill"}, "legend": {"legendLabelNoTitle": "<PERSON><PERSON> se<PERSON>, {chartTitle}", "legendLabel": "Diagramforklaring: {legendTitle}", "legendItem": "Vis {itemName}"}, "zoom": {"mapZoomIn": "Zoom inn", "mapZoomOut": "Zoom ut", "resetZoomButton": "Tilbakestill zoom"}, "rangeSelector": {"dropdownLabel": "{rangeTitle}", "minInputLabel": "<PERSON><PERSON>g startdato.", "maxInputLabel": "<PERSON><PERSON><PERSON> slutt<PERSON>.", "clickButtonAnnouncement": "Viser {axisRangeDescription}"}, "navigator": {"handleLabel": "{#eq handleIx 0}Start, prosent{else}Slutt, prosent{/eq}", "groupLabel": "Aksezoom", "changeAnnouncement": "{axisRangeDescription}"}, "table": {"viewAsDataTableButtonText": "Vis som datatabell, {chartTitle}", "tableSummary": "Tabellrepresentasjon av diagram."}, "announceNewData": {"newDataAnnounce": "Oppdaterte data for diagram {chartTitle}", "newSeriesAnnounceSingle": "Ny dataserie: {seriesDesc}", "newPointAnnounceSingle": "Nytt datapunkt: {pointDesc}", "newSeriesAnnounceMultiple": "Ny dataserie i diagram {chartTitle}: {seriesDesc}", "newPointAnnounceMultiple": "Nytt datapunkt i diagram {chartTitle}: {pointDesc}"}, "seriesTypeDescriptions": {"boxplot": "Boksplott diagrammer brukes typisk til å vise grupper av statistiske data. Hvert datapunkt i diagrammet kan ha opptil 5 verdier: minimum, nedre kvartil, median, øvre kvartil, og maksimum.", "arearange": "Områdeområde diagrammer er linjediagrammer som viser et område mellom en lavere og høyere verdi for hvert punkt.", "areasplinerange": "Disse diagrammene er linjediagrammer som viser et område mellom en lavere og høyere verdi for hvert punkt.", "bubble": "Boblediagrammer er spredningsdiagrammer der hvert datapunkt også har en størrelsesverdi.", "columnrange": "Kolonneområde diagrammer er kolonnediagrammer som viser et område mellom en lavere og høyere verdi for hvert punkt.", "errorbar": "Feilbjelkeserier brukes til å vise variabiliteten av data.", "funnel": "Traktdiagrammer brukes til å vise reduksjon av data i stadier.", "pyramid": "Pyramidediagrammer består av en enkelt pyramide med elementhøyder som svarer til hver punktverdi.", "waterfall": "Et fossediagram er et kolonnediagram der hver kolonne bidrar mot en total sluttkverdi."}, "chartTypes": {"emptyChart": "<PERSON><PERSON> diagram", "mapTypeDescription": "Kart over {mapTitle} med {numSeries} dataserier.", "unknownMap": "Kart over uspesifisert region med {numSeries} dataserier.", "combinationChart": "Kombinasjonsdiagram med {numSeries} dataserier.", "defaultSingle": "Diagram med {numPoints} data {#eq numPoints 1}punkt{else}punkter{/eq}.", "defaultMultiple": "Diagram med {numSeries} dataserier.", "splineSingle": "Linjediagram med {numPoints} data {#eq numPoints 1}punkt{else}punkter{/eq}.", "splineMultiple": "Linjediagram med {numSeries} linjer.", "lineSingle": "Linjediagram med {numPoints} data {#eq numPoints 1}punkt{else}punkter{/eq}.", "lineMultiple": "Linjediagram med {numSeries} linjer.", "columnSingle": "Stolpediagram med {numPoints} {#eq numPoints 1}stolpe{else}stolper{/eq}.", "columnMultiple": "Stolpediagram med {numSeries} dataserier.", "barSingle": "Stolpediagram med {numPoints} {#eq numPoints 1}stolpe{else}stolper{/eq}.", "barMultiple": "Stolpediagram med {numSeries} dataserier.", "pieSingle": "Piediagram med {numPoints} {#eq numPoints 1}skive{else}skiver{/eq}.", "pieMultiple": "Piediagram med {numSeries} paier.", "scatterSingle": "Spredningsdiagram med {numPoints} {#eq numPoints 1}punkt{else}punkter{/eq}.", "scatterMultiple": "Spredningsdiagram med {numSeries} dataserier.", "boxplotSingle": "Boksplott med {numPoints} {#eq numPoints 1}boks{else}bokser{/eq}.", "boxplotMultiple": "Boksplott med {numSeries} dataserier.", "bubbleSingle": "Boblediagram med {numPoints} {#eq numPoints 1}boble{else}bobler{/eq}.", "bubbleMultiple": "Boblediagram med {numSeries} dataserier."}, "axis": {"xAxisDescriptionSingular": "Diagrammet har 1 X-akse som viser {names[0]}. {ranges[0]}", "xAxisDescriptionPlural": "Diagrammet har {numAxes} X-akser som viser {#each names}{#unless @first},{/unless}{#if @last} og{/if} {this}{/each}.", "yAxisDescriptionSingular": "Diagrammet har 1 Y-akse som viser {names[0]}. {ranges[0]}", "yAxisDescriptionPlural": "Diagrammet har {numAxes} Y-akser som viser {#each names}{#unless @first},{/unless}{#if @last} og{/if} {this}{/each}.", "timeRangeDays": "Dataområde: {range} dager.", "timeRangeHours": "Dataområde: {range} timer.", "timeRangeMinutes": "Dataområde: {range} minutter.", "timeRangeSeconds": "Dataområde: {range} sekunder.", "rangeFromTo": "Datarekkevidde fra {rangeFrom} til {rangeTo}.", "rangeCategories": "Dataområde: {numCategories} kategorier."}, "exporting": {"chartMenuLabel": "Diagrammeny", "menuButtonLabel": "Vis diagrammeny, {chartTitle}"}, "series": {"summary": {"default": "{series.name}, serie {seriesNumber} av {chart.series.length} med {series.points.length} data {#eq series.points.length 1}punkt{else}punkter{/eq}.", "defaultCombination": "{series.name}, serie {seriesNumber} av {chart.series.length} med {series.points.length} data {#eq series.points.length 1}punkt{else}punkter{/eq}.", "line": "{series.name}, linje {seriesNumber} av {chart.series.length} med {series.points.length} data {#eq series.points.length 1}punkt{else}punkter{/eq}.", "lineCombination": "{series.name}, serie {seriesNumber} av {chart.series.length}. <PERSON>je med {series.points.length} data {#eq series.points.length 1}punkt{else}punkter{/eq}.", "spline": "{series.name}, linje {seriesNumber} av {chart.series.length} med {series.points.length} data {#eq series.points.length 1}punkt{else}punkter{/eq}.", "splineCombination": "{series.name}, serie {seriesNumber} av {chart.series.length}. <PERSON>je med {series.points.length} data {#eq series.points.length 1}punkt{else}punkter{/eq}.", "column": "{series.name}, stolpeserie {seriesNumber} av {chart.series.length} med {series.points.length} {#eq series.points.length 1}stolpe{else}stolper{/eq}.", "columnCombination": "{series.name}, serie {seriesNumber} av {chart.series.length}. Stolpeserie med {series.points.length} {#eq series.points.length 1}stolpe{else}stolper{/eq}.", "bar": "{series.name}, stolpeserie {seriesNumber} av {chart.series.length} med {series.points.length} {#eq series.points.length 1}stolpe{else}stolper{/eq}.", "barCombination": "{series.name}, serie {seriesNumber} av {chart.series.length}. Stolpeserie med {series.points.length} {#eq series.points.length 1}stolpe{else}stolper{/eq}.", "pie": "{series.name}, pai {seriesNumber} av {chart.series.length} med {series.points.length} {#eq series.points.length 1}skive{else}skiver{/eq}.", "pieCombination": "{series.name}, serie {seriesNumber} av {chart.series.length}. Pai med {series.points.length} {#eq series.points.length 1}skive{else}skiver{/eq}.", "scatter": "{series.name}, spredningsdiagram {seriesNumber} av {chart.series.length} med {series.points.length} {#eq series.points.length 1}punkt{else}punkter{/eq}.", "scatterCombination": "{series.name}, serie {seriesNumber} av {chart.series.length}, spredningsdiagram med {series.points.length} {#eq series.points.length 1}punkt{else}punkter{/eq}.", "boxplot": "{series.name}, bok<PERSON>lott {seriesNumber} av {chart.series.length} med {series.points.length} {#eq series.points.length 1}boks{else}bokser{/eq}.", "boxplotCombination": "{series.name}, serie {seriesNumber} av {chart.series.length}. Boksplott med {series.points.length} {#eq series.points.length 1}boks{else}bokser{/eq}.", "bubble": "{series.name}, bobleserie {seriesNumber} av {chart.series.length} med {series.points.length} {#eq series.points.length 1}boble{else}bobler{/eq}.", "bubbleCombination": "{series.name}, serie {seriesNumber} av {chart.series.length}. Bobleserie med {series.points.length} {#eq series.points.length 1}boble{else}bobler{/eq}.", "map": "{series.name}, kart {seriesNumber} av {chart.series.length} med {series.points.length} {#eq series.points.length 1}område{else}områder{/eq}.", "mapCombination": "{series.name}, serie {seriesNumber} av {chart.series.length}. Kart med {series.points.length} {#eq series.points.length 1}område{else}områder{/eq}.", "mapline": "{series.name}, linje {seriesNumber} av {chart.series.length} med {series.points.length} data {#eq series.points.length 1}punkt{else}punkter{/eq}.", "maplineCombination": "{series.name}, serie {seriesNumber} av {chart.series.length}. <PERSON>je med {series.points.length} data {#eq series.points.length 1}punkt{else}punkter{/eq}.", "mapbubble": "{series.name}, bobleserie {seriesNumber} av {chart.series.length} med {series.points.length} {#eq series.points.length 1}boble{else}bobler{/eq}.", "mapbubbleCombination": "{series.name}, serie {seriesNumber} av {chart.series.length}. Bobleserie med {series.points.length} {#eq series.points.length 1}boble{else}bobler{/eq}."}, "description": "{description}", "xAxisDescription": "<PERSON>-<PERSON><PERSON><PERSON>, {name}", "yAxisDescription": "<PERSON>-aks<PERSON>, {name}", "nullPointValue": "Ingen verdi", "pointAnnotationsDescription": "{#each annotations}Annotasjon: {this}{/each}"}}, "navigation": {"popup": {"simpleShapes": "<PERSON><PERSON> former", "lines": "<PERSON><PERSON>", "circle": "<PERSON><PERSON>", "ellipse": "Ellipse", "rectangle": "<PERSON><PERSON><PERSON><PERSON>", "label": "Etikett", "shapeOptions": "Formalternativer", "typeOptions": "<PERSON><PERSON><PERSON>", "fill": "<PERSON><PERSON><PERSON>", "format": "Tekst", "strokeWidth": "Linje<PERSON><PERSON>", "stroke": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "name": "Navn", "labelOptions": "Etikettalternativer", "labels": "Etiketter", "backgroundColor": "Bakgrunnsfarge", "backgroundColors": "Bakgrunnsfarger", "borderColor": "<PERSON><PERSON><PERSON><PERSON>", "borderRadius": "<PERSON><PERSON><PERSON><PERSON>", "borderWidth": "Kantbredde", "style": "Stil", "padding": "Polstring", "fontSize": "Skriftstørrelse", "color": "<PERSON><PERSON>", "height": "<PERSON><PERSON><PERSON><PERSON>", "shapes": "Formalternativer", "segment": "Segment", "arrowSegment": "Pilsegment", "ray": "Stråle", "arrowRay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "line": "<PERSON><PERSON>", "arrowInfinityLine": "<PERSON><PERSON><PERSON>", "horizontalLine": "Horisontal linje", "verticalLine": "Vertikal linje", "crooked3": "Kroket 3-linje", "crooked5": "Kroket 5-lin<PERSON>", "elliott3": "Elliott 3-l<PERSON><PERSON>", "elliott5": "<PERSON> 5-<PERSON><PERSON><PERSON>", "verticalCounter": "Vertikal teller", "verticalLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "verticalArrow": "Vertikal pil", "fibonacci": "<PERSON><PERSON><PERSON><PERSON>", "fibonacciTimeZones": "<PERSON><PERSON><PERSON><PERSON>", "pitchfork": "<PERSON><PERSON><PERSON><PERSON>", "parallelChannel": "<PERSON><PERSON><PERSON> kanal", "infinityLine": "Uendelighetslinje", "measure": "<PERSON><PERSON><PERSON>", "measureXY": "Mål XY", "measureX": "Mål X", "measureY": "<PERSON><PERSON><PERSON> Y", "timeCycles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flags": "<PERSON>g", "addButton": "Legg til", "saveButton": "Lagre", "editButton": "<PERSON><PERSON>", "removeButton": "<PERSON><PERSON><PERSON>", "series": "Serier", "volume": "Volum", "connector": "<PERSON><PERSON>", "innerBackground": "<PERSON><PERSON> b<PERSON>", "outerBackground": "<PERSON><PERSON> b<PERSON>", "crosshairX": "Krysshår X", "crosshairY": "Krysshår Y", "tunnel": "Tunnel", "background": "Bakgrunn", "noFilterMatch": "<PERSON>gen treff", "searchIndicators": "<PERSON><PERSON><PERSON>di<PERSON>", "clearFilter": "✕ fjern filter", "index": "<PERSON><PERSON><PERSON>", "period": "Periode", "periods": "Perioder", "standardDeviation": "Standardavvik", "periodTenkan": "Tenkan-periode", "periodSenkouSpanB": "Senkou Span B-periode", "periodATR": "ATR-periode", "multiplierATR": "ATR-multiplikator", "shortPeriod": "<PERSON>rt periode", "longPeriod": "<PERSON> periode", "signalPeriod": "Signalperiode", "decimals": "<PERSON><PERSON><PERSON>", "algorithm": "Algoritme", "topBand": "<PERSON><PERSON> bånd", "bottomBand": "<PERSON><PERSON>", "initialAccelerationFactor": "Innledende akselerasjonsfaktor", "maxAccelerationFactor": "Maksimal akselerasjonsfaktor", "increment": "Økning", "multiplier": "Multiplikator", "ranges": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "highIndex": "<PERSON><PERSON><PERSON> indeks", "lowIndex": "<PERSON>v in<PERSON>s", "deviation": "Avvik", "xAxisUnit": "x-aks<PERSON><PERSON><PERSON>", "factor": "<PERSON><PERSON><PERSON>", "fastAvgPeriod": "Rask gjennomsnittlig periode", "slowAvgPeriod": "Langsom gjennomsnittlig periode", "average": "Gjennomsnitt", "indicatorAliases": {"abands": ["Akselerasjonsbånd"], "bb": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "dema": ["Dobbelt eksponentielt glidende gjennomsnitt"], "ema": ["Eksponentielt glidende gjennomsnitt"], "ikh": ["<PERSON><PERSON><PERSON><PERSON>"], "keltnerchannels": ["Ke<PERSON>ner-kanaler"], "linearRegression": ["<PERSON><PERSON><PERSON> reg<PERSON>"], "pivotpoints": ["Pivotpunkter"], "pc": ["<PERSON><PERSON><PERSON><PERSON>"], "priceenvelopes": ["Prisenvelopper"], "psar": ["Parabolic SAR"], "sma": ["Enkelt glidende gjennomsnitt"], "supertrend": ["Supertrend"], "tema": ["Trippel eksponentielt glidende gjennomsnitt"], "vbp": ["Volum ved pris"], "vwap": ["Volumvektet glidende gjennomsnitt"], "wma": ["Vektet glidende gjennomsnitt"], "zigzag": ["Zigzag"], "apo": ["<PERSON><PERSON><PERSON><PERSON> prisindikator"], "ad": ["Akkumulering/Distribusjon"], "aroon": ["Aroon"], "aroonoscillator": ["Aroon-oscillator"], "atr": ["Gjennomsnittlig sann rekkevidde"], "ao": ["Fantastisk oscillator"], "cci": ["Commodity Channel Index"], "chaikin": ["<PERSON><PERSON><PERSON>"], "cmf": ["Chaikin Money Flow"], "cmo": ["Chande Momentum Oscillator"], "disparityindex": ["Disparitetsindeks"], "dmi": ["Retningsbevegelsesindeks"], "dpo": ["Detrended prisoscillator"], "klinger": ["<PERSON>linger Oscillator"], "linearRegressionAngle": ["<PERSON><PERSON><PERSON>g<PERSON>"], "linearRegressionIntercept": ["<PERSON>ær regresjonsavskjæring"], "linearRegressionSlope": ["<PERSON>ær regresjonsstigning"], "macd": ["Glidende gjennomsnittskonvergensdivergens"], "mfi": ["Pengeflytindek<PERSON>"], "momentum": ["Momentum"], "natr": ["Normalisert gjennomsnittlig sann rekkevidde"], "obv": ["On-Balance Volume"], "ppo": ["Prosentvis prisoscillator"], "roc": ["Endringsrate"], "rsi": ["Relativ styrkeindeks"], "slowstochastic": ["Langsom stokastisk"], "stochastic": ["Stokastisk"], "trix": ["TRIX"], "williamsr": ["Williams %R"]}}}, "mainBreadcrumb": "Hoved", "downloadMIDI": "Last ned MIDI", "playAsSound": "Spill av som lyd", "stockTools": {"gui": {"simpleShapes": "<PERSON><PERSON> former", "lines": "<PERSON><PERSON>", "crookedLines": "<PERSON><PERSON><PERSON> linjer", "measure": "<PERSON><PERSON><PERSON>", "advanced": "Avansert", "toggleAnnotations": "<PERSON><PERSON><PERSON>", "verticalLabels": "<PERSON><PERSON><PERSON><PERSON>", "flags": "<PERSON>g", "zoomChange": "Zoom endring", "typeChange": "Type endring", "saveChart": "Lagre diagram", "indicators": "Indikatorer", "currentPriceIndicator": "<PERSON><PERSON><PERSON><PERSON><PERSON> for nåværende pris", "zoomX": "Zoom X", "zoomY": "Zoom Y", "zoomXY": "Zoom XY", "fullScreen": "Fullskjerm", "typeOHLC": "OHLC", "typeLine": "<PERSON><PERSON>", "typeCandlestick": "Lysestake", "typeHLC": "HLC", "typeHollowCandlestick": "<PERSON><PERSON>", "typeHeikinAshi": "<PERSON><PERSON><PERSON>", "circle": "<PERSON><PERSON>", "ellipse": "Ellipse", "label": "Etikett", "rectangle": "<PERSON><PERSON><PERSON><PERSON>", "flagCirclepin": "<PERSON>g sir<PERSON>", "flagDiamondpin": "<PERSON><PERSON> di<PERSON>t", "flagSquarepin": "<PERSON><PERSON> k<PERSON>", "flagSimplepin": "<PERSON><PERSON> enkel", "measureXY": "Mål XY", "measureX": "Mål X", "measureY": "<PERSON><PERSON><PERSON> Y", "segment": "Segment", "arrowSegment": "Pilsegment", "ray": "Stråle", "arrowRay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "line": "<PERSON><PERSON>", "arrowInfinityLine": "<PERSON><PERSON><PERSON>", "horizontalLine": "Horisontal linje", "verticalLine": "Vertikal linje", "infinityLine": "Uendelighetslinje", "crooked3": "Kroket 3-linje", "crooked5": "Kroket 5-lin<PERSON>", "elliott3": "Elliott 3-l<PERSON><PERSON>", "elliott5": "<PERSON> 5-<PERSON><PERSON><PERSON>", "verticalCounter": "Vertikal teller", "verticalLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "verticalArrow": "Vertikal pil", "fibonacci": "<PERSON><PERSON><PERSON><PERSON>", "fibonacciTimeZones": "<PERSON><PERSON><PERSON><PERSON>", "pitchfork": "Høygaffel", "parallelChannel": "<PERSON><PERSON><PERSON> kanal", "timeCycles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "noData": "Ingen data å vise"}