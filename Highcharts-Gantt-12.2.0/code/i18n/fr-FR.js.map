{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n* @license Highcharts JS v12.2.0 (2025-04-07)\n* @module highcharts/i18n/fr-FR\n* @requires highcharts\n*\n* fr-FR language pack\n*\n* (c) 2009-2025 Torstein Honsi\n*\n* License: www.highcharts.com/license\n*\n* **Do not edit this file!** This file is generated using the 'gulp lang-build' task.\n*/\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/i18n/fr-FR\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/i18n/fr-FR\"] = factory(root[\"_Highcharts\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (fr_FR_src)\n/* harmony export */ });\n/* harmony import */ var fr_FR_src_Core_Defaults_js_WEBPACK_IMPORTED_MODULE_0_ = __webpack_require__(944);\n/* harmony import */ var fr_FR_src_Core_Defaults_js_WEBPACK_IMPORTED_MODULE_0_default = /*#__PURE__*/__webpack_require__.n(fr_FR_src_Core_Defaults_js_WEBPACK_IMPORTED_MODULE_0_);\n\n\nconst { setOptions: fr_FR_src_setOptions } = (fr_FR_src_Core_Defaults_js_WEBPACK_IMPORTED_MODULE_0_default());\nconst fr_FR_src_languageOptions = {\n    \"locale\": \"fr-FR\",\n    \"chartTitle\": \"Titre du graphique\",\n    \"pieSliceName\": \"Part\",\n    \"seriesName\": \"Série {add index 1}\",\n    \"yAxisTitle\": \"Valeurs\",\n    \"rangeSelector\": {\n        \"allText\": \"Tout\",\n        \"allTitle\": \"Voir tout\",\n        \"monthText\": \"{count}m\",\n        \"monthTitle\": \"Voir {count} mois\",\n        \"yearText\": \"{count}a\",\n        \"yearTitle\": \"Voir {count} {#eq count 1}an{else}ans{/eq}\",\n        \"ytdText\": \"ACD\",\n        \"ytdTitle\": \"Voir depuis le début de l'année\"\n    },\n    \"viewFullscreen\": \"Voir en plein écran\",\n    \"stockOpen\": \"Ouverture\",\n    \"stockHigh\": \"Haut\",\n    \"stockLow\": \"Bas\",\n    \"stockClose\": \"Clôture\",\n    \"weekFrom\": \"semaine à partir de\",\n    \"exitFullscreen\": \"Quitter le plein écran\",\n    \"printChart\": \"Imprimer le graphique\",\n    \"downloadPNG\": \"Télécharger l'image PNG\",\n    \"downloadJPEG\": \"Télécharger l'image JPEG\",\n    \"downloadPDF\": \"Télécharger le document PDF\",\n    \"downloadSVG\": \"Télécharger l'image vectorielle SVG\",\n    \"contextButtonTitle\": \"Menu contextuel du graphique\",\n    \"loading\": \"Chargement...\",\n    \"numericSymbols\": [\n        \"k\",\n        \"M\",\n        \"G\",\n        \"T\",\n        \"P\",\n        \"E\"\n    ],\n    \"resetZoom\": \"Réinitialiser le zoom\",\n    \"resetZoomTitle\": \"Réinitialiser le niveau de zoom 1:1\",\n    \"rangeSelectorZoom\": \"Zoom\",\n    \"rangeSelectorFrom\": \"\",\n    \"rangeSelectorTo\": \"→\",\n    \"zoomIn\": \"Zoomer\",\n    \"zoomOut\": \"Dézoomer\",\n    \"downloadCSV\": \"Télécharger CSV\",\n    \"downloadXLS\": \"Télécharger XLS\",\n    \"exportData\": {\n        \"annotationHeader\": \"Annotations\",\n        \"categoryHeader\": \"Catégorie\",\n        \"categoryDatetimeHeader\": \"DateTime\"\n    },\n    \"viewData\": \"Voir le tableau de données\",\n    \"hideData\": \"Cacher le tableau de données\",\n    \"exportInProgress\": \"Exportation en cours...\",\n    \"accessibility\": {\n        \"defaultChartTitle\": \"Graphique\",\n        \"chartContainerLabel\": \"{title}. Graphique interactif Highcharts.\",\n        \"svgContainerLabel\": \"Graphique interactif\",\n        \"drillUpButton\": \"{buttonText}\",\n        \"credits\": \"Crédits du graphique : {creditsStr}\",\n        \"thousandsSep\": \",\",\n        \"svgContainerTitle\": \"\",\n        \"graphicContainerLabel\": \"\",\n        \"screenReaderSection\": {\n            \"beforeRegionLabel\": \"\",\n            \"afterRegionLabel\": \"\",\n            \"annotations\": {\n                \"heading\": \"Résumé des annotations du graphique\",\n                \"descriptionSinglePoint\": \"{annotationText}. En relation avec {annotationPoint}\",\n                \"descriptionMultiplePoints\": \"{annotationText}. En relation avec {annotationPoint}{#each additionalAnnotationPoints}, également lié à {this}{/each}\",\n                \"descriptionNoPoints\": \"{annotationText}\"\n            },\n            \"endOfChartMarker\": \"Fin du graphique interactif.\"\n        },\n        \"sonification\": {\n            \"playAsSoundButtonText\": \"Jouer en tant que son, {chartTitle}\",\n            \"playAsSoundClickAnnouncement\": \"Jouer\"\n        },\n        \"legend\": {\n            \"legendLabelNoTitle\": \"Changer la visibilité de la série, {chartTitle}\",\n            \"legendLabel\": \"Légende du graphique : {legendTitle}\",\n            \"legendItem\": \"Montrer {itemName}\"\n        },\n        \"zoom\": {\n            \"mapZoomIn\": \"Zoomer sur le graphique\",\n            \"mapZoomOut\": \"Dézoomer le graphique\",\n            \"resetZoomButton\": \"Réinitialiser le zoom\"\n        },\n        \"rangeSelector\": {\n            \"dropdownLabel\": \"{rangeTitle}\",\n            \"minInputLabel\": \"Sélectionner la date de début.\",\n            \"maxInputLabel\": \"Sélectionner la date de fin.\",\n            \"clickButtonAnnouncement\": \"Affichage de {axisRangeDescription}\"\n        },\n        \"navigator\": {\n            \"handleLabel\": \"{#eq handleIx 0}Début, pourcentage{else}Fin, pourcentage{/eq}\",\n            \"groupLabel\": \"Zoom de l'axe\",\n            \"changeAnnouncement\": \"{axisRangeDescription}\"\n        },\n        \"table\": {\n            \"viewAsDataTableButtonText\": \"Voir en tant que tableau de données, {chartTitle}\",\n            \"tableSummary\": \"Représentation en tableau du graphique.\"\n        },\n        \"announceNewData\": {\n            \"newDataAnnounce\": \"Données mises à jour pour le graphique {chartTitle}\",\n            \"newSeriesAnnounceSingle\": \"Nouvelle série de données : {seriesDesc}\",\n            \"newPointAnnounceSingle\": \"Nouveau point de données : {pointDesc}\",\n            \"newSeriesAnnounceMultiple\": \"Nouvelles séries de données dans le graphique {chartTitle} : {seriesDesc}\",\n            \"newPointAnnounceMultiple\": \"Nouveau point de données dans le graphique {chartTitle} : {pointDesc}\"\n        },\n        \"seriesTypeDescriptions\": {\n            \"boxplot\": \"Les graphiques en boîte sont typiquement utilisés pour afficher des groupes de données statistiques. Chaque point de données dans le graphique peut avoir jusqu'à 5 valeurs : minimum, premier quartile, médiane, troisième quartile et maximum.\",\n            \"arearange\": \"Les graphiques en aire montrent une plage entre une valeur inférieure et une valeur supérieure pour chaque point.\",\n            \"areasplinerange\": \"Ces graphiques sont des graphiques en ligne affichant une plage entre une valeur inférieure et une valeur supérieure pour chaque point.\",\n            \"bubble\": \"Les graphiques à bulles sont des graphiques de dispersion où chaque point de données a également une valeur de taille.\",\n            \"columnrange\": \"Les graphiques en colonnes montrent une plage entre une valeur inférieure et une valeur supérieure pour chaque point.\",\n            \"errorbar\": \"Les séries de barres d'erreur sont utilisées pour afficher la variabilité des données.\",\n            \"funnel\": \"Les graphiques en entonnoir sont utilisés pour afficher la réduction des données en étapes.\",\n            \"pyramid\": \"Les graphiques en pyramide se composent d'une seule pyramide dont les hauteurs des éléments correspondent à chaque valeur de point.\",\n            \"waterfall\": \"Un graphique en cascade est un graphique en colonnes où chaque colonne contribue à une valeur finale totale.\"\n        },\n        \"chartTypes\": {\n            \"emptyChart\": \"Graphique vide\",\n            \"mapTypeDescription\": \"Carte de {mapTitle} avec {numSeries} séries de données.\",\n            \"unknownMap\": \"Carte de région non spécifiée avec {numSeries} séries de données.\",\n            \"combinationChart\": \"Graphique combiné avec {numSeries} séries de données.\",\n            \"defaultSingle\": \"Graphique avec {numPoints} donnée{#eq numPoints 1}{else}s{/eq}.\",\n            \"defaultMultiple\": \"Graphique avec {numSeries} séries de données.\",\n            \"splineSingle\": \"Graphique en ligne avec {numPoints} donnée{#eq numPoints 1}{else}s{/eq}.\",\n            \"splineMultiple\": \"Graphique en ligne avec {numSeries} lignes.\",\n            \"lineSingle\": \"Graphique en ligne avec {numPoints} donnée{#eq numPoints 1}{else}s{/eq}.\",\n            \"lineMultiple\": \"Graphique en ligne avec {numSeries} lignes.\",\n            \"columnSingle\": \"Graphique en barres avec {numPoints} barre{#eq numPoints 1}{else}s{/eq}.\",\n            \"columnMultiple\": \"Graphique en barres avec {numSeries} séries de données.\",\n            \"barSingle\": \"Graphique en barres avec {numPoints} barre{#eq numPoints 1}{else}s{/eq}.\",\n            \"barMultiple\": \"Graphique en barres avec {numSeries} séries de données.\",\n            \"pieSingle\": \"Graphique en camembert avec {numPoints} part{#eq numPoints 1}{else}s{/eq}.\",\n            \"pieMultiple\": \"Graphique en camembert avec {numSeries} camemberts.\",\n            \"scatterSingle\": \"Graphique de dispersion avec {numPoints} point{#eq numPoints 1}{else}s{/eq}.\",\n            \"scatterMultiple\": \"Graphique de dispersion avec {numSeries} séries de données.\",\n            \"boxplotSingle\": \"Graphique en boîte avec {numPoints} boîte{#eq numPoints 1}{else}s{/eq}.\",\n            \"boxplotMultiple\": \"Graphique en boîte avec {numSeries} séries de données.\",\n            \"bubbleSingle\": \"Graphique à bulles avec {numPoints} bulle{#eq numPoints 1}{else}s{/eq}.\",\n            \"bubbleMultiple\": \"Graphique à bulles avec {numSeries} séries de données.\"\n        },\n        \"axis\": {\n            \"xAxisDescriptionSingular\": \"Le graphique a 1 axe X affichant {names[0]}. {ranges[0]}\",\n            \"xAxisDescriptionPlural\": \"Le graphique a {numAxes} axes X affichant {#each names}{#unless @first},{/unless}{#if @last} et{/if} {this}{/each}.\",\n            \"yAxisDescriptionSingular\": \"Le graphique a 1 axe Y affichant {names[0]}. {ranges[0]}\",\n            \"yAxisDescriptionPlural\": \"Le graphique a {numAxes} axes Y affichant {#each names}{#unless @first},{/unless}{#if @last} et{/if} {this}{/each}.\",\n            \"timeRangeDays\": \"Plage de données : {range} jours.\",\n            \"timeRangeHours\": \"Plage de données : {range} heures.\",\n            \"timeRangeMinutes\": \"Plage de données : {range} minutes.\",\n            \"timeRangeSeconds\": \"Plage de données : {range} secondes.\",\n            \"rangeFromTo\": \"Les données vont de {rangeFrom} à {rangeTo}.\",\n            \"rangeCategories\": \"Plage de données : {numCategories} catégories.\"\n        },\n        \"exporting\": {\n            \"chartMenuLabel\": \"Menu du graphique\",\n            \"menuButtonLabel\": \"Voir le menu du graphique, {chartTitle}\"\n        },\n        \"series\": {\n            \"summary\": {\n                \"default\": \"{series.name}, série {seriesNumber} sur {chart.series.length} avec {series.points.length} point{#eq series.points.length 1}{else}s{/eq}.\",\n                \"defaultCombination\": \"{series.name}, série {seriesNumber} sur {chart.series.length} avec {series.points.length} point{#eq series.points.length 1}{else}s{/eq}.\",\n                \"line\": \"{series.name}, ligne {seriesNumber} sur {chart.series.length} avec {series.points.length} point{#eq series.points.length 1}{else}s{/eq}.\",\n                \"lineCombination\": \"{series.name}, série {seriesNumber} sur {chart.series.length}. Ligne avec {series.points.length} point{#eq series.points.length 1}{else}s{/eq}.\",\n                \"spline\": \"{series.name}, ligne {seriesNumber} sur {chart.series.length} avec {series.points.length} point{#eq series.points.length 1}{else}s{/eq}.\",\n                \"splineCombination\": \"{series.name}, série {seriesNumber} sur {chart.series.length}. Ligne avec {series.points.length} point{#eq series.points.length 1}{else}s{/eq}.\",\n                \"column\": \"{series.name}, série de barres {seriesNumber} sur {chart.series.length} avec {series.points.length} barre{#eq series.points.length 1}{else}s{/eq}.\",\n                \"columnCombination\": \"{series.name}, série {seriesNumber} sur {chart.series.length}. Série de barres avec {series.points.length} barre{#eq series.points.length 1}{else}s{/eq}.\",\n                \"bar\": \"{series.name}, série de barres {seriesNumber} sur {chart.series.length} avec {series.points.length} barre{#eq series.points.length 1}{else}s{/eq}.\",\n                \"barCombination\": \"{series.name}, série {seriesNumber} sur {chart.series.length}. Série de barres avec {series.points.length} barre{#eq series.points.length 1}{else}s{/eq}.\",\n                \"pie\": \"{series.name}, tarte {seriesNumber} sur {chart.series.length} avec {series.points.length} part{#eq series.points.length 1}{else}s{/eq}.\",\n                \"pieCombination\": \"{series.name}, série {seriesNumber} sur {chart.series.length}. Tarte avec {series.points.length} part{#eq series.points.length 1}{else}s{/eq}.\",\n                \"scatter\": \"{series.name}, graphique de dispersion {seriesNumber} sur {chart.series.length} avec {series.points.length} point{#eq series.points.length 1}{else}s{/eq}.\",\n                \"scatterCombination\": \"{series.name}, série {seriesNumber} sur {chart.series.length}, graphique de dispersion avec {series.points.length} point{#eq series.points.length 1}{else}s{/eq}.\",\n                \"boxplot\": \"{series.name}, graphique en boîte {seriesNumber} sur {chart.series.length} avec {series.points.length} boîte{#eq series.points.length 1}{else}s{/eq}.\",\n                \"boxplotCombination\": \"{series.name}, série {seriesNumber} sur {chart.series.length}. Graphique en boîte avec {series.points.length} boîte{#eq series.points.length 1}{else}s{/eq}.\",\n                \"bubble\": \"{series.name}, série de bulles {seriesNumber} sur {chart.series.length} avec {series.points.length} bulle{#eq series.points.length 1}{else}s{/eq}.\",\n                \"bubbleCombination\": \"{series.name}, série {seriesNumber} sur {chart.series.length}. Série de bulles avec {series.points.length} bulle{#eq series.points.length 1}{else}s{/eq}.\",\n                \"map\": \"{series.name}, carte {seriesNumber} sur {chart.series.length} avec {series.points.length} zone{#eq series.points.length 1}{else}s{/eq}.\",\n                \"mapCombination\": \"{series.name}, série {seriesNumber} sur {chart.series.length}. Carte avec {series.points.length} zone{#eq series.points.length 1}{else}s{/eq}.\",\n                \"mapline\": \"{series.name}, ligne {seriesNumber} sur {chart.series.length} avec {series.points.length} donnée{#eq series.points.length 1}{else}s{/eq}.\",\n                \"maplineCombination\": \"{series.name}, série {seriesNumber} sur {chart.series.length}. Ligne avec {series.points.length} donnée{#eq series.points.length 1}{else}s{/eq}.\",\n                \"mapbubble\": \"{series.name}, série de bulles {seriesNumber} sur {chart.series.length} avec {series.points.length} bulle{#eq series.points.length 1}{else}s{/eq}.\",\n                \"mapbubbleCombination\": \"{series.name}, série {seriesNumber} sur {chart.series.length}. Série de bulles avec {series.points.length} bulle{#eq series.points.length 1}{else}s{/eq}.\"\n            },\n            \"description\": \"{description}\",\n            \"xAxisDescription\": \"Axe X, {name}\",\n            \"yAxisDescription\": \"Axe Y, {name}\",\n            \"nullPointValue\": \"Aucune valeur\",\n            \"pointAnnotationsDescription\": \"{#each annotations}Annotation : {this}{/each}\"\n        }\n    },\n    \"navigation\": {\n        \"popup\": {\n            \"simpleShapes\": \"Formes simples\",\n            \"lines\": \"Lignes\",\n            \"circle\": \"Cercle\",\n            \"ellipse\": \"Ellipse\",\n            \"rectangle\": \"Rectangle\",\n            \"label\": \"Étiquette\",\n            \"shapeOptions\": \"Options de forme\",\n            \"typeOptions\": \"Détails\",\n            \"fill\": \"Remplissage\",\n            \"format\": \"Texte\",\n            \"strokeWidth\": \"Largeur de ligne\",\n            \"stroke\": \"Couleur de ligne\",\n            \"title\": \"Titre\",\n            \"name\": \"Nom\",\n            \"labelOptions\": \"Options d'étiquette\",\n            \"labels\": \"Étiquettes\",\n            \"backgroundColor\": \"Couleur d'arrière-plan\",\n            \"backgroundColors\": \"Couleurs d'arrière-plan\",\n            \"borderColor\": \"Couleur de bordure\",\n            \"borderRadius\": \"Rayon de bordure\",\n            \"borderWidth\": \"Largeur de bordure\",\n            \"style\": \"Style\",\n            \"padding\": \"Marge\",\n            \"fontSize\": \"Taille de police\",\n            \"color\": \"Couleur\",\n            \"height\": \"Hauteur\",\n            \"shapes\": \"Options de forme\",\n            \"segment\": \"Segment\",\n            \"arrowSegment\": \"Segment fléché\",\n            \"ray\": \"Rayon\",\n            \"arrowRay\": \"Rayon fléché\",\n            \"line\": \"Ligne\",\n            \"arrowInfinityLine\": \"Ligne fléchée\",\n            \"horizontalLine\": \"Ligne horizontale\",\n            \"verticalLine\": \"Ligne verticale\",\n            \"crooked3\": \"Ligne courbée 3\",\n            \"crooked5\": \"Ligne courbée 5\",\n            \"elliott3\": \"Ligne Elliott 3\",\n            \"elliott5\": \"Ligne Elliott 5\",\n            \"verticalCounter\": \"Compteur vertical\",\n            \"verticalLabel\": \"Étiquette verticale\",\n            \"verticalArrow\": \"Flèche verticale\",\n            \"fibonacci\": \"Fibonacci\",\n            \"fibonacciTimeZones\": \"Zones temporelles Fibonacci\",\n            \"pitchfork\": \"Fourchette\",\n            \"parallelChannel\": \"Canal parallèle\",\n            \"infinityLine\": \"Ligne infinie\",\n            \"measure\": \"Mesure\",\n            \"measureXY\": \"Mesurer XY\",\n            \"measureX\": \"Mesurer X\",\n            \"measureY\": \"Mesurer Y\",\n            \"timeCycles\": \"Cycles temporels\",\n            \"flags\": \"Drapeaux\",\n            \"addButton\": \"Ajouter\",\n            \"saveButton\": \"Enregistrer\",\n            \"editButton\": \"Modifier\",\n            \"removeButton\": \"Retirer\",\n            \"series\": \"Séries\",\n            \"volume\": \"Volume\",\n            \"connector\": \"Connecteur\",\n            \"innerBackground\": \"Arrière-plan intérieur\",\n            \"outerBackground\": \"Arrière-plan extérieur\",\n            \"crosshairX\": \"Réticule X\",\n            \"crosshairY\": \"Réticule Y\",\n            \"tunnel\": \"Tunnel\",\n            \"background\": \"Arrière-plan\",\n            \"noFilterMatch\": \"Aucune correspondance\",\n            \"searchIndicators\": \"Rechercher des indicateurs\",\n            \"clearFilter\": \"✕ effacer le filtre\",\n            \"index\": \"Index\",\n            \"period\": \"Période\",\n            \"periods\": \"Périodes\",\n            \"standardDeviation\": \"Écart type\",\n            \"periodTenkan\": \"Période Tenkan\",\n            \"periodSenkouSpanB\": \"Période Senkou Span B\",\n            \"periodATR\": \"Période ATR\",\n            \"multiplierATR\": \"Multiplicateur ATR\",\n            \"shortPeriod\": \"Période courte\",\n            \"longPeriod\": \"Période longue\",\n            \"signalPeriod\": \"Période de signal\",\n            \"decimals\": \"Décimales\",\n            \"algorithm\": \"Algorithme\",\n            \"topBand\": \"Bande supérieure\",\n            \"bottomBand\": \"Bande inférieure\",\n            \"initialAccelerationFactor\": \"Facteur d'accélération initial\",\n            \"maxAccelerationFactor\": \"Facteur d'accélération maximal\",\n            \"increment\": \"Incrément\",\n            \"multiplier\": \"Multiplicateur\",\n            \"ranges\": \"Plages\",\n            \"highIndex\": \"Index élevé\",\n            \"lowIndex\": \"Index bas\",\n            \"deviation\": \"Déviation\",\n            \"xAxisUnit\": \"Unité de l'axe X\",\n            \"factor\": \"Facteur\",\n            \"fastAvgPeriod\": \"Période moyenne rapide\",\n            \"slowAvgPeriod\": \"Période moyenne lente\",\n            \"average\": \"Moyenne\",\n            \"indicatorAliases\": {\n                \"abands\": [\n                    \"Bandes d'accélération\"\n                ],\n                \"bb\": [\n                    \"Bandes de Bollinger\"\n                ],\n                \"dema\": [\n                    \"Moyenne mobile exponentielle double\"\n                ],\n                \"ema\": [\n                    \"Moyenne mobile exponentielle\"\n                ],\n                \"ikh\": [\n                    \"Ichimoku Kinko Hyo\"\n                ],\n                \"keltnerchannels\": [\n                    \"Canaux de Keltner\"\n                ],\n                \"linearRegression\": [\n                    \"Régression linéaire\"\n                ],\n                \"pivotpoints\": [\n                    \"Points de pivot\"\n                ],\n                \"pc\": [\n                    \"Canal de prix\"\n                ],\n                \"priceenvelopes\": [\n                    \"Enveloppes de prix\"\n                ],\n                \"psar\": [\n                    \"Parabolic SAR\"\n                ],\n                \"sma\": [\n                    \"Moyenne mobile simple\"\n                ],\n                \"supertrend\": [\n                    \"Super tendance\"\n                ],\n                \"tema\": [\n                    \"Moyenne mobile exponentielle triple\"\n                ],\n                \"vbp\": [\n                    \"Volume par prix\"\n                ],\n                \"vwap\": [\n                    \"Moyenne pondérée par le volume\"\n                ],\n                \"wma\": [\n                    \"Moyenne mobile pondérée\"\n                ],\n                \"zigzag\": [\n                    \"Zig Zag\"\n                ],\n                \"apo\": [\n                    \"Indicateur de prix absolu\"\n                ],\n                \"ad\": [\n                    \"Accumulation/Distribution\"\n                ],\n                \"aroon\": [\n                    \"Aroon\"\n                ],\n                \"aroonoscillator\": [\n                    \"Oscillateur Aroon\"\n                ],\n                \"atr\": [\n                    \"Average True Range\"\n                ],\n                \"ao\": [\n                    \"Oscillateur impressionnant\"\n                ],\n                \"cci\": [\n                    \"Indice de canal des marchandises\"\n                ],\n                \"chaikin\": [\n                    \"Chaikin\"\n                ],\n                \"cmf\": [\n                    \"Flux monétaire de Chaikin\"\n                ],\n                \"cmo\": [\n                    \"Oscillateur de momentum de Chande\"\n                ],\n                \"disparityindex\": [\n                    \"Indice de disparité\"\n                ],\n                \"dmi\": [\n                    \"Indice de mouvement directionnel\"\n                ],\n                \"dpo\": [\n                    \"Oscillateur de prix détendu\"\n                ],\n                \"klinger\": [\n                    \"Oscillateur Klinger\"\n                ],\n                \"linearRegressionAngle\": [\n                    \"Angle de régression linéaire\"\n                ],\n                \"linearRegressionIntercept\": [\n                    \"Interception de régression linéaire\"\n                ],\n                \"linearRegressionSlope\": [\n                    \"Pente de régression linéaire\"\n                ],\n                \"macd\": [\n                    \"Convergence et divergence de moyenne mobile\"\n                ],\n                \"mfi\": [\n                    \"Indice de flux monétaire\"\n                ],\n                \"momentum\": [\n                    \"Momentum\"\n                ],\n                \"natr\": [\n                    \"Average True Range normalisé\"\n                ],\n                \"obv\": [\n                    \"Volume équilibré\"\n                ],\n                \"ppo\": [\n                    \"Oscillateur de prix en pourcentage\"\n                ],\n                \"roc\": [\n                    \"Taux de changement\"\n                ],\n                \"rsi\": [\n                    \"Indice de force relative\"\n                ],\n                \"slowstochastic\": [\n                    \"Stochastique lent\"\n                ],\n                \"stochastic\": [\n                    \"Stochastique\"\n                ],\n                \"trix\": [\n                    \"TRIX\"\n                ],\n                \"williamsr\": [\n                    \"Williams %R\"\n                ]\n            }\n        }\n    },\n    \"mainBreadcrumb\": \"Principal\",\n    \"downloadMIDI\": \"Télécharger MIDI\",\n    \"playAsSound\": \"Jouer comme un son\",\n    \"stockTools\": {\n        \"gui\": {\n            \"simpleShapes\": \"Formes simples\",\n            \"lines\": \"Lignes\",\n            \"crookedLines\": \"Lignes courbées\",\n            \"measure\": \"Mesure\",\n            \"advanced\": \"Avancé\",\n            \"toggleAnnotations\": \"Basculer les annotations\",\n            \"verticalLabels\": \"Étiquettes verticales\",\n            \"flags\": \"Drapeaux\",\n            \"zoomChange\": \"Changement de zoom\",\n            \"typeChange\": \"Changement de type\",\n            \"saveChart\": \"Enregistrer le graphique\",\n            \"indicators\": \"Indicateurs\",\n            \"currentPriceIndicator\": \"Indicateurs du prix actuel\",\n            \"zoomX\": \"Zoom X\",\n            \"zoomY\": \"Zoom Y\",\n            \"zoomXY\": \"Zoom XY\",\n            \"fullScreen\": \"Plein écran\",\n            \"typeOHLC\": \"OHLC\",\n            \"typeLine\": \"Ligne\",\n            \"typeCandlestick\": \"Chandelier\",\n            \"typeHLC\": \"HLC\",\n            \"typeHollowCandlestick\": \"Chandelier creux\",\n            \"typeHeikinAshi\": \"Heikin Ashi\",\n            \"circle\": \"Cercle\",\n            \"ellipse\": \"Ellipse\",\n            \"label\": \"Étiquette\",\n            \"rectangle\": \"Rectangle\",\n            \"flagCirclepin\": \"Drapeau cercle\",\n            \"flagDiamondpin\": \"Drapeau losange\",\n            \"flagSquarepin\": \"Drapeau carré\",\n            \"flagSimplepin\": \"Drapeau simple\",\n            \"measureXY\": \"Mesurer XY\",\n            \"measureX\": \"Mesurer X\",\n            \"measureY\": \"Mesurer Y\",\n            \"segment\": \"Segment\",\n            \"arrowSegment\": \"Segment fléché\",\n            \"ray\": \"Rayon\",\n            \"arrowRay\": \"Rayon fléché\",\n            \"line\": \"Ligne\",\n            \"arrowInfinityLine\": \"Ligne fléchée\",\n            \"horizontalLine\": \"Ligne horizontale\",\n            \"verticalLine\": \"Ligne verticale\",\n            \"infinityLine\": \"Ligne infinie\",\n            \"crooked3\": \"Ligne courbée 3\",\n            \"crooked5\": \"Ligne courbée 5\",\n            \"elliott3\": \"Ligne Elliott 3\",\n            \"elliott5\": \"Ligne Elliott 5\",\n            \"verticalCounter\": \"Compteur vertical\",\n            \"verticalLabel\": \"Étiquette verticale\",\n            \"verticalArrow\": \"Flèche verticale\",\n            \"fibonacci\": \"Fibonacci\",\n            \"fibonacciTimeZones\": \"Zones temporelles Fibonacci\",\n            \"pitchfork\": \"Fourche\",\n            \"parallelChannel\": \"Canal parallèle\",\n            \"timeCycles\": \"Cycles temporels\"\n        }\n    },\n    \"noData\": \"Aucune donnée à afficher\"\n};\nfr_FR_src_setOptions({\n    lang: fr_FR_src_languageOptions\n});\n// Export Highcharts\n/* harmony default export */ const fr_FR_src = ((fr_FR_src_Core_Defaults_js_WEBPACK_IMPORTED_MODULE_0_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "fr_FR_src", "fr_FR_src_Core_Defaults_js_WEBPACK_IMPORTED_MODULE_0_", "fr_FR_src_Core_Defaults_js_WEBPACK_IMPORTED_MODULE_0_default", "setOptions", "fr_FR_src_setOptions", "lang"], "mappings": "CAaA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,EACrC,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,wBAAyB,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAM,GAC1F,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,wBAAwB,CAAGD,EAAQD,EAAK,WAAc,EAE9DA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,AAACC,GACnC,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIE,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaX,OAAO,CAG5B,IAAIC,EAASO,CAAwB,CAACE,EAAS,CAAG,CAGjDV,QAAS,CAAC,CACX,EAMA,OAHAO,CAAmB,CAACG,EAAS,CAACT,EAAQA,EAAOD,OAAO,CAAES,GAG/CR,EAAOD,OAAO,AACtB,CAMCS,EAAoBI,CAAC,CAAG,AAACZ,IACxB,IAAIa,EAASb,GAAUA,EAAOc,UAAU,CACvC,IAAOd,EAAO,OAAU,CACxB,IAAOA,EAER,OADAQ,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAChB,EAASkB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACpB,EAASmB,IAC5EE,OAAOC,cAAc,CAACtB,EAASmB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EACNrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAOC,CACpB,GACA,IAAIC,EAAwDvB,EAAoB,KAC5EwB,EAA4ExB,EAAoBI,CAAC,CAACmB,GAG3H,GAAM,CAAEE,WAAYC,CAAoB,CAAE,CAAIF,IAyf9CE,EAAqB,CACjBC,KAzf8B,CAC9B,OAAU,QACV,WAAc,qBACd,aAAgB,OAChB,WAAc,yBACd,WAAc,UACd,cAAiB,CACb,QAAW,OACX,SAAY,YACZ,UAAa,WACb,WAAc,oBACd,SAAY,WACZ,UAAa,6CACb,QAAW,MACX,SAAY,uCAChB,EACA,eAAkB,yBAClB,UAAa,YACb,UAAa,OACb,SAAY,MACZ,WAAc,aACd,SAAY,yBACZ,eAAkB,4BAClB,WAAc,wBACd,YAAe,gCACf,aAAgB,iCAChB,YAAe,oCACf,YAAe,4CACf,mBAAsB,+BACtB,QAAW,gBACX,eAAkB,CACd,IACA,IACA,IACA,IACA,IACA,IACH,CACD,UAAa,2BACb,eAAkB,yCAClB,kBAAqB,OACrB,kBAAqB,GACrB,gBAAmB,IACnB,OAAU,SACV,QAAW,cACX,YAAe,wBACf,YAAe,wBACf,WAAc,CACV,iBAAoB,cACpB,eAAkB,eAClB,uBAA0B,UAC9B,EACA,SAAY,gCACZ,SAAY,kCACZ,iBAAoB,0BACpB,cAAiB,CACb,kBAAqB,YACrB,oBAAuB,4CACvB,kBAAqB,uBACrB,cAAiB,eACjB,QAAW,yCACX,aAAgB,IAChB,kBAAqB,GACrB,sBAAyB,GACzB,oBAAuB,CACnB,kBAAqB,GACrB,iBAAoB,GACpB,YAAe,CACX,QAAW,4CACX,uBAA0B,uDAC1B,0BAA6B,iIAC7B,oBAAuB,kBAC3B,EACA,iBAAoB,8BACxB,EACA,aAAgB,CACZ,sBAAyB,sCACzB,6BAAgC,OACpC,EACA,OAAU,CACN,mBAAsB,wDACtB,YAAe,0CACf,WAAc,oBAClB,EACA,KAAQ,CACJ,UAAa,0BACb,WAAc,2BACd,gBAAmB,0BACvB,EACA,cAAiB,CACb,cAAiB,eACjB,cAAiB,uCACjB,cAAiB,kCACjB,wBAA2B,qCAC/B,EACA,UAAa,CACT,YAAe,mEACf,WAAc,gBACd,mBAAsB,wBAC1B,EACA,MAAS,CACL,0BAA6B,uDAC7B,aAAgB,4CACpB,EACA,gBAAmB,CACf,gBAAmB,4DACnB,wBAA2B,iDAC3B,uBAA0B,4CAC1B,0BAA6B,kFAC7B,yBAA4B,0EAChC,EACA,uBAA0B,CACtB,QAAW,wQACX,UAAa,0HACb,gBAAmB,gJACnB,OAAU,qIACV,YAAe,8HACf,SAAY,qGACZ,OAAU,0GACV,QAAW,+IACX,UAAa,oHACjB,EACA,WAAc,CACV,WAAc,iBACd,mBAAsB,gEACtB,WAAc,mFACd,iBAAoB,iEACpB,cAAiB,qEACjB,gBAAmB,sDACnB,aAAgB,8EAChB,eAAkB,8CAClB,WAAc,8EACd,aAAgB,8CAChB,aAAgB,2EAChB,eAAkB,gEAClB,UAAa,2EACb,YAAe,gEACf,UAAa,6EACb,YAAe,sDACf,cAAiB,+EACjB,gBAAmB,oEACnB,cAAiB,gFACjB,gBAAmB,kEACnB,aAAgB,6EAChB,eAAkB,iEACtB,EACA,KAAQ,CACJ,yBAA4B,2DAC5B,uBAA0B,sHAC1B,yBAA4B,2DAC5B,uBAA0B,sHAC1B,cAAiB,uCACjB,eAAkB,wCAClB,iBAAoB,yCACpB,iBAAoB,0CACpB,YAAe,qDACf,gBAAmB,sDACvB,EACA,UAAa,CACT,eAAkB,oBAClB,gBAAmB,yCACvB,EACA,OAAU,CACN,QAAW,CACP,QAAW,8IACX,mBAAsB,8IACtB,KAAQ,2IACR,gBAAmB,qJACnB,OAAU,2IACV,kBAAqB,qJACrB,OAAU,wJACV,kBAAqB,kKACrB,IAAO,wJACP,eAAkB,kKAClB,IAAO,0IACP,eAAkB,oJAClB,QAAW,6JACX,mBAAsB,uKACtB,QAAW,8JACX,mBAAsB,wKACtB,OAAU,wJACV,kBAAqB,kKACrB,IAAO,0IACP,eAAkB,oJAClB,QAAW,+IACX,mBAAsB,yJACtB,UAAa,wJACb,qBAAwB,iKAC5B,EACA,YAAe,gBACf,iBAAoB,gBACpB,iBAAoB,gBACpB,eAAkB,gBAClB,4BAA+B,+CACnC,CACJ,EACA,WAAc,CACV,MAAS,CACL,aAAgB,iBAChB,MAAS,SACT,OAAU,SACV,QAAW,UACX,UAAa,YACb,MAAS,eACT,aAAgB,mBAChB,YAAe,aACf,KAAQ,cACR,OAAU,QACV,YAAe,mBACf,OAAU,mBACV,MAAS,QACT,KAAQ,MACR,aAAgB,yBAChB,OAAU,gBACV,gBAAmB,4BACnB,iBAAoB,6BACpB,YAAe,qBACf,aAAgB,mBAChB,YAAe,qBACf,MAAS,QACT,QAAW,QACX,SAAY,mBACZ,MAAS,UACT,OAAU,UACV,OAAU,mBACV,QAAW,UACX,aAAgB,uBAChB,IAAO,QACP,SAAY,qBACZ,KAAQ,QACR,kBAAqB,sBACrB,eAAkB,oBAClB,aAAgB,kBAChB,SAAY,qBACZ,SAAY,qBACZ,SAAY,kBACZ,SAAY,kBACZ,gBAAmB,oBACnB,cAAiB,yBACjB,cAAiB,sBACjB,UAAa,YACb,mBAAsB,8BACtB,UAAa,aACb,gBAAmB,qBACnB,aAAgB,gBAChB,QAAW,SACX,UAAa,aACb,SAAY,YACZ,SAAY,YACZ,WAAc,mBACd,MAAS,WACT,UAAa,UACb,WAAc,cACd,WAAc,WACd,aAAgB,UAChB,OAAU,YACV,OAAU,SACV,UAAa,aACb,gBAAmB,+BACnB,gBAAmB,+BACnB,WAAc,gBACd,WAAc,gBACd,OAAU,SACV,WAAc,kBACd,cAAiB,wBACjB,iBAAoB,6BACpB,YAAe,sBACf,MAAS,QACT,OAAU,aACV,QAAW,cACX,kBAAqB,gBACrB,aAAgB,oBAChB,kBAAqB,2BACrB,UAAa,iBACb,cAAiB,qBACjB,YAAe,oBACf,WAAc,oBACd,aAAgB,uBAChB,SAAY,eACZ,UAAa,aACb,QAAW,sBACX,WAAc,sBACd,0BAA6B,uCAC7B,sBAAyB,uCACzB,UAAa,eACb,WAAc,iBACd,OAAU,SACV,UAAa,oBACb,SAAY,YACZ,UAAa,eACb,UAAa,sBACb,OAAU,UACV,cAAiB,4BACjB,cAAiB,2BACjB,QAAW,UACX,iBAAoB,CAChB,OAAU,CACN,8BACH,CACD,GAAM,CACF,sBACH,CACD,KAAQ,CACJ,sCACH,CACD,IAAO,CACH,+BACH,CACD,IAAO,CACH,qBACH,CACD,gBAAmB,CACf,oBACH,CACD,iBAAoB,CAChB,4BACH,CACD,YAAe,CACX,kBACH,CACD,GAAM,CACF,gBACH,CACD,eAAkB,CACd,qBACH,CACD,KAAQ,CACJ,gBACH,CACD,IAAO,CACH,wBACH,CACD,WAAc,CACV,iBACH,CACD,KAAQ,CACJ,sCACH,CACD,IAAO,CACH,kBACH,CACD,KAAQ,CACJ,uCACH,CACD,IAAO,CACH,gCACH,CACD,OAAU,CACN,UACH,CACD,IAAO,CACH,4BACH,CACD,GAAM,CACF,4BACH,CACD,MAAS,CACL,QACH,CACD,gBAAmB,CACf,oBACH,CACD,IAAO,CACH,qBACH,CACD,GAAM,CACF,6BACH,CACD,IAAO,CACH,mCACH,CACD,QAAW,CACP,UACH,CACD,IAAO,CACH,+BACH,CACD,IAAO,CACH,oCACH,CACD,eAAkB,CACd,yBACH,CACD,IAAO,CACH,mCACH,CACD,IAAO,CACH,iCACH,CACD,QAAW,CACP,sBACH,CACD,sBAAyB,CACrB,qCACH,CACD,0BAA6B,CACzB,4CACH,CACD,sBAAyB,CACrB,qCACH,CACD,KAAQ,CACJ,8CACH,CACD,IAAO,CACH,8BACH,CACD,SAAY,CACR,WACH,CACD,KAAQ,CACJ,kCACH,CACD,IAAO,CACH,yBACH,CACD,IAAO,CACH,qCACH,CACD,IAAO,CACH,qBACH,CACD,IAAO,CACH,2BACH,CACD,eAAkB,CACd,oBACH,CACD,WAAc,CACV,eACH,CACD,KAAQ,CACJ,OACH,CACD,UAAa,CACT,cACH,AACL,CACJ,CACJ,EACA,eAAkB,YAClB,aAAgB,yBAChB,YAAe,qBACf,WAAc,CACV,IAAO,CACH,aAAgB,iBAChB,MAAS,SACT,aAAgB,qBAChB,QAAW,SACX,SAAY,YACZ,kBAAqB,2BACrB,eAAkB,2BAClB,MAAS,WACT,WAAc,qBACd,WAAc,qBACd,UAAa,2BACb,WAAc,cACd,sBAAyB,6BACzB,MAAS,SACT,MAAS,SACT,OAAU,UACV,WAAc,iBACd,SAAY,OACZ,SAAY,QACZ,gBAAmB,aACnB,QAAW,MACX,sBAAyB,mBACzB,eAAkB,cAClB,OAAU,SACV,QAAW,UACX,MAAS,eACT,UAAa,YACb,cAAiB,iBACjB,eAAkB,kBAClB,cAAiB,mBACjB,cAAiB,iBACjB,UAAa,aACb,SAAY,YACZ,SAAY,YACZ,QAAW,UACX,aAAgB,uBAChB,IAAO,QACP,SAAY,qBACZ,KAAQ,QACR,kBAAqB,sBACrB,eAAkB,oBAClB,aAAgB,kBAChB,aAAgB,gBAChB,SAAY,qBACZ,SAAY,qBACZ,SAAY,kBACZ,SAAY,kBACZ,gBAAmB,oBACnB,cAAiB,yBACjB,cAAiB,sBACjB,UAAa,YACb,mBAAsB,8BACtB,UAAa,UACb,gBAAmB,qBACnB,WAAc,kBAClB,CACJ,EACA,OAAU,gCACd,CAGA,GAE6B,IAAML,EAAcE,IAGvC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}