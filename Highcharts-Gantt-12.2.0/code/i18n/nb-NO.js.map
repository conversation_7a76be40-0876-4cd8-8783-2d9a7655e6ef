{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n* @license Highcharts JS v12.2.0 (2025-04-07)\n* @module highcharts/i18n/nb-NO\n* @requires highcharts\n*\n* nb-NO language pack\n*\n* (c) 2009-2025 Torstein Honsi\n*\n* License: www.highcharts.com/license\n*\n* **Do not edit this file!** This file is generated using the 'gulp lang-build' task.\n*/\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/i18n/nb-NO\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/i18n/nb-NO\"] = factory(root[\"_Highcharts\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (nb_NO_src)\n/* harmony export */ });\n/* harmony import */ var nb_NO_src_Core_Defaults_js_WEBPACK_IMPORTED_MODULE_0_ = __webpack_require__(944);\n/* harmony import */ var nb_NO_src_Core_Defaults_js_WEBPACK_IMPORTED_MODULE_0_default = /*#__PURE__*/__webpack_require__.n(nb_NO_src_Core_Defaults_js_WEBPACK_IMPORTED_MODULE_0_);\n\n\nconst { setOptions: nb_NO_src_setOptions } = (nb_NO_src_Core_Defaults_js_WEBPACK_IMPORTED_MODULE_0_default());\nconst nb_NO_src_languageOptions = {\n    \"locale\": \"nb-NO\",\n    \"chartTitle\": \"Diagramtittel\",\n    \"pieSliceName\": \"Skive\",\n    \"seriesName\": \"Serie {add index 1}\",\n    \"yAxisTitle\": \"Verdier\",\n    \"rangeSelector\": {\n        \"allText\": \"Alle\",\n        \"allTitle\": \"Vis alle\",\n        \"monthText\": \"{count}m\",\n        \"monthTitle\": \"Vis {count} {#eq count 1}måned{else}måneder{/eq}\",\n        \"yearText\": \"{count}år\",\n        \"yearTitle\": \"Vis {count} {#eq count 1}år{else}år{/eq}\",\n        \"ytdText\": \"YTD\",\n        \"ytdTitle\": \"Vis år til dato\"\n    },\n    \"stockOpen\": \"Åpning\",\n    \"stockHigh\": \"Høy\",\n    \"stockLow\": \"Lav\",\n    \"stockClose\": \"Slutt\",\n    \"viewFullscreen\": \"Vis i fullskjerm\",\n    \"weekFrom\": \"uka fra\",\n    \"exitFullscreen\": \"Avslutt fullskjerm\",\n    \"printChart\": \"Skriv ut diagram\",\n    \"downloadPNG\": \"Last ned PNG\",\n    \"downloadJPEG\": \"Last ned JPEG\",\n    \"downloadPDF\": \"Last ned PDF\",\n    \"downloadSVG\": \"Last ned SVG\",\n    \"contextButtonTitle\": \"Diagramkontekstmeny\",\n    \"loading\": \"Laster...\",\n    \"numericSymbols\": [\n        \"k\",\n        \"M\",\n        \"G\",\n        \"T\",\n        \"P\",\n        \"E\"\n    ],\n    \"resetZoom\": \"Tilbakestill zoom\",\n    \"resetZoomTitle\": \"Tilbakestill zoomnivå 1:1\",\n    \"rangeSelectorZoom\": \"Zoom\",\n    \"rangeSelectorFrom\": \"\",\n    \"rangeSelectorTo\": \"→\",\n    \"zoomIn\": \"Zoom inn\",\n    \"zoomOut\": \"Zoom ut\",\n    \"downloadCSV\": \"Last ned CSV\",\n    \"downloadXLS\": \"Last ned XLS\",\n    \"exportData\": {\n        \"annotationHeader\": \"Merknader\",\n        \"categoryHeader\": \"Kategori\",\n        \"categoryDatetimeHeader\": \"Dato og tid\"\n    },\n    \"viewData\": \"Vis datatabell\",\n    \"hideData\": \"Skjul datatabell\",\n    \"exportInProgress\": \"Eksporterer...\",\n    \"accessibility\": {\n        \"defaultChartTitle\": \"Diagram\",\n        \"chartContainerLabel\": \"{title}. Highcharts interaktivt diagram.\",\n        \"svgContainerLabel\": \"Interaktivt diagram\",\n        \"drillUpButton\": \"{buttonText}\",\n        \"credits\": \"Diagramkreditter: {creditsStr}\",\n        \"thousandsSep\": \",\",\n        \"svgContainerTitle\": \"\",\n        \"graphicContainerLabel\": \"\",\n        \"screenReaderSection\": {\n            \"beforeRegionLabel\": \"\",\n            \"afterRegionLabel\": \"\",\n            \"annotations\": {\n                \"heading\": \"Sammendrag av diagramannotasjoner\",\n                \"descriptionSinglePoint\": \"{annotationText}. Relatert til {annotationPoint}\",\n                \"descriptionMultiplePoints\": \"{annotationText}. Relatert til {annotationPoint}{#each additionalAnnotationPoints}, også relatert til {this}{/each}\",\n                \"descriptionNoPoints\": \"{annotationText}\"\n            },\n            \"endOfChartMarker\": \"Slutt på interaktivt diagram.\"\n        },\n        \"sonification\": {\n            \"playAsSoundButtonText\": \"Spill som lyd, {chartTitle}\",\n            \"playAsSoundClickAnnouncement\": \"Spill\"\n        },\n        \"legend\": {\n            \"legendLabelNoTitle\": \"Endre seriens synlighet, {chartTitle}\",\n            \"legendLabel\": \"Diagramforklaring: {legendTitle}\",\n            \"legendItem\": \"Vis {itemName}\"\n        },\n        \"zoom\": {\n            \"mapZoomIn\": \"Zoom inn\",\n            \"mapZoomOut\": \"Zoom ut\",\n            \"resetZoomButton\": \"Tilbakestill zoom\"\n        },\n        \"rangeSelector\": {\n            \"dropdownLabel\": \"{rangeTitle}\",\n            \"minInputLabel\": \"Velg startdato.\",\n            \"maxInputLabel\": \"Velg sluttdato.\",\n            \"clickButtonAnnouncement\": \"Viser {axisRangeDescription}\"\n        },\n        \"navigator\": {\n            \"handleLabel\": \"{#eq handleIx 0}Start, prosent{else}Slutt, prosent{/eq}\",\n            \"groupLabel\": \"Aksezoom\",\n            \"changeAnnouncement\": \"{axisRangeDescription}\"\n        },\n        \"table\": {\n            \"viewAsDataTableButtonText\": \"Vis som datatabell, {chartTitle}\",\n            \"tableSummary\": \"Tabellrepresentasjon av diagram.\"\n        },\n        \"announceNewData\": {\n            \"newDataAnnounce\": \"Oppdaterte data for diagram {chartTitle}\",\n            \"newSeriesAnnounceSingle\": \"Ny dataserie: {seriesDesc}\",\n            \"newPointAnnounceSingle\": \"Nytt datapunkt: {pointDesc}\",\n            \"newSeriesAnnounceMultiple\": \"Ny dataserie i diagram {chartTitle}: {seriesDesc}\",\n            \"newPointAnnounceMultiple\": \"Nytt datapunkt i diagram {chartTitle}: {pointDesc}\"\n        },\n        \"seriesTypeDescriptions\": {\n            \"boxplot\": \"Boksplott diagrammer brukes typisk til å vise grupper av statistiske data. Hvert datapunkt i diagrammet kan ha opptil 5 verdier: minimum, nedre kvartil, median, øvre kvartil, og maksimum.\",\n            \"arearange\": \"Områdeområde diagrammer er linjediagrammer som viser et område mellom en lavere og høyere verdi for hvert punkt.\",\n            \"areasplinerange\": \"Disse diagrammene er linjediagrammer som viser et område mellom en lavere og høyere verdi for hvert punkt.\",\n            \"bubble\": \"Boblediagrammer er spredningsdiagrammer der hvert datapunkt også har en størrelsesverdi.\",\n            \"columnrange\": \"Kolonneområde diagrammer er kolonnediagrammer som viser et område mellom en lavere og høyere verdi for hvert punkt.\",\n            \"errorbar\": \"Feilbjelkeserier brukes til å vise variabiliteten av data.\",\n            \"funnel\": \"Traktdiagrammer brukes til å vise reduksjon av data i stadier.\",\n            \"pyramid\": \"Pyramidediagrammer består av en enkelt pyramide med elementhøyder som svarer til hver punktverdi.\",\n            \"waterfall\": \"Et fossediagram er et kolonnediagram der hver kolonne bidrar mot en total sluttkverdi.\"\n        },\n        \"chartTypes\": {\n            \"emptyChart\": \"Tomt diagram\",\n            \"mapTypeDescription\": \"Kart over {mapTitle} med {numSeries} dataserier.\",\n            \"unknownMap\": \"Kart over uspesifisert region med {numSeries} dataserier.\",\n            \"combinationChart\": \"Kombinasjonsdiagram med {numSeries} dataserier.\",\n            \"defaultSingle\": \"Diagram med {numPoints} data {#eq numPoints 1}punkt{else}punkter{/eq}.\",\n            \"defaultMultiple\": \"Diagram med {numSeries} dataserier.\",\n            \"splineSingle\": \"Linjediagram med {numPoints} data {#eq numPoints 1}punkt{else}punkter{/eq}.\",\n            \"splineMultiple\": \"Linjediagram med {numSeries} linjer.\",\n            \"lineSingle\": \"Linjediagram med {numPoints} data {#eq numPoints 1}punkt{else}punkter{/eq}.\",\n            \"lineMultiple\": \"Linjediagram med {numSeries} linjer.\",\n            \"columnSingle\": \"Stolpediagram med {numPoints} {#eq numPoints 1}stolpe{else}stolper{/eq}.\",\n            \"columnMultiple\": \"Stolpediagram med {numSeries} dataserier.\",\n            \"barSingle\": \"Stolpediagram med {numPoints} {#eq numPoints 1}stolpe{else}stolper{/eq}.\",\n            \"barMultiple\": \"Stolpediagram med {numSeries} dataserier.\",\n            \"pieSingle\": \"Piediagram med {numPoints} {#eq numPoints 1}skive{else}skiver{/eq}.\",\n            \"pieMultiple\": \"Piediagram med {numSeries} paier.\",\n            \"scatterSingle\": \"Spredningsdiagram med {numPoints} {#eq numPoints 1}punkt{else}punkter{/eq}.\",\n            \"scatterMultiple\": \"Spredningsdiagram med {numSeries} dataserier.\",\n            \"boxplotSingle\": \"Boksplott med {numPoints} {#eq numPoints 1}boks{else}bokser{/eq}.\",\n            \"boxplotMultiple\": \"Boksplott med {numSeries} dataserier.\",\n            \"bubbleSingle\": \"Boblediagram med {numPoints} {#eq numPoints 1}boble{else}bobler{/eq}.\",\n            \"bubbleMultiple\": \"Boblediagram med {numSeries} dataserier.\"\n        },\n        \"axis\": {\n            \"xAxisDescriptionSingular\": \"Diagrammet har 1 X-akse som viser {names[0]}. {ranges[0]}\",\n            \"xAxisDescriptionPlural\": \"Diagrammet har {numAxes} X-akser som viser {#each names}{#unless @first},{/unless}{#if @last} og{/if} {this}{/each}.\",\n            \"yAxisDescriptionSingular\": \"Diagrammet har 1 Y-akse som viser {names[0]}. {ranges[0]}\",\n            \"yAxisDescriptionPlural\": \"Diagrammet har {numAxes} Y-akser som viser {#each names}{#unless @first},{/unless}{#if @last} og{/if} {this}{/each}.\",\n            \"timeRangeDays\": \"Dataområde: {range} dager.\",\n            \"timeRangeHours\": \"Dataområde: {range} timer.\",\n            \"timeRangeMinutes\": \"Dataområde: {range} minutter.\",\n            \"timeRangeSeconds\": \"Dataområde: {range} sekunder.\",\n            \"rangeFromTo\": \"Datarekkevidde fra {rangeFrom} til {rangeTo}.\",\n            \"rangeCategories\": \"Dataområde: {numCategories} kategorier.\"\n        },\n        \"exporting\": {\n            \"chartMenuLabel\": \"Diagrammeny\",\n            \"menuButtonLabel\": \"Vis diagrammeny, {chartTitle}\"\n        },\n        \"series\": {\n            \"summary\": {\n                \"default\": \"{series.name}, serie {seriesNumber} av {chart.series.length} med {series.points.length} data {#eq series.points.length 1}punkt{else}punkter{/eq}.\",\n                \"defaultCombination\": \"{series.name}, serie {seriesNumber} av {chart.series.length} med {series.points.length} data {#eq series.points.length 1}punkt{else}punkter{/eq}.\",\n                \"line\": \"{series.name}, linje {seriesNumber} av {chart.series.length} med {series.points.length} data {#eq series.points.length 1}punkt{else}punkter{/eq}.\",\n                \"lineCombination\": \"{series.name}, serie {seriesNumber} av {chart.series.length}. Linje med {series.points.length} data {#eq series.points.length 1}punkt{else}punkter{/eq}.\",\n                \"spline\": \"{series.name}, linje {seriesNumber} av {chart.series.length} med {series.points.length} data {#eq series.points.length 1}punkt{else}punkter{/eq}.\",\n                \"splineCombination\": \"{series.name}, serie {seriesNumber} av {chart.series.length}. Linje med {series.points.length} data {#eq series.points.length 1}punkt{else}punkter{/eq}.\",\n                \"column\": \"{series.name}, stolpeserie {seriesNumber} av {chart.series.length} med {series.points.length} {#eq series.points.length 1}stolpe{else}stolper{/eq}.\",\n                \"columnCombination\": \"{series.name}, serie {seriesNumber} av {chart.series.length}. Stolpeserie med {series.points.length} {#eq series.points.length 1}stolpe{else}stolper{/eq}.\",\n                \"bar\": \"{series.name}, stolpeserie {seriesNumber} av {chart.series.length} med {series.points.length} {#eq series.points.length 1}stolpe{else}stolper{/eq}.\",\n                \"barCombination\": \"{series.name}, serie {seriesNumber} av {chart.series.length}. Stolpeserie med {series.points.length} {#eq series.points.length 1}stolpe{else}stolper{/eq}.\",\n                \"pie\": \"{series.name}, pai {seriesNumber} av {chart.series.length} med {series.points.length} {#eq series.points.length 1}skive{else}skiver{/eq}.\",\n                \"pieCombination\": \"{series.name}, serie {seriesNumber} av {chart.series.length}. Pai med {series.points.length} {#eq series.points.length 1}skive{else}skiver{/eq}.\",\n                \"scatter\": \"{series.name}, spredningsdiagram {seriesNumber} av {chart.series.length} med {series.points.length} {#eq series.points.length 1}punkt{else}punkter{/eq}.\",\n                \"scatterCombination\": \"{series.name}, serie {seriesNumber} av {chart.series.length}, spredningsdiagram med {series.points.length} {#eq series.points.length 1}punkt{else}punkter{/eq}.\",\n                \"boxplot\": \"{series.name}, boksplott {seriesNumber} av {chart.series.length} med {series.points.length} {#eq series.points.length 1}boks{else}bokser{/eq}.\",\n                \"boxplotCombination\": \"{series.name}, serie {seriesNumber} av {chart.series.length}. Boksplott med {series.points.length} {#eq series.points.length 1}boks{else}bokser{/eq}.\",\n                \"bubble\": \"{series.name}, bobleserie {seriesNumber} av {chart.series.length} med {series.points.length} {#eq series.points.length 1}boble{else}bobler{/eq}.\",\n                \"bubbleCombination\": \"{series.name}, serie {seriesNumber} av {chart.series.length}. Bobleserie med {series.points.length} {#eq series.points.length 1}boble{else}bobler{/eq}.\",\n                \"map\": \"{series.name}, kart {seriesNumber} av {chart.series.length} med {series.points.length} {#eq series.points.length 1}område{else}områder{/eq}.\",\n                \"mapCombination\": \"{series.name}, serie {seriesNumber} av {chart.series.length}. Kart med {series.points.length} {#eq series.points.length 1}område{else}områder{/eq}.\",\n                \"mapline\": \"{series.name}, linje {seriesNumber} av {chart.series.length} med {series.points.length} data {#eq series.points.length 1}punkt{else}punkter{/eq}.\",\n                \"maplineCombination\": \"{series.name}, serie {seriesNumber} av {chart.series.length}. Linje med {series.points.length} data {#eq series.points.length 1}punkt{else}punkter{/eq}.\",\n                \"mapbubble\": \"{series.name}, bobleserie {seriesNumber} av {chart.series.length} med {series.points.length} {#eq series.points.length 1}boble{else}bobler{/eq}.\",\n                \"mapbubbleCombination\": \"{series.name}, serie {seriesNumber} av {chart.series.length}. Bobleserie med {series.points.length} {#eq series.points.length 1}boble{else}bobler{/eq}.\"\n            },\n            \"description\": \"{description}\",\n            \"xAxisDescription\": \"X-akse, {name}\",\n            \"yAxisDescription\": \"Y-akse, {name}\",\n            \"nullPointValue\": \"Ingen verdi\",\n            \"pointAnnotationsDescription\": \"{#each annotations}Annotasjon: {this}{/each}\"\n        }\n    },\n    \"navigation\": {\n        \"popup\": {\n            \"simpleShapes\": \"Enkle former\",\n            \"lines\": \"Linjer\",\n            \"circle\": \"Sirkel\",\n            \"ellipse\": \"Ellipse\",\n            \"rectangle\": \"Rektangel\",\n            \"label\": \"Etikett\",\n            \"shapeOptions\": \"Formalternativer\",\n            \"typeOptions\": \"Detaljer\",\n            \"fill\": \"Fyll\",\n            \"format\": \"Tekst\",\n            \"strokeWidth\": \"Linjebredde\",\n            \"stroke\": \"Linjefarge\",\n            \"title\": \"Tittel\",\n            \"name\": \"Navn\",\n            \"labelOptions\": \"Etikettalternativer\",\n            \"labels\": \"Etiketter\",\n            \"backgroundColor\": \"Bakgrunnsfarge\",\n            \"backgroundColors\": \"Bakgrunnsfarger\",\n            \"borderColor\": \"Kantfarge\",\n            \"borderRadius\": \"Kantradius\",\n            \"borderWidth\": \"Kantbredde\",\n            \"style\": \"Stil\",\n            \"padding\": \"Polstring\",\n            \"fontSize\": \"Skriftstørrelse\",\n            \"color\": \"Farge\",\n            \"height\": \"Høyde\",\n            \"shapes\": \"Formalternativer\",\n            \"segment\": \"Segment\",\n            \"arrowSegment\": \"Pilsegment\",\n            \"ray\": \"Stråle\",\n            \"arrowRay\": \"Pilstråle\",\n            \"line\": \"Linje\",\n            \"arrowInfinityLine\": \"Pillinje\",\n            \"horizontalLine\": \"Horisontal linje\",\n            \"verticalLine\": \"Vertikal linje\",\n            \"crooked3\": \"Kroket 3-linje\",\n            \"crooked5\": \"Kroket 5-linje\",\n            \"elliott3\": \"Elliott 3-linje\",\n            \"elliott5\": \"Elliott 5-linje\",\n            \"verticalCounter\": \"Vertikal teller\",\n            \"verticalLabel\": \"Vertikal etikett\",\n            \"verticalArrow\": \"Vertikal pil\",\n            \"fibonacci\": \"Fibonacci\",\n            \"fibonacciTimeZones\": \"Fibonacci tidssoner\",\n            \"pitchfork\": \"Gaffel\",\n            \"parallelChannel\": \"Parallel kanal\",\n            \"infinityLine\": \"Uendelighetslinje\",\n            \"measure\": \"Mål\",\n            \"measureXY\": \"Mål XY\",\n            \"measureX\": \"Mål X\",\n            \"measureY\": \"Mål Y\",\n            \"timeCycles\": \"Tidssykluser\",\n            \"flags\": \"Flagg\",\n            \"addButton\": \"Legg til\",\n            \"saveButton\": \"Lagre\",\n            \"editButton\": \"Rediger\",\n            \"removeButton\": \"Fjern\",\n            \"series\": \"Serier\",\n            \"volume\": \"Volum\",\n            \"connector\": \"Kobling\",\n            \"innerBackground\": \"Indre bakgrunn\",\n            \"outerBackground\": \"Ytre bakgrunn\",\n            \"crosshairX\": \"Krysshår X\",\n            \"crosshairY\": \"Krysshår Y\",\n            \"tunnel\": \"Tunnel\",\n            \"background\": \"Bakgrunn\",\n            \"noFilterMatch\": \"Ingen treff\",\n            \"searchIndicators\": \"Søk indikatorer\",\n            \"clearFilter\": \"✕ fjern filter\",\n            \"index\": \"Indeks\",\n            \"period\": \"Periode\",\n            \"periods\": \"Perioder\",\n            \"standardDeviation\": \"Standardavvik\",\n            \"periodTenkan\": \"Tenkan-periode\",\n            \"periodSenkouSpanB\": \"Senkou Span B-periode\",\n            \"periodATR\": \"ATR-periode\",\n            \"multiplierATR\": \"ATR-multiplikator\",\n            \"shortPeriod\": \"Kort periode\",\n            \"longPeriod\": \"Lang periode\",\n            \"signalPeriod\": \"Signalperiode\",\n            \"decimals\": \"Desimaler\",\n            \"algorithm\": \"Algoritme\",\n            \"topBand\": \"Øvre bånd\",\n            \"bottomBand\": \"Nedre bånd\",\n            \"initialAccelerationFactor\": \"Innledende akselerasjonsfaktor\",\n            \"maxAccelerationFactor\": \"Maksimal akselerasjonsfaktor\",\n            \"increment\": \"Økning\",\n            \"multiplier\": \"Multiplikator\",\n            \"ranges\": \"Områder\",\n            \"highIndex\": \"Høy indeks\",\n            \"lowIndex\": \"Lav indeks\",\n            \"deviation\": \"Avvik\",\n            \"xAxisUnit\": \"x-akseenhet\",\n            \"factor\": \"Faktor\",\n            \"fastAvgPeriod\": \"Rask gjennomsnittlig periode\",\n            \"slowAvgPeriod\": \"Langsom gjennomsnittlig periode\",\n            \"average\": \"Gjennomsnitt\",\n            \"indicatorAliases\": {\n                \"abands\": [\n                    \"Akselerasjonsbånd\"\n                ],\n                \"bb\": [\n                    \"Bollinger-bånd\"\n                ],\n                \"dema\": [\n                    \"Dobbelt eksponentielt glidende gjennomsnitt\"\n                ],\n                \"ema\": [\n                    \"Eksponentielt glidende gjennomsnitt\"\n                ],\n                \"ikh\": [\n                    \"Ichimoku Kinko Hyo\"\n                ],\n                \"keltnerchannels\": [\n                    \"Keltner-kanaler\"\n                ],\n                \"linearRegression\": [\n                    \"Lineær regresjon\"\n                ],\n                \"pivotpoints\": [\n                    \"Pivotpunkter\"\n                ],\n                \"pc\": [\n                    \"Priskanal\"\n                ],\n                \"priceenvelopes\": [\n                    \"Prisenvelopper\"\n                ],\n                \"psar\": [\n                    \"Parabolic SAR\"\n                ],\n                \"sma\": [\n                    \"Enkelt glidende gjennomsnitt\"\n                ],\n                \"supertrend\": [\n                    \"Supertrend\"\n                ],\n                \"tema\": [\n                    \"Trippel eksponentielt glidende gjennomsnitt\"\n                ],\n                \"vbp\": [\n                    \"Volum ved pris\"\n                ],\n                \"vwap\": [\n                    \"Volumvektet glidende gjennomsnitt\"\n                ],\n                \"wma\": [\n                    \"Vektet glidende gjennomsnitt\"\n                ],\n                \"zigzag\": [\n                    \"Zigzag\"\n                ],\n                \"apo\": [\n                    \"Absolutt prisindikator\"\n                ],\n                \"ad\": [\n                    \"Akkumulering/Distribusjon\"\n                ],\n                \"aroon\": [\n                    \"Aroon\"\n                ],\n                \"aroonoscillator\": [\n                    \"Aroon-oscillator\"\n                ],\n                \"atr\": [\n                    \"Gjennomsnittlig sann rekkevidde\"\n                ],\n                \"ao\": [\n                    \"Fantastisk oscillator\"\n                ],\n                \"cci\": [\n                    \"Commodity Channel Index\"\n                ],\n                \"chaikin\": [\n                    \"Chaikin\"\n                ],\n                \"cmf\": [\n                    \"Chaikin Money Flow\"\n                ],\n                \"cmo\": [\n                    \"Chande Momentum Oscillator\"\n                ],\n                \"disparityindex\": [\n                    \"Disparitetsindeks\"\n                ],\n                \"dmi\": [\n                    \"Retningsbevegelsesindeks\"\n                ],\n                \"dpo\": [\n                    \"Detrended prisoscillator\"\n                ],\n                \"klinger\": [\n                    \"Klinger Oscillator\"\n                ],\n                \"linearRegressionAngle\": [\n                    \"Lineær regresjonsvinkel\"\n                ],\n                \"linearRegressionIntercept\": [\n                    \"Lineær regresjonsavskjæring\"\n                ],\n                \"linearRegressionSlope\": [\n                    \"Lineær regresjonsstigning\"\n                ],\n                \"macd\": [\n                    \"Glidende gjennomsnittskonvergensdivergens\"\n                ],\n                \"mfi\": [\n                    \"Pengeflytindeks\"\n                ],\n                \"momentum\": [\n                    \"Momentum\"\n                ],\n                \"natr\": [\n                    \"Normalisert gjennomsnittlig sann rekkevidde\"\n                ],\n                \"obv\": [\n                    \"On-Balance Volume\"\n                ],\n                \"ppo\": [\n                    \"Prosentvis prisoscillator\"\n                ],\n                \"roc\": [\n                    \"Endringsrate\"\n                ],\n                \"rsi\": [\n                    \"Relativ styrkeindeks\"\n                ],\n                \"slowstochastic\": [\n                    \"Langsom stokastisk\"\n                ],\n                \"stochastic\": [\n                    \"Stokastisk\"\n                ],\n                \"trix\": [\n                    \"TRIX\"\n                ],\n                \"williamsr\": [\n                    \"Williams %R\"\n                ]\n            }\n        }\n    },\n    \"mainBreadcrumb\": \"Hoved\",\n    \"downloadMIDI\": \"Last ned MIDI\",\n    \"playAsSound\": \"Spill av som lyd\",\n    \"stockTools\": {\n        \"gui\": {\n            \"simpleShapes\": \"Enkle former\",\n            \"lines\": \"Linjer\",\n            \"crookedLines\": \"Krokete linjer\",\n            \"measure\": \"Mål\",\n            \"advanced\": \"Avansert\",\n            \"toggleAnnotations\": \"Veksle annotasjoner\",\n            \"verticalLabels\": \"Vertikale etiketter\",\n            \"flags\": \"Flagg\",\n            \"zoomChange\": \"Zoom endring\",\n            \"typeChange\": \"Type endring\",\n            \"saveChart\": \"Lagre diagram\",\n            \"indicators\": \"Indikatorer\",\n            \"currentPriceIndicator\": \"Indikatorer for nåværende pris\",\n            \"zoomX\": \"Zoom X\",\n            \"zoomY\": \"Zoom Y\",\n            \"zoomXY\": \"Zoom XY\",\n            \"fullScreen\": \"Fullskjerm\",\n            \"typeOHLC\": \"OHLC\",\n            \"typeLine\": \"Linje\",\n            \"typeCandlestick\": \"Lysestake\",\n            \"typeHLC\": \"HLC\",\n            \"typeHollowCandlestick\": \"Hul lysestake\",\n            \"typeHeikinAshi\": \"Heikin Ashi\",\n            \"circle\": \"Sirkel\",\n            \"ellipse\": \"Ellipse\",\n            \"label\": \"Etikett\",\n            \"rectangle\": \"Rektangel\",\n            \"flagCirclepin\": \"Flagg sirkel\",\n            \"flagDiamondpin\": \"Flagg diamant\",\n            \"flagSquarepin\": \"Flagg kvadrat\",\n            \"flagSimplepin\": \"Flagg enkel\",\n            \"measureXY\": \"Mål XY\",\n            \"measureX\": \"Mål X\",\n            \"measureY\": \"Mål Y\",\n            \"segment\": \"Segment\",\n            \"arrowSegment\": \"Pilsegment\",\n            \"ray\": \"Stråle\",\n            \"arrowRay\": \"Pilstråle\",\n            \"line\": \"Linje\",\n            \"arrowInfinityLine\": \"Pillinje\",\n            \"horizontalLine\": \"Horisontal linje\",\n            \"verticalLine\": \"Vertikal linje\",\n            \"infinityLine\": \"Uendelighetslinje\",\n            \"crooked3\": \"Kroket 3-linje\",\n            \"crooked5\": \"Kroket 5-linje\",\n            \"elliott3\": \"Elliott 3-linje\",\n            \"elliott5\": \"Elliott 5-linje\",\n            \"verticalCounter\": \"Vertikal teller\",\n            \"verticalLabel\": \"Vertikal etikett\",\n            \"verticalArrow\": \"Vertikal pil\",\n            \"fibonacci\": \"Fibonacci\",\n            \"fibonacciTimeZones\": \"Fibonacci tidssoner\",\n            \"pitchfork\": \"Høygaffel\",\n            \"parallelChannel\": \"Parallel kanal\",\n            \"timeCycles\": \"Tidssykluser\"\n        }\n    },\n    \"noData\": \"Ingen data å vise\"\n};\nnb_NO_src_setOptions({\n    lang: nb_NO_src_languageOptions\n});\n// Export Highcharts\n/* harmony default export */ const nb_NO_src = ((nb_NO_src_Core_Defaults_js_WEBPACK_IMPORTED_MODULE_0_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "nb_NO_src", "nb_NO_src_Core_Defaults_js_WEBPACK_IMPORTED_MODULE_0_", "nb_NO_src_Core_Defaults_js_WEBPACK_IMPORTED_MODULE_0_default", "setOptions", "nb_NO_src_setOptions", "lang"], "mappings": "CAaA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,EACrC,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,wBAAyB,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAM,GAC1F,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,wBAAwB,CAAGD,EAAQD,EAAK,WAAc,EAE9DA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,AAACC,GACnC,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIE,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaX,OAAO,CAG5B,IAAIC,EAASO,CAAwB,CAACE,EAAS,CAAG,CAGjDV,QAAS,CAAC,CACX,EAMA,OAHAO,CAAmB,CAACG,EAAS,CAACT,EAAQA,EAAOD,OAAO,CAAES,GAG/CR,EAAOD,OAAO,AACtB,CAMCS,EAAoBI,CAAC,CAAG,AAACZ,IACxB,IAAIa,EAASb,GAAUA,EAAOc,UAAU,CACvC,IAAOd,EAAO,OAAU,CACxB,IAAOA,EAER,OADAQ,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAChB,EAASkB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACpB,EAASmB,IAC5EE,OAAOC,cAAc,CAACtB,EAASmB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EACNrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAOC,CACpB,GACA,IAAIC,EAAwDvB,EAAoB,KAC5EwB,EAA4ExB,EAAoBI,CAAC,CAACmB,GAG3H,GAAM,CAAEE,WAAYC,CAAoB,CAAE,CAAIF,IAyf9CE,EAAqB,CACjBC,KAzf8B,CAC9B,OAAU,QACV,WAAc,gBACd,aAAgB,QAChB,WAAc,sBACd,WAAc,UACd,cAAiB,CACb,QAAW,OACX,SAAY,WACZ,UAAa,WACb,WAAc,yDACd,SAAY,eACZ,UAAa,iDACb,QAAW,MACX,SAAY,oBAChB,EACA,UAAa,YACb,UAAa,SACb,SAAY,MACZ,WAAc,QACd,eAAkB,mBAClB,SAAY,UACZ,eAAkB,qBAClB,WAAc,mBACd,YAAe,eACf,aAAgB,gBAChB,YAAe,eACf,YAAe,eACf,mBAAsB,sBACtB,QAAW,YACX,eAAkB,CACd,IACA,IACA,IACA,IACA,IACA,IACH,CACD,UAAa,oBACb,eAAkB,+BAClB,kBAAqB,OACrB,kBAAqB,GACrB,gBAAmB,IACnB,OAAU,WACV,QAAW,UACX,YAAe,eACf,YAAe,eACf,WAAc,CACV,iBAAoB,YACpB,eAAkB,WAClB,uBAA0B,aAC9B,EACA,SAAY,iBACZ,SAAY,mBACZ,iBAAoB,iBACpB,cAAiB,CACb,kBAAqB,UACrB,oBAAuB,2CACvB,kBAAqB,sBACrB,cAAiB,eACjB,QAAW,iCACX,aAAgB,IAChB,kBAAqB,GACrB,sBAAyB,GACzB,oBAAuB,CACnB,kBAAqB,GACrB,iBAAoB,GACpB,YAAe,CACX,QAAW,oCACX,uBAA0B,mDAC1B,0BAA6B,yHAC7B,oBAAuB,kBAC3B,EACA,iBAAoB,kCACxB,EACA,aAAgB,CACZ,sBAAyB,8BACzB,6BAAgC,OACpC,EACA,OAAU,CACN,mBAAsB,wCACtB,YAAe,mCACf,WAAc,gBAClB,EACA,KAAQ,CACJ,UAAa,WACb,WAAc,UACd,gBAAmB,mBACvB,EACA,cAAiB,CACb,cAAiB,eACjB,cAAiB,kBACjB,cAAiB,kBACjB,wBAA2B,8BAC/B,EACA,UAAa,CACT,YAAe,0DACf,WAAc,WACd,mBAAsB,wBAC1B,EACA,MAAS,CACL,0BAA6B,mCAC7B,aAAgB,kCACpB,EACA,gBAAmB,CACf,gBAAmB,2CACnB,wBAA2B,6BAC3B,uBAA0B,8BAC1B,0BAA6B,oDAC7B,yBAA4B,oDAChC,EACA,uBAA0B,CACtB,QAAW,oMACX,UAAa,+HACb,gBAAmB,mHACnB,OAAU,iGACV,YAAe,+HACf,SAAY,gEACZ,OAAU,oEACV,QAAW,0GACX,UAAa,wFACjB,EACA,WAAc,CACV,WAAc,eACd,mBAAsB,mDACtB,WAAc,4DACd,iBAAoB,kDACpB,cAAiB,yEACjB,gBAAmB,sCACnB,aAAgB,8EAChB,eAAkB,uCAClB,WAAc,8EACd,aAAgB,uCAChB,aAAgB,2EAChB,eAAkB,4CAClB,UAAa,2EACb,YAAe,4CACf,UAAa,sEACb,YAAe,oCACf,cAAiB,8EACjB,gBAAmB,gDACnB,cAAiB,oEACjB,gBAAmB,wCACnB,aAAgB,wEAChB,eAAkB,0CACtB,EACA,KAAQ,CACJ,yBAA4B,4DAC5B,uBAA0B,uHAC1B,yBAA4B,4DAC5B,uBAA0B,uHAC1B,cAAiB,gCACjB,eAAkB,gCAClB,iBAAoB,mCACpB,iBAAoB,mCACpB,YAAe,gDACf,gBAAmB,4CACvB,EACA,UAAa,CACT,eAAkB,cAClB,gBAAmB,+BACvB,EACA,OAAU,CACN,QAAW,CACP,QAAW,oJACX,mBAAsB,oJACtB,KAAQ,oJACR,gBAAmB,2JACnB,OAAU,oJACV,kBAAqB,2JACrB,OAAU,sJACV,kBAAqB,6JACrB,IAAO,sJACP,eAAkB,6JAClB,IAAO,4IACP,eAAkB,mJAClB,QAAW,2JACX,mBAAsB,kKACtB,QAAW,iJACX,mBAAsB,wJACtB,OAAU,mJACV,kBAAqB,0JACrB,IAAO,qJACP,eAAkB,4JAClB,QAAW,oJACX,mBAAsB,2JACtB,UAAa,mJACb,qBAAwB,yJAC5B,EACA,YAAe,gBACf,iBAAoB,iBACpB,iBAAoB,iBACpB,eAAkB,cAClB,4BAA+B,8CACnC,CACJ,EACA,WAAc,CACV,MAAS,CACL,aAAgB,eAChB,MAAS,SACT,OAAU,SACV,QAAW,UACX,UAAa,YACb,MAAS,UACT,aAAgB,mBAChB,YAAe,WACf,KAAQ,OACR,OAAU,QACV,YAAe,cACf,OAAU,aACV,MAAS,SACT,KAAQ,OACR,aAAgB,sBAChB,OAAU,YACV,gBAAmB,iBACnB,iBAAoB,kBACpB,YAAe,YACf,aAAgB,aAChB,YAAe,aACf,MAAS,OACT,QAAW,YACX,SAAY,qBACZ,MAAS,QACT,OAAU,WACV,OAAU,mBACV,QAAW,UACX,aAAgB,aAChB,IAAO,YACP,SAAY,eACZ,KAAQ,QACR,kBAAqB,WACrB,eAAkB,mBAClB,aAAgB,iBAChB,SAAY,iBACZ,SAAY,iBACZ,SAAY,kBACZ,SAAY,kBACZ,gBAAmB,kBACnB,cAAiB,mBACjB,cAAiB,eACjB,UAAa,YACb,mBAAsB,sBACtB,UAAa,SACb,gBAAmB,iBACnB,aAAgB,oBAChB,QAAW,SACX,UAAa,YACb,SAAY,WACZ,SAAY,WACZ,WAAc,eACd,MAAS,QACT,UAAa,WACb,WAAc,QACd,WAAc,UACd,aAAgB,QAChB,OAAU,SACV,OAAU,QACV,UAAa,UACb,gBAAmB,iBACnB,gBAAmB,gBACnB,WAAc,gBACd,WAAc,gBACd,OAAU,SACV,WAAc,WACd,cAAiB,cACjB,iBAAoB,qBACpB,YAAe,iBACf,MAAS,SACT,OAAU,UACV,QAAW,WACX,kBAAqB,gBACrB,aAAgB,iBAChB,kBAAqB,wBACrB,UAAa,cACb,cAAiB,oBACjB,YAAe,eACf,WAAc,eACd,aAAgB,gBAChB,SAAY,YACZ,UAAa,YACb,QAAW,kBACX,WAAc,gBACd,0BAA6B,iCAC7B,sBAAyB,+BACzB,UAAa,YACb,WAAc,gBACd,OAAU,aACV,UAAa,gBACb,SAAY,aACZ,UAAa,QACb,UAAa,cACb,OAAU,SACV,cAAiB,+BACjB,cAAiB,kCACjB,QAAW,eACX,iBAAoB,CAChB,OAAU,CACN,uBACH,CACD,GAAM,CACF,oBACH,CACD,KAAQ,CACJ,8CACH,CACD,IAAO,CACH,sCACH,CACD,IAAO,CACH,qBACH,CACD,gBAAmB,CACf,kBACH,CACD,iBAAoB,CAChB,sBACH,CACD,YAAe,CACX,eACH,CACD,GAAM,CACF,YACH,CACD,eAAkB,CACd,iBACH,CACD,KAAQ,CACJ,gBACH,CACD,IAAO,CACH,+BACH,CACD,WAAc,CACV,aACH,CACD,KAAQ,CACJ,8CACH,CACD,IAAO,CACH,iBACH,CACD,KAAQ,CACJ,oCACH,CACD,IAAO,CACH,+BACH,CACD,OAAU,CACN,SACH,CACD,IAAO,CACH,yBACH,CACD,GAAM,CACF,4BACH,CACD,MAAS,CACL,QACH,CACD,gBAAmB,CACf,mBACH,CACD,IAAO,CACH,kCACH,CACD,GAAM,CACF,wBACH,CACD,IAAO,CACH,0BACH,CACD,QAAW,CACP,UACH,CACD,IAAO,CACH,qBACH,CACD,IAAO,CACH,6BACH,CACD,eAAkB,CACd,oBACH,CACD,IAAO,CACH,2BACH,CACD,IAAO,CACH,2BACH,CACD,QAAW,CACP,qBACH,CACD,sBAAyB,CACrB,6BACH,CACD,0BAA6B,CACzB,oCACH,CACD,sBAAyB,CACrB,+BACH,CACD,KAAQ,CACJ,4CACH,CACD,IAAO,CACH,kBACH,CACD,SAAY,CACR,WACH,CACD,KAAQ,CACJ,8CACH,CACD,IAAO,CACH,oBACH,CACD,IAAO,CACH,4BACH,CACD,IAAO,CACH,eACH,CACD,IAAO,CACH,uBACH,CACD,eAAkB,CACd,qBACH,CACD,WAAc,CACV,aACH,CACD,KAAQ,CACJ,OACH,CACD,UAAa,CACT,cACH,AACL,CACJ,CACJ,EACA,eAAkB,QAClB,aAAgB,gBAChB,YAAe,mBACf,WAAc,CACV,IAAO,CACH,aAAgB,eAChB,MAAS,SACT,aAAgB,iBAChB,QAAW,SACX,SAAY,WACZ,kBAAqB,sBACrB,eAAkB,sBAClB,MAAS,QACT,WAAc,eACd,WAAc,eACd,UAAa,gBACb,WAAc,cACd,sBAAyB,uCACzB,MAAS,SACT,MAAS,SACT,OAAU,UACV,WAAc,aACd,SAAY,OACZ,SAAY,QACZ,gBAAmB,YACnB,QAAW,MACX,sBAAyB,gBACzB,eAAkB,cAClB,OAAU,SACV,QAAW,UACX,MAAS,UACT,UAAa,YACb,cAAiB,eACjB,eAAkB,gBAClB,cAAiB,gBACjB,cAAiB,cACjB,UAAa,YACb,SAAY,WACZ,SAAY,WACZ,QAAW,UACX,aAAgB,aAChB,IAAO,YACP,SAAY,eACZ,KAAQ,QACR,kBAAqB,WACrB,eAAkB,mBAClB,aAAgB,iBAChB,aAAgB,oBAChB,SAAY,iBACZ,SAAY,iBACZ,SAAY,kBACZ,SAAY,kBACZ,gBAAmB,kBACnB,cAAiB,mBACjB,cAAiB,eACjB,UAAa,YACb,mBAAsB,sBACtB,UAAa,eACb,gBAAmB,iBACnB,WAAc,cAClB,CACJ,EACA,OAAU,sBACd,CAGA,GAE6B,IAAML,EAAcE,IAGvC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}