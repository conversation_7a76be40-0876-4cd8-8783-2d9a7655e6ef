
<!DOCTYPE HTML>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>Highcharts Gantt Examples</title>
        <style>
            * {
                font-family: sans-serif;
            }
            ul.nav > li > div {
                font-size: 1.5em;
                font-weight: bold;
                margin: 1em 0 0.3em 0;
            }
            ul.nav > li {
                list-style: none;
                display: black
            }
            div > ul > li {
                padding-bottom: 0.5em;
            }
            ul ul {
                list-style-type: initial;
                padding-left: 1.25em;
                font-size: 1.15em;
            }
            li button.sidebar-category {
                border: none;
                background: none;
                padding: 0;
                margin: 1rem 0 0.5rem 0;
                font-size: 1.4rem;
            }
            li a {
                text-decoration: none;
                color: #6065c8;
            }
            li a:hover {
                text-decoration: underline;
            }
        </style>
    </head>
    <body>
    <h1>Highcharts Gantt Examples</h1>
    <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12 sidebar d-md-block"><ul class="nav nav-sidebar" role="menu">
    <li>
        <button aria-controls="highcharts-gantt-demos" aria-expanded="false" class="sidebar-category" tabindex="0"><span>Highcharts Gantt</span><icon class="toggle"></icon></button>
        <ul id="highcharts-gantt-demos" role="group" >
        <li role="menuitem"><a href="examples/./download-pdf/index.html">Download PDF</a></li><li role="menuitem"><a href="examples/./custom-labels/index.html">Custom data labels with symbols</a></li><li role="menuitem"><a href="examples/./inverted/index.html">Inverted chart</a></li><li role="menuitem"><a href="examples/./styled-mode/index.html">Styled mode</a></li><li role="menuitem"><a href="examples/./left-axis-table/index.html">Left axis as a table</a></li><li role="menuitem"><a href="examples/./progress-indicator/index.html">Progress indicator</a></li><li role="menuitem"><a href="examples/./subtasks/index.html">Subtasks</a></li><li role="menuitem"><a href="examples/./interactive-gantt/index.html">Interactive gantt</a></li><li role="menuitem"><a href="examples/./resource-management/index.html">Resource Management</a></li><li role="menuitem"><a href="examples/./project-management/index.html">Project Management</a></li><li role="menuitem"><a href="examples/./treegrid-columns/index.html">Treegrid axis with columns</a></li><li role="menuitem"><a href="examples/./with-navigation/index.html">With navigation</a></li>
        </ul>
    </li>
    </ul></div>
    </body>
</html>