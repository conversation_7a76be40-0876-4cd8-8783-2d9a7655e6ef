#!/usr/bin/env python3
"""
简化的UI改进测试脚本
"""

import os
import re

def test_template_files():
    """测试模板文件"""
    print("🚀 开始测试UI改进功能...")
    
    # 测试模板文件存在性
    template_files = [
        'templates/gantt.html',
        'templates/project_tasks.html',
        'templates/project_detail.html',
        'templates/index.html',
        'templates/base.html',
    ]
    
    print("\n📁 测试模板文件:")
    all_exist = True
    for template in template_files:
        exists = os.path.exists(template)
        status = "✅ 存在" if exists else "❌ 不存在"
        print(f"  {template}: {status}")
        if not exists:
            all_exist = False
    
    return all_exist

def test_gantt_improvements():
    """测试甘特图页面改进"""
    print("\n🎨 测试甘特图页面改进:")
    
    gantt_template_path = 'templates/gantt.html'
    if not os.path.exists(gantt_template_path):
        print("  ❌ 甘特图模板文件不存在")
        return False
    
    with open(gantt_template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    features_to_check = [
        ('模板选择功能', 'template-section'),
        ('项目模板卡片', 'template-card'),
        ('任务搜索功能', 'task-search'),
        ('任务筛选功能', 'filterTasks'),
        ('通知系统', 'showNotification'),
        ('响应式设计', '@media'),
        ('现代化样式', 'linear-gradient'),
        ('任务详情面板', 'task-details-panel'),
        ('工具栏优化', 'toolbar-section'),
        ('空状态设计', 'empty-state'),
        ('进度条动画', 'task-progress-bar'),
        ('优先级图标', 'getPriorityIcon'),
        ('状态颜色', 'getStatusColor'),
        ('日期格式化', 'formatDate'),
    ]
    
    improvements_count = 0
    for feature_name, feature_code in features_to_check:
        exists = feature_code in content
        status = "✅ 已实现" if exists else "❌ 未实现"
        print(f"  {feature_name}: {status}")
        if exists:
            improvements_count += 1
    
    print(f"\n  📊 甘特图改进完成度: {improvements_count}/{len(features_to_check)} ({improvements_count/len(features_to_check)*100:.1f}%)")
    return improvements_count > len(features_to_check) * 0.8

def test_project_tasks_page():
    """测试项目任务页面"""
    print("\n📋 测试项目任务页面:")
    
    project_tasks_template_path = 'templates/project_tasks.html'
    if not os.path.exists(project_tasks_template_path):
        print("  ❌ 项目任务模板文件不存在")
        return False
    
    with open(project_tasks_template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    features_to_check = [
        ('项目头部设计', 'project-header'),
        ('面包屑导航', 'breadcrumb-nav'),
        ('项目统计网格', 'stats-grid'),
        ('统计卡片', 'stat-card'),
        ('任务卡片设计', 'task-card'),
        ('工具栏功能', 'toolbar'),
        ('搜索筛选', 'task-filter'),
        ('空状态设计', 'empty-state'),
        ('响应式布局', '@media'),
        ('现代化样式', 'linear-gradient'),
        ('功能图标', 'feature-icon'),
        ('任务进度条', 'task-progress'),
    ]
    
    improvements_count = 0
    for feature_name, feature_code in features_to_check:
        exists = feature_code in content
        status = "✅ 已实现" if exists else "❌ 未实现"
        print(f"  {feature_name}: {status}")
        if exists:
            improvements_count += 1
    
    print(f"\n  📊 项目任务页面完成度: {improvements_count}/{len(features_to_check)} ({improvements_count/len(features_to_check)*100:.1f}%)")
    return improvements_count > len(features_to_check) * 0.8

def test_url_configuration():
    """测试URL配置"""
    print("\n🔗 测试URL配置:")
    
    urls_file = 'ai_deep_cpms/urls.py'
    if not os.path.exists(urls_file):
        print("  ❌ URL配置文件不存在")
        return False
    
    with open(urls_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    url_patterns = [
        ('甘特图页面', 'gantt/'),
        ('项目详情页面', 'project/'),
        ('项目任务页面', 'project-tasks/'),
        ('创建项目页面', 'create-project/'),
    ]
    
    configured_count = 0
    for page_name, url_pattern in url_patterns:
        exists = url_pattern in content
        status = "✅ 已配置" if exists else "❌ 未配置"
        print(f"  {page_name}: {status}")
        if exists:
            configured_count += 1
    
    print(f"\n  📊 URL配置完成度: {configured_count}/{len(url_patterns)} ({configured_count/len(url_patterns)*100:.1f}%)")
    return configured_count == len(url_patterns)

def test_home_page_improvements():
    """测试首页改进"""
    print("\n🏠 测试首页改进:")
    
    index_template_path = 'templates/index.html'
    if not os.path.exists(index_template_path):
        print("  ❌ 首页模板文件不存在")
        return False
    
    with open(index_template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    improvements = [
        ('项目任务链接', 'viewProjectTasks'),
        ('任务按钮', 'fas fa-tasks'),
        ('项目卡片优化', 'project-card'),
        ('统计数据显示', 'stats-card'),
        ('功能特性展示', 'feature-icon'),
    ]
    
    improved_count = 0
    for improvement_name, improvement_code in improvements:
        exists = improvement_code in content
        status = "✅ 已改进" if exists else "❌ 未改进"
        print(f"  {improvement_name}: {status}")
        if exists:
            improved_count += 1
    
    print(f"\n  📊 首页改进完成度: {improved_count}/{len(improvements)} ({improved_count/len(improvements)*100:.1f}%)")
    return improved_count > len(improvements) * 0.8

def analyze_code_quality():
    """分析代码质量"""
    print("\n🔍 代码质量分析:")
    
    template_files = [
        'templates/gantt.html',
        'templates/project_tasks.html',
    ]
    
    total_lines = 0
    css_lines = 0
    js_lines = 0
    
    for template_file in template_files:
        if os.path.exists(template_file):
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                total_lines += len(lines)
                
                # 统计CSS行数
                css_match = re.search(r'<style>(.*?)</style>', content, re.DOTALL)
                if css_match:
                    css_lines += len(css_match.group(1).split('\n'))
                
                # 统计JavaScript行数
                js_match = re.search(r'<script>(.*?)</script>', content, re.DOTALL)
                if js_match:
                    js_lines += len(js_match.group(1).split('\n'))
    
    print(f"  📄 总代码行数: {total_lines}")
    print(f"  🎨 CSS样式行数: {css_lines}")
    print(f"  ⚡ JavaScript行数: {js_lines}")
    print(f"  📊 前端代码占比: {(css_lines + js_lines)/total_lines*100:.1f}%")

def main():
    """主函数"""
    print("🎨 AI-DEEP-CPMS UI改进测试")
    print("=" * 50)
    
    # 执行各项测试
    tests = [
        ("模板文件", test_template_files),
        ("甘特图改进", test_gantt_improvements),
        ("项目任务页面", test_project_tasks_page),
        ("URL配置", test_url_configuration),
        ("首页改进", test_home_page_improvements),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed_tests += 1
        except Exception as e:
            print(f"  ❌ 测试 {test_name} 时出错: {str(e)}")
    
    # 代码质量分析
    analyze_code_quality()
    
    # 总结
    print(f"\n📊 测试总结:")
    print(f"  通过测试: {passed_tests}/{total_tests}")
    print(f"  成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！UI改进功能已成功实现。")
    else:
        print(f"\n⚠️  有 {total_tests - passed_tests} 项测试未通过，请检查相关功能。")
    
    print("\n✨ UI改进亮点:")
    print("  • 🎨 现代化设计语言，模仿上传界面风格")
    print("  • 📱 完全响应式布局，支持移动端")
    print("  • 🔄 流畅的交互动画和过渡效果")
    print("  • 🎯 项目模板选择功能")
    print("  • 🔍 高级搜索和筛选功能")
    print("  • 📋 增强的任务详情面板")
    print("  • 🔔 智能通知系统")
    print("  • 🎪 精美的空状态设计")
    print("  • 📊 直观的项目统计展示")
    print("  • 🛠️  优化的工具栏和操作界面")
    
    print("\n💡 使用说明:")
    print("  1. 启动开发服务器: python manage.py runserver")
    print("  2. 访问首页: http://localhost:8000/")
    print("  3. 体验甘特图页面: http://localhost:8000/gantt/")
    print("  4. 体验项目任务页面: http://localhost:8000/project-tasks/")

if __name__ == '__main__':
    main()
