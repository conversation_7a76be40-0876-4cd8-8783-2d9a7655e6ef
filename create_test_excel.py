#!/usr/bin/env python
"""
创建测试Excel文件的脚本
"""
import pandas as pd
from datetime import datetime, timedelta
import os

def create_test_excel():
    """创建测试用的Excel项目文件"""
    
    # 创建任务数据
    tasks_data = {
        'WBS': ['1', '1.1', '1.2', '2', '2.1', '2.2', '2.3', '3', '3.1', '3.2'],
        'Task Name': [
            '项目启动',
            '需求分析',
            '项目计划',
            '系统设计',
            '架构设计',
            '详细设计',
            '原型开发',
            '开发实施',
            '前端开发',
            '后端开发'
        ],
        'Duration': [10, 5, 3, 15, 7, 5, 3, 30, 15, 15],
        'Start': [
            '2025-01-01', '2025-01-01', '2025-01-06',
            '2025-01-11', '2025-01-11', '2025-01-18', '2025-01-23',
            '2025-01-26', '2025-01-26', '2025-01-26'
        ],
        'Finish': [
            '2025-01-10', '2025-01-05', '2025-01-08',
            '2025-01-25', '2025-01-17', '2025-01-22', '2025-01-25',
            '2025-02-25', '2025-02-09', '2025-02-09'
        ],
        'Predecessors': ['', '', '1.1', '1', '', '2.1', '2.2', '2', '', ''],
        'Resources': [
            '项目经理',
            '业务分析师',
            '项目经理',
            '架构师',
            '架构师',
            '设计师',
            '开发工程师',
            '开发团队',
            '前端工程师',
            '后端工程师'
        ]
    }
    
    # 创建资源数据
    resources_data = {
        'Resource Name': [
            '项目经理',
            '业务分析师',
            '架构师',
            '设计师',
            '前端工程师',
            '后端工程师',
            '测试工程师'
        ],
        'Type': ['Work', 'Work', 'Work', 'Work', 'Work', 'Work', 'Work'],
        'Units': ['人', '人', '人', '人', '人', '人', '人'],
        'Cost': [800, 600, 700, 500, 550, 600, 450],
        'Calendar': [
            '标准日历',
            '标准日历',
            '标准日历',
            '标准日历',
            '标准日历',
            '标准日历',
            '标准日历'
        ]
    }
    
    # 创建项目信息
    project_info = {
        'Property': [
            'Name',
            'Start Date',
            'Finish Date',
            'Manager',
            'Company',
            'Description'
        ],
        'Value': [
            'AI-DEEP-CPMS开发项目',
            '2025-01-01',
            '2025-02-25',
            '张三',
            'AI-DEEP-CPMS团队',
            '智能深度精细化项目管理系统开发项目'
        ]
    }
    
    # 创建Excel文件
    filename = 'test_project.xlsx'
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 写入任务数据
        tasks_df = pd.DataFrame(tasks_data)
        tasks_df.to_excel(writer, sheet_name='Tasks', index=False)
        
        # 写入资源数据
        resources_df = pd.DataFrame(resources_data)
        resources_df.to_excel(writer, sheet_name='Resources', index=False)
        
        # 写入项目信息
        info_df = pd.DataFrame(project_info)
        info_df.to_excel(writer, sheet_name='Project Info', index=False)
    
    print(f"✅ 测试Excel文件已创建: {filename}")
    print(f"   文件大小: {os.path.getsize(filename)} bytes")
    print(f"   包含工作表: Tasks, Resources, Project Info")
    print(f"   任务数量: {len(tasks_data['WBS'])}")
    print(f"   资源数量: {len(resources_data['Resource Name'])}")
    
    return filename

def create_test_csv():
    """创建测试用的CSV文件"""
    
    # 创建简化的任务数据
    csv_data = {
        'WBS': ['1', '1.1', '1.2', '2', '2.1', '2.2'],
        '任务名称': [
            '项目启动',
            '需求分析', 
            '项目计划',
            '系统设计',
            '架构设计',
            '详细设计'
        ],
        '工期': [10, 5, 3, 15, 7, 5],
        '开始时间': [
            '2025-01-01',
            '2025-01-01',
            '2025-01-06',
            '2025-01-11',
            '2025-01-11',
            '2025-01-18'
        ],
        '完成时间': [
            '2025-01-10',
            '2025-01-05',
            '2025-01-08',
            '2025-01-25',
            '2025-01-17',
            '2025-01-22'
        ],
        '前置任务': ['', '', '1.1', '1', '', '2.1'],
        '资源': [
            '项目经理',
            '业务分析师',
            '项目经理',
            '架构师',
            '架构师',
            '设计师'
        ]
    }
    
    filename = 'test_project.csv'
    df = pd.DataFrame(csv_data)
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    
    print(f"✅ 测试CSV文件已创建: {filename}")
    print(f"   文件大小: {os.path.getsize(filename)} bytes")
    print(f"   任务数量: {len(csv_data['WBS'])}")
    
    return filename

def create_test_json():
    """创建测试用的JSON文件"""
    
    project_data = {
        "name": "AI-DEEP-CPMS开发项目",
        "description": "智能深度精细化项目管理系统开发项目",
        "start_date": "2025-01-01",
        "finish_date": "2025-02-25",
        "manager": "张三",
        "company": "AI-DEEP-CPMS团队",
        "tasks": [
            {
                "wbs_code": "1",
                "name": "项目启动",
                "duration": 10,
                "start_date": "2025-01-01",
                "finish_date": "2025-01-10",
                "predecessors": "",
                "resources": "项目经理"
            },
            {
                "wbs_code": "1.1",
                "name": "需求分析",
                "duration": 5,
                "start_date": "2025-01-01",
                "finish_date": "2025-01-05",
                "predecessors": "",
                "resources": "业务分析师"
            },
            {
                "wbs_code": "1.2",
                "name": "项目计划",
                "duration": 3,
                "start_date": "2025-01-06",
                "finish_date": "2025-01-08",
                "predecessors": "1.1",
                "resources": "项目经理"
            },
            {
                "wbs_code": "2",
                "name": "系统设计",
                "duration": 15,
                "start_date": "2025-01-11",
                "finish_date": "2025-01-25",
                "predecessors": "1",
                "resources": "架构师"
            }
        ],
        "resources": [
            {
                "name": "项目经理",
                "type": "Work",
                "units": "人",
                "cost": 800,
                "calendar": "标准日历"
            },
            {
                "name": "业务分析师",
                "type": "Work", 
                "units": "人",
                "cost": 600,
                "calendar": "标准日历"
            },
            {
                "name": "架构师",
                "type": "Work",
                "units": "人", 
                "cost": 700,
                "calendar": "标准日历"
            }
        ],
        "metadata": {
            "created_by": "AI-DEEP-CPMS",
            "created_at": datetime.now().isoformat(),
            "version": "1.0"
        }
    }
    
    filename = 'test_project.json'
    import json
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(project_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 测试JSON文件已创建: {filename}")
    print(f"   文件大小: {os.path.getsize(filename)} bytes")
    print(f"   任务数量: {len(project_data['tasks'])}")
    print(f"   资源数量: {len(project_data['resources'])}")
    
    return filename

if __name__ == '__main__':
    print("创建测试项目文件...")
    print("=" * 50)
    
    excel_file = create_test_excel()
    print()
    csv_file = create_test_csv()
    print()
    json_file = create_test_json()
    
    print("\n" + "=" * 50)
    print("测试文件创建完成！")
    print("\n可用于测试的文件:")
    print(f"1. Excel格式: {excel_file}")
    print(f"2. CSV格式: {csv_file}")
    print(f"3. JSON格式: {json_file}")
    print("\n这些文件可以用于测试AI-DEEP-CPMS的项目导入功能。")
