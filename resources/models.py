from django.db import models
from projects.models import Project
from quotas.models import ProjectQuota


class ProjectResource(models.Model):
    """项目资源信息模型"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='resources', verbose_name='项目')
    quota = models.ForeignKey(
        ProjectQuota,
        on_delete=models.CASCADE,
        related_name='resources',
        verbose_name='定额项',
        null=True, blank=True
    )

    # 资源基本信息
    resource_number = models.CharField(max_length=50, verbose_name='资源编号')
    name = models.CharField(max_length=200, verbose_name='资源名称')

    # 资源分类
    CATEGORY_CHOICES = [
        ('labor', '人工'),
        ('material', '材料'),
        ('machinery', '机械'),
        ('indirect', '间接费用'),
    ]
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, verbose_name='资源类别')

    unit = models.CharField(max_length=50, verbose_name='单位')

    # 价格信息
    market_price = models.DecimalField(
        max_digits=10, decimal_places=2,
        verbose_name='市场单价',
        default=0,
        help_text='当前市场价格'
    )
    budget_price = models.DecimalField(
        max_digits=10, decimal_places=2,
        verbose_name='预算单价',
        default=0,
        help_text='项目预算价格'
    )

    # 供应商信息
    supplier = models.CharField(max_length=200, verbose_name='供应商', blank=True)
    supplier_contact = models.CharField(max_length=100, verbose_name='供应商联系方式', blank=True)

    # 规格型号
    specification = models.TextField(verbose_name='规格型号', blank=True)

    # 备注
    remarks = models.TextField(verbose_name='备注', blank=True)

    # 是否启用
    is_active = models.BooleanField(default=True, verbose_name='是否启用')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '项目资源'
        verbose_name_plural = '项目资源'
        unique_together = ['project', 'resource_number']
        ordering = ['category', 'resource_number']

    def __str__(self):
        return f"{self.resource_number} - {self.name}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        # 如果关联了定额，更新定额中的资源价格
        if self.quota:
            try:
                consumption = self.quota.resource_consumptions.get(resource_number=self.resource_number)
                consumption.unit_price = self.budget_price
                consumption.save()
            except:
                pass


class ResourceCostSplit(models.Model):
    """资源成本拆分模型"""
    resource = models.ForeignKey(
        ProjectResource,
        on_delete=models.CASCADE,
        related_name='cost_splits',
        verbose_name='资源'
    )
    description = models.CharField(
        max_length=200,
        verbose_name='拆分描述'
    )
    percentage = models.DecimalField(
        max_digits=5, decimal_places=2,
        verbose_name='拆分比例（%）',
        help_text='0-100之间的数值'
    )
    cost = models.DecimalField(
        max_digits=10, decimal_places=2,
        verbose_name='拆分成本'
    )

    # 成本类型
    COST_TYPE_CHOICES = [
        ('material', '材料费'),
        ('labor', '人工费'),
        ('machinery', '机械费'),
        ('management', '管理费'),
        ('profit', '利润'),
        ('tax', '税费'),
        ('other', '其他'),
    ]
    cost_type = models.CharField(max_length=20, choices=COST_TYPE_CHOICES, verbose_name='成本类型')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '资源成本拆分'
        verbose_name_plural = '资源成本拆分'
        ordering = ['cost_type', 'percentage']

    def __str__(self):
        return f"{self.resource.name} - {self.description} ({self.percentage}%)"

    def save(self, *args, **kwargs):
        # 根据比例计算成本
        if self.resource and self.percentage:
            self.cost = (self.resource.budget_price * self.percentage) / 100
        super().save(*args, **kwargs)
