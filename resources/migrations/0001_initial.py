# Generated by Django 5.2.3 on 2025-06-15 23:58

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('projects', '0001_initial'),
        ('quotas', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProjectResource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('resource_number', models.CharField(max_length=50, verbose_name='资源编号')),
                ('name', models.CharField(max_length=200, verbose_name='资源名称')),
                ('category', models.CharField(choices=[('labor', '人工'), ('material', '材料'), ('machinery', '机械'), ('indirect', '间接费用')], max_length=20, verbose_name='资源类别')),
                ('unit', models.CharField(max_length=50, verbose_name='单位')),
                ('market_price', models.DecimalField(decimal_places=2, default=0, help_text='当前市场价格', max_digits=10, verbose_name='市场单价')),
                ('budget_price', models.DecimalField(decimal_places=2, default=0, help_text='项目预算价格', max_digits=10, verbose_name='预算单价')),
                ('supplier', models.CharField(blank=True, max_length=200, verbose_name='供应商')),
                ('supplier_contact', models.CharField(blank=True, max_length=100, verbose_name='供应商联系方式')),
                ('specification', models.TextField(blank=True, verbose_name='规格型号')),
                ('remarks', models.TextField(blank=True, verbose_name='备注')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resources', to='projects.project', verbose_name='项目')),
                ('quota', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='resources', to='quotas.projectquota', verbose_name='定额项')),
            ],
            options={
                'verbose_name': '项目资源',
                'verbose_name_plural': '项目资源',
                'ordering': ['category', 'resource_number'],
                'unique_together': {('project', 'resource_number')},
            },
        ),
        migrations.CreateModel(
            name='ResourceCostSplit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(max_length=200, verbose_name='拆分描述')),
                ('percentage', models.DecimalField(decimal_places=2, help_text='0-100之间的数值', max_digits=5, verbose_name='拆分比例（%）')),
                ('cost', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='拆分成本')),
                ('cost_type', models.CharField(choices=[('material', '材料费'), ('labor', '人工费'), ('machinery', '机械费'), ('management', '管理费'), ('profit', '利润'), ('tax', '税费'), ('other', '其他')], max_length=20, verbose_name='成本类型')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('resource', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cost_splits', to='resources.projectresource', verbose_name='资源')),
            ],
            options={
                'verbose_name': '资源成本拆分',
                'verbose_name_plural': '资源成本拆分',
                'ordering': ['cost_type', 'percentage'],
            },
        ),
    ]
