from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.core.files.storage import default_storage
from django.conf import settings
import pandas as pd
import json
import os
import tempfile
from datetime import datetime, timedelta
from .models import Project, Holiday, ProjectListItem
from .serializers import ProjectSerializer, ProjectCreateSerializer, HolidaySerializer, ProjectListItemSerializer
from .file_formats import file_handler
from tasks.models import Task, TaskDependency


class ProjectViewSet(viewsets.ModelViewSet):
    serializer_class = ProjectSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Project.objects.filter(owner=self.request.user)

    def get_serializer_class(self):
        if self.action == 'create':
            return ProjectCreateSerializer
        return ProjectSerializer

    @action(detail=True, methods=['get'])
    def gantt_data(self, request, pk=None):
        """获取甘特图数据"""
        project = self.get_object()
        tasks = list(project.tasks.all().prefetch_related('predecessor_dependencies', 'successor_dependencies'))

        task_data = []
        for task in tasks:
            task_data.append({
                'id': task.id,
                'name': f"{task.wbs_code} {task.name}",
                'start': task.start_date.isoformat(),
                'end': task.end_date.isoformat(),
                'parent': task.parent.id if task.parent else None,
                'critical': task.is_critical,
                'progress': float(task.progress_percentage) / 100,
                'status': task.status
            })

        dependency_data = []
        for task in tasks:
            for dep in task.predecessor_dependencies.all():
                dependency_data.append({
                    'from': dep.from_task.id,
                    'to': dep.to_task.id,
                    'type': dep.dependency_type,
                    'lag': dep.lag_time
                })

        if tasks:
            min_date = min([task.start_date for task in tasks]).timestamp() * 1000
            max_date = max([task.end_date for task in tasks]).timestamp() * 1000
        else:
            min_date = project.start_date.timestamp() * 1000
            max_date = min_date + (30 * 24 * 60 * 60 * 1000)  # 30天后

        return Response({
            'tasks': task_data,
            'dependencies': dependency_data,
            'min_date': min_date,
            'max_date': max_date
        })

    @action(detail=True, methods=['post'])
    def import_list(self, request, pk=None):
        """导入项目清单"""
        project = self.get_object()

        if 'file' not in request.FILES:
            return Response({'error': '请选择文件'}, status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['file']
        filename = file.name.lower()

        try:
            if filename.endswith('.xlsx') or filename.endswith('.xls'):
                df = pd.read_excel(file)

                # 检查必要的列
                required_columns = ['编码', '描述', '单位', '数量', '综合单价']
                missing_columns = [col for col in required_columns if col not in df.columns]
                if missing_columns:
                    return Response({
                        'error': f'缺少必要的列: {", ".join(missing_columns)}'
                    }, status=status.HTTP_400_BAD_REQUEST)

                created_count = 0
                for index, row in df.iterrows():
                    try:
                        list_item, created = ProjectListItem.objects.get_or_create(
                            project=project,
                            code=str(row['编码']),
                            defaults={
                                'description': str(row['描述']),
                                'work_content': str(row.get('工作内容', '')),
                                'unit': str(row['单位']),
                                'quantity': float(row['数量']),
                                'comprehensive_unit_price': float(row['综合单价'])
                            }
                        )
                        if created:
                            created_count += 1
                    except Exception as e:
                        continue

                return Response({
                    'message': f'成功导入 {created_count} 条清单项',
                    'created_count': created_count
                })

            else:
                return Response({
                    'error': '不支持的文件格式，请使用Excel文件'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'error': f'文件处理错误: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def import_project(self, request):
        """导入项目文件"""
        if 'file' not in request.FILES:
            return Response({'error': '请选择文件'}, status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['file']

        try:
            # 保存临时文件
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.name)[1])
            for chunk in file.chunks():
                temp_file.write(chunk)
            temp_file.close()

            # 读取项目数据
            project_data = file_handler.read_project_file(temp_file.name)

            # 创建项目
            project = Project.objects.create(
                name=project_data.get('name', file.name),
                description=f"从文件 {file.name} 导入",
                owner=request.user,
                start_date=datetime.now().date(),
                status='planning'
            )

            # 创建任务
            task_map = {}
            for task_data in project_data.get('tasks', []):
                try:
                    start_date = datetime.now()
                    if task_data.get('start_date'):
                        try:
                            if isinstance(task_data['start_date'], str):
                                start_date = datetime.strptime(task_data['start_date'], '%Y-%m-%d')
                            else:
                                start_date = task_data['start_date']
                        except:
                            pass

                    duration = task_data.get('duration', 1)
                    end_date = start_date + timedelta(days=duration)

                    task = Task.objects.create(
                        project=project,
                        wbs_code=task_data.get('wbs_code', ''),
                        name=task_data.get('name', ''),
                        description=task_data.get('description', ''),
                        unit='天',
                        quantity=duration,
                        daily_efficiency=1,
                        start_date=start_date,
                        end_date=end_date,
                        status='not_started'
                    )
                    task_map[task_data.get('wbs_code', '')] = task

                except Exception as e:
                    continue

            # 处理任务依赖关系
            for task_data in project_data.get('tasks', []):
                predecessors = task_data.get('predecessors', '')
                if predecessors and task_data.get('wbs_code') in task_map:
                    to_task = task_map[task_data['wbs_code']]
                    for pred_wbs in predecessors.split(','):
                        pred_wbs = pred_wbs.strip()
                        if pred_wbs in task_map:
                            from_task = task_map[pred_wbs]
                            TaskDependency.objects.get_or_create(
                                from_task=from_task,
                                to_task=to_task,
                                defaults={'dependency_type': 'FS'}
                            )

            # 清理临时文件
            os.unlink(temp_file.name)

            return Response({
                'message': '项目导入成功',
                'project_id': project.id,
                'project_name': project.name,
                'tasks_count': len(task_map),
                'file_format': file_handler.detect_file_format(file.name)
            })

        except Exception as e:
            # 清理临时文件
            if 'temp_file' in locals():
                try:
                    os.unlink(temp_file.name)
                except:
                    pass

            return Response({
                'error': f'项目导入失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def export_project(self, request, pk=None):
        """导出项目文件"""
        project = self.get_object()
        export_format = request.query_params.get('format', 'xlsx')

        try:
            # 准备项目数据
            project_data = {
                'name': project.name,
                'description': project.description,
                'start_date': project.start_date,
                'created_date': project.created_at,
                'tasks': [],
                'resources': [],
                'metadata': {
                    'exported_by': request.user.username,
                    'exported_at': datetime.now(),
                    'system': 'AI-DEEP-CPMS'
                }
            }

            # 获取任务数据
            tasks = project.tasks.all().order_by('wbs_code')
            for task in tasks:
                task_data = {
                    'wbs_code': task.wbs_code,
                    'name': task.name,
                    'description': task.description,
                    'duration': task.planned_duration or 0,
                    'start_date': task.start_date,
                    'finish_date': task.end_date,
                    'status': task.status,
                    'progress': float(task.progress_percentage),
                    'predecessors': ','.join([
                        dep.from_task.wbs_code
                        for dep in task.predecessor_dependencies.all()
                    ])
                }
                project_data['tasks'].append(task_data)

            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(
                delete=False,
                suffix=f'.{export_format}',
                prefix=f'{project.name}_'
            )
            temp_file.close()

            # 导出文件
            file_handler.write_project_file(project_data, temp_file.name, export_format)

            # 读取文件内容
            with open(temp_file.name, 'rb') as f:
                file_content = f.read()

            # 清理临时文件
            os.unlink(temp_file.name)

            # 返回文件
            response = HttpResponse(
                file_content,
                content_type=self._get_content_type(export_format)
            )
            response['Content-Disposition'] = f'attachment; filename="{project.name}.{export_format}"'

            return response

        except Exception as e:
            return Response({
                'error': f'项目导出失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_content_type(self, file_format):
        """获取文件MIME类型"""
        content_types = {
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'xls': 'application/vnd.ms-excel',
            'xml': 'application/xml',
            'json': 'application/json',
            'csv': 'text/csv',
            'pdf': 'application/pdf'
        }
        return content_types.get(file_format, 'application/octet-stream')

    @action(detail=False, methods=['get'])
    def supported_formats(self, request):
        """获取支持的文件格式"""
        return Response({
            'read_formats': file_handler.get_supported_formats('read'),
            'write_formats': file_handler.get_supported_formats('write'),
            'description': 'AI-DEEP-CPMS支持多种项目管理文件格式的导入导出'
        })


class HolidayViewSet(viewsets.ModelViewSet):
    queryset = Holiday.objects.all()
    serializer_class = HolidaySerializer
    permission_classes = [IsAuthenticated]


class ProjectListItemViewSet(viewsets.ModelViewSet):
    serializer_class = ProjectListItemSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        project_id = self.request.query_params.get('project_id')
        if project_id:
            return ProjectListItem.objects.filter(project_id=project_id, project__owner=self.request.user)
        return ProjectListItem.objects.filter(project__owner=self.request.user)
