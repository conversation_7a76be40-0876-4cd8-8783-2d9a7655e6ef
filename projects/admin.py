from django.contrib import admin
from .models import Holiday, Project, ProjectListItem


@admin.register(Holiday)
class HolidayAdmin(admin.ModelAdmin):
    list_display = ['date', 'name', 'description']
    list_filter = ['date']
    search_fields = ['name', 'description']
    ordering = ['date']


@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ['name', 'owner', 'status', 'start_date', 'location', 'created_at']
    list_filter = ['status', 'start_date', 'created_at']
    search_fields = ['name', 'description', 'location']
    filter_horizontal = ['holidays']
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'owner', 'location', 'status')
        }),
        ('时间设置', {
            'fields': ('start_date', 'work_hours_per_day')
        }),
        ('工作日历', {
            'fields': ('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday', 'holidays')
        }),
    )


@admin.register(ProjectListItem)
class ProjectListItemAdmin(admin.ModelAdmin):
    list_display = ['code', 'description', 'project', 'unit', 'quantity', 'comprehensive_unit_price', 'comprehensive_total_price']
    list_filter = ['project', 'unit']
    search_fields = ['code', 'description', 'work_content']
    readonly_fields = ['comprehensive_total_price']
