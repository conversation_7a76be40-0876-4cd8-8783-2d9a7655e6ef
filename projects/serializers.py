from rest_framework import serializers
from .models import Project, Holiday, ProjectListItem


class HolidaySerializer(serializers.ModelSerializer):
    class Meta:
        model = Holiday
        fields = '__all__'


class ProjectListItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProjectListItem
        fields = '__all__'
        read_only_fields = ['comprehensive_total_price']


class ProjectSerializer(serializers.ModelSerializer):
    list_items = ProjectListItemSerializer(many=True, read_only=True)
    holidays = HolidaySerializer(many=True, read_only=True)
    owner_name = serializers.CharField(source='owner.username', read_only=True)
    
    class Meta:
        model = Project
        fields = '__all__'
        read_only_fields = ['owner', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        validated_data['owner'] = self.context['request'].user
        return super().create(validated_data)


class ProjectCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Project
        fields = ['name', 'description', 'start_date', 'location', 'work_hours_per_day', 
                 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    
    def create(self, validated_data):
        validated_data['owner'] = self.context['request'].user
        return super().create(validated_data)
