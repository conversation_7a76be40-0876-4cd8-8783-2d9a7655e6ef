from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import ProjectViewSet, HolidayViewSet, ProjectListItemViewSet

router = DefaultRouter()
router.register(r'projects', ProjectViewSet, basename='project')
router.register(r'holidays', HolidayViewSet)
router.register(r'list-items', ProjectListItemViewSet, basename='projectlistitem')

urlpatterns = [
    path('api/', include(router.urls)),
]
