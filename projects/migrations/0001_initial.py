# Generated by Django 5.2.3 on 2025-06-15 23:58

import datetime
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Holiday',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(unique=True, verbose_name='日期')),
                ('name', models.CharField(max_length=100, verbose_name='节日名称')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
            ],
            options={
                'verbose_name': '节假日',
                'verbose_name_plural': '节假日',
                'ordering': ['date'],
            },
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='项目名称')),
                ('description', models.TextField(blank=True, verbose_name='项目描述')),
                ('start_date', models.DateField(default=datetime.date.today, verbose_name='项目开始日期')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='项目地点')),
                ('work_hours_per_day', models.IntegerField(default=8, verbose_name='每天工作小时数')),
                ('monday', models.BooleanField(default=True, verbose_name='周一')),
                ('tuesday', models.BooleanField(default=True, verbose_name='周二')),
                ('wednesday', models.BooleanField(default=True, verbose_name='周三')),
                ('thursday', models.BooleanField(default=True, verbose_name='周四')),
                ('friday', models.BooleanField(default=True, verbose_name='周五')),
                ('saturday', models.BooleanField(default=False, verbose_name='周六')),
                ('sunday', models.BooleanField(default=False, verbose_name='周日')),
                ('status', models.CharField(choices=[('planning', '规划中'), ('active', '进行中'), ('paused', '暂停'), ('completed', '已完成'), ('cancelled', '已取消')], default='planning', max_length=20, verbose_name='项目状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('holidays', models.ManyToManyField(blank=True, to='projects.holiday', verbose_name='节假日')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='项目负责人')),
            ],
            options={
                'verbose_name': '项目',
                'verbose_name_plural': '项目',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProjectListItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, verbose_name='清单编码')),
                ('description', models.TextField(verbose_name='清单描述')),
                ('work_content', models.TextField(blank=True, verbose_name='清单工作内容')),
                ('unit', models.CharField(max_length=50, verbose_name='清单单位')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='清单数量')),
                ('comprehensive_unit_price', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='清单综合单价')),
                ('comprehensive_total_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='清单综合合价')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='list_items', to='projects.project', verbose_name='项目')),
            ],
            options={
                'verbose_name': '项目清单项',
                'verbose_name_plural': '项目清单项',
                'ordering': ['code'],
                'unique_together': {('project', 'code')},
            },
        ),
    ]
