"""
项目文件格式处理模块
支持多种项目管理文件格式的导入导出
"""
import os
import json
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd
from django.core.files.storage import default_storage
from django.conf import settings


class ProjectFileHandler:
    """项目文件处理器"""
    
    # 支持的文件格式
    SUPPORTED_FORMATS = {
        'read': {
            'mpp': 'Microsoft Project',
            'mpt': 'Microsoft Project Template', 
            'xml': 'Microsoft Project XML',
            'mpx': 'Microsoft Project Exchange',
            'xlsx': 'Excel Workbook',
            'xls': 'Excel 97-2003',
            'csv': 'Comma Separated Values',
            'json': 'JSON Format',
            'gan': 'GanttProject',
            'planner': 'Planner',
            'pp': 'Asta Powerproject',
        },
        'write': {
            'xml': 'Microsoft Project XML',
            'mpx': 'Microsoft Project Exchange', 
            'xlsx': 'Excel Workbook',
            'csv': 'Comma Separated Values',
            'json': 'JSON Format',
            'pdf': 'PDF Report',
        }
    }
    
    def __init__(self):
        self.temp_dir = os.path.join(settings.MEDIA_ROOT, 'temp')
        os.makedirs(self.temp_dir, exist_ok=True)
    
    def get_supported_formats(self, operation='read'):
        """获取支持的文件格式"""
        return self.SUPPORTED_FORMATS.get(operation, {})
    
    def detect_file_format(self, file_path: str) -> str:
        """检测文件格式"""
        _, ext = os.path.splitext(file_path)
        return ext.lower().lstrip('.')
    
    def read_excel_project(self, file_path: str) -> Dict[str, Any]:
        """读取Excel项目文件"""
        try:
            # 读取Excel文件的多个工作表
            excel_file = pd.ExcelFile(file_path)
            project_data = {
                'name': os.path.splitext(os.path.basename(file_path))[0],
                'tasks': [],
                'resources': [],
                'calendars': [],
                'metadata': {}
            }
            
            # 读取任务工作表
            if 'Tasks' in excel_file.sheet_names or '任务' in excel_file.sheet_names:
                sheet_name = 'Tasks' if 'Tasks' in excel_file.sheet_names else '任务'
                tasks_df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                for _, row in tasks_df.iterrows():
                    task = {
                        'wbs_code': str(row.get('WBS', row.get('编码', ''))),
                        'name': str(row.get('Task Name', row.get('任务名称', ''))),
                        'duration': int(row.get('Duration', row.get('工期', 0))),
                        'start_date': row.get('Start', row.get('开始时间')),
                        'finish_date': row.get('Finish', row.get('完成时间')),
                        'predecessors': str(row.get('Predecessors', row.get('前置任务', ''))),
                        'resources': str(row.get('Resources', row.get('资源', ''))),
                    }
                    project_data['tasks'].append(task)
            
            # 读取资源工作表
            if 'Resources' in excel_file.sheet_names or '资源' in excel_file.sheet_names:
                sheet_name = 'Resources' if 'Resources' in excel_file.sheet_names else '资源'
                resources_df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                for _, row in resources_df.iterrows():
                    resource = {
                        'name': str(row.get('Resource Name', row.get('资源名称', ''))),
                        'type': str(row.get('Type', row.get('类型', 'Work'))),
                        'units': str(row.get('Units', row.get('单位', ''))),
                        'cost': float(row.get('Cost', row.get('成本', 0))),
                        'calendar': str(row.get('Calendar', row.get('日历', ''))),
                    }
                    project_data['resources'].append(resource)
            
            return project_data
            
        except Exception as e:
            raise Exception(f"读取Excel文件失败: {str(e)}")
    
    def read_csv_project(self, file_path: str) -> Dict[str, Any]:
        """读取CSV项目文件"""
        try:
            df = pd.read_csv(file_path, encoding='utf-8')
            project_data = {
                'name': os.path.splitext(os.path.basename(file_path))[0],
                'tasks': [],
                'resources': [],
                'calendars': [],
                'metadata': {}
            }
            
            for _, row in df.iterrows():
                task = {
                    'wbs_code': str(row.get('WBS', row.get('编码', ''))),
                    'name': str(row.get('Task Name', row.get('任务名称', ''))),
                    'duration': int(row.get('Duration', row.get('工期', 0))),
                    'start_date': row.get('Start', row.get('开始时间')),
                    'finish_date': row.get('Finish', row.get('完成时间')),
                    'predecessors': str(row.get('Predecessors', row.get('前置任务', ''))),
                    'resources': str(row.get('Resources', row.get('资源', ''))),
                }
                project_data['tasks'].append(task)
            
            return project_data
            
        except Exception as e:
            raise Exception(f"读取CSV文件失败: {str(e)}")
    
    def read_json_project(self, file_path: str) -> Dict[str, Any]:
        """读取JSON项目文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)
            return project_data
        except Exception as e:
            raise Exception(f"读取JSON文件失败: {str(e)}")
    
    def read_xml_project(self, file_path: str) -> Dict[str, Any]:
        """读取XML项目文件（简化版Microsoft Project XML）"""
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            project_data = {
                'name': root.get('Name', os.path.splitext(os.path.basename(file_path))[0]),
                'tasks': [],
                'resources': [],
                'calendars': [],
                'metadata': {}
            }
            
            # 解析任务
            for task_elem in root.findall('.//Task'):
                task = {
                    'wbs_code': task_elem.get('WBS', ''),
                    'name': task_elem.get('Name', ''),
                    'duration': int(task_elem.get('Duration', 0)),
                    'start_date': task_elem.get('Start'),
                    'finish_date': task_elem.get('Finish'),
                    'predecessors': task_elem.get('Predecessors', ''),
                    'resources': task_elem.get('Resources', ''),
                }
                project_data['tasks'].append(task)
            
            # 解析资源
            for resource_elem in root.findall('.//Resource'):
                resource = {
                    'name': resource_elem.get('Name', ''),
                    'type': resource_elem.get('Type', 'Work'),
                    'units': resource_elem.get('Units', ''),
                    'cost': float(resource_elem.get('Cost', 0)),
                    'calendar': resource_elem.get('Calendar', ''),
                }
                project_data['resources'].append(resource)
            
            return project_data
            
        except Exception as e:
            raise Exception(f"读取XML文件失败: {str(e)}")
    
    def read_project_file(self, file_path: str) -> Dict[str, Any]:
        """读取项目文件（自动检测格式）"""
        file_format = self.detect_file_format(file_path)
        
        if file_format in ['xlsx', 'xls']:
            return self.read_excel_project(file_path)
        elif file_format == 'csv':
            return self.read_csv_project(file_path)
        elif file_format == 'json':
            return self.read_json_project(file_path)
        elif file_format == 'xml':
            return self.read_xml_project(file_path)
        else:
            raise Exception(f"不支持的文件格式: {file_format}")
    
    def write_excel_project(self, project_data: Dict[str, Any], file_path: str):
        """导出Excel项目文件"""
        try:
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 导出任务
                if project_data.get('tasks'):
                    tasks_df = pd.DataFrame(project_data['tasks'])
                    tasks_df.to_excel(writer, sheet_name='Tasks', index=False)
                
                # 导出资源
                if project_data.get('resources'):
                    resources_df = pd.DataFrame(project_data['resources'])
                    resources_df.to_excel(writer, sheet_name='Resources', index=False)
                
                # 导出项目信息
                project_info = {
                    'Property': ['Name', 'Start Date', 'Finish Date', 'Created'],
                    'Value': [
                        project_data.get('name', ''),
                        project_data.get('start_date', ''),
                        project_data.get('finish_date', ''),
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    ]
                }
                info_df = pd.DataFrame(project_info)
                info_df.to_excel(writer, sheet_name='Project Info', index=False)
                
        except Exception as e:
            raise Exception(f"导出Excel文件失败: {str(e)}")
    
    def write_json_project(self, project_data: Dict[str, Any], file_path: str):
        """导出JSON项目文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            raise Exception(f"导出JSON文件失败: {str(e)}")
    
    def write_xml_project(self, project_data: Dict[str, Any], file_path: str):
        """导出XML项目文件"""
        try:
            root = ET.Element('Project')
            root.set('Name', project_data.get('name', ''))
            root.set('StartDate', str(project_data.get('start_date', '')))
            root.set('FinishDate', str(project_data.get('finish_date', '')))
            
            # 添加任务
            tasks_elem = ET.SubElement(root, 'Tasks')
            for task in project_data.get('tasks', []):
                task_elem = ET.SubElement(tasks_elem, 'Task')
                for key, value in task.items():
                    task_elem.set(key, str(value))
            
            # 添加资源
            resources_elem = ET.SubElement(root, 'Resources')
            for resource in project_data.get('resources', []):
                resource_elem = ET.SubElement(resources_elem, 'Resource')
                for key, value in resource.items():
                    resource_elem.set(key, str(value))
            
            tree = ET.ElementTree(root)
            tree.write(file_path, encoding='utf-8', xml_declaration=True)
            
        except Exception as e:
            raise Exception(f"导出XML文件失败: {str(e)}")
    
    def write_project_file(self, project_data: Dict[str, Any], file_path: str, file_format: str = None):
        """导出项目文件"""
        if not file_format:
            file_format = self.detect_file_format(file_path)
        
        if file_format in ['xlsx', 'xls']:
            self.write_excel_project(project_data, file_path)
        elif file_format == 'json':
            self.write_json_project(project_data, file_path)
        elif file_format == 'xml':
            self.write_xml_project(project_data, file_path)
        else:
            raise Exception(f"不支持的导出格式: {file_format}")


# 全局文件处理器实例
file_handler = ProjectFileHandler()
