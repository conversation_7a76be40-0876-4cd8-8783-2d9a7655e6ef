from django.db import models
from django.contrib.auth.models import User
from datetime import date
import math


class Holiday(models.Model):
    """节假日模型"""
    date = models.DateField(unique=True, verbose_name='日期')
    name = models.CharField(max_length=100, verbose_name='节日名称')
    description = models.TextField(blank=True, verbose_name='描述')

    class Meta:
        verbose_name = '节假日'
        verbose_name_plural = '节假日'
        ordering = ['date']

    def __str__(self):
        return f"{self.date} - {self.name}"


class Project(models.Model):
    """项目模型"""
    name = models.CharField(max_length=100, verbose_name='项目名称')
    description = models.TextField(verbose_name='项目描述', blank=True)
    owner = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='项目负责人')
    start_date = models.DateField(verbose_name='项目开始日期', default=date.today)
    location = models.Char<PERSON>ield(max_length=200, verbose_name='项目地点', blank=True)
    work_hours_per_day = models.IntegerField(verbose_name='每天工作小时数', default=8)

    # 工作日历相关字段
    monday = models.BooleanField(default=True, verbose_name='周一')
    tuesday = models.BooleanField(default=True, verbose_name='周二')
    wednesday = models.BooleanField(default=True, verbose_name='周三')
    thursday = models.BooleanField(default=True, verbose_name='周四')
    friday = models.BooleanField(default=True, verbose_name='周五')
    saturday = models.BooleanField(default=False, verbose_name='周六')
    sunday = models.BooleanField(default=False, verbose_name='周日')
    holidays = models.ManyToManyField(Holiday, blank=True, verbose_name='节假日')

    # 项目状态
    STATUS_CHOICES = [
        ('planning', '规划中'),
        ('active', '进行中'),
        ('paused', '暂停'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='planning', verbose_name='项目状态')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '项目'
        verbose_name_plural = '项目'
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def get_working_days(self):
        """获取工作日列表"""
        days = []
        if self.monday: days.append(0)  # 周一
        if self.tuesday: days.append(1)  # 周二
        if self.wednesday: days.append(2)  # 周三
        if self.thursday: days.append(3)  # 周四
        if self.friday: days.append(4)  # 周五
        if self.saturday: days.append(5)  # 周六
        if self.sunday: days.append(6)  # 周日
        return days

    def is_working_day(self, check_date):
        """检查某一天是否是工作日"""
        # 检查是否是节假日
        if self.holidays.filter(date=check_date).exists():
            return False
        # 检查是否是工作日
        weekday = check_date.weekday()
        return weekday in self.get_working_days()


class ProjectListItem(models.Model):
    """项目清单项模型"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='list_items', verbose_name='项目')
    code = models.CharField(max_length=50, verbose_name='清单编码')
    description = models.TextField(verbose_name='清单描述')
    work_content = models.TextField(verbose_name='清单工作内容', blank=True)
    unit = models.CharField(max_length=50, verbose_name='清单单位')
    quantity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='清单数量')
    comprehensive_unit_price = models.DecimalField(
        max_digits=10, decimal_places=2,
        verbose_name='清单综合单价',
        default=0
    )
    comprehensive_total_price = models.DecimalField(
        max_digits=10, decimal_places=2,
        verbose_name='清单综合合价',
        blank=True, null=True
    )

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '项目清单项'
        verbose_name_plural = '项目清单项'
        unique_together = ['project', 'code']
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.description}"

    def save(self, *args, **kwargs):
        # 自动计算综合合价
        self.comprehensive_total_price = self.quantity * self.comprehensive_unit_price
        super().save(*args, **kwargs)
