#!/usr/bin/env python
"""
测试登录功能的脚本
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_deep_cpms.settings')
django.setup()

from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from allauth.account.models import EmailAddress


def test_login():
    """测试登录功能"""
    print("测试登录功能...")
    
    # 测试用户登录
    test_email = '<EMAIL>'
    test_password = 'test123'
    
    print(f"尝试使用邮箱 {test_email} 登录...")
    
    # 检查用户是否存在
    try:
        user = User.objects.get(email=test_email)
        print(f"找到用户: {user.username}")
        
        # 检查邮箱是否验证
        try:
            email_address = EmailAddress.objects.get(user=user, email=test_email)
            print(f"邮箱验证状态: {email_address.verified}")
        except EmailAddress.DoesNotExist:
            print("邮箱地址记录不存在，创建中...")
            EmailAddress.objects.create(
                user=user,
                email=test_email,
                verified=True,
                primary=True
            )
            print("邮箱地址记录已创建并验证")
        
        # 测试认证
        auth_user = authenticate(username=user.username, password=test_password)
        if auth_user:
            print("✅ 认证成功！")
        else:
            print("❌ 认证失败！")
            
    except User.DoesNotExist:
        print(f"❌ 用户 {test_email} 不存在")
    
    print("\n" + "="*50)
    
    # 测试管理员登录
    admin_email = '<EMAIL>'
    admin_password = 'admin123'
    
    print(f"尝试使用管理员邮箱 {admin_email} 登录...")
    
    try:
        admin_user = User.objects.get(email=admin_email)
        print(f"找到管理员: {admin_user.username}")
        
        # 检查邮箱是否验证
        try:
            email_address = EmailAddress.objects.get(user=admin_user, email=admin_email)
            print(f"邮箱验证状态: {email_address.verified}")
        except EmailAddress.DoesNotExist:
            print("管理员邮箱地址记录不存在，创建中...")
            EmailAddress.objects.create(
                user=admin_user,
                email=admin_email,
                verified=True,
                primary=True
            )
            print("管理员邮箱地址记录已创建并验证")
        
        # 测试认证
        auth_user = authenticate(username=admin_user.username, password=admin_password)
        if auth_user:
            print("✅ 管理员认证成功！")
        else:
            print("❌ 管理员认证失败！")
            
    except User.DoesNotExist:
        print(f"❌ 管理员 {admin_email} 不存在")
    
    print("\n登录测试完成！")
    print("\n可用的登录信息：")
    print("测试用户: <EMAIL> / test123")
    print("管理员: <EMAIL> / admin123")


if __name__ == '__main__':
    test_login()
