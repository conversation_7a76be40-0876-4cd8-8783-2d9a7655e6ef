from rest_framework import serializers
from .models import Task, TaskDependency
from projects.models import ProjectListItem


class TaskDependencySerializer(serializers.ModelSerializer):
    from_task_name = serializers.CharField(source='from_task.name', read_only=True)
    to_task_name = serializers.CharField(source='to_task.name', read_only=True)
    
    class Meta:
        model = TaskDependency
        fields = '__all__'


class TaskSerializer(serializers.ModelSerializer):
    predecessor_dependencies = TaskDependencySerializer(many=True, read_only=True)
    successor_dependencies = TaskDependencySerializer(many=True, read_only=True)
    list_items = serializers.PrimaryKeyRelatedField(
        many=True, 
        queryset=ProjectListItem.objects.all(),
        required=False
    )
    total_cost = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    children_count = serializers.IntegerField(read_only=True)
    is_parent = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Task
        fields = '__all__'
        read_only_fields = ['planned_duration', 'created_at', 'updated_at']
    
    def to_representation(self, instance):
        data = super().to_representation(instance)
        data['total_cost'] = instance.get_total_cost()
        data['children_count'] = instance.get_children_count()
        data['is_parent'] = instance.is_parent_task()
        return data


class TaskCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = ['project', 'parent', 'wbs_code', 'name', 'description', 'unit', 
                 'quantity', 'daily_efficiency', 'start_date', 'end_date', 
                 'status', 'priority', 'order']


class TaskUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = ['name', 'description', 'unit', 'quantity', 'daily_efficiency', 
                 'start_date', 'end_date', 'actual_start_date', 'actual_end_date',
                 'progress_percentage', 'status', 'priority', 'list_items', 'order']
