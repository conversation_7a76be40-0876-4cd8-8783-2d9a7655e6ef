from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .models import Task, TaskDependency
from .serializers import TaskSerializer, TaskCreateSerializer, TaskUpdateSerializer, TaskDependencySerializer
from projects.models import ProjectListItem


class TaskViewSet(viewsets.ModelViewSet):
    serializer_class = TaskSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        project_id = self.request.query_params.get('project_id')
        if project_id:
            return Task.objects.filter(project_id=project_id, project__owner=self.request.user)
        return Task.objects.filter(project__owner=self.request.user)

    def get_serializer_class(self):
        if self.action == 'create':
            return TaskCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return TaskUpdateSerializer
        return TaskSerializer

    @action(detail=True, methods=['post'])
    def add_list_item(self, request, pk=None):
        """为任务添加关联清单项"""
        task = self.get_object()
        list_item_id = request.data.get('list_item_id')

        try:
            list_item = ProjectListItem.objects.get(
                id=list_item_id,
                project=task.project
            )
            task.list_items.add(list_item)
            return Response({'message': '清单项添加成功'})
        except ProjectListItem.DoesNotExist:
            return Response(
                {'error': '清单项不存在'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['delete'])
    def remove_list_item(self, request, pk=None):
        """移除任务的关联清单项"""
        task = self.get_object()
        list_item_id = request.data.get('list_item_id')

        try:
            list_item = ProjectListItem.objects.get(
                id=list_item_id,
                project=task.project
            )
            task.list_items.remove(list_item)
            return Response({'message': '清单项移除成功'})
        except ProjectListItem.DoesNotExist:
            return Response(
                {'error': '清单项不存在'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['post'])
    def add_dependency(self, request, pk=None):
        """添加任务依赖关系"""
        to_task = self.get_object()
        from_task_id = request.data.get('from_task_id')
        dependency_type = request.data.get('dependency_type', 'FS')
        lag_time = request.data.get('lag_time', 0)

        try:
            from_task = Task.objects.get(
                id=from_task_id,
                project=to_task.project
            )

            dependency, created = TaskDependency.objects.get_or_create(
                from_task=from_task,
                to_task=to_task,
                defaults={
                    'dependency_type': dependency_type,
                    'lag_time': lag_time
                }
            )

            if created:
                return Response({'message': '依赖关系添加成功'})
            else:
                return Response(
                    {'error': '依赖关系已存在'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        except Task.DoesNotExist:
            return Response(
                {'error': '前置任务不存在'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['post'])
    def calculate_critical_path(self, request):
        """计算关键路径"""
        project_id = request.data.get('project_id')

        if not project_id:
            return Response(
                {'error': '请提供项目ID'},
                status=status.HTTP_400_BAD_REQUEST
            )

        tasks = Task.objects.filter(
            project_id=project_id,
            project__owner=request.user
        )

        # 这里应该实现关键路径算法
        # 简化版本：将所有任务标记为非关键路径
        tasks.update(is_critical=False)

        # TODO: 实现完整的关键路径算法

        return Response({'message': '关键路径计算完成'})


class TaskDependencyViewSet(viewsets.ModelViewSet):
    serializer_class = TaskDependencySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        project_id = self.request.query_params.get('project_id')
        if project_id:
            return TaskDependency.objects.filter(
                from_task__project_id=project_id,
                from_task__project__owner=self.request.user
            )
        return TaskDependency.objects.filter(
            from_task__project__owner=self.request.user
        )
