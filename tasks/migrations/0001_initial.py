# Generated by Django 5.2.3 on 2025-06-15 23:58

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('projects', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Task',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('wbs_code', models.CharField(max_length=50, verbose_name='WBS标识')),
                ('name', models.CharField(max_length=200, verbose_name='任务名称')),
                ('description', models.TextField(blank=True, verbose_name='任务描述')),
                ('unit', models.CharField(blank=True, max_length=50, verbose_name='任务单位')),
                ('quantity', models.DecimalField(decimal_places=2, default=1, max_digits=10, verbose_name='工程数量')),
                ('daily_efficiency', models.DecimalField(decimal_places=2, default=1, help_text='每日计划完成的工程量', max_digits=10, verbose_name='每日工效')),
                ('planned_duration', models.IntegerField(blank=True, help_text='自动计算：工程数量/每日工效，向上取整', null=True, verbose_name='计划工期（天）')),
                ('start_date', models.DateTimeField(verbose_name='开始时间')),
                ('end_date', models.DateTimeField(verbose_name='计划完成时间')),
                ('actual_start_date', models.DateTimeField(blank=True, null=True, verbose_name='实际开始时间')),
                ('actual_end_date', models.DateTimeField(blank=True, null=True, verbose_name='实际完成时间')),
                ('progress_percentage', models.DecimalField(decimal_places=2, default=0, help_text='0-100之间的数值', max_digits=5, verbose_name='完成百分比')),
                ('status', models.CharField(choices=[('not_started', '未开始'), ('in_progress', '进行中'), ('completed', '已完成'), ('paused', '暂停'), ('cancelled', '已取消')], default='not_started', max_length=20, verbose_name='任务状态')),
                ('priority', models.CharField(choices=[('low', '低'), ('normal', '普通'), ('high', '高'), ('critical', '紧急')], default='normal', max_length=20, verbose_name='优先级')),
                ('is_critical', models.BooleanField(default=False, verbose_name='是否关键路径')),
                ('order', models.IntegerField(default=0, verbose_name='排序')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('list_items', models.ManyToManyField(blank=True, to='projects.projectlistitem', verbose_name='关联清单项')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='tasks.task', verbose_name='父任务')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='projects.project', verbose_name='项目')),
            ],
            options={
                'verbose_name': '任务',
                'verbose_name_plural': '任务',
                'ordering': ['order', 'wbs_code'],
                'unique_together': {('project', 'wbs_code')},
            },
        ),
        migrations.CreateModel(
            name='TaskDependency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dependency_type', models.CharField(choices=[('FS', 'FS:完成-开始'), ('SS', 'SS:开始-开始'), ('SF', 'SF:开始-完成'), ('FF', 'FF:完成-完成')], default='FS', max_length=2, verbose_name='依赖类型')),
                ('lag_time', models.IntegerField(default=0, help_text='正数表示延迟，负数表示提前', verbose_name='滞后时间（分钟）')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('from_task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='successor_dependencies', to='tasks.task', verbose_name='前置任务')),
                ('to_task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='predecessor_dependencies', to='tasks.task', verbose_name='后续任务')),
            ],
            options={
                'verbose_name': '任务依赖关系',
                'verbose_name_plural': '任务依赖关系',
                'unique_together': {('from_task', 'to_task')},
            },
        ),
    ]
