#!/usr/bin/env python
"""
测试MPXJ功能的脚本
"""
import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_deep_cpms.settings')
django.setup()

try:
    from mpxj import Project
    print("✅ MPXJ导入成功")
    print("MPXJ版本: 14.1.0")
except ImportError as e:
    print(f"❌ MPXJ导入失败: {e}")
    sys.exit(1)


def test_mpxj_capabilities():
    """测试MPXJ的功能"""
    print("\n" + "="*60)
    print("MPXJ功能测试")
    print("="*60)
    
    try:
        # 创建一个新的项目文件
        print("\n1. 创建新项目...")
        project = Project()
        
        # 设置项目基本信息
        project.project_properties.project_title = "AI-DEEP-CPMS测试项目"
        project.project_properties.company = "AI-DEEP-CPMS"
        project.project_properties.manager = "项目经理"
        project.project_properties.start_date = datetime.now()
        
        print(f"   项目标题: {project.project_properties.project_title}")
        print(f"   公司: {project.project_properties.company}")
        print(f"   项目经理: {project.project_properties.manager}")
        print(f"   开始日期: {project.project_properties.start_date}")
        
        # 创建日历
        print("\n2. 创建工作日历...")
        calendar = project.calendars.add()
        calendar.name = "标准工作日历"
        
        # 设置工作时间
        for day in range(1, 8):  # 周一到周日
            if day <= 5:  # 周一到周五
                calendar.set_working_day(day, True)
                # 设置工作时间 8:00-12:00, 13:00-17:00
                calendar.add_calendar_hours(day, datetime.strptime("08:00", "%H:%M").time(), 
                                          datetime.strptime("12:00", "%H:%M").time())
                calendar.add_calendar_hours(day, datetime.strptime("13:00", "%H:%M").time(), 
                                          datetime.strptime("17:00", "%H:%M").time())
            else:  # 周末
                calendar.set_working_day(day, False)
        
        print(f"   日历名称: {calendar.name}")
        print(f"   工作日: 周一到周五")
        print(f"   工作时间: 8:00-12:00, 13:00-17:00")
        
        # 创建资源
        print("\n3. 创建项目资源...")
        resources = [
            ("项目经理", "工日", 800),
            ("高级工程师", "工日", 600),
            ("普通工程师", "工日", 400),
            ("技术员", "工日", 300),
        ]
        
        for name, unit, cost in resources:
            resource = project.resources.add()
            resource.name = name
            resource.units = unit
            resource.standard_rate = cost
            print(f"   资源: {name} - {unit} - ¥{cost}")
        
        # 创建任务
        print("\n4. 创建项目任务...")
        tasks_data = [
            ("1", "项目启动", None, 5, None),
            ("1.1", "需求分析", "1", 3, None),
            ("1.2", "项目计划", "1", 2, "1.1"),
            ("2", "系统设计", None, 10, "1"),
            ("2.1", "架构设计", "2", 5, None),
            ("2.2", "详细设计", "2", 5, "2.1"),
            ("3", "开发实施", None, 20, "2"),
            ("3.1", "前端开发", "3", 10, None),
            ("3.2", "后端开发", "3", 10, None),
            ("3.3", "集成测试", "3", 5, "3.1,3.2"),
            ("4", "项目收尾", None, 3, "3"),
        ]
        
        task_map = {}
        start_date = datetime.now()
        
        for wbs, name, parent_wbs, duration, predecessors in tasks_data:
            task = project.tasks.add()
            task.wbs = wbs
            task.name = name
            task.duration = timedelta(days=duration) if duration else timedelta(days=0)
            task.start = start_date
            task.finish = start_date + task.duration
            
            # 设置父任务
            if parent_wbs and parent_wbs in task_map:
                task.parent_task = task_map[parent_wbs]
            
            task_map[wbs] = task
            print(f"   任务: {wbs} - {name} ({duration}天)")
            
            start_date += timedelta(days=1)
        
        # 设置任务依赖关系
        print("\n5. 设置任务依赖关系...")
        for wbs, name, parent_wbs, duration, predecessors in tasks_data:
            if predecessors:
                task = task_map[wbs]
                for pred_wbs in predecessors.split(','):
                    pred_task = task_map[pred_wbs.strip()]
                    task.add_predecessor(pred_task, mpxj.RelationType.FINISH_START, timedelta(0))
                    print(f"   依赖: {pred_wbs} -> {wbs}")
        
        print(f"\n✅ 项目创建成功!")
        print(f"   任务总数: {len(project.tasks)}")
        print(f"   资源总数: {len(project.resources)}")
        print(f"   日历总数: {len(project.calendars)}")
        
        return project
        
    except Exception as e:
        print(f"❌ 项目创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_file_formats(project):
    """测试文件格式支持"""
    if not project:
        return
        
    print("\n" + "="*60)
    print("文件格式支持测试")
    print("="*60)
    
    # 创建测试目录
    test_dir = "test_exports"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 支持的导出格式
    export_formats = [
        ("MPX", "mpx"),
        ("XML", "xml"),
        ("JSON", "json"),
        ("MSPDI", "xml"),  # Microsoft Project XML
    ]
    
    print("\n测试文件导出...")
    for format_name, extension in export_formats:
        try:
            filename = f"{test_dir}/test_project.{extension}"
            
            if format_name == "MPX":
                project.write_mpx(filename)
            elif format_name == "XML":
                project.write_xml(filename)
            elif format_name == "JSON":
                project.write_json(filename)
            elif format_name == "MSPDI":
                project.write_mspdi(filename)
            
            if os.path.exists(filename):
                size = os.path.getsize(filename)
                print(f"   ✅ {format_name}格式导出成功: {filename} ({size} bytes)")
            else:
                print(f"   ❌ {format_name}格式导出失败")
                
        except Exception as e:
            print(f"   ❌ {format_name}格式导出失败: {e}")
    
    print("\n测试文件导入...")
    # 测试导入刚才导出的文件
    for format_name, extension in export_formats:
        try:
            filename = f"{test_dir}/test_project.{extension}"
            if os.path.exists(filename):
                if format_name == "MPX":
                    imported_project = mpxj.Project.read_mpx(filename)
                elif format_name == "XML":
                    imported_project = mpxj.Project.read_xml(filename)
                elif format_name == "JSON":
                    imported_project = mpxj.Project.read_json(filename)
                elif format_name == "MSPDI":
                    imported_project = mpxj.Project.read_mspdi(filename)
                
                if imported_project:
                    print(f"   ✅ {format_name}格式导入成功: {len(imported_project.tasks)}个任务")
                else:
                    print(f"   ❌ {format_name}格式导入失败")
        except Exception as e:
            print(f"   ❌ {format_name}格式导入失败: {e}")


def print_supported_formats():
    """打印支持的文件格式"""
    print("\n" + "="*60)
    print("MPXJ支持的文件格式")
    print("="*60)
    
    print("\n📖 读取支持的格式:")
    read_formats = [
        "Microsoft Project (.mpp, .mpt)",
        "Microsoft Project XML (.xml)",
        "Microsoft Project Database (.mpd)",
        "Microsoft Project Exchange (.mpx)",
        "Primavera P6 (.xml, .xer)",
        "Primavera P3 (.prx)",
        "Primavera SureTrak (.stx)",
        "Asta Powerproject (.pp)",
        "ConceptDraw PROJECT (.cdpx, .cdpz)",
        "Deltek Open Plan (.bk?)",
        "FastTrack Schedule (.ft)",
        "GanttProject (.gan)",
        "Merlin (.merlin)",
        "Phoenix Project Manager (.ppx)",
        "Planner (.planner)",
        "Sage 100 Contractor (.schedule)",
        "Synchro (.sp)",
        "TurboProject (.tpx)",
        "Excel (.xlsx, .xls)",
        "JSON (.json)",
    ]
    
    for fmt in read_formats:
        print(f"   • {fmt}")
    
    print("\n📝 写入支持的格式:")
    write_formats = [
        "Microsoft Project XML (.xml)",
        "Microsoft Project Exchange (.mpx)",
        "Primavera P6 (.xml)",
        "Planner (.planner)",
        "JSON (.json)",
        "SDEF (.sdef)",
    ]
    
    for fmt in write_formats:
        print(f"   • {fmt}")


if __name__ == '__main__':
    print_supported_formats()
    project = test_mpxj_capabilities()
    test_file_formats(project)
    
    print("\n" + "="*60)
    print("MPXJ测试完成!")
    print("="*60)
