#!/usr/bin/env python
"""
验证用户邮箱的脚本
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_deep_cpms.settings')
django.setup()

from django.contrib.auth.models import User
from allauth.account.models import EmailAddress


def verify_all_users():
    """验证所有用户的邮箱"""
    print("开始验证用户邮箱...")
    
    users = User.objects.all()
    
    for user in users:
        if user.email:
            # 获取或创建邮箱地址记录
            email_address, created = EmailAddress.objects.get_or_create(
                user=user,
                email=user.email,
                defaults={
                    'verified': True,
                    'primary': True
                }
            )
            
            if not email_address.verified:
                email_address.verified = True
                email_address.primary = True
                email_address.save()
                print(f"已验证用户 {user.username} 的邮箱: {user.email}")
            else:
                print(f"用户 {user.username} 的邮箱已验证: {user.email}")
        else:
            print(f"用户 {user.username} 没有邮箱地址")
    
    print("邮箱验证完成！")


if __name__ == '__main__':
    verify_all_users()
